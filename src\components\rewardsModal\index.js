import React, { Component, useReducer, useState, useEffect, useRef } from 'react';
import {
    StyleSheet,
    ScrollView,
    Image,
    View,
    Text,
    TouchableOpacity,
    TextInput,
    FlatList,
    RefreshControl,
    Alert,
    Modal,
    Dimensions,
    Platform,
    useWindowDimensions,
    ActivityIndicator,
} from 'react-native';
import Colors from '../../constant/Colors';
import * as Cart from '../../util/Cart';
import * as User from '../../util/User';
import Styles from '../../constant/Styles';
// import ActionSheet from 'react-native-actionsheet';
import ApiClient from '../../util/ApiClient';
import API from '../../constant/API';
import Ionicons from 'react-native-vector-icons/Ionicons';
import Icon from 'react-native-vector-icons/Ionicons';
import Icons from 'react-native-vector-icons/Feather';
// import NumericInput from 'react-native-numeric-input';
// import { color } from 'react-native-reanimated';
// import QRCode from 'react-native-qrcode-svg';
import Close from 'react-native-vector-icons/AntDesign';
import { CommonStore } from '../../store/commonStore';
import { UserStore } from '../../store/userStore';
import moment from 'moment';
import Entypo from 'react-native-vector-icons/Entypo';
import AsyncImage from '../../components/asyncImage';
// import LinearGradient from 'react-native-linear-gradient';
import LinearGradient from 'react-native-web-linear-gradient';
import { prefix } from "../../constant/env";
import { useFocusEffect, useLinkTo } from "@react-navigation/native";

import FontAwesome from "react-native-vector-icons/FontAwesome";
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";

import { ReactComponent as CircleCheck } from "../../asset/svg/Circle-check.svg";
import { ReactComponent as GCalendar } from '../../svg/GCalendar.svg';

import { isMobile, signInWithPhoneForCRMUser, updateWebTokenAnonymous, } from "../../util/commonFuncs";
import { TempStore } from '../../store/tempStore';
import { LOYALTY_PROMOTION_TYPE } from "../../constant/loyalty";

import AsyncStorage from '@react-native-async-storage/async-storage';
import { DataStore } from '../../store/dataStore';
import AntDesign from 'react-native-vector-icons/AntDesign';

const RewardsModal = (props) => {
    const { navigation, route } = props;

    const { width: windowWidth, height: windowHeight } = useWindowDimensions();
    const linkTo = useLinkTo();

    const [cartItem, setCartItem] = useState([]);
    const [refreshing, setRefreshing] = useState(false);
    const [visible, setVisible] = useState(false);
    const [visible1, setVisible1] = useState(false);
    const [value, setValue] = useState('');
    const [voucher, setVoucher] = useState([]);
    const [qrCodeModalVisibility, setQrCodeModalVisibility] = useState(false);
    const [selectedItem, setSelectedItem] = useState({});
    const [expandDetails, setExpandDetails] = useState(false);
    const [name, setName] = useState('');
    const [phone, setPhone] = useState('');
    //const [pageFrom, setPageFrom] = useState(pageFromParam);

    const setState = () => { };

    const selectedOutlet = CommonStore.useState(s => s.selectedOutlet);
    const availableTaggableVouchers = CommonStore.useState(
        (s) => s.availableTaggableVouchers,
    );
    const selectedTaggableVoucher = CommonStore.useState(s => s.selectedTaggableVoucher);

    const selectedOutletItems = CommonStore.useState(s => s.selectedOutletItems);

    const selectedOutletItemsSkuDict = CommonStore.useState(s => s.selectedOutletItemsSkuDict);

    //const allOutletsItemsSkuDict = CommonStore.useState(s => s.allOutletsItemsSkuDict);

    /* useEffect(() => {
        if (availableTaggableVouchers !== '' && availableTaggableVouchers.length > 0 ) {
          var availablePromotionDetails = {};
  
          for (var i = 0; i < availableTaggableVouchers.length; i++) {
          var PromoDetails = {
          campaignName = availableTaggableVouchers[i].campaignName,
          campaignDescription = availableTaggableVouchers[i].campaignDescription
          };
  
          availablePromotionDetails[availableTaggableVouchers[i].uniqueId] = PromoDetails;
          }
  
        }
        }); */



    ///////////////////////// Test Functionalities /////////////////////////

    const selectedOutletItemCategoriesDict = CommonStore.useState(s => s.selectedOutletItemCategoriesDict);
    const selectedOutletItemsCategoriesDict = CommonStore.useState(s => s.selectedOutletItemsCategoriesDict);
    const selectedOutletItemCategory = CommonStore.useState(s => s.selectedOutletItemCategory);

    ///////////////////////// Test Functionalities /////////////////////////

    const [promotionItem, setPromotionItem] = useState({});

    ////////////////////////////////////////////////////////////////////////

    const userPointsBalance = UserStore.useState(s => s.userPointsBalance);

    const userName = UserStore.useState((s) => s.name);
    const userNumber = UserStore.useState((s) => s.number);
    const email = UserStore.useState((s) => s.email);
    const userIdAnonymous = UserStore.useState((s) => s.userIdAnonymous);

    const [birthday, setBirthday] = useState(moment(Date.now()));
    const [address, setAddress] = useState('');
    const [lat, setLat] = useState(0);
    const [lng, setLng] = useState(0);

    const isLoading = CommonStore.useState((s) => s.isLoading);
    const linkToFunc = DataStore.useState(s => s.linkToFunc);

    const rewardsModal = TempStore.useState((s) => s.rewardsModal);
    const claimVoucherAnonymous = TempStore.useState(s => s.claimVoucherAnonymous);

    const anonymousPointsBalance = UserStore.useState(s => s.anonymousPointsBalance);

    // const tempDes = 'asuifhaidgfhiuadhfuihaduifhiadgfyuagdiuffhiaudhiuahdfhauodhfoadyhfouahdofyhaodhfhioadhfiadf9qaejfiahdfihadihaodhfoadhfoadhfoaef0uadoifhaodhfofahdfouadifhaidhfoaeeidffhaoiddhfoaehfhae9ufjaedifvhaeodghaoisuf93qgvuehgvoaudgfivuoaeusfdoiaehdgfociahjsiodhgiawhsfiovaegsidhxiwadhvuaiuewsjdoujeadghfviaeskhfoaisfgvahoasuifhaidgfhiuadhfuihaduifhiadgfyuagdiuffhiaudhiuahdfhauodhfoadyhfouahdofyhaodhfhioadhfiadf9qaejfiahdfihadihaodhfoadhfoadhfoaef0uadoifhaodhfofahdfouadifhaidhfoaeeidffhaoiddhfoaehfhae9ufjaedifvhaeodghaoisuf93qgvuehgvoaudgfivuoaeusfdoiaehdgfociahjsiodhgiawhsfiovaegsidhxiwadhvuaiuewsjdoujeadghfviaeskhfoaisfgvahoasuifhaidgfhiuadhfuihaduifhiadgfyuagdiuffhiaudhiuahdfhauodhfoadyhfouahdofyhaodhfhioadhfiadf9qaejfiahdfihadihaodhfoadhfoadhfoaef0uadoifhaodhfofahdfouadifhaidhfoaeeidffhaoiddhfoaehfhae9ufjaedifvhaeodghaoisuf93qgvuehgvoaudgfivuoaeusfdoiaehdgfociahjsiodhgiawhsfiovaegsidhxiwadhvuaiuewsjdoujeadghfviaeskhfoaisfgvahoasuifhaidgfhiuadhfuihaduifhiadgfyuagdiuffhiaudhiuahdfhauodhfoadyhfouahdofyhaodhfhioadhfiadf9qaejfiahdfihadihaodhfoadhfoadhfoaef0uadoifhaodhfofahdfouadifhaidhfoaeeidffhaoiddhfoaehfhae9ufjaedifvhaeodghaoisuf93qgvuehgvoaudgfivuoaeusfdoiaehdgfociahjsiodhgiawhsfiovaegsidhxiwadhvuaiuewsjdoujeadghfviaeskhfoaisfgvahoasuifhaidgfhiuadhfuihaduifhiadgfyuagdiuffhiaudhiuahdfhauodhfoadyhfouahdofyhaodhfhioadhfiadf9qaejfiahdfihadihaodhfoadhfoadhfoaef0uadoifhaodhfofahdfouadifhaidhfoaeeidffhaoiddhfoaehfhae9ufjaedifvhaeodghaoisuf93qgvuehgvoaudgfivuoaeusfdoiaehdgfociahjsiodhgiawhsfiovaegsidhxiwadhvuaiuewsjdoujeadghfviaeskhfoaisfgvahoasuifhaidgfhiuadhfuihaduifhiadgfyuagdiuffhiaudhiuahdfhauodhfoadyhfouahdofyhaodhfhioadhfiadf9qaejfiahdfihadihaodhfoadhfoadhfoaef0uadoifhaodhfofahdfouadifhaidhfoaeeidffhaoiddhfoaehfhae9ufjaedifvhaeodghaoisuf93qgvuehgvoaudgfivuoaeusfdoiaehdgfociahjsiodhgiawhsfiovaegsidhxiwadhvuaiuewsjdoujeadghfviaeskhfoaisfgvaho idpxufyhchoaespfuhcoeuadkhfjpawsfh waifhnalesdxfuchahousjfghcjwpasikfgbneasiofkgvhdazxsdftgyuiakdemifuvhfatgeyudsjfnvfgyujhbvcxdfrtyujnbvftgyujhbvcftyhjbvftgyujnbvfghnbvfg'

    /* useEffect(() => {
        if (selectedTaggableVoucher && selectedTaggableVoucher.uniqueId) {
            if (promotionItem.uniqueId === undefined) {
                retrievePromotionItem();
            }
        }
    }, [selectedTaggableVoucher, promotionItem]);
  
    const retrievePromotionItem = async () => {
      const outletSnapshot = await firestore().collection(Collections.OutletItem)
          .where('uniqueId', '==', selectedTaggableVoucher.outletId)
          .limit(1)
          .get();
  
      if (!outletSnapshot.empty) {
          const outlet = outletSnapshot.docs[0].data();
  
          setPromotionItem(outlet);
      }
    }; */

    // navigation.setOptions({
    //     headerLeft: () => (
    //         <TouchableOpacity
    //             style={{}}
    //             onPress={() => {
    //                 props.navigation.goBack();
    //             }}>
    //             <View
    //                 style={{
    //                     marginLeft: 10,
    //                     display: 'flex',
    //                     flexDirection: 'row',
    //                     alignItems: 'center',
    //                     justifyContent: 'flex-start',
    //                 }}>
    //                 <Ionicons
    //                     name="chevron-back"
    //                     size={26}
    //                     color={Colors.fieldtTxtColor}
    //                     style={{}}
    //                 />

    //                 <Text
    //                     style={{
    //                         color: Colors.fieldtTxtColor,
    //                         fontSize: 16,
    //                         textAlign: 'center',
    //                         fontFamily: 'NunitoSans-Regular',
    //                         lineHeight: 22,
    //                         marginTop: -1,
    //                     }}>
    //                     Back
    //                 </Text>
    //             </View>
    //         </TouchableOpacity>
    //     ),
    //     headerRight: () => (
    //         <>
    //             {/* <TouchableOpacity
    //     onPress={() => {
    //       props.navigation.navigate('Profile');
    //     }}
    //     style={{}}>
    //     <View style={{ marginRight: 15 }}>
    //       <Ionicons name="menu" size={30} color={Colors.primaryColor} />
    //     </View>
    //   </TouchableOpacity> */}
    //         </>
    //     ),
    //     headerTitle: () => (
    //         <View
    //             style={{
    //                 justifyContent: 'center',
    //                 alignItems: 'center',
    //                 bottom: -1,
    //             }}>
    //             <Text
    //                 style={{
    //                     fontSize: 20,
    //                     lineHeight: 25,
    //                     textAlign: 'center',
    //                     fontFamily: 'NunitoSans-Bold',
    //                     color: Colors.mainTxtColor,
    //                 }}>
    //                 Reward Details
    //             </Text>
    //         </View>
    //     ),
    // });

    var itemNameFontSize = 15;

    if (windowWidth <= 360) {
        itemNameFontSize = 13;
        //console.log(windowWidth)
    }

    const itemNameTextScale = {
        fontSize: itemNameFontSize,
    };

    const renderPromotion = ({ item, index }) => {
        return (
            <View>
                {
                    item.criteriaList && item.criteriaList.map(outletItemSku => {
                        var outletItem = {};

                        let foundItem = selectedOutletItems.find(findItem => findItem.sku === outletItemSku.variationItemsSku);

                        if (foundItem) {
                            outletItem = foundItem;
                        }

                        return (

                            <View style={{ flexDirection: 'column' }}>
                                <View
                                    style={{
                                        flexDirection: 'row',
                                        padding: 15,
                                        alignItems: 'center',
                                        width: '70%',
                                    }}>

                                    <View>
                                        <View style={[{
                                            backgroundColor: Colors.secondaryColor,
                                            // width: 60,
                                            // height: 60,
                                            width: windowWidth * 0.22,
                                            height: windowWidth * 0.22,
                                            borderRadius: 10,
                                        }]}>
                                            {outletItem.image
                                                ?
                                                // <Image source={{ uri: item.image }} style={{
                                                //   width: windowWidth * 0.22,
                                                //   height: windowWidth * 0.22,
                                                //   borderRadius: 10
                                                // }} />
                                                <AsyncImage source={{ uri: outletItem.image }} item={outletItem} style={{
                                                    width: windowWidth * 0.22,
                                                    height: windowWidth * 0.22,
                                                    borderRadius: 10
                                                }} />
                                                :
                                                <Ionicons name="fast-food-outline" size={50} />
                                            }
                                        </View>
                                    </View>
                                    <View style={{ flexDirection: 'column' }}>
                                        <Text
                                            style={{
                                                marginLeft: 5,
                                                fontSize: 16,
                                                fontFamily: 'NunitoSans-Regular',
                                            }}
                                            numberOfLines={3}
                                        >
                                            {outletItem.name}
                                        </Text>

                                        <Text
                                            style={[itemNameTextScale, {
                                                textTransform:
                                                    'uppercase',
                                                fontFamily: "NunitoSans-Bold",
                                            }]} numberOfLines={3}>{outletItem.name}
                                        </Text>

                                        <View style={{ flexDirection: 'row', marginTop: 10 }}>
                                            <Text
                                                style={{
                                                    marginLeft: 5,
                                                    fontSize: 13,
                                                    fontFamily: 'NunitoSans-SemiBold',
                                                }}>
                                                Before: RM7.99
                                            </Text>
                                            <Text
                                                style={{
                                                    marginLeft: 10,
                                                    fontSize: 13,
                                                    fontFamily: 'NunitoSans-SemiBold',
                                                }}>
                                                After: RM1.99
                                            </Text>
                                        </View>
                                    </View>

                                </View>
                            </View>
                        );
                    })}
            </View>
        )
    };

    const redeemVoucher = async () => {
        console.log("Redeemed");

        if (selectedTaggableVoucher.voucherType !== LOYALTY_PROMOTION_TYPE.FREE_ITEM) {

            if (selectedTaggableVoucher.criteriaList.length > 0) {
                const firstCriteria = selectedTaggableVoucher.criteriaList[0];

                if (firstCriteria.variation === 'SPECIFIC_PRODUCTS') {
                    // check variationItemsSku which product is required
                    //variationItemsSku is using outletitem.sku - outletitem = selectedoutletitems 

                    console.log('first criteria list', firstCriteria.variationItemsSku[0])

                    CommonStore.update((s) => {
                        s.selectedTaggableVoucher = selectedTaggableVoucher
                    })
                    linkTo && linkTo(`${prefix}/outlet/menu`)
                }
                else { // mean is PRODUCT_OF_CATEGORY
                    //variationItemsSku is OutletItemCategory.name
                    console.log('first criteria list', firstCriteria.variationItemsSku[0])

                    CommonStore.update((s) => {
                        s.selectedTaggableVoucher = selectedTaggableVoucher
                    })

                    linkTo && linkTo(`${prefix}/outlet/menu`)
                }

            }
            else {
                alert('Info, Something went wrong please contact support')
            }
        }
    }

    const claimVoucher = async () => {
        // var userPhone = userNumber.replaceAll('-', '').replaceAll(' ', '').replaceAll('+', '');
        var userPhone = phone.replaceAll('-', '').replaceAll(' ', '').replaceAll('+', '');

        userPhone = userPhone.replace(/[^0-9]/g, '');

        if (!userPhone.startsWith('6')) {
            userPhone = '6' + userPhone;
        }

        if (userPhone && name) {

        }
        else {
            alert('Please fill in the name and phone number before proceed.');

            global.isClaimVoucherButtonClicked = false;

            return;
        }

        const body = {
            userPhone: userPhone,
            outletId: claimVoucherAnonymous.outletId,
            merchantId: claimVoucherAnonymous.merchantId,
            merchantName: claimVoucherAnonymous.merchantName,
            outletName: claimVoucherAnonymous.outletName || selectedOutlet.name,
            merchantLogo: claimVoucherAnonymous.merchantLogo || '',
            outletCover: claimVoucherAnonymous.outletCover || '',
            // userName: userName,
            userName: name,

            dob: moment(birthday).valueOf(),
            email: email || '',

            address: address,
            lat: lat,
            lng: lng,

            taggableVoucherId: claimVoucherAnonymous.uniqueId,

            voucherPoints: -claimVoucherAnonymous.voucherPointsRequired,

            userIdAnonymous: userIdAnonymous,
            toConvertAnonymousPoints: true,

            tracking: '5',

            // userOrderId: (registerUserOrder.uniqueId && !registerUserOrder.isCashbackClaimed) ? registerUserOrder.uniqueId : null,
        };

        //////////////////////////////////////

        // check validity

        if (userPointsBalance < claimVoucherAnonymous.voucherPointsRequired) {
            alert('Info\n\nInsufficient points to claim the voucher.');

            global.isClaimVoucherButtonClicked = false;

            return;
        }

        if (claimVoucherAnonymous && claimVoucherAnonymous.voucherQuantity > 0) {

        }
        else {
            alert('Info\n\nThis voucher has been fully redeemed.');

            global.isClaimVoucherButtonClicked = false;

            return;
        }

        CommonStore.update(s => {
            s.isLoading = true;
        });

        ApiClient.POST(API.claimTaggableVoucherKweb, body).then(async (result) => {
            if (result && result.status === "success") {
                // TempStore.update(s => {
                //     s.selectedClaimVoucher = null;
                // });

                // alert(`${result.message}`);

                // linkTo && linkTo(`${prefix}/outlet/outlet-rewards`)

                ////////////////////////////////////////////////////////////////////////////////

                // write in the user name and phone first

                await AsyncStorage.setItem('storedUserName', name);
                await AsyncStorage.setItem('storedUserPhone', userPhone);

                global.storedUserName = name;
                global.storedUserPhone = userPhone;

                ////////////////////////////////////////////////////////////////////////////////

                ///////////////////////////////////////////////////////////

                await signInWithPhoneForCRMUser(selectedOutlet);

                updateWebTokenAnonymous();

                ///////////////////////////////////////////////////////////
            }

            else {
                // TempStore.update(s => {
                //     s.selectedClaimVoucher = null;
                // });

                // CommonStore.update((s) => {
                //     s.alertObj = {
                //         title: "Info",
                //         message: "Unable to claim the voucher for now.",
                //     };
                // });
            }

            CommonStore.update(s => {
                s.isLoading = false;
            });

            TempStore.update(s => {
                s.rewardsModal = false;
                s.claimVoucherAnonymous = {};
            });

            alert(`${result.message}`);

            global.isClaimVoucherButtonClicked = false;
        });
    };

    const [isExpanded, setIsExpanded] = useState(false);

    const toggleReadMore = () => {
        setIsExpanded((prevState) => !prevState);
    };

    const maxLines = 8;

    const containerStyle = {
        maxHeight: isExpanded ? null : maxLines * 18,
        overflow: 'hidden',
    };


    return (
        <>
            {/* {selectedTaggableVoucher && */}
            <Modal
                style={{
                    width: windowWidth,
                    height: windowHeight,
                }}
                visible={rewardsModal}
                transparent={false}
                animationType={"none"}
            >
                <View
                    style={{
                        height: windowHeight,
                        width: windowWidth,
                        backgroundColor: Colors.whiteColor,
                    }}
                >
                    <TouchableOpacity
                        onPress={() => {
                            TempStore.update((s) => {
                                s.rewardsModal = false;
                            })
                        }}
                    >
                        <View
                            style={{
                                position: "absolute",
                                top: 10, right: 15,
                            }}
                        >
                            <AntDesign name="closecircle" size={25} color={Colors.blackColor} />
                        </View>
                    </TouchableOpacity>
                    <View style={{ zIndex: -1, }}>
                        <ScrollView
                            style={{
                                height: windowHeight * 0.9
                            }}>
                            <View style={{ /* height: '75%' */ }}>
                                <View style={[styles.container, { height: windowHeight }]}>
                                    <View style={styles.container}>

                                        {(claimVoucherAnonymous && claimVoucherAnonymous.uniqueId) ?
                                            !isLoading ? (
                                                <>
                                                    <View
                                                        style={{
                                                            backgroundColor: '#ffffff',
                                                            borderRadius: 20,
                                                            // width: '100%',
                                                            // height: '80%',
                                                            // flexDirection: 'row',
                                                            zIndex: -1,
                                                        }}>
                                                        <View
                                                            style={{
                                                                //borderRadius: 8,
                                                                alignItems: 'center',
                                                                backgroundColor: '#ffffff',
                                                                //borderWidth: 1,
                                                                // width: '100%',
                                                                // height: '100%',
                                                                justifyContent: 'center',
                                                                // borderRightWidth: 1,
                                                                borderColor: Colors.fieldtTxtColor,
                                                            }}>
                                                            {claimVoucherAnonymous.image ?
                                                                <>
                                                                    <AsyncImage
                                                                        source={{ uri: claimVoucherAnonymous.image }}
                                                                        item={claimVoucherAnonymous}
                                                                        style={{
                                                                            width: windowWidth,
                                                                            height: windowWidth * 0.8,
                                                                            marginBottom: 20,
                                                                        }}
                                                                    />
                                                                </>
                                                                :
                                                                <>
                                                                    <View style={{
                                                                        backgroundColor: Colors.secondaryColor,
                                                                        width: windowWidth,
                                                                        height: windowWidth * 0.8,
                                                                        marginBottom: 20,
                                                                        alignSelf: 'center',
                                                                        justifyContent: 'center',
                                                                        alignItems: 'center',
                                                                    }}>
                                                                        <Ionicons name="fast-food-outline" size={200} />
                                                                    </View>
                                                                </>
                                                            }
                                                        </View>
                                                    </View>

                                                    <View style={{
                                                        marginHorizontal: 30,
                                                        marginBottom: 15,
                                                    }}>
                                                        <Text style={{
                                                            fontSize: 25,
                                                            fontFamily: "NunitoSans-Bold",
                                                            marginBottom: 10,
                                                        }}>
                                                            {claimVoucherAnonymous.campaignName}
                                                        </Text>
                                                        <View style={{
                                                            flexDirection: 'row',
                                                        }}>
                                                            <Text style={{
                                                                fontSize: 15,
                                                                fontFamily: "NunitoSans-Regular",
                                                                color: Colors.descriptionColor,
                                                            }}>
                                                                {
                                                                    claimVoucherAnonymous.expirationDate
                                                                        ? `Expires by ${moment(claimVoucherAnonymous.expirationDate).format('dddd, Do MMM YYYY, hh:mm A')}`
                                                                        : `Expires by ${moment(claimVoucherAnonymous.promoDateEnd).format('dddd, Do MMM YYYY')}, ${moment(claimVoucherAnonymous.promoTimeEnd).format('hh:mm A')}`
                                                                }
                                                            </Text>
                                                        </View>
                                                    </View>

                                                    <View style={{
                                                        marginHorizontal: 30,
                                                        marginBottom: 15,
                                                    }}>
                                                        <View style={containerStyle}>
                                                            <Text
                                                                style={{
                                                                    fontSize: 15,
                                                                    fontFamily: "NunitoSans-Regular",
                                                                }}>
                                                                {claimVoucherAnonymous.campaignDescription ? claimVoucherAnonymous.campaignDescription : ''}
                                                            </Text>
                                                        </View>
                                                        {(claimVoucherAnonymous.campaignDescription ? claimVoucherAnonymous.campaignDescription : '').length > maxLines * 40 ? (
                                                            <TouchableOpacity style={{
                                                                marginTop: 10,
                                                            }}
                                                                onPress={toggleReadMore}>
                                                                <Text style={{
                                                                    color: Colors.primaryColor,
                                                                    alignSelf: 'center',
                                                                    justifyContent: 'center',

                                                                    fontSize: 15,
                                                                    fontFamily: "NunitoSans-Bold",
                                                                }}>
                                                                    {isExpanded ? 'Read Less' : 'Read More'}
                                                                </Text>
                                                            </TouchableOpacity>
                                                        ) : <></>}
                                                    </View>

                                                    <View style={{
                                                        marginHorizontal: 30,
                                                        marginBottom: 15,
                                                    }}>
                                                        <Text style={{
                                                            fontSize: 15,
                                                            fontFamily: "NunitoSans-Bold",
                                                            marginBottom: 20,
                                                        }}>
                                                            Requirements
                                                        </Text>
                                                        {/* <View style={{
                                                    flexDirection: 'row',
                                                    justifyContent: 'center',
                                                    justifyContent: 'space-between',
                                                    alignItems: 'center',
                                                    marginBottom: 20,
                                                }}>
                                                    <View style={{
                                                        flexDirection: 'row',
                                                        alignItems: 'center',
                                                        width: '90%',
                                                    }}>
                                                        <FontAwesome
                                                            name={"user"}
                                                            color={Colors.primaryColor}
                                                            size={31}
                                                            style={{
                                                                marginLeft: 3,
                                                            }}
                                                        />
                                                        <Text style={{
                                                            fontSize: 15,
                                                            fontFamily: "NunitoSans-Regular",

                                                            paddingLeft: 50,
                                                        }}>
                                                            Login/Register
                                                        </Text>
                                                    </View>
                                                    <View style={{ width: '10%' }}>
                                                        <CircleCheck
                                                            color={Colors.primaryColor}
                                                            style={{
                                                                // marginRight: 30,
                                                                width: 30,
                                                                height: 30,

                                                                paddingLeft: 10,
                                                            }}
                                                        />
                                                    </View>
                                                </View> */}
                                                        {/* <View style={{
                          flexDirection: 'row',
                          justifyContent: 'center',
                          justifyContent: 'space-between',
                          alignItems: 'center',
                          marginBottom: 20,
                        }}>
                          <View style={{
                            flexDirection: 'row',
                            alignItems: 'center',
                            width: '90%',
                          }}>
                            <AntDesign
                              name='clockcircleo'
                              color={Colors.primaryColor}
                              size={25}
                              style={{
                                marginLeft: 3,
                              }}
                            />
                            <Text style={{
                              fontSize: 15,
                              fontFamily: "NunitoSans-Regular",

                              paddingLeft: 46,
                            }}>
                              {`${moment(selectedTaggableVoucher.promoTimeStart).format('hh:mm A')} - ${moment(selectedTaggableVoucher.promoTimeEnd).format('hh:mm A')}`}
                            </Text>
                          </View>
                          <View style={{ width: '10%' }}>
                            <CircleCheck
                              color={moment(selectedTaggableVoucher.promoTimeEnd).isSameOrBefore(Date.now()) ? Colors.primaryColor : Colors.descriptionColor}
                              style={{
                                // marginRight: 30,
                                width: 30,
                                height: 30,

                                paddingLeft: 10,
                              }}
                            />
                          </View>
                        </View> */}
                                                        {/* {!availableTaggableVouchers.includes(selectedTaggableVoucher) ? (
                              <View style={{
                                flexDirection: 'row',
                                justifyContent: 'center',
                                // justifyContent: 'space-between',
                                alignItems: 'center',
                                marginBottom: 15,
                              }}>
                                <View style={{
                                  flexDirection: 'row',
                                  alignItems: 'center',
                                  width: '90%',
                                }}>
                                  <MaterialCommunityIcons
                                    name={"gift-outline"}
                                    color={Colors.primaryColor}
                                    size={30}
                                    style={{
                                      // top: 1,
                                    }}
                                  />
                                  <Text style={{
                                    fontSize: 15,
                                    fontFamily: "NunitoSans-Regular",
                                    textAlign: 'left',

                                    paddingLeft: 45,
                                  }}>
                                    {`${selectedTaggableVoucher.voucherPointsRequired} Points`}
                                  </Text>
                                </View>
                                <View style={{ width: '10%' }}>
                                  <CircleCheck
                                    color={userPointsBalance >= selectedTaggableVoucher.voucherPointsRequired ? Colors.primaryColor : Colors.descriptionColor}
                                    style={{
                                      // marginRight: 30,
                                      width: 30,
                                      height: 30,

                                      paddingLeft: 10,
                                    }}
                                  />
                                </View>
                              </View>
                            ) : <></>} */}
                                                        <View style={{
                                                            flexDirection: 'row',
                                                            justifyContent: 'center',
                                                            justifyContent: 'space-between',
                                                            alignItems: 'center',
                                                            marginBottom: 20,
                                                        }}>
                                                            <View style={{
                                                                flexDirection: 'row',
                                                                alignItems: 'center',
                                                                width: '100%',
                                                            }}>
                                                                <FontAwesome
                                                                    name={"user"}
                                                                    color={Colors.primaryColor}
                                                                    size={31}
                                                                    style={{
                                                                        marginLeft: 3,
                                                                        marginRight: 15,
                                                                    }}
                                                                />
                                                                <TextInput
                                                                    style={[
                                                                        styles.textInput,
                                                                        {
                                                                            width: "100%",
                                                                        },
                                                                    ]}
                                                                    multiline={false}
                                                                    clearButtonMode="while-editing"
                                                                    placeholder="John Smith"
                                                                    placeholderTextColor={Colors.fieldtTxtColor}
                                                                    onChangeText={(text) => {
                                                                        setName(text);
                                                                    }}
                                                                    value={name}
                                                                    defaultValue={name}
                                                                />
                                                            </View>
                                                            <View style={{}}>
                                                                {/* <CircleCheck
                              color={Colors.primaryColor}
                              style={{
                                // marginRight: 30,
                                width: 30,
                                height: 30,

                                paddingLeft: 10,
                              }}
                            /> */}
                                                            </View>
                                                        </View>
                                                        <View style={{
                                                            flexDirection: 'row',
                                                            justifyContent: 'center',
                                                            justifyContent: 'space-between',
                                                            alignItems: 'center',
                                                            marginBottom: 20,
                                                        }}>
                                                            <View style={{
                                                                flexDirection: 'row',
                                                                alignItems: 'center',
                                                                width: '100%',
                                                            }}>
                                                                <FontAwesome
                                                                    name={"phone"}
                                                                    color={Colors.primaryColor}
                                                                    size={31}
                                                                    style={{
                                                                        marginLeft: 3,
                                                                        marginRight: 15,
                                                                    }}
                                                                />
                                                                <TextInput
                                                                    style={[
                                                                        styles.textInput,
                                                                        {
                                                                            width: "100%",
                                                                        },
                                                                    ]}
                                                                    multiline={false}
                                                                    clearButtonMode="while-editing"
                                                                    placeholder="60123456789"
                                                                    placeholderTextColor={Colors.fieldtTxtColor}
                                                                    onChangeText={(text) => {
                                                                        setPhone(text);
                                                                    }}
                                                                    value={phone}
                                                                    defaultValue={phone}
                                                                    keyboardType={"decimal-pad"}
                                                                />
                                                            </View>
                                                            <View style={{}}>
                                                                {/* <CircleCheck
                              color={Colors.primaryColor}
                              style={{
                                // marginRight: 30,
                                width: 30,
                                height: 30,

                                paddingLeft: 10,
                              }}
                            /> */}
                                                            </View>
                                                        </View>
                                                    </View>

                                                    {/* <View style={{ justifyContent: 'center', alignItems: 'center', }}>
              <TouchableOpacity
                onPress={() => {
                  setExpandDetails(!expandDetails);
                }}> */}
                                                    {/* <Entypo
                  name={expandDetails ? 'chevron-up' : 'chevron-down'}
                  size={30}
                  color={Colors.primaryColor}
                /> */}
                                                    {/* <Text
                  style={{
                    marginTop: 0,
                    fontSize: 14,
                    fontFamily: 'NunitoSans-Bold',
                    color: Colors.primaryColor,
                  }}>
                  MORE
                </Text> */}
                                                    {/* </TouchableOpacity>
            </View> */}
                                                </>
                                            ) : (
                                                <>
                                                    <View style={{ justifyContent: 'center', alignSelf: 'center', marginTop: '70%' }}>
                                                        <ActivityIndicator color={Colors.primaryColor} size={60} />
                                                    </View>
                                                </>
                                            ) : (
                                                <>
                                                    <View style={{ justifyContent: 'center', alignSelf: 'center', marginTop: '70%' }}>
                                                        <Text style={{
                                                            fontSize: 15,
                                                            fontFamily: "NunitoSans-Regular",
                                                            color: Colors.descriptionColor,
                                                        }}>
                                                            You have not selected any rewards.
                                                        </Text>
                                                    </View>
                                                </>
                                            )}
                                    </View>
                                </View>
                            </View>
                        </ScrollView>
                        {/* {availableTaggableVouchers.includes(selectedTaggableVoucher) ? ( */}
                        <TouchableOpacity
                            onPress={async () => {
                                // if (selectedTaggableVoucher.voucherType !== LOYALTY_PROMOTION_TYPE.FREE_ITEM) {
                                //   redeemVoucher();
                                // } else {

                                //   CommonStore.update((s) => {
                                //     s.selectedTaggableVoucher = selectedTaggableVoucher;
                                //   });

                                //   // alert('Please proceed with the payment method of Pay Now to redeem your voucher');

                                //   const subdomain = await AsyncStorage.getItem("latestSubdomain");

                                //   if (!subdomain) {
                                //     global.linkToFunc && global.linkToFunc(`${prefix}/outlet/cart`)
                                //   } else {
                                //     global.linkToFunc && global.linkToFunc(`${prefix}/outlet/${subdomain}/cart`)
                                //   }

                                //   CommonStore.update((s) => {
                                //     s.isFromCartPage = false;
                                //   });
                                // }
                                if (!global.isClaimVoucherButtonClicked) {
                                    global.isClaimVoucherButtonClicked = true;

                                    // setTimeout(() => {
                                    //     global.isClaimVoucherButtonClicked = false;
                                    // }, 2000);

                                    claimVoucher();
                                }
                            }}>
                            <View style={{
                                paddingVertical: 12,
                                backgroundColor: Colors.primaryColor,
                                padding: 20,
                                borderRadius: 10,
                                alignItems: 'center',
                                marginVertical: 20,
                                width: windowWidth * 0.8,
                                alignSelf: 'center'
                            }}>
                                <Text style={{
                                    fontSize: 15,
                                    fontFamily: "NunitoSans-Bold",
                                    color: Colors.whiteColor,
                                }}>CLAIM</Text>
                            </View>
                        </TouchableOpacity>
                        {/* ) :
              <TouchableOpacity
                disabled={userPointsBalance >= selectedTaggableVoucher.voucherPointsRequired ? false : true}
                onPress={async () => {
                  TempStore.update(s => {
                    s.selectedClaimVoucher = selectedTaggableVoucher;
                  });
                  claimVoucher();
                }}>
                <View style={{
                  paddingVertical: 12,
                  backgroundColor: userPointsBalance >= selectedTaggableVoucher.voucherPointsRequired ? Colors.primaryColor : Colors.descriptionColor,
                  padding: 20,
                  borderRadius: 10,
                  alignItems: 'center',
                  marginVertical: 20,
                  width: windowWidth * 0.8,
                  alignSelf: 'center'
                }}>
                  <Text style={{
                    fontSize: 15,
                    fontFamily: "NunitoSans-Bold",
                    color: Colors.whiteColor,
                  }}>{userPointsBalance >= selectedTaggableVoucher.voucherPointsRequired ? 'CLAIM' : 'This reward is locked'}
                  </Text>
                </View>
              </TouchableOpacity> 
            }*/}
                    </View>
                </View>
            </Modal >
        </>
    );
};

const styles = StyleSheet.create({
    container1: {
        backgroundColor: '#ffffff',
        // backgroundColor: 'red',
    },
    container: {
        backgroundColor: '#ffffff',
        //padding: 16,
    },
    searchBar: {
        marginHorizontal: 16,
        backgroundColor: Colors.fieldtBgColor,
        flexDirection: 'row',
        padding: 12,
        borderRadius: 10,
        alignContent: 'center',
    },
    searchBarText: {
        color: Colors.fieldtTxtColor,
        marginLeft: 10,
    },
    card: {
        flex: 1,
        minWidth: Styles.width - 64,
        minHeight: 100,
        // backgroundColor: Colors.primaryColor,
        backgroundColor: '#416D5C',
        flexDirection: 'row',
        marginTop: 20,
        alignContent: 'center',
        alignItems: 'center',
        borderTopLeftRadius: 20,
        borderTopRightRadius: 20,
    },
    card1: {
        flex: 1,
        minWidth: Styles.width - 64,
        minHeight: 100,
        backgroundColor: Colors.fieldtBgColor,
        flexDirection: 'row',
        marginBottom: 20,
        alignContent: 'center',
        alignItems: 'center',
        borderBottomLeftRadius: 20,
        borderBottomRightRadius: 20,
        marginBottom: 10,
    },
    cornerleft: {
        backgroundColor: 'white',
        height: 30,
        width: 30,
        alignSelf: 'flex-start',
        marginTop: '22%',
        borderTopRightRadius: 50,
    },
    cornerright: {
        backgroundColor: 'white',
        height: 30,
        width: 30,
        alignSelf: 'flex-start',
        borderBottomRightRadius: 50,

        // borderStyle: 'dashed',
        // borderWidth: 1,
        // borderColor: 'black',
    },

    cornerleft1: {
        backgroundColor: 'white',
        height: 30,
        width: 30,
        alignSelf: 'flex-start',
        marginTop: '22%',
        borderTopLeftRadius: 50,
    },
    cornerright1: {
        backgroundColor: 'white',
        height: 30,
        width: 30,
        alignSelf: 'flex-start',
        borderBottomLeftRadius: 50,
    },
    text: {
        fontSize: 13,
        color: Colors.fieldtTxtColor,
        fontFamily: 'NunitoSans-Bold',
    },
    title: {
        color: Colors.whiteColor,
        // fontSize: Platform.OS == 'ios' ? 30 : 20,
        fontSize: Platform.OS == 'ios' ? 30 : 22,
        fontFamily: 'NunitoSans-SemiBold',
        marginLeft: '1%',
        // marginTop: 10,
        marginBottom: 5,
        textAlign: 'right',
    },
    title1: {
        color: Colors.blackColor,
        fontSize: 16,
        fontFamily: 'NunitoSans-Bold',
    },
    title2: {
        color: Colors.primaryColor,
        fontSize: Platform.OS == 'ios' ? 13 : 15,
        fontFamily: 'NunitoSans-SemiBold',
        // fontWeight: 'bold',
        marginBottom: 8,
    },
    title3: {
        color: Colors.descriptionColor,
        fontSize: Platform.OS == 'ios' ? 13 : 14,
        fontFamily: 'NunitoSans-SemiBold',
        // fontWeight: 'bold',
    },
    viewContainer: {
        marginTop: 10,
        justifyContent: 'space-between',
        flexDirection: 'row',
        flex: 10,
    },
    total: {
        fontSize: 14,
        color: Colors.blackColor,
        fontFamily: 'NunitoSans-Bold',
    },
    price: {
        fontSize: 14,
        color: Colors.primaryColor,
        fontFamily: 'NunitoSans-Regular',
    },
    totalBorder: {
        paddingTop: 5,
        justifyContent: 'space-between',
        flex: 1,
        flexDirection: 'row',
        borderTopWidth: StyleSheet.hairlineWidth,
        borderColor: Colors.blackColor,
        borderRadius: 1,
    },
    pic: {
        backgroundColor: Colors.secondaryColor,
        width: 90,
        height: 90,
        borderRadius: 10,
        alignSelf: 'center',
        justifyContent: 'center',
        alignItems: 'center',
    },
    searchBar1: {
        backgroundColor: Colors.fieldtBgColor,
        flexDirection: 'row',
        padding: 12,
        borderRadius: 10,
        alignContent: 'center',
        marginBottom: '2%',
    },

    centerTextHolder: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        justifyContent: 'center',
        alignItems: 'center',
    },
    picOutlet: {
        backgroundColor: Colors.secondaryColor,
        width: 45,
        height: 45,
        borderRadius: 10,
        alignSelf: 'center',
        justifyContent: 'center',
        alignItems: 'center',
    },
    textInput: {
        height: 50,
        paddingHorizontal: 20,
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 5,
        marginTop: 10,
        marginBottom: 10,

        shadowColor: "#000",
        shadowOffset: {
            width: 0,
            height: 1,
        },
        shadowOpacity: 0.22,
        shadowRadius: 2.22,
        elevation: 2,
    },
});

export default RewardsModal;
