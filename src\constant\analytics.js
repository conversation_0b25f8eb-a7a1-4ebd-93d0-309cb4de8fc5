export const ANALYTICS = {
    EVENT_QR_TAKEAWAY: 'e_qr_takeaway',
    EVENT_QR_DINEIN_U: 'e_qr_dinein_u',
    EVENT_QR_DINEIN_G: 'e_qr_dinein_g',

    EVENT_QR_CART_PAY_NOW_METHODS: 'e_qr_cart_pay_now_methods',
    EVENT_QR_CART_PAY_NOW_CHANNEL: 'e_qr_cart_pay_now_channel',

    EVENT_QR_CART_PAY_LATER_METHODS: 'e_qr_cart_pay_later_methods',
    EVENT_QR_CART_PAY_LATER_CHANNEL: 'e_qr_cart_pay_later_channel',

    WO_OUTLET_MENU: 'wo_outlet_menu',
    WO_OUTLET_MENU_CATEGORY_CLICK: 'wo_outlet_menu_category_click',
    WO_OUTLET_MENU_ITEM_CLICK: 'wo_outlet_menu_item_click',

    WO_ITEM_DETAILS: 'wo_item_details',
    WO_ITEM_DETAILS_BACK_CLICK: 'wo_item_details_back_click',
    WO_ITEM_DETAILS_VARIANT_CLICK: 'wo_item_details_variant_click',
    WO_ITEM_DETAILS_ADDON_CLICK: 'wo_item_details_addon_click',
    WO_ITEM_DETAILS_QUANTITY_CLICK: 'wo_item_details_quantity_click',
    WO_ITEM_DETAILS_ADD_CLICK: 'wo_item_details_add_click',

    WO_CART: 'wo_cart',
    WO_CART_BACK_CLICK: 'wo_cart_back_click',
    WO_CART_EDIT_ITEM_CLICK: 'wo_cart_edit_item_click',
    WO_CART_REMOVE_ITEM_CLICK: 'wo_cart_remove_item_click',
    WO_CART_PLACE_ORDER_CLICK: 'wo_cart_place_order_click',

    WO_UPSELL_ITEM_CLICK: 'wo_upsell_item_click',
};

export const ANALYTICS_PARSED = {
    EVENT_QR_TAKEAWAY: '[E] QR Takeaway',
    EVENT_QR_DINEIN_U: '[E] QR Dine-In Unique',
    EVENT_QR_DINEIN_G: '[E] QR Dine-In Generic',

    EVENT_QR_CART_PAY_NOW_METHODS: '[E] QR Cart > Pay Now > Methods',
    EVENT_QR_CART_PAY_NOW_CHANNEL: '[E] QR Cart > Pay Now > Channel',

    EVENT_QR_CART_PAY_LATER_METHODS: '[E] QR Cart > Pay Later > Methods',
    EVENT_QR_CART_PAY_LATER_CHANNEL: '[E] QR Cart > Pay Later > Channel',

    WO_OUTLET_MENU: '[WO] Outlet Menu',
    WO_OUTLET_MENU_CATEGORY_CLICK: '[WO] Outlet Menu > Click Category',
    WO_OUTLET_MENU_ITEM_CLICK: '[WO] Outlet Menu > Click Item',

    WO_ITEM_DETAILS: '[WO] Item Details',
    WO_ITEM_DETAILS_BACK_CLICK: '[WO] Item Details > Click Back',
    WO_ITEM_DETAILS_VARIANT_CLICK: '[WO] Item Details > Click Variant',
    WO_ITEM_DETAILS_ADDON_CLICK: '[WO] Item Details > Click Addon',
    WO_ITEM_DETAILS_QUANTITY_CLICK: '[WO] Item Details > Click Quantity',
    WO_ITEM_DETAILS_ADD_CLICK: '[WO] Item Details > Click Add',

    WO_CART: '[WO] Cart',
    WO_CART_BACK_CLICK: '[WO] Cart > Click Back',
    WO_CART_EDIT_ITEM_CLICK: '[WO] Cart > Click Edit Item',
    WO_CART_REMOVE_ITEM_CLICK: '[WO] Cart > Click Remove Item',
    WO_CART_PLACE_ORDER_CLICK: '[WO] Cart > Click Place Order',

    WO_UPSELL_ITEM_CLICK: '[WO] Upsell > Click Item',
};
