import React, { Component } from 'react';

import {
  StyleSheet,
  ScrollView,
  Image,
  View,
  Text,
  TouchableOpacity,
  Alert,
  TextInput
} from 'react-native';
import Colors from '../constant/Colors';
import ApiClient from '../util/ApiClient';
import API from '../constant/API';
import Octicons from 'react-native-vector-icons/Octicons';
import Entypo from 'react-native-vector-icons/Entypo';
import AntDesign from 'react-native-vector-icons/AntDesign';
import Ionicons from "react-native-vector-icons/Ionicons";
import Styles from '../constant/Styles';
import * as Cart from '../util/Cart';
import * as User from '../util/User';
import Icon from 'react-native-vector-icons/MaterialIcons';
// import { FlatList, TextInput } from 'react-native-gesture-handler';
import moment from 'moment';
import Icons from 'react-native-vector-icons/EvilIcons';
// import DateTimePickerModal from 'react-native-modal-datetime-picker';
import { prefix } from '../constant/env';
import { CommonActions, useLinkTo } from "@react-navigation/native";
// import Autocomplete from 'react-google-autocomplete';

/**
 * EditAddress
 * function
 * *edit the existing address
 * 
 * route.params
 * *address: an array of the address to be edited
 */
class EditAddress extends Component {
  constructor({ navigation, props, route }) {
    const { address, } = route.params;
    super(props);

    // navigation.setOptions({
    //   headerBackTitle: 'Back',
    //   headerRight: () => (
    //     <View style={{ marginRight: 15 }}></View>
    //   ),
    //   headerTitle: () => (
    //     <View style={{ justifyContent: 'center', alignItems: 'center', marginTop: '1%' }}>
    //       <Text
    //         style={{
    //           fontWeight: 'bold',
    //           fontSize: 20,
    //           textAlign: 'center',
    //         }}>
    //         Add to Saved Places
    //       </Text>
    //     </View>
    //   ),
    // });

    navigation.setOptions({
      headerLeft: () => (
        <TouchableOpacity style={{
        }} onPress={() => { navigation.goBack(); }}>
          <View style={{
            marginLeft: 10,
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'flex-start',
          }}>
            <Ionicons
              name="chevron-back"
              size={26}
              color={Colors.fieldtTxtColor}
              style={{
              }}
            />

            <Text
              style={{
                color: Colors.fieldtTxtColor,
                fontSize: 16,
                textAlign: 'center',
                fontFamily: 'NunitoSans-Regular',
                lineHeight: 22,
                marginTop: -1,
              }}>
              Back
            </Text>
          </View>
        </TouchableOpacity>
      ),
      headerRight: () => (
        <TouchableOpacity 
        onPress={() => { 
          // props.navigation.navigate('Profile') 
        }} style={{
        }}>
          <View style={{ marginRight: 15 }}>
            <Ionicons name="menu" size={30} color={Colors.primaryColor} />
          </View>
        </TouchableOpacity>
      ),
      headerTitle: () => (
        <View style={{
          justifyContent: 'center',
          alignItems: 'center',
          bottom: -1,
        }}>
          <Text
            style={{
              fontSize: 20,
              lineHeight: 25,
              textAlign: 'center',
              fontFamily: 'NunitoSans-Bold',
              color: Colors.mainTxtColor,
            }}>
            Add To Saved Places
          </Text>
        </View>
      ),
    });

    this.state = {
      //test: test,
      address: address,
      //name: name
      newAddress: '',
      newNote: '',
      newDetail: '',
    };
  }

  componentDidMount() {
    this.setState({ newAddress: this.state.address.address, newNote: this.state.address.note })
  }

  // function here
  check() {
    if (this.state.test == 1) { return "Home" }
    else if (this.state.test == 2) { return "Work" }
    else { return "New" }
  }



  editAddress() {
    var body = {
      addressId: this.state.address.id,
      address: this.state.newAddress,
      type: this.state.address.type,
      note: this.state.newNote
    }
    ApiClient.POST(API.editUserAddress, body).then((result) => {
      console.log(result)
      if (result !== null) {

        Alert.alert(
          'Successful',
          'Address has been updated',
          [
            { text: "OK", onPress: () => { this.props.navigation.navigate('Address - KooDoo Web Order'); } }
          ],
          { cancelable: false },
        );
      }
    });
  }
  // function end

  render() {
    return (
      <View style={{ flex: 1 }}>
        <ScrollView style={{ backgroundColor: Colors.whiteColor }}>

          <View style={{ width: "90%", alignSelf: "center", marginTop: 20 }}>
            <View>
              <Text style={{
                // fontWeight: "400",
                fontSize: 17,
                fontFamily: 'NunitoSans-Bold',
              }}>*Name</Text>

              <TextInput
                underlineColorAndroid={Colors.fieldtBgColor}
                clearButtonMode='while-editing'
                autoFocus={true}
                placeholder={this.check()}
                style={styles.textInput}
                onChangeText={text => this.setState({ name: text })}
                value={this.state.address.type}
              />
              <Text style={{
                fontSize: 15,
                color: "#aba9a9",
                fontFamily: 'NunitoSans-Regular',
                marginTop: 10,
                marginLeft: 5,
                marginBottom: 15,
              }}>Label this address for easy reference</Text>
            </View>

            <View style={{
              marginTop: 10,
              marginBottom: 15,
            }}>
              <Text style={{
                // fontWeight: "400",
                fontSize: 17,
                fontFamily: 'NunitoSans-Bold',
              }}>*Address</Text>

              <TextInput
                underlineColorAndroid={Colors.fieldtBgColor}
                clearButtonMode='while-editing'
                autoFocus={true}
                placeholder="📍  KL Eco City, 3 Jalan Bangsar, Kuala Lumpur, Wilayah....."
                style={styles.textInput1}
                onChangeText={text => this.setState({ newAddress: text })}
                value={this.state.newAddress}
              />
            </View>

            <View style={{
              marginTop: 10,
              marginBottom: 15,
            }}>
              <Text style={{
                // fontWeight: "400",
                fontSize: 17,
                fontFamily: 'NunitoSans-Bold',
              }}>Address Details</Text>

              <TextInput
                underlineColorAndroid={Colors.fieldtBgColor}
                clearButtonMode='while-editing'
                autoFocus={true}
                placeholder="e.g floor, unit number"
                style={styles.textInput1}
                onChangeText={text => this.setState({ newDetail: text })}
                value={this.state.newDetail}
              />
            </View>

            <View style={{
              marginTop: 10,
            }}>
              <Text style={{
                // fontWeight: "400",
                fontSize: 17,
                fontFamily: 'NunitoSans-Bold',
              }}>Note To Driver</Text>

              <TextInput
                underlineColorAndroid={Colors.fieldtBgColor}
                clearButtonMode='while-editing'
                autoFocus={true}
                placeholder="e.g meet me at the lobby"
                style={styles.textInput1}
                onChangeText={text => this.setState({ newNote: text })}
                value={this.state.newNote}
              />
              <Text style={{
                fontSize: 15,
                color: "#aba9a9",
                fontFamily: 'NunitoSans-Regular',
                marginTop: 10,
                marginLeft: 5,
                marginBottom: 15,
              }}>Put delivery instruction or directions here</Text>
            </View>

            <TouchableOpacity style={{
              marginTop: 30,
              backgroundColor:
                Colors.primaryColor,
              width: "90%",
              // height: 60,
              alignSelf: "center",
              alignItems: 'center',
              borderRadius: 10,
              justifyContent: "center",

              // marginHorizontal: 48,
              // marginTop: 24,
              // marginBottom: 24,
              padding: 20,
              paddingVertical: 16,
            }}
              onPress={() => { this.editAddress() }}>
              <Text style={{
                color: '#ffffff',
                fontSize: 21,
                fontFamily: 'NunitoSans-Regular',
              }}>SAVE ADDRESS</Text>
            </TouchableOpacity>
          </View>

          {/* <View style={{ height: 120 }}></View> */}
        </ScrollView>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#ffffff',
    height: 70,
    alignSelf: "center", justifyContent: "center",
    width: "90%",
    borderBottomWidth: StyleSheet.hairlineWidth,
  },
  searchBar: {
    backgroundColor: Colors.fieldtBgColor,
    flexDirection: 'row',
    padding: 12,
    borderRadius: 10,
    alignContent: 'center'
  },
  searchBarText: {
    color: Colors.fieldtTxtColor,
    width: "80%"
  },
  BarText: {
    color: Colors.primaryColor,
    marginLeft: 10,
    fontSize: 15,
    fontWeight: 'bold',
    textAlign: 'center',
  },

  text: {
    fontSize: 15,
    fontWeight: '700',
  },
  text1: {
    fontSize: 13,
    fontWeight: '700',
    marginTop: '2%',
  },
  textInput: {
    // height: 60,
    // paddingVertical: 10,
    paddingLeft: 15,
    paddingRight: 15,
    width: "100%",
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    marginTop: 8,
    fontSize: 15,
    borderWidth: StyleSheet.hairlineWidth,
    borderColor: '#dedede'
  },
  textInput1: {
    // height: 60,
    paddingLeft: 15,
    paddingRight: 15,
    width: "100%",
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    marginTop: 8,
    fontSize: 15,
    borderWidth: StyleSheet.hairlineWidth,
    borderColor: '#dedede'
  },
  text2: {
    fontSize: 24,
    fontWeight: '700',
    marginTop: '8%',
  },
  title: {
    color: Colors.blackColor,
    fontSize: 20,
    fontWeight: 'bold',
  },
  viewContainer: {
    marginTop: 10,
    justifyContent: 'space-between',
    flexDirection: 'row',
    flex: 10,
  },
  total: {
    fontSize: 14,
    color: Colors.blackColor,
    fontWeight: '700',
  },
  price: {
    fontSize: 14,
    color: Colors.primaryColor,
    fontWeight: '700',
  },
  totalBorder: {
    paddingTop: 5,
    justifyContent: 'space-between',
    flex: 1,
    flexDirection: 'row',
    borderTopWidth: StyleSheet.hairlineWidth,
    borderColor: Colors.blackColor,
    borderRadius: 1,
  },
  pic: {
    width: 90,
    height: 90,
    borderRadius: 10,
    marginBottom: 5,
    alignItems: 'center',
    justifyContent: 'center',
  },
  Bar1: {
    flexDirection: 'row',
    padding: 12,
    borderRadius: 10,
    alignContent: 'center',
    marginBottom: '2%',
  },
});
export default EditAddress;
