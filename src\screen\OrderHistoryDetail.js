import React, { Component, useReducer, useState, useEffect, useRef, useCallback, useMemo } from 'react';
import {
    StyleSheet,
    ScrollView,
    Image,
    View,
    Text,
    TouchableOpacity,
    TextInput,
    FlatList,
    RefreshControl,
    Alert,
    Modal,
    Dimensions,
    ActivityIndicator,
    useWindowDimensions,
} from 'react-native';
// import { CheckBox } from 'react-native-web';
import Colors from '../constant/Colors';
import * as Cart from '../util/Cart';
import * as User from '../util/User';
import Styles from '../constant/Styles';
import ApiClient from '../util/ApiClient';
import API from '../constant/API';
import Feather from 'react-native-vector-icons/Feather';
import Icons from 'react-native-vector-icons/EvilIcons';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { CommonStore } from '../store/commonStore';
import { Collections } from '../constant/firebase';
//import firestore from '@react-native-firebase/firestore';
// import firebase from "firebase";
import { getFirestore, collection, query, where, getDocs, limit } from "firebase/firestore";
import { ORDER_TYPE_PARSED, USER_ORDER_STATUS, COURIER_CODE, LALAMOVE_STATUS, ORDER_TYPE, MRSPEEDY_STATUS, COURIER_INFO_DICT, SENDER_DROPDOWN_LIST, USER_ORDER_PRIORITY, APP_TYPE, OFFLINE_BILL_TYPE, PRODUCT_PRICE_TYPE, UNIT_TYPE_SHORT, UNIT_TYPE, ORDER_TYPE_DETAILS } from '../constant/common';
import AsyncImage from '../components/asyncImage';
import AntDesign from 'react-native-vector-icons/AntDesign';
import { Picker } from "react-native";
import { UserStore } from '../store/userStore';
import { CommonActions, useFocusEffect, useLinkTo } from "@react-navigation/native";
//import molpay from "molpay-mobile-xdk-reactnative-beta";
import moment from 'moment';
//import DropDownPicker from 'react-native-dropdown-picker';
//import RNPickerSelect from 'react-native-picker-select';
import { prefix } from "../constant/env";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { TempStore } from '../store/tempStore';
import { TableStore } from '../store/tableStore';
import Checkbox from 'rc-checkbox';
import '../constant/web-styles.css';
import 'react-native-get-random-values';
import { v4 as uuidv4 } from 'uuid';
import { customAlphabet } from 'nanoid';
import { excludeSkipScItems, checkToApplyScOrNot, checkToApplyTaxOrNot, isMobile, filterOldRejectedCancelledOrders } from '../util/commonFuncs';
import BigNumber from 'bignumber.js';

import koodoo_logo from "../asset/image/logo.png";
import { PaymentStore } from '../store/paymentStore';
import { DataStore } from '../store/dataStore';
import { idbGet } from '../util/db';

const alphabet = '**********';
const nanoid = customAlphabet(alphabet, 12);

/**
 * OrderDetailScreen
 * function
 * *display detail of the order
 * 
 * route.params
 * *orderId: id of the selected order to be displayed
 */

const ORDER_STATUS_PARSED = {
    PLACED: 'PLACED',
    PREPARING: 'PREPARING',
    PICKING_UP: 'PICKING_UP',
    DELIVERING: 'DELIVERING',
    DELIVERED: 'DELIVERED',
};

const ORDER_STATUS_IMAGES_DELIVERY = {
    PLACED: require('../asset/image/order-status-placed.png'),
    PREPARING: require('../asset/image/order-status-preparing.png'),
    PICKING_UP: require('../asset/image/order-status-picking-up.png'),
    DELIVERING: require('../asset/image/order-status-delivering.png'),
    DELIVERED: require('../asset/image/order-status-delivered.png'),
};

const ORDER_STATUS_IMAGES = {
    PLACED: require('../asset/image/order-status-placed.png'),
    PREPARING: require('../asset/image/order-status-preparing.png'),
    PICKING_UP: require('../asset/image/order-status-preparing.png'),
    DELIVERING: require('../asset/image/order-status-preparing.png'),
    DELIVERED: require('../asset/image/order-status-delivered.png'),
};

const ORDER_STATUS_IMAGES_BOTTOM = {
    PLACED: require('../asset/image/order-status-placed-bottom.png'),
    PREPARING: require('../asset/image/order-status-preparing-bottom.png'),
    PICKING_UP: require('../asset/image/order-status-picking-up-bottom.png'),
    DELIVERING: require('../asset/image/order-status-delivering-bottom.png'),
    DELIVERED: require('../asset/image/order-status-delivered-bottom.png'),
};

const ORDER_STATUS_TEXT_DELIVERY = {
    PLACED: 'Your order placed successfully!',
    PREPARING: 'Restaurant is currently preparing your order!',
    PICKING_UP: 'Your driver is picking up your order!',
    DELIVERING: 'Driver on the way delivering your order!',
    DELIVERED: 'Your order has been delivered.\nEnjoy your meal!',
};

const ORDER_STATUS_TEXT = {
    PLACED: 'Your order placed successfully!',
    PREPARING: 'Restaurant is currently preparing your order!',
    PICKING_UP: 'Restaurant is currently preparing your order!',
    DELIVERING: 'Restaurant is currently preparing your order!',
    DELIVERED: 'Your order is ready, enjoy your meal!',
};

/////////////////////////////////////////////////////////////////////////

const ORDER_STATUS_IMAGES_BOTTOM_TAKEAWAY = {
    PLACED: require('../asset/image/t-received.png'),
    PREPARING: require('../asset/image/t-preparing.png'),
    DELIVERED: require('../asset/image/t-delivered.png'),
    PICKED_UP: require('../asset/image/order-status-picking-up.png'),
};

const OrderDetailScreen = props => {
    const {
        navigation,
        route,
    } = props;

    const linkTo = useLinkTo();
    const { width: windowWidth, height: windowHeight } = useWindowDimensions();

    navigation.setOptions({
        headerLeft: () => (
            <TouchableOpacity style={{
            }} onPress={async () => {
                // props.navigation.goBack();
                const subdomain = await AsyncStorage.getItem('latestSubdomain');

                if (!subdomain) {
                    linkTo && linkTo(`${prefix}/outlet/menu`);
                }
                else {
                    linkTo && linkTo(`${prefix}/outlet/${subdomain}/menu`);
                }
            }}>
                <View style={{
                    marginLeft: 10,
                    display: 'flex',
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'flex-start',
                }}>
                    <Ionicons
                        name="chevron-back"
                        size={26}
                        color={Colors.fieldtTxtColor}
                        style={{
                        }}
                    />

                    <Text
                        style={{
                            color: Colors.fieldtTxtColor,
                            fontSize: 16,
                            textAlign: 'center',
                            fontFamily: 'NunitoSans-Regular',
                            lineHeight: 22,
                            marginTop: -1,
                        }}>
                        Back
                    </Text>
                </View>
            </TouchableOpacity>
        ),
        headerRight: () => (
            // <TouchableOpacity onPress={() => { props.navigation.navigate('Profile') }} style={{
            // }}>
            //     <View style={{ marginRight: 15 }}>
            //         <Ionicons name="menu" size={30} color={Colors.primaryColor} />
            //     </View>
            // </TouchableOpacity>
            <View></View>
        ),
        headerTitle: () => (
            <View style={{
                justifyContent: 'center',
                alignItems: 'center',
                bottom: -1,
            }}>
                <Text
                    style={{
                        fontSize: 20,
                        lineHeight: 25,
                        textAlign: 'center',
                        fontFamily: 'NunitoSans-Bold',
                        color: Colors.mainTxtColor,
                    }}>
                    Order Details
                </Text>
            </View>
        ),
        headerTintColor: "#000000",
    });

    var orderIdParam = null;
    if (route && route.params) {
        const orderIdParam = route.params.orderId;
    }

    const [orderId, setOrderId] = useState(orderIdParam);
    const [orderData, setOrderData] = useState([]);
    const [orderMerchant, setOrderMerchant] = useState([]);
    const [orderItems, setOrderItems] = useState(null);
    const [orderTax, setOrderTax] = useState([]);

    const [expandDetails, setExpandDetails] = useState(false);
    const [expandCompleteQuestionnaire, setExpandCompleteQuestionnaire] = useState(false);
    const [questionnaireIndex, setQuestionnaireIndex] = useState(1);
    //const current = question[questionnaireIndex];

    const [reviewComment, setReviewComment] = useState('');
    const [reviewTotal, setReviewTotal] = useState(18);

    const [starRatingDefault, setStarRatingDefault] = useState(5);
    const [starRating, setStarRating] = useState([1, 2, 3, 4, 5]);

    const selectedOutlet = CommonStore.useState(s => s.selectedOutlet);

    const setState = () => { };

    const [orderOutlet, setOrderOutlet] = useState({});

    const [orderDetails, setOrderDetails] = useState(null);
    const [orderDetailsDict, setOrderDetailsDict] = useState(null);

    const scrollViewRef = useRef();
    const [actionModalVisibility, setActionModalVisibility] = useState(false);
    const [completeModalVisibility, setCompleteModalVisibility] = useState(false);
    const [questionnaireVisibility, setQuestionnaireVisibility] = useState(false);
    const [receiptModal, setReceiptModal] = useState(false);

    const [selectedSender, setSelectedSender] = useState(SENDER_DROPDOWN_LIST[0].value);

    const [deliveryQuotation, setDeliveryQuotation] = useState({
        totalFee: 0,
    });

    /////////////////////////////////////////////////////////

    // 2022-11-22 - To support pick unpaid orders to paid

    const [toRenderOrders, setToRenderOrders] = useState([]);
    const [toApproveOrders, setToApproveOrders] = useState([]);

    /////////////////////////////////////////////////////////

    const [selectedProductOrdersDict, setSelectedProductOrdersDict] = useState({});

    /////////////////////////////////////////////////////////

    const [selectedTablePendingApprovalOrders, setSelectedTablePendingApprovalOrders] = useState([]);
    const [selectedTableOtherPeoplePromoVoucherOrders, setSelectedTableOtherPeoplePromoVoucherOrders] = useState([]);

    /////////////////////////////////////////////////////////

    const orderToShow = CommonStore.useState(s => s.orderToShow);
    const selectedUserOrder = CommonStore.useState(s => s.selectedUserOrder);
    const isLoading = CommonStore.useState(s => s.isLoading);

    const userInfoName = UserStore.useState((s) => s.userInfoName);
    const userInfoPhone = UserStore.useState((s) => s.userInfoPhone);

    /////////////////////////////////////////////////////////

    const selectedTableOrders = CommonStore.useState(s => s.selectedTableOrders);

    const selectedUserOrderOthers = CommonStore.useState(s => s.selectedUserOrderOthers);

    /////////////////////////////////////////////////////////

    const userEmail = UserStore.useState(s => s.email);
    const userName = UserStore.useState(s => s.name);
    const userAvatar = UserStore.useState(s => s.avatar);
    const userAddresses = UserStore.useState(s => s.userAddresses);
    const firebaseUid = UserStore.useState(s => s.firebaseUid);
    const userNumber = UserStore.useState(s => s.number);
    const userIdAnonymous = UserStore.useState((s) => s.userIdAnonymous);

    const isPlacingReservation = CommonStore.useState((s) => s.isPlacingReservation);

    const selectedSummaryOrdersDict = TableStore.useState(s => s.selectedSummaryOrdersDict);
    const selectedCancelledOrdersDict = TableStore.useState(s => s.selectedCancelledOrdersDict);
    const selectedDeliveredOrdersDict = TableStore.useState(s => s.selectedDeliveredOrdersDict);

    const count = TableStore.useState(s => s.count);

    const cancelUser = TableStore.useState(s => s.cancelUser);
    const deliveredUser = TableStore.useState(s => s.deliveredUser);
    const cancelledUser = TableStore.useState(s => s.cancelledUser);

    const isPay = TableStore.useState(s => s.isPay);

    const orderDisplaySummary = TableStore.useState(s => s.orderDisplaySummary);
    const orderDisplayIndividual = TableStore.useState(s => s.orderDisplayIndividual);
    const orderDisplayProduct = TableStore.useState(s => s.orderDisplayProduct);

    const selectedOrderToPayUserId = CommonStore.useState(s => s.selectedOrderToPayUserId);

    const selectedOutletTableCode = CommonStore.useState(s => s.selectedOutletTableCode);

    const currCrmUser = CommonStore.useState(s => s.currCrmUser);

    ////////////////////////////////////////////////////////

    useFocusEffect(
        useCallback(() => {
            // setTimeout(() => {
            //     if (global.selectedOutlet === null) {
            //         linkTo && linkTo(`${prefix}/scan`);
            //     }
            // }, 10000);

            // setIsMounted(true);
            // return () => {
            //   setIsMounted(false);
            // };
        }, [])
    );

    useEffect(() => {
        global.currPageStack = [
            ...global.currPageStack,
            'OrderHistoryDetail',
        ];

        CommonStore.update(s => {
            s.isLoading = false;
        });
    }, []);

    useEffect(() => {
        TableStore.update(s => {
            s.orderDisplaySummary = false;
            s.orderDisplayProduct = true;
        });

        CommonStore.update((s) => {
            s.currPage = "";
        });
    }, []);

    useEffect(() => {
        if (currCrmUser) {
            CommonStore.update(s => {
                s.selectedOrderToPayUserId = currCrmUser.email;
            });
        }
        else if (userEmail || firebaseUid) {
            CommonStore.update(s => {
                s.selectedOrderToPayUserId = userEmail ? userEmail : firebaseUid;
            });
        }
    }, [currCrmUser, userEmail, firebaseUid]);

    ////////////////////////////////////////////////////////

    useEffect(() => {
        if (selectedOutlet === null) {
            readStates();
        }

        readCommonStates();
    }, [selectedOutlet]);

    const readStates = async () => {
        console.log('global.selectedoutlet = readStates (1) (==test==)');

        if (selectedOutlet === null) {
            console.log('global.selectedoutlet = readStates (2) (==test==)');

            // const commonStoreDataRaw = await AsyncStorage.getItem("@commonStore");
            const commonStoreDataRaw = await idbGet('@commonStore');
            if (commonStoreDataRaw !== undefined) {
                console.log('global.selectedoutlet = readStates (3) (==test==)');

                const commonStoreData = JSON.parse(commonStoreDataRaw);

                const latestOutletId = await AsyncStorage.getItem("latestOutletId");

                console.log('latestOutletId');
                console.log(latestOutletId);
                console.log('commonStoreData.selectedOutlet');
                console.log(commonStoreData.selectedOutlet);

                if (
                    commonStoreData.selectedOutlet &&
                    latestOutletId === commonStoreData.selectedOutlet.uniqueId
                ) {
                    // check if it's the same outlet user scanned

                    console.log('global.selectedoutlet = readStates (4) (==test==)');

                    if (isPlacingReservation) {
                    } else {
                        // if (
                        //   commonStoreData.orderType === ORDER_TYPE.DINEIN 
                        //   // 2022-10-08 - Try to disable this
                        //   // &&
                        //   // commonStoreData.userCart.uniqueId === undefined
                        // ) {
                        //   // logout the user

                        //   linkTo && linkTo(`${prefix}/scan`);
                        // }
                    }

                    console.log('global.selectedoutlet = commonStoreData.selectedOutlet (3) (==test==)');

                    global.selectedOutlet = commonStoreData.selectedOutlet;

                    CommonStore.replace(commonStoreData);

                    // const userStoreDataRaw = await AsyncStorage.getItem("@userStore");
                    const userStoreDataRaw = await idbGet('@userStore');
                    if (userStoreDataRaw !== undefined) {
                        const userStoreData = JSON.parse(userStoreDataRaw);

                        UserStore.replace(userStoreData);
                    }

                    // const dataStoreDataRaw = await AsyncStorage.getItem('@dataStore');
                    const dataStoreDataRaw = await idbGet('@dataStore');
                    if (dataStoreDataRaw !== undefined) {
                        const dataStoreData = JSON.parse(dataStoreDataRaw);

                        DataStore.replace(dataStoreData);
                        // DataStore.replace({
                        //   ...dataStoreData,
                        //   ...dataStoreData.linkToFunc !== undefined && {
                        //     linkToFunc: dataStoreData.linkToFunc,
                        //   },
                        // });
                    }

                    // const tableStoreDataRaw = await AsyncStorage.getItem("@tableStore");
                    const tableStoreDataRaw = await idbGet('@tableStore');
                    if (tableStoreDataRaw !== undefined) {
                        const tableStoreData = JSON.parse(tableStoreDataRaw);

                        TableStore.replace(tableStoreData);
                    }

                    // const paymentStoreDataRaw = await AsyncStorage.getItem("@paymentStore");
                    const paymentStoreDataRaw = await idbGet('@paymentStore');
                    if (paymentStoreDataRaw !== undefined) {
                        const paymentStoreData = JSON.parse(paymentStoreDataRaw);

                        PaymentStore.replace(paymentStoreData);
                    }

                    // const dataStoreDataRaw = await AsyncStorage.getItem('@dataStore');
                    // if (dataStoreDataRaw !== undefined) {
                    //   const dataStoreData = JSON.parse(dataStoreDataRaw);

                    //   DataStore.replace(dataStoreData);
                    // }
                }
            }
        }
    };

    const readCommonStates = async () => {
        // if (!linkToFunc) {
        //   const dataStoreDataRaw = await AsyncStorage.getItem('@dataStore');
        //   if (dataStoreDataRaw !== undefined) {
        //     const dataStoreData = JSON.parse(dataStoreDataRaw);
        //     DataStore.replace(dataStoreData);
        //   }
        // }
    };

    ////////////////////////////////////////////////////////


    useEffect(() => {
        let selectedTableOtherPeoplePromoVoucherOrdersTemp = [];
        let selectedTablePendingApprovalOrdersTemp = [];

        var selectedTableOrdersOwn = selectedTableOrders.filter(order => {
            let validStatus = false;

            // if (
            //     order.userIdAnonymous === userIdAnonymous ||
            //     order.userIdAnonymous === '' ||
            //     order.userIdAnonymous === undefined
            // ) {
            //     validStatus = true;
            // }

            validStatus = true;

            if (selectedOutlet.dineInRequiredAuthorization &&
                order.orderType === ORDER_TYPE.DINEIN &&
                order.orderStatus === USER_ORDER_STATUS.ORDER_RECEIVED) {
                // means need wait merchant approved first, then only can show

                validStatus = false;

                selectedTablePendingApprovalOrdersTemp.push(order);

                return validStatus;
            }

            if (order.userPhone) {
                if (order.userPhone !== userNumber) {
                    // means should be other people orders

                    if (
                        order.taggableVoucherId ||
                        (order.promotionIdList &&
                            order.promotionIdList.length > 0)
                        ||
                        (order.promoCodePromotionIdList &&
                            order.promoCodePromotionIdList.length > 0)
                        ||
                        (order.cartPromotionIdList &&
                            order.cartPromotionIdList.length > 0)
                    ) {
                        // means is promotional order, needs separated

                        validStatus = false;

                        selectedTableOtherPeoplePromoVoucherOrdersTemp.push(order);
                    }
                }
            }

            return validStatus;
        });

        if (selectedTableOrdersOwn && selectedTableOrdersOwn.length > 0) {
            CommonStore.update(s => {
                s.selectedUserOrder = selectedTableOrdersOwn[0];

                s.selectedUserOrderOthers = selectedTableOrdersOwn.slice(1);

                s.orderToShow = selectedTableOrdersOwn[0];
            });

            setToRenderOrders(selectedTableOrdersOwn);
        }
        else {
            CommonStore.update(s => {
                s.selectedUserOrder = {};

                s.selectedUserOrderOthers = [];

                s.orderToShow = {};
            });

            setToRenderOrders(selectedTableOrdersOwn);
        }

        setSelectedTableOtherPeoplePromoVoucherOrders(selectedTableOtherPeoplePromoVoucherOrdersTemp);
        setSelectedTablePendingApprovalOrders(selectedTablePendingApprovalOrdersTemp);

        if (selectedTablePendingApprovalOrdersTemp.length > 0) {
            if (selectedTableOrdersOwn.length === 0) {
                CommonStore.update(s => {
                    s.orderToShow = selectedTablePendingApprovalOrdersTemp[0];
                });
            }
        }
        else {

        }
    }, [selectedTableOrders, userIdAnonymous, userNumber, selectedOutlet]);

    useEffect(() => {
        if (selectedOutlet && selectedOutlet.uniqueId) {
            setOrderOutlet(selectedOutlet);
        }
        else {

        }
    }, [selectedOutlet, userIdAnonymous]);

    // console.log('selectedTableOrders');
    // console.log(selectedTableOrders);
    // console.log('selectedUserOrder');
    // console.log(selectedUserOrder);
    // console.log('selectedUserOrderOthers');
    // console.log(selectedUserOrderOthers);

    ////////////////////////////////////////////////////////   

    // useEffect(() => {
    //     if (!selectedOutlet && selectedUserOrder && selectedUserOrder.outletId) {
    //         retrieveOutletData();
    //     }
    // }, [selectedOutlet, selectedUserOrder]);

    // const retrieveOutletData = async () => {
    //     const outletSnapshot = await firebase.firestore().collection(Collections.Outlet)
    //         .where('uniqueId', '==', selectedUserOrder.outletId)
    //         .limit(1)
    //         .get();

    //     if (!outletSnapshot.empty) {
    //         CommonStore.update(s => {
    //             s.selectedOutlet = outletSnapshot.docs[0].data();
    //         });
    //     }
    // };

    // useEffect(() => {
    //     if (selectedUserOrder && selectedUserOrder.uniqueId) {
    //         if (orderOutlet.uniqueId === undefined) {
    //             retrieveOrderOutlet();
    //         }
    //     }
    // }, [selectedUserOrder, orderOutlet]);

    // const retrieveOrderOutlet = async () => {
    //     const outletSnapshot = await firebase.firestore().collection(Collections.Outlet)
    //         .where('uniqueId', '==', selectedUserOrder.outletId)
    //         .limit(1)
    //         .get();

    //     if (!outletSnapshot.empty) {
    //         const outlet = outletSnapshot.docs[0].data();

    //         setOrderOutlet(outlet);
    //     }
    // };

    const [parsedOrderStatus, setParsedOrderStatus] = useState(ORDER_STATUS_PARSED.PLACED)
    const [parsedOrderStatusDict, setParsedOrderStatusDict] = useState({});

    // const selectedUserOrder = CommonStore.useState(s => s.selectedUserOrder)

    useEffect(() => {

        if (selectedUserOrder && selectedUserOrder.uniqueId) {
            if (selectedUserOrder.orderStatus === USER_ORDER_STATUS.ORDER_PREPARING ||
                selectedUserOrder.orderStatus === USER_ORDER_STATUS.ORDER_AUTHORIZED) {
                // means is still cooking

                setParsedOrderStatus(ORDER_STATUS_PARSED.PREPARING);
            }

            if (selectedUserOrder.orderStatus === USER_ORDER_STATUS.ORDER_PREPARED) {
                // means done cooking

                if (selectedUserOrder.courierCode === COURIER_CODE.LALAMOVE) {
                    // means is delivery and lalamove

                    if (selectedUserOrder.courierStatus === LALAMOVE_STATUS.REJECTED ||
                        selectedUserOrder.courierStatus === LALAMOVE_STATUS.CANCELED ||
                        selectedUserOrder.courierStatus === LALAMOVE_STATUS.EXPIRED) {
                        setParsedOrderStatus(ORDER_STATUS_PARSED.PICKING_UP);
                    }

                    if (selectedUserOrder.courierStatus === LALAMOVE_STATUS.ASSIGNING_DRIVER ||
                        selectedUserOrder.courierStatus === LALAMOVE_STATUS.ON_GOING) {
                        setParsedOrderStatus(ORDER_STATUS_PARSED.PICKING_UP);
                    }
                    else if (selectedUserOrder.courierStatus === LALAMOVE_STATUS.PICKED_UP) {
                        setParsedOrderStatus(ORDER_STATUS_PARSED.DELIVERING);
                    }
                    else if (selectedUserOrder.courierStatus === LALAMOVE_STATUS.COMPLETED) {
                        setParsedOrderStatus(ORDER_STATUS_PARSED.DELIVERED);
                    }
                }
                else if (selectedUserOrder.courierCode === COURIER_CODE.MRSPEEDY) {
                    // means is delivery and mrspeedy

                    if (selectedUserOrder.courierStatus === MRSPEEDY_STATUS.reactivated ||
                        selectedUserOrder.courierStatus === MRSPEEDY_STATUS.canceled ||
                        selectedUserOrder.courierStatus === MRSPEEDY_STATUS.delayed ||
                        selectedUserOrder.courierStatus === MRSPEEDY_STATUS.draft) {
                        setParsedOrderStatus(ORDER_STATUS_PARSED.PICKING_UP);
                    }

                    if (selectedUserOrder.courierStatus === MRSPEEDY_STATUS.new ||
                        selectedUserOrder.courierStatus === MRSPEEDY_STATUS.available) {
                        setParsedOrderStatus(ORDER_STATUS_PARSED.PICKING_UP);
                    }
                    else if (selectedUserOrder.courierStatus === MRSPEEDY_STATUS.active) {
                        setParsedOrderStatus(ORDER_STATUS_PARSED.DELIVERING);
                    }
                    else if (selectedUserOrder.courierStatus === MRSPEEDY_STATUS.completed) {
                        setParsedOrderStatus(ORDER_STATUS_PARSED.DELIVERED);
                    }
                }

                if (selectedUserOrder.orderType === ORDER_TYPE.PICKUP) {
                    setParsedOrderStatus(ORDER_STATUS_PARSED.DELIVERED);
                }
            }

            if (selectedUserOrder.orderStatus === USER_ORDER_STATUS.ORDER_DELIVERED ||
                selectedUserOrder.orderStatus === USER_ORDER_STATUS.ORDER_COMPLETED) {
                setParsedOrderStatus(ORDER_STATUS_PARSED.DELIVERED);
            }

            if (selectedUserOrder.orderStatus === USER_ORDER_STATUS.ORDER_CANCELLED_BY_MERCHANT) {
                setParsedOrderStatus(ORDER_STATUS_PARSED.DELIVERED);

                setOrderDetails({
                    message: 'Order cancelled by merchant.',

                    orderStatus: USER_ORDER_STATUS.ORDER_CANCELLED_BY_MERCHANT,
                });
            }

            if (selectedUserOrder.orderStatus === USER_ORDER_STATUS.ORDER_CANCELLED_BY_USER) {
                setParsedOrderStatus(ORDER_STATUS_PARSED.DELIVERED);

                setOrderDetails({
                    message: 'Order cancelled by user.',

                    orderStatus: USER_ORDER_STATUS.ORDER_CANCELLED_BY_USER,
                });
            }

            if (selectedUserOrder.orderStatus === USER_ORDER_STATUS.ORDER_SENDER_REJECTED) {
                setParsedOrderStatus(ORDER_STATUS_PARSED.DELIVERED);

                setOrderDetails({
                    message: `Order rejected by ${COURIER_INFO_DICT[selectedUserOrder.courierCode].name} side.`,

                    orderStatus: USER_ORDER_STATUS.ORDER_SENDER_REJECTED,
                });
            }

            if (selectedUserOrder.orderStatus === USER_ORDER_STATUS.ORDER_SENDER_CANCELED) {
                setParsedOrderStatus(ORDER_STATUS_PARSED.DELIVERED);

                setOrderDetails({
                    message: 'Order canceled.',

                    orderStatus: USER_ORDER_STATUS.ORDER_SENDER_CANCELED,
                });
            }

            if (selectedUserOrder.orderStatus === USER_ORDER_STATUS.ORDER_SENDER_EXPIRED) {
                setParsedOrderStatus(ORDER_STATUS_PARSED.DELIVERED);

                setOrderDetails({
                    message: `Order expired in ${COURIER_INFO_DICT[selectedUserOrder.courierCode].name} side.`,

                    orderStatus: USER_ORDER_STATUS.ORDER_SENDER_EXPIRED,
                });
            }

            ////////////////////////////////////////

            setDeliveryQuotation({
                totalFee: selectedUserOrder.deliveryFee,
            });
        }
    }, [selectedUserOrder]);

    useEffect(() => {
        if (selectedUserOrderOthers && selectedUserOrderOthers.length > 0) {
            for (var i = 0; i < selectedUserOrderOthers.length; i++) {
                var selectedUserOrder = selectedUserOrderOthers[i];

                if (selectedUserOrder.orderStatus === USER_ORDER_STATUS.ORDER_PREPARING ||
                    selectedUserOrder.orderStatus === USER_ORDER_STATUS.ORDER_AUTHORIZED) {
                    // means is still cooking

                    setParsedOrderStatusDict({
                        [selectedUserOrder.uniqueId]: ORDER_STATUS_PARSED.PREPARING,
                    });
                }

                if (selectedUserOrder.orderStatus === USER_ORDER_STATUS.ORDER_PREPARED) {
                    // means done cooking

                    if (selectedUserOrder.courierCode === COURIER_CODE.LALAMOVE) {
                        // means is delivery and lalamove

                        if (selectedUserOrder.courierStatus === LALAMOVE_STATUS.REJECTED ||
                            selectedUserOrder.courierStatus === LALAMOVE_STATUS.CANCELED ||
                            selectedUserOrder.courierStatus === LALAMOVE_STATUS.EXPIRED) {
                            // setParsedOrderStatus(ORDER_STATUS_PARSED.PICKING_UP);

                            setParsedOrderStatusDict({
                                ...parsedOrderStatusDict,
                                [selectedUserOrder.uniqueId]: ORDER_STATUS_PARSED.PICKING_UP,
                            });
                        }

                        if (selectedUserOrder.courierStatus === LALAMOVE_STATUS.ASSIGNING_DRIVER ||
                            selectedUserOrder.courierStatus === LALAMOVE_STATUS.ON_GOING) {
                            // setParsedOrderStatus(ORDER_STATUS_PARSED.PICKING_UP);

                            setParsedOrderStatusDict({
                                ...parsedOrderStatusDict,
                                [selectedUserOrder.uniqueId]: ORDER_STATUS_PARSED.PICKING_UP,
                            });
                        }
                        else if (selectedUserOrder.courierStatus === LALAMOVE_STATUS.PICKED_UP) {
                            // setParsedOrderStatus(ORDER_STATUS_PARSED.DELIVERING);

                            setParsedOrderStatusDict({
                                ...parsedOrderStatusDict,
                                [selectedUserOrder.uniqueId]: ORDER_STATUS_PARSED.DELIVERING,
                            });
                        }
                        else if (selectedUserOrder.courierStatus === LALAMOVE_STATUS.COMPLETED) {
                            // setParsedOrderStatus(ORDER_STATUS_PARSED.DELIVERED);

                            setParsedOrderStatusDict({
                                ...parsedOrderStatusDict,
                                [selectedUserOrder.uniqueId]: ORDER_STATUS_PARSED.DELIVERED,
                            });
                        }
                    }
                    else if (selectedUserOrder.courierCode === COURIER_CODE.MRSPEEDY) {
                        // means is delivery and mrspeedy

                        if (selectedUserOrder.courierStatus === MRSPEEDY_STATUS.reactivated ||
                            selectedUserOrder.courierStatus === MRSPEEDY_STATUS.canceled ||
                            selectedUserOrder.courierStatus === MRSPEEDY_STATUS.delayed ||
                            selectedUserOrder.courierStatus === MRSPEEDY_STATUS.draft) {
                            // setParsedOrderStatus(ORDER_STATUS_PARSED.PICKING_UP);

                            setParsedOrderStatusDict({
                                ...parsedOrderStatusDict,
                                [selectedUserOrder.uniqueId]: ORDER_STATUS_PARSED.PICKING_UP,
                            });
                        }

                        if (selectedUserOrder.courierStatus === MRSPEEDY_STATUS.new ||
                            selectedUserOrder.courierStatus === MRSPEEDY_STATUS.available) {
                            // setParsedOrderStatus(ORDER_STATUS_PARSED.PICKING_UP);

                            setParsedOrderStatusDict({
                                ...parsedOrderStatusDict,
                                [selectedUserOrder.uniqueId]: ORDER_STATUS_PARSED.PICKING_UP,
                            });
                        }
                        else if (selectedUserOrder.courierStatus === MRSPEEDY_STATUS.active) {
                            // setParsedOrderStatus(ORDER_STATUS_PARSED.DELIVERING);

                            setParsedOrderStatusDict({
                                ...parsedOrderStatusDict,
                                [selectedUserOrder.uniqueId]: ORDER_STATUS_PARSED.DELIVERING,
                            });
                        }
                        else if (selectedUserOrder.courierStatus === MRSPEEDY_STATUS.completed) {
                            // setParsedOrderStatus(ORDER_STATUS_PARSED.DELIVERED);

                            setParsedOrderStatusDict({
                                ...parsedOrderStatusDict,
                                [selectedUserOrder.uniqueId]: ORDER_STATUS_PARSED.DELIVERED,
                            });
                        }
                    }

                    if (selectedUserOrder.orderType === ORDER_TYPE.PICKUP) {
                        // setParsedOrderStatus(ORDER_STATUS_PARSED.DELIVERED);

                        setParsedOrderStatusDict({
                            ...parsedOrderStatusDict,
                            [selectedUserOrder.uniqueId]: ORDER_STATUS_PARSED.DELIVERED,
                        });
                    }
                }

                if (selectedUserOrder.orderStatus === USER_ORDER_STATUS.ORDER_DELIVERED ||
                    selectedUserOrder.orderStatus === USER_ORDER_STATUS.ORDER_COMPLETED) {
                    // setParsedOrderStatus(ORDER_STATUS_PARSED.DELIVERED);

                    setParsedOrderStatusDict({
                        ...parsedOrderStatusDict,
                        [selectedUserOrder.uniqueId]: ORDER_STATUS_PARSED.DELIVERED,
                    });
                }

                if (selectedUserOrder.orderStatus === USER_ORDER_STATUS.ORDER_CANCELLED_BY_MERCHANT) {
                    // setParsedOrderStatus(ORDER_STATUS_PARSED.DELIVERED);

                    setParsedOrderStatusDict({
                        ...parsedOrderStatusDict,
                        [selectedUserOrder.uniqueId]: ORDER_STATUS_PARSED.DELIVERED,
                    });

                    // setOrderDetails({
                    //     message: 'Order cancelled by merchant.',

                    //     orderStatus: USER_ORDER_STATUS.ORDER_CANCELLED_BY_MERCHANT,
                    // });

                    setOrderDetailsDict({
                        ...orderDetailsDict,
                        [selectedUserOrder.uniqueId]: {
                            message: 'Order cancelled by merchant.',

                            orderStatus: USER_ORDER_STATUS.ORDER_CANCELLED_BY_MERCHANT,
                        },
                    });
                }

                if (selectedUserOrder.orderStatus === USER_ORDER_STATUS.ORDER_CANCELLED_BY_USER) {
                    // setParsedOrderStatus(ORDER_STATUS_PARSED.DELIVERED);

                    setParsedOrderStatusDict({
                        ...parsedOrderStatusDict,
                        [selectedUserOrder.uniqueId]: ORDER_STATUS_PARSED.DELIVERED,
                    });

                    // setOrderDetails({
                    //     message: 'Order cancelled by user.',

                    //     orderStatus: USER_ORDER_STATUS.ORDER_CANCELLED_BY_USER,
                    // });

                    setOrderDetailsDict({
                        ...orderDetailsDict,
                        [selectedUserOrder.uniqueId]: {
                            message: 'Order cancelled by user.',

                            orderStatus: USER_ORDER_STATUS.ORDER_CANCELLED_BY_USER,
                        },
                    });
                }

                if (selectedUserOrder.orderStatus === USER_ORDER_STATUS.ORDER_SENDER_REJECTED) {
                    // setParsedOrderStatus(ORDER_STATUS_PARSED.DELIVERED);

                    setParsedOrderStatusDict({
                        ...parsedOrderStatusDict,
                        [selectedUserOrder.uniqueId]: ORDER_STATUS_PARSED.DELIVERED,
                    });

                    // setOrderDetails({
                    //     message: `Order rejected by ${COURIER_INFO_DICT[selectedUserOrderTakeaway.courierCode].name} side.`,

                    //     orderStatus: USER_ORDER_STATUS.ORDER_SENDER_REJECTED,
                    // });

                    setOrderDetailsDict({
                        ...orderDetailsDict,
                        [selectedUserOrder.uniqueId]: {
                            message: `Order rejected by ${COURIER_INFO_DICT[selectedUserOrder.courierCode].name} side.`,

                            orderStatus: USER_ORDER_STATUS.ORDER_SENDER_REJECTED,
                        },
                    });
                }

                if (selectedUserOrder.orderStatus === USER_ORDER_STATUS.ORDER_SENDER_CANCELED) {
                    // setParsedOrderStatus(ORDER_STATUS_PARSED.DELIVERED);

                    setParsedOrderStatusDict({
                        ...parsedOrderStatusDict,
                        [selectedUserOrder.uniqueId]: ORDER_STATUS_PARSED.DELIVERED,
                    });

                    // setOrderDetails({
                    //     message: 'Order cancelled.',

                    //     orderStatus: USER_ORDER_STATUS.ORDER_SENDER_CANCELED,
                    // });

                    setOrderDetailsDict({
                        ...orderDetailsDict,
                        [selectedUserOrder.uniqueId]: {
                            message: 'Order cancelled.',

                            orderStatus: USER_ORDER_STATUS.ORDER_SENDER_CANCELED,
                        },
                    });
                }

                if (selectedUserOrder.orderStatus === USER_ORDER_STATUS.ORDER_SENDER_EXPIRED) {
                    // setParsedOrderStatus(ORDER_STATUS_PARSED.DELIVERED);

                    setParsedOrderStatusDict({
                        ...parsedOrderStatusDict,
                        [selectedUserOrder.uniqueId]: ORDER_STATUS_PARSED.DELIVERED,
                    });

                    // setOrderDetails({
                    //     message: `Order expired in ${COURIER_INFO_DICT[selectedUserOrderTakeaway.courierCode].name} side.`,

                    //     orderStatus: USER_ORDER_STATUS.ORDER_SENDER_EXPIRED,
                    // });

                    setOrderDetailsDict({
                        ...orderDetailsDict,
                        [selectedUserOrder.uniqueId]: {
                            message: `Order expired in ${COURIER_INFO_DICT[selectedUserOrder.courierCode].name} side.`,

                            orderStatus: USER_ORDER_STATUS.ORDER_SENDER_EXPIRED,
                        },
                    });
                }

                ////////////////////////////////////////

                // setDeliveryQuotation({
                //     totalFee: selectedUserOrderTakeaway.deliveryFee,
                // });
            }
        }
    }, [selectedUserOrderOthers]);

    useEffect(() => {
        if (actionModalVisibility) {
            if (selectedUserOrder.crUserAddress) {
                // valid order to proceed

                if (selectedSender === COURIER_CODE.LALAMOVE) {
                    var body = {
                        outletLat: selectedUserOrder.crOutletLat,
                        outletLng: selectedUserOrder.crOutletLng,
                        outletAddress: selectedUserOrder.crOutletAddress,

                        outletPhone: selectedUserOrder.crOutletPhone,
                        outletName: selectedUserOrder.outletName,

                        userLat: selectedUserOrder.crUserLat,
                        userLng: selectedUserOrder.crUserLng,
                        userAddress: selectedUserOrder.crUserAddress,

                        userName: selectedUserOrder.crUserName,
                        userPhone: selectedUserOrder.crUserPhone,
                        userRemarks: selectedUserOrder.crUserRemarks,

                        // scheduleAt: moment().add(totalPrepareTime, 'second').add(15, 'minute').utc().toISOString(),
                    };

                    console.log('quotation body');
                    console.log(body);

                    ApiClient.POST(API.lalamoveQuotation, body).then((result) => {
                        console.log("lalamove quotation result");
                        console.log(result);

                        if (result === undefined) {
                            // means lalamove can't deliver to this address

                            Alert.alert(
                                'Info',
                                'Sorry, we unable to deliver to this address, please try another one.',
                            );
                        }
                        else if (result && result.totalFee) {
                            // { totalFee: "0.00", totalFeeCurrency: "MYR" }

                            setDeliveryQuotation({
                                totalFee: parseFloat(result.totalFee),
                                totalFeeCurrency: result.totalFeeCurrency,
                                courierCode: COURIER_CODE.LALAMOVE,
                            });
                        }
                    });
                }
                else if (selectedSender === COURIER_CODE.MRSPEEDY) {
                    var body = {
                        outletLat: selectedUserOrder.crOutletLat,
                        outletLng: selectedUserOrder.crOutletLng,
                        outletAddress: selectedUserOrder.crOutletAddress,

                        outletPhone: selectedUserOrder.crOutletPhone,
                        outletName: selectedUserOrder.outletName,

                        userLat: selectedUserOrder.crUserLat,
                        userLng: selectedUserOrder.crUserLng,
                        userAddress: selectedUserOrder.crUserAddress,

                        userName: selectedUserOrder.crUserName,
                        userPhone: selectedUserOrder.crUserPhone,
                        userRemarks: selectedUserOrder.crUserRemarks,

                        totalWeightKg: selectedUserOrder.totalWeightKg,
                        // outletRequiredStartDatetime: moment().add(totalPrepareTime, 'second').add(5, 'minute').utc().toISOString(),
                        // outletRequiredFinishDatetime: moment().add(totalPrepareTime, 'second').add(10, 'minute').utc().toISOString(),
                        // userRequiredStartDatetime: moment().add(totalPrepareTime, 'second').add(15, 'minute').utc().toISOString(),
                        // userRequiredFinishDatetime: moment().add(totalPrepareTime, 'second').add(30, 'minute').utc().toISOString(),
                    };

                    console.log('quotation body');
                    console.log(body);

                    ApiClient.POST(API.mrSpeedyCalculateOrder, body).then((result) => {
                        console.log("mr speedy quotation result");
                        console.log(result);

                        if (!result || !result.is_successful) {
                            // means lalamove can't deliver to this address

                            Alert.alert(
                                'Info',
                                'Sorry, we unable to deliver to this address, please try another one.',
                            );
                        }
                        else if (result.is_successful && result.order && result.order.payment_amount) {
                            // { totalFee: "0.00", totalFeeCurrency: "MYR" }

                            setDeliveryQuotation({
                                totalFee: parseFloat(result.order.payment_amount),
                                totalFeeCurrency: 'MYR',
                                courierCode: COURIER_CODE.MRSPEEDY,
                            });
                        }
                    });
                }
            }
            else {
                // do nothing for now
            }
        }
    }, [actionModalVisibility, selectedUserOrder, selectedSender]);

    // componentDidMount() {
    //     ApiClient.GET(API.order2 + orderId).then((result) => {
    //         console.log(result);
    //         setState({
    //             orderId: result,
    //             orderItems: result.orderItems,
    //             orderData: result.outlet,
    //             orderMerchant: result.outlet.merchant,
    //             orderTax: result.outlet.orderTax,
    //         });
    //     });
    // }
    // function end

    var detailsFontSize = 20;

    if (Dimensions.get('screen').width <= 360) {
        detailsFontSize = 13;
        //console.log(Dimensions.get('screen').width)
    }

    const detailsTextScale = {
        fontSize: detailsFontSize,
    };

    var detailsFontSize2 = 16;

    if (Dimensions.get('screen').width <= 360) {
        detailsFontSize2 = 13;
        //console.log(Dimensions.get('screen').width)
    }

    const detailsTextScale2 = {
        fontSize: detailsFontSize2,
    };

    var detailsTopSpace = '48%';

    if (Dimensions.get('screen').width <= 360) {
        detailsTopSpace = '56.5%';
        //console.log(Dimensions.get('screen').width)
    }

    const detailsTopSpacing = {
        top: detailsTopSpace,
    };

    const waitForSender = () => {
        const body = {
            orderId: selectedUserOrder.uniqueId,
        };

        ApiClient.POST(API.waitForSender, body).then(async (result) => {
            // Alert.alert(
            //     'Success',
            //     'The user of this order had been notified.',
            //     [{ text: 'OK', onPress: () => { } }],
            //     { cancelable: false },
            // );

            // const userOrderSnapshot = await firebase().firestore()
            //     .collection(Collections.UserOrder)
            //     .where('uniqueId', '==', selectedUserOrder.uniqueId)
            //     .limit(1)
            //     .get();

            const userOrderSnapshot = await getDocs(
                query(
                    collection(global.db, Collections.UserOrder),
                    where('uniqueId', '==', selectedUserOrder.uniqueId),
                    limit(1),
                )
            );

            var userOrder = null;
            if (!userOrderSnapshot.empty) {
                userOrder = userOrderSnapshot.docs[0].data();
            }

            CommonStore.update(s => {
                s.selectedUserOrder = userOrder;
            }, () => {
                setActionModalVisibility(false);
            });
        });
    }

    // const getTestData = async () => {
    //     const userOrderSnapshot = await firebase.firestore().collection(Collections.UserOrder)

    //         .where('uniqueId', '==', orderId)
    //         .limit(1)
    //         .get();

    //     var userOrder = null;

    //     if (!userOrderSnapshot.empty) {
    //         userOrder = userOrderSnapshot.docs[0].data();
    //     }

    //     CommonStore.update(s => {
    //         s.selectedUserOrder = userOrder;
    //     });
    // };

    // useEffect(() => {
    //     getTestData()
    // }, [])


    const startMolPay = () => {
        // need get readable order id from api first

        const body = {
            outletId: selectedUserOrder.outletId,
        };

        var amountToPay = Math.max(deliveryQuotation.totalFee - selectedUserOrder.deliveryFee, 0);

        // Testing

        amountToPay = amountToPay > 5 ? 5 : amountToPay;

        var paymentDetails = {
            // Optional, REQUIRED when use online Sandbox environment and account credentials.
            'mp_dev_mode': true,

            // Mandatory String. Values obtained from Razer Merchant Services.
            'mp_username': 'api_SB_mykoodoo',
            'mp_password': 'WaaU1IeZ*&(%%',
            'mp_merchant_ID': 'SB_mykoodoo',
            'mp_app_name': 'mykoodoo',
            'mp_verification_key': '0bd0efac623106b4c19bc28b8e9a0d8d',

            // Mandatory String. Payment values.
            'mp_amount': amountToPay, // Minimum 1.01
            'mp_order_ID': `#${selectedUserOrder.orderType === ORDER_TYPE.DINEIN ? '' : 'T'}${selectedUserOrder.orderId}`,
            'mp_currency': 'MYR',
            'mp_country': 'MY',

            // Optional String.
            'mp_channel': 'multi', // Use 'multi' for all available channels option. For individual channel seletion, please refer to https://github.com/RazerMS/rms-mobile-xdk-examples/blob/master/channel_list.tsv.
            'mp_bill_description': selectedUserOrder.outletName + ' delivery topup RM' + parseFloat(amountToPay).toFixed(2),
            'mp_bill_name': selectedUserOrder.userName,
            'mp_bill_email': userEmail,
            'mp_bill_mobile': selectedUserOrder.userPhone,

            'mp_sandbox_mode': true,
            'mp_tcctype': 'AUTH',
        }

        window.startMolpay(paymentDetails, (data) => {
            //callback after payment success

            console.log("razer result v2");
            console.log(data);

            const result = JSON.parse(data);
            console.log(result);

            if (result.error_code || result.Error) {
                console.log('razer error');

                Alert.alert(
                    "Error",
                    "Failed to process your payment",
                    [
                        {
                            text: "OK",
                            onPress: () => {
                                // navigation.jumpTo('Home')
                                // navigation.navigate('Home');
                                navigation.dispatch(
                                    CommonActions.reset({
                                        index: 0,
                                        routes: [{ name: "Home" }]
                                    }));
                            },
                        },
                    ],
                    { cancelable: false }
                );

                CommonStore.update(s => {
                    s.isLoading = false;
                });
            }
            else {
                console.log("payment success v2");

                topupUpdateCourier(result);
            }

            // placeUserOrder();
        });
    }

    const topup = () => {
        CommonStore.update(s => {
            s.isLoading = true;
        });

        // think should record down the topup payment details

        const deliveryFeeDiff = Math.max(deliveryQuotation.totalFee - selectedUserOrder.deliveryFee, 0);

        if (deliveryFeeDiff > 0) {
            startMolPay();
        }
        else {
            topupUpdateCourier();
        }
    }

    const topupUpdateCourier = (paymentDetails) => {
        var body = {
            orderId: selectedUserOrder.uniqueId,
            merchantId: selectedUserOrder.merchantId,

            deliveryFeeDiff: Math.max(deliveryQuotation.totalFee - selectedUserOrder.deliveryFee, 0),

            deliveryFee: deliveryQuotation.totalFee,
            courierCode: deliveryQuotation.courierCode,
            deliveryCurrency: deliveryQuotation.totalFeeCurrency,

            paymentDetailsCourierAction: paymentDetails ? paymentDetails : null,
        };

        ApiClient.POST(API.updateUserOrderCourierByUser, body, {
            timeout: 10000,
        }).then(async (result) => {
            console.log("updateUserOrderCourierByUser");
            console.log(result)

            if (result) {
                if (result.status === 'success') {
                    Alert.alert(
                        "Success",
                        "Sender for this order changed successfully.",
                        [
                            {
                                text: "OK",
                                onPress: async () => {
                                    // const userOrderSnapshot = await firebase.firestore()
                                    //     .collection(Collections.UserOrder)
                                    //     .where('uniqueId', '==', selectedUserOrder.uniqueId)
                                    //     .limit(1)
                                    //     .get();

                                    const userOrderSnapshot = await getDocs(
                                        query(
                                            collection(global.db, Collections.UserOrder),
                                            where('uniqueId', '==', selectedUserOrder.uniqueId),
                                            limit(1),
                                        )
                                    );

                                    var userOrder = null;
                                    if (!userOrderSnapshot.empty) {
                                        userOrder = userOrderSnapshot.docs[0].data();
                                    }

                                    CommonStore.update(s => {
                                        s.selectedUserOrder = userOrder;
                                    }, () => {
                                        setActionModalVisibility(false);
                                    });
                                },
                            },
                        ],
                        { cancelable: false }
                    );
                }
                else {
                    Alert.alert(
                        "Error",
                        result.message,
                        [
                            {
                                text: "OK",
                                onPress: () => {
                                },
                            },
                        ],
                        { cancelable: false }
                    );
                }
            }
            else {
                Alert.alert(
                    "Error",
                    "Failed to change the sender for this order.",
                    [
                        {
                            text: "OK",
                            onPress: () => {
                            },
                        },
                    ],
                    { cancelable: false }
                );
            }

            CommonStore.update(s => {
                s.isLoading = false;
            });
        });
    };

    const completeUserOrderByUser = () => {
        CommonStore.update(s => {
            s.isLoading = true;
        });

        const body = {
            orderId: selectedUserOrder.uniqueId,
        };

        ApiClient.POST(API.completeUserOrderByUser, body).then(async (result) => {
            // const userOrderSnapshot = await firebase.firestore()
            //     .collection(Collections.UserOrder)
            //     .where('uniqueId', '==', selectedUserOrder.uniqueId)
            //     .limit(1)
            //     .get();

            const userOrderSnapshot = await getDocs(
                query(
                    collection(global.db, Collections.UserOrder),
                    where('uniqueId', '==', selectedUserOrder.uniqueId),
                    limit(1),
                )
            );

            var userOrder = null;
            if (!userOrderSnapshot.empty) {
                userOrder = userOrderSnapshot.docs[0].data();
            }

            CommonStore.update(s => {
                s.selectedUserOrder = userOrder;

                s.isLoading = false;
            }, () => {
                // setActionModalVisibility(false);

                setCompleteModalVisibility(true);
            });
        });
    }

    const submitReview = () => {
        CommonStore.update(s => {
            s.isLoading = true;
        });

        const body = {
            orderId: selectedUserOrder.uniqueId,
            userId: selectedUserOrder.userId,
            userName: userName,
            userImage: userAvatar,
            outletId: selectedUserOrder.outletId,
            outletName: selectedUserOrder.outletName,
            merchantId: selectedUserOrder.merchantId,
            merchantName: selectedUserOrder.merchantName,
            ratings: starRatingDefault,
            comments: reviewComment,
        };

        console.log(body);

        ApiClient.POST(API.submitOrderReviewByUser, body).then(async (result) => {
            // const userOrderSnapshot = await firebase.firestore()
            //     .collection(Collections.UserOrder)
            //     .where('uniqueId', '==', selectedUserOrder.uniqueId)
            //     .limit(1)
            //     .get();

            const userOrderSnapshot = await getDocs(
                query(
                    collection(global.db, Collections.UserOrder),
                    where('uniqueId', '==', selectedUserOrder.uniqueId),
                    limit(1),
                )
            );

            var userOrder = null;
            if (!userOrderSnapshot.empty) {
                userOrder = userOrderSnapshot.docs[0].data();
            }

            CommonStore.update(s => {
                s.selectedUserOrder = userOrder;

                s.isLoading = false;
            }, () => {
                // setActionModalVisibility(false);

                setCompleteModalVisibility(false);
            });
        });
    };

    //const starFilled = 'https://raw.githubusercontent.com/tranhonghan/images/main/star_filled.png'
    //const starUnfilled = 'https://raw.githubusercontent.com/tranhonghan/images/main/star_corner.png'

    //const starFilled = 'https://lh3.googleusercontent.com/proxy/DhwXgAC-E6rS9vYy3DIgsv8d9_G7b7hGCi5Aa3-LF0rCnWE3yhGoAoM_2ucnL-BEAHtWhAq-RHyho_LnDskAXBA0jA5OlCuISQ'
    //const starUnfilled = 'https://raw.githubusercontent.com/tranhonghan/images/main/star_corner.png'

    const StarRatingView = () => {

        return (
            <>
                {
                    starRating.map((item, key) => {
                        return (
                            <>
                                <TouchableOpacity
                                    style={{ marginLeft: 3 }}
                                    key={item}
                                    onPress={() =>
                                        setStarRatingDefault(item)
                                    }
                                >
                                    {/* <Image
                    style={styles.starStyle}
                    source={
                        item <= starRatingDefault
                        ? {uri: starFilled}
                        : {uri: starUnfilled}
                    }
                    /> */}
                                    <AntDesign name={'star'} size={35}
                                        color={
                                            item <=
                                                starRatingDefault
                                                ? Colors.primaryColor
                                                : Colors.fieldtTxtColor
                                        } />
                                </TouchableOpacity>
                            </>
                        )
                    }
                    )
                }
            </>
        )
    }


    const renderQuestionnaire = ({ item, index }) => (
        <>
            <View style={{ flexDirection: 'row', justifyContent: 'flex-end', alignItems: 'flex-end', padding: 10 }}>
                <TouchableOpacity
                    style={styles.closeButton}
                    onPress={() => {
                        setQuestionnaireVisibility(false);
                    }}>
                    <AntDesign name="closecircle" size={25} color={Colors.fieldtTxtColor} />
                </TouchableOpacity>
            </View>
            <View style={{ flexDirection: 'column', alignItems: 'flex-start', justifyContent: 'flex-start' }}>
                <Text style={{
                    fontSize: 20,
                    fontFamily: "NunitoSans-Bold"
                }}>
                    Questionnaire
                </Text>
                <Text style={{
                    fontSize: 13,
                    fontFamily: "NunitoSans-Regular",
                    marginTop: 5
                }}>
                    Help us serve you better
                </Text>
            </View>

            <Text style={{
                fontSize: 13,
                fontFamily: "NunitoSans-Regular",
                marginTop: 10
            }}>
                Are you a vegetarian?
            </Text>

            <Picker style={{ marginTop: 5, borderWidth: 1, borderColor: '#E5E5E5', borderRadius: 5, height: 30, width: 200, paddingVertical: 0, }}
                dropDownStyle={{ borderWidth: 1, borderColor: '#E5E5E5', borderRadius: 5, width: 200, paddingVertical: 0, marginHorizontal: 10 }}
                arrowSize={17}
                arrowColor={'black'}
                arrowStyle={{ paddingVertical: 0 }}
                itemStyle={{ justifyContent: 'flex-start', marginLeft: 5 }}
                placeholderStyle={{ marginLeft: 0 }}
                placeholder={" Answer"}
                items={[{ label: 'Yes', value: 'Yes' }, { label: 'No', value: 'No' }]}

            />

            {/* <Text style={{
                fontSize: 13,
                fontFamily: "NunitoSans-Regular",
                marginTop: 10
            }}>
                Are you allergic to any food or ingredient?
        </Text>
        <DropDownPicker style={{ marginTop: 5, borderWidth: 1, borderColor: '#E5E5E5', borderRadius: 5, height: 30, width: 200, paddingVertical: 0,  }}
            
            dropDownStyle={{ borderWidth: 1, borderColor: '#E5E5E5', borderRadius: 5, width: 200, paddingVertical: 0, marginHorizontal: 10 }}
            arrowSize={17}
            arrowColor={'black'}
            arrowStyle={{ paddingVertical: 0 }}
            itemStyle={{ justifyContent: 'flex-start', marginLeft: 5 }}
            placeholderStyle={{ marginLeft: 0 }}
            placeholder={" Answer"}
            items={[{ label: 'Yes', value: 'Yes' }, { label: 'No', value: 'No' }]}

        />

        
        <Text style={{
                fontSize: 13,
                fontFamily: "NunitoSans-Regular",
                marginTop: 10
            }}>
                What foods or ingredients are you allergic to?
        </Text>
        <TextInput
            underlineColorAndroid={Colors.fieldtBgColor}
            clearButtonMode='while-editing'
            placeholder={"Answer"}
            style={styles.questionInput}
            />

        <Text style={{
                fontSize: 13,
                fontFamily: "NunitoSans-Regular",
                marginTop: 10
            }}>
                Do you like fast food or healthy food?
        </Text>
        <DropDownPicker style={{ marginTop: 5, borderWidth: 1, borderColor: '#E5E5E5', borderRadius: 5, height: 30, width: 200, paddingVertical: 0,  }}
            
            dropDownStyle={{ borderWidth: 1, borderColor: '#E5E5E5', borderRadius: 5, width: 200, paddingVertical: 0, marginHorizontal: 10 }}
            arrowSize={17}
            arrowColor={'black'}
            arrowStyle={{ paddingVertical: 0 }}
            itemStyle={{ justifyContent: 'flex-start', marginLeft: 5 }}
            placeholderStyle={{ marginLeft: 0 }}
            placeholder={" Answer"}
            items={[{ label: 'Fast Food', value: 'Fast Food' }, { label: 'Healthy Food', value: 'Healthy Food' }]}

        />

        <Text style={{
                fontSize: 13,
                fontFamily: "NunitoSans-Regular",
                marginTop: 10
            }}>
                Do you like dessert?
        </Text>
        <DropDownPicker style={{ marginTop: 5, borderWidth: 1, borderColor: '#E5E5E5', borderRadius: 5, height: 30, width: 200, paddingVertical: 0,  }}
            
            dropDownStyle={{ borderWidth: 1, borderColor: '#E5E5E5', borderRadius: 5, width: 200, paddingVertical: 0, marginHorizontal: 10 }}
            arrowSize={17}
            arrowColor={'black'}
            arrowStyle={{ paddingVertical: 0 }}
            itemStyle={{ justifyContent: 'flex-start', marginLeft: 5 }}
            placeholderStyle={{ marginLeft: 0 }}
            placeholder={" Answer"}
            items={[{ label: 'Yes', value: 'Yes' }, { label: 'No', value: 'No' }]}

        /> */}
            {expandCompleteQuestionnaire ? (
                <View style={{ flexDirection: 'column', alignItems: 'flex-start', justifyContent: 'flex-start' }}>
                    <Text style={{
                        fontSize: 20,
                        fontFamily: "NunitoSans-Bold"
                    }}>
                        Thank you !
                    </Text>
                    <Text style={{
                        fontSize: 13,
                        fontFamily: "NunitoSans-Regular",
                        marginTop: 5
                    }}>
                        Hope to see you again
                    </Text>
                </View>
            ) : null}
            <View style={{ flexDirection: 'row', marginTop: 25, justifyContent: 'flex-end', alignItems: 'flex-end', padding: 10, }}>
                <TouchableOpacity style={{
                    width: "40%",
                    height: 40,
                    backgroundColor: Colors.primaryColor,
                    borderRadius: 10,
                    justifyContent: "center",
                    alignItems: "center",
                    borderWidth: 1,
                    borderColor: Colors.descriptionColor,
                    zIndex: 1000,
                    marginRight: 10
                }}
                    onPress={() => {
                        setExpandCompleteQuestionnaire(true);
                    }}
                >

                    <Text style={{ color: Colors.whiteColor, fontFamily: "NunitoSans-Regular" }}>SUBMIT</Text>

                </TouchableOpacity>
                <TouchableOpacity style={{
                    width: "40%",
                    height: 40,
                    backgroundColor: Colors.primaryColor,
                    borderRadius: 10,
                    justifyContent: "center",
                    alignItems: "center",
                    borderWidth: 1,
                    borderColor: Colors.descriptionColor,
                    zIndex: 1000,
                }}
                >

                    <Text style={{ color: Colors.whiteColor, fontFamily: "NunitoSans-Regular" }}>NEXT</Text>

                </TouchableOpacity>
            </View>
        </>
    );

    var orderCodeFont = 16;

    if (Dimensions.get('screen').width <= 360) {
        orderCodeFont = 14;
        //console.log(Dimensions.get('screen').width)
    }

    const orderCodeFontSize = {
        fontSize: orderCodeFont,
    };

    var orderCodeFont1 = 16;

    if (Dimensions.get('screen').width <= 360) {
        orderCodeFont1 = 12;
        //console.log(Dimensions.get('screen').width)
    }

    const orderCodeFontSize1 = {
        fontSize: orderCodeFont1,
    };

    const proceedToReceiptDetails = async (item) => {
        // const outletSnapshot = await firebase().collection(Collections.Outlet)
        //     .firestore
        //     .where('uniqueId', '==', item.outletId)
        //     .limit(1)
        //     .get();

        const outletSnapshot = await getDocs(
            query(
                collection(global.db, Collections.Outlet),
                where('uniqueId', '==', item.outletId),
                limit(1),
            )
        );

        var outlet = null;

        if (!outletSnapshot.empty) {
            outlet = outletSnapshot.docs[0].data();
        }

        CommonStore.update(s => {
            s.selectedUserOrder = item;
            s.selectedOutlet = outlet;
        });

        var tempUserAddress = null;
        for (var i = 0; i < userAddresses.length; i++) {
            if (userAddresses[i].uniqueId === item.userAddressId) {
                tempUserAddress = userAddresses[i];
            }
        }

        props.navigation.navigate('ReceiptDetail', {
            orderId: item.uniqueId,
            order: item,
            outlet: outlet,

            userDetails: {
                address: tempUserAddress.address,
                name: userName,
                phone: userNumber,
                email: userEmail,
                userId: firebaseUid,
            },
        });
    }

    const renderTableOrder = ({ item, index }) => {
        var styleStripEffect = {};
        var checkboxShowed = true;

        var isAllPaid = false;
        var isPaidItem = false;

        if (item.priceToPay !== undefined && item.priceToPay === item.price) {
            isAllPaid = true;
            isPaidItem = true;

            styleStripEffect = {
                // textDecorationLine: 'line-through',
                color: Colors.primaryColor,
            };
            checkboxShowed = false;
        }

        return (
            <>
                {
                    (item.addOns !== undefined &&
                        (item.priceToPay === undefined ||
                            (item.priceToPay !== undefined && item.priceToPay === item.price)))
                        ?
                        <View style={{
                            flexDirection: 'row',
                            alignItems: 'center',
                        }}>
                            {/* <View style={{
                                flexDirection: 'row',
                                alignItems: 'center',
                                // width: '2%',
                                width: '5%',
                                // backgroundColor: 'red',
                            }}>
                                <View
                                    style={{
                                        // width: '7.5%',
                                        // height: '2%',
                                        shadowColor: '#000',
                                        shadowOffset: {
                                            width: 0,
                                            height: 2,
                                        },
                                        shadowOpacity: 0.34,
                                        shadowRadius: 3.32,
                                        elevation: 1,
                                        //backgroundColor: 'blue',
                                        justifyContent: 'center',
                                        alignItems: 'center',
                                        // borderWidth: 0,
                                    }}>
                                    {!item.priceToPay || item.priceToPay === 0 ? (
                                        <CheckBox
                                            disabled={!checkboxShowed}
                                            value={
                                                selectedSummaryOrdersDict[
                                                item.itemId + item.cartItemDate.toString()
                                                ] !== false &&
                                                selectedSummaryOrdersDict[
                                                item.itemId + item.cartItemDate.toString()
                                                ] !== undefined
                                            }
                                            onValueChange={(value) => {
                                                // console.log(count, 'count');                      

                                                if (value) {
                                                    // setCount(count + 1);

                                                    TableStore.update(s => {
                                                        s.count = count + 1;
                                                    });
                                                } else {
                                                    // setCount(count - 1);

                                                    TableStore.update(s => {
                                                        s.count = count - 1;
                                                    });
                                                }

                                                TableStore.update(s => {
                                                    s.isDelivered = false;
                                                    s.isCancelled = false;
                                                    s.cancelledCount = 0;
                                                    s.deliveryCount = 0;
                                                    s.deliveredUser = [];
                                                    s.cancelledUser = [];
                                                    s.selectedCancelledOrdersDict = {};
                                                    s.selectedDeliveredOrdersDict = {};
                                                    s.selectedSummaryOrdersDict = {
                                                        ...selectedSummaryOrdersDict,
                                                        [item.itemId + item.cartItemDate.toString()]: value,
                                                    };
                                                });

                                                if (value) {
                                                    var tempAdd = [...cancelUser];
                                                    tempAdd.push({
                                                        cartItemDate: item.cartItemDate,
                                                        itemId: item.itemId,
                                                        userOrderId: item.userOrderId ? item.userOrderId : '',
                                                        quantity: item.quantity,
                                                        discountPromotions: item.discountPromotions ? item.discountPromotions : 0,
                                                        isFreeItem: item.isFreeItem ? item.isFreeItem : false,
                                                    });

                                                    // setCancelUser(tempAdd);

                                                    TableStore.update(s => {
                                                        s.cancelUser = tempAdd;
                                                    });
                                                } else {
                                                    var tempMinus = [...cancelUser];
                                                    for (let i = 0; i < tempMinus.length; i++) {
                                                        if (
                                                            tempMinus[i].cartItemDate === item.cartItemDate &&
                                                            tempMinus[i].itemId === item.itemId
                                                        ) {
                                                            tempMinus.splice(i, 1);
                                                            break;
                                                        }
                                                    }

                                                    // setCancelUser(tempMinus);

                                                    TableStore.update(s => {
                                                        s.cancelUser = tempMinus;
                                                    });
                                                }
                                            }}
                                        />
                                    ) : null}
                                </View>
                            </View> */}

                            <View style={{
                                flexDirection: 'column',
                                // alignItems: 'center',
                                width: '100%',
                                // backgroundColor: 'blue',
                            }}>
                                <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', paddingVertical: 5, width: '100%' }}>
                                    {/* 1/3/23 Hide first */}

                                    {
                                        item.checkBox
                                            ?
                                            <View style={{
                                                width: isMobile() ? '5%' : '3%',
                                                marginRight: 10,

                                                // opacity: item.isCancelledItem ? 0 : 100,
                                            }}>
                                                <Checkbox
                                                    name={`my-checkbox-${index}`}
                                                    checked={isAllPaid ||
                                                        (selectedProductOrdersDict[
                                                            item.userOrderId +
                                                            item.itemId +
                                                            item.cartItemDate.toString()
                                                        ] !== false &&
                                                            selectedProductOrdersDict[
                                                            item.userOrderId +
                                                            item.itemId +
                                                            item.cartItemDate.toString()
                                                            ] !== undefined)}
                                                    onChange={(e) => {
                                                        setSelectedProductOrdersDict({
                                                            ...selectedProductOrdersDict,
                                                            [item.userOrderId +
                                                                item.itemId +
                                                                item.cartItemDate.toString()]: !selectedProductOrdersDict[item.userOrderId +
                                                                item.itemId +
                                                                item.cartItemDate.toString()],
                                                        });
                                                    }}
                                                    style={{
                                                        // backgroundColor: Colors.primaryColor,
                                                        // color: Colors.primaryColor,
                                                        // borderWidth: 1,
                                                        // borderColor: Colors.primaryColor,
                                                    }}
                                                    disabled={isAllPaid || item.isCancelledItem}
                                                />
                                            </View>
                                            :
                                            <></>
                                    }

                                    <View style={{ width: "10%" }}>
                                        <Text style={[{ fontFamily: "NunitoSans-Bold", fontSize: 10 }, styleStripEffect]}>#{item.orderId}{` (${item.userName})`}</Text>
                                    </View>
                                    <View style={{ width: isMobile() ? '52%' : "54%" }}>
                                        <Text style={[{ fontFamily: "NunitoSans-Bold", fontSize: 10 }, styleStripEffect]}>{item.name}{item.priceType === PRODUCT_PRICE_TYPE.UNIT ? ` (${UNIT_TYPE_SHORT[item.unitType]})` : ''}{isPaidItem ? ' (Paid)' : ''}</Text>
                                    </View>
                                    <View style={{ width: "10%", flexDirection: 'row' }}>
                                        <Text style={[{ fontFamily: "NunitoSans-Bold", fontSize: 7, marginTop: 3 }, styleStripEffect]}>X</Text>
                                        <Text style={[{ fontFamily: "NunitoSans-Bold", fontSize: 10 }, styleStripEffect]}>{item.quantity}</Text>
                                    </View>
                                    <View style={{ width: "5%" }}>
                                        <Text style={[{ fontFamily: "NunitoSans-Bold", fontSize: 10 }, styleStripEffect]}></Text>
                                    </View>
                                    <View style={{ width: "15%" }}>
                                        <View style={{ flexDirection: 'row', justifyContent: 'flex-end', }}>
                                            <Text style={[{
                                                fontSize: 10, alignSelf: 'flex-end', fontFamily: "NunitoSans-Bold",
                                                textDecorationLine: item.isCancelledItem ? 'line-through' : 'none',
                                            }, styleStripEffect]}>RM </Text>
                                            <Text style={[{
                                                alignSelf: 'flex-end', fontFamily: "NunitoSans-Bold", fontSize: 10,
                                                textDecorationLine: item.isCancelledItem ? 'line-through' : 'none',
                                            }, styleStripEffect]}>{(item.price + (item.discountPromotions ? item.discountPromotions : 0)).toFixed(2)}</Text>

                                            {item.isCancelledItem ? <Text style={[{ fontSize: 10, alignSelf: 'flex-end', fontFamily: "NunitoSans-Bold", }, styleStripEffect]}>{`\n(Rejected)`}</Text> : <></>}
                                        </View>
                                    </View>
                                </View>

                                {item.remarks && item.remarks.length > 0 ?
                                    <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                                        <View style={{ width: "60%" }}>
                                            <Text style={[{ fontFamily: "NunitoSans-Bold", fontSize: 10, }, styleStripEffect]}><Text style={{ color: '#a8a8a8' }}>- </Text>{item.remarks}</Text>
                                        </View>
                                        <View style={{ width: "10%" }}>
                                            <Text style={[{ fontFamily: "NunitoSans-Bold", fontSize: 10, }, styleStripEffect]}></Text>
                                        </View>
                                        <View style={{ width: "5%" }}>
                                            <Text style={[{ fontFamily: "NunitoSans-Bold", fontSize: 10, }, styleStripEffect]}></Text>
                                        </View>
                                        <View style={{ width: "25%" }}>
                                            <Text style={[{ alignSelf: 'flex-end', fontFamily: "NunitoSans-Bold", fontSize: 10, }, styleStripEffect]}></Text>
                                        </View>
                                    </View>
                                    : <></>
                                }

                                {item.addOns.map((addOnChoice, i) => {
                                    const addOnChoices = addOnChoice.choiceNames.join(", ");
                                    return (
                                        <View style={{ flexDirection: 'row', justifyContent: 'space-between', paddingVertical: 5, }}>
                                            <View style={{ width: "60%", }}>
                                                <View style={{ flexDirection: 'row', }}>
                                                    <Text style={[{ fontFamily: "NunitoSans-Bold", fontSize: 10, color: Colors.descriptionColor, width: '30%' }, styleStripEffect]}>{`${addOnChoice.name}:`}</Text>
                                                    <Text style={[{ fontFamily: "NunitoSans-Bold", fontSize: 10, color: Colors.descriptionColor, width: '70%' }, styleStripEffect]}>{`${addOnChoices}`}</Text>
                                                </View>
                                            </View>
                                            <View style={{ width: "10%" }}>
                                                <Text style={[{ fontFamily: "NunitoSans-Bold", fontSize: 10, color: Colors.descriptionColor, }, styleStripEffect]}>{`${addOnChoice.quantities
                                                    ? `x${addOnChoice.quantities[0]}`
                                                    : ''
                                                    }`}</Text>
                                            </View>
                                            <View style={{ width: "5%" }}>
                                                <Text style={[{ fontFamily: "NunitoSans-Bold", fontSize: 10, color: Colors.descriptionColor, }, styleStripEffect]}></Text>
                                            </View>
                                            <View style={{ width: "25%" }}>
                                                <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                                                    <Text style={[{ alignSelf: 'flex-end', fontFamily: "NunitoSans-Bold", fontSize: 10, color: Colors.descriptionColor, }, styleStripEffect]}>RM </Text>
                                                    <Text style={[{ alignSelf: 'flex-end', fontFamily: "NunitoSans-Bold", fontSize: 10, color: Colors.descriptionColor, }, styleStripEffect]}>{addOnChoice.prices.reduce((accumAddOn, priceAddOn) => accumAddOn + priceAddOn, 0).toFixed(2)}</Text>
                                                </View>
                                            </View>
                                        </View>
                                    )
                                })}
                            </View>

                        </View>
                        :
                        <></>
                }
            </>
        );
    };

    const proceedToPaymentPage = async (orderDisplaySummary, orderDisplayIndividual, orderDisplayProduct) => {
        if (toRenderOrders && toRenderOrders.length > 0) {
            var selectedOrderToPayListTemp = [];
            var selectedOrdersCartItemsTemp = [];

            var orderToProceedList = [];

            let tempTotalPriceTaxDict = {};

            orderToProceedList = toRenderOrders;

            if (orderDisplaySummary) {
                // for joining all orders into one (summary)
                for (var i = 0; i < orderToProceedList.length; i++) {
                    if (orderToProceedList[i].paymentDetails === null) {
                        var isOrderFullyPaid = true;

                        for (var j = 0; j < orderToProceedList[i].cartItems.length; j++) {
                            const cartItem = orderToProceedList[i].cartItems[j];

                            if (cartItem.priceToPay === undefined) {
                                selectedOrdersCartItemsTemp.push({
                                    isFreeItem: cartItem.isFreeItem || false,
                                    promotionId: cartItem.promotionId || '',
                                    voucherId: cartItem.voucherId || '',

                                    promotionIdList: cartItem.promotionIdList ? cartItem.promotionIdList : [],

                                    priceOriginal: cartItem.priceOriginal || cartItem.price,

                                    addOns: cartItem.addOns,
                                    cartItemDate: moment(cartItem.cartItemDate).valueOf(),
                                    choices: cartItem.choices,
                                    cookedAt: cartItem.cookedAt,
                                    deliveredAt: cartItem.deliveredAt,
                                    fireOrder: cartItem.fireOrder,
                                    image: cartItem.image,
                                    isChecked: cartItem.isChecked,
                                    itemId: cartItem.itemId,
                                    itemName: cartItem.itemName,
                                    name: cartItem.name,
                                    orderType: cartItem.orderType,
                                    prepareTime: cartItem.prepareTime,
                                    price: cartItem.price,
                                    quantity: cartItem.quantity,
                                    remarks: cartItem.remarks,

                                    userOrderId: orderToProceedList[i].uniqueId,

                                    printerAreaList: cartItem.printerAreaList || [],
                                    printingTypeList: cartItem.printingTypeList || [],

                                    itemSku: cartItem.itemSku || null,
                                    categoryId: cartItem.categoryId || null,

                                    discountPromotions: cartItem.discountPromotions || 0,
                                    discountPromotionsLCC: cartItem.discountPromotionsLCC || 0,
                                    discount: cartItem.discount || 0,

                                    isDocket: cartItem.isDocket || false,
                                    printDocketQuantity: cartItem.printDocketQuantity || 1,

                                    originCartItemId: cartItem.originCartItemId || null,

                                    extraPrice: cartItem.extraPrice || 0,

                                    ...(cartItem.priceBundle !== undefined) && {
                                        priceBundle: cartItem.priceBundle,
                                    },

                                    ...(cartItem.priceVariable !== undefined) && {
                                        priceVariable: cartItem.priceVariable,
                                    },

                                    ...(cartItem.tId !== undefined && {
                                        tId: cartItem.tId,
                                        tRate: cartItem.tRate,
                                        tCode: cartItem.tCode,
                                        tName: cartItem.tName,
                                    }),

                                    priceType: cartItem.priceType ? cartItem.priceType : PRODUCT_PRICE_TYPE.FIXED,
                                    unitType: cartItem.unitType ? cartItem.unitType : UNIT_TYPE.GRAM,

                                    itemCostPrice: cartItem.itemCostPrice ? cartItem.itemCostPrice : 0,
                                });

                                isOrderFullyPaid = false;
                            }
                        }

                        if (!isOrderFullyPaid) {
                            selectedOrderToPayListTemp.push(orderToProceedList[i]);
                        }
                    }
                }
            }
            else if (orderDisplayIndividual) {
                var validIndividualOrderIdList = [];

                for (var i = 0; i < orderToProceedList.length; i++) {
                    if (
                        // orderToProceedList[i].userIdAnonymous === userIdAnonymous ||
                        // orderToProceedList[i].userIdAnonymous === '' ||
                        // orderToProceedList[i].userIdAnonymous === undefined
                        true
                    ) {
                        validIndividualOrderIdList.push(
                            orderToProceedList[i].uniqueId,
                        );
                    }
                }

                // if (validIndividualOrderIdList.length <= 0) {
                //     window.confirm('Please select an least one order to proceed');
                //     return;
                // }

                for (var i = 0; i < orderToProceedList.length; i++) {
                    if (
                        validIndividualOrderIdList.includes(
                            orderToProceedList[i].uniqueId,
                        ) &&
                        orderToProceedList[i].paymentDetails === null
                    ) {
                        var isOrderFullyPaid = true;

                        for (var j = 0; j < orderToProceedList[i].cartItems.length; j++) {
                            const cartItem = orderToProceedList[i].cartItems[j];

                            if (cartItem.priceToPay === undefined) {
                                selectedOrdersCartItemsTemp.push({
                                    isFreeItem: cartItem.isFreeItem || false,
                                    promotionId: cartItem.promotionId || '',
                                    voucherId: cartItem.voucherId || '',

                                    promotionIdList: cartItem.promotionIdList ? cartItem.promotionIdList : [],

                                    priceOriginal: cartItem.priceOriginal || cartItem.price,

                                    addOns: cartItem.addOns,
                                    cartItemDate: moment(cartItem.cartItemDate).valueOf(),
                                    choices: cartItem.choices,
                                    cookedAt: cartItem.cookedAt,
                                    deliveredAt: cartItem.deliveredAt,
                                    fireOrder: cartItem.fireOrder,
                                    image: cartItem.image,
                                    isChecked: cartItem.isChecked,
                                    itemId: cartItem.itemId,
                                    itemName: cartItem.itemName,
                                    name: cartItem.name,
                                    orderType: cartItem.orderType,
                                    prepareTime: cartItem.prepareTime,
                                    price: cartItem.price,
                                    quantity: cartItem.quantity,
                                    remarks: cartItem.remarks,

                                    userOrderId: orderToProceedList[i].uniqueId,

                                    printerAreaList: cartItem.printerAreaList || [],
                                    printingTypeList: cartItem.printingTypeList || [],

                                    itemSku: cartItem.itemSku || null,
                                    categoryId: cartItem.categoryId || null,

                                    discountPromotions: cartItem.discountPromotions || 0,
                                    discountPromotionsLCC: cartItem.discountPromotionsLCC || 0,
                                    discount: cartItem.discount || 0,

                                    isDocket: cartItem.isDocket || false,
                                    printDocketQuantity: cartItem.printDocketQuantity || 1,

                                    originCartItemId: cartItem.originCartItemId || null,

                                    extraPrice: cartItem.extraPrice || 0,

                                    ...(cartItem.priceBundle !== undefined) && {
                                        priceBundle: cartItem.priceBundle,
                                    },

                                    ...(cartItem.priceVariable !== undefined) && {
                                        priceVariable: cartItem.priceVariable,
                                    },

                                    ...(cartItem.tId !== undefined && {
                                        tId: cartItem.tId,
                                        tRate: cartItem.tRate,
                                        tCode: cartItem.tCode,
                                        tName: cartItem.tName,
                                    }),

                                    priceType: cartItem.priceType ? cartItem.priceType : PRODUCT_PRICE_TYPE.FIXED,
                                    unitType: cartItem.unitType ? cartItem.unitType : UNIT_TYPE.GRAM,

                                    itemCostPrice: cartItem.itemCostPrice ? cartItem.itemCostPrice : 0,
                                });

                                isOrderFullyPaid = false;
                            }
                        }

                        if (!isOrderFullyPaid) {
                            selectedOrderToPayListTemp.push(orderToProceedList[i]);
                        }
                    }
                }
            }
            else if (orderDisplayProduct) {
                const selectedProductOrderIdList = Object.entries(
                    selectedProductOrdersDict,
                ).map(([key, value]) => ({ key: key, value: value }));

                var validProductOrderIdList = [];

                for (var i = 0; i < selectedProductOrderIdList.length; i++) {
                    if (selectedProductOrderIdList[i].value) {
                        validProductOrderIdList.push(selectedProductOrderIdList[i].key);
                    }
                }

                validProductOrderIdList = [...new Set(validProductOrderIdList)];

                var validCartItemList = [];
                var validOrderIdList = [];
                for (var i = 0; i < validProductOrderIdList.length; i++) {
                    validOrderIdList.push(validProductOrderIdList[i].slice(0, 36));
                    // validCartItemList.push({
                    //   itemId: validProductOrderIdList[i].slice(36, 72),
                    //   cartItemDate: validProductOrderIdList[i].slice(72, 85),
                    // });
                    validCartItemList.push(validProductOrderIdList[i].slice(36, 85));
                }

                if (validProductOrderIdList.length <= 0) {
                    window.confirm('Please select at least one product to proceed');
                    return;
                }

                for (var i = 0; i < orderToProceedList.length; i++) {
                    if (
                        validOrderIdList.includes(orderToProceedList[i].uniqueId) &&
                        orderToProceedList[i].paymentDetails === null
                    ) {
                        var isOrderFullyPaid = true;

                        for (var j = 0; j < orderToProceedList[i].cartItems.length; j++) {
                            const cartItem = orderToProceedList[i].cartItems[j];

                            if (
                                cartItem.priceToPay === undefined &&
                                validCartItemList.includes(
                                    cartItem.itemId + cartItem.cartItemDate.toString(),
                                )
                            ) {
                                selectedOrdersCartItemsTemp.push({
                                    isFreeItem: cartItem.isFreeItem || false,
                                    promotionId: cartItem.promotionId || '',
                                    voucherId: cartItem.voucherId || '',

                                    promotionIdList: cartItem.promotionIdList ? cartItem.promotionIdList : [],

                                    priceOriginal: cartItem.priceOriginal || cartItem.price,

                                    addOns: cartItem.addOns,
                                    cartItemDate: moment(cartItem.cartItemDate).valueOf(),
                                    choices: cartItem.choices,
                                    cookedAt: cartItem.cookedAt,
                                    deliveredAt: cartItem.deliveredAt,
                                    fireOrder: cartItem.fireOrder,
                                    image: cartItem.image,
                                    isChecked: cartItem.isChecked,
                                    itemId: cartItem.itemId,
                                    itemName: cartItem.itemName,
                                    name: cartItem.name,
                                    orderType: cartItem.orderType,
                                    prepareTime: cartItem.prepareTime,
                                    price: cartItem.price,
                                    quantity: cartItem.quantity,
                                    remarks: cartItem.remarks,

                                    userOrderId: orderToProceedList[i].uniqueId,

                                    printerAreaList: cartItem.printerAreaList || [],
                                    printingTypeList: cartItem.printingTypeList || [],

                                    itemSku: cartItem.itemSku || null,
                                    categoryId: cartItem.categoryId || null,

                                    discountPromotions: cartItem.discountPromotions || 0,
                                    discountPromotionsLCC: cartItem.discountPromotionsLCC || 0,
                                    discount: cartItem.discount || 0,

                                    isDocket: cartItem.isDocket || false,
                                    printDocketQuantity: cartItem.printDocketQuantity || 1,

                                    originCartItemId: cartItem.originCartItemId || null,

                                    extraPrice: cartItem.extraPrice || 0,

                                    ...(cartItem.priceBundle !== undefined) && {
                                        priceBundle: cartItem.priceBundle,
                                    },

                                    ...(cartItem.priceVariable !== undefined) && {
                                        priceVariable: cartItem.priceVariable,
                                    },

                                    ...(cartItem.tId !== undefined && {
                                        tId: cartItem.tId,
                                        tRate: cartItem.tRate,
                                        tCode: cartItem.tCode,
                                        tName: cartItem.tName,
                                    }),

                                    priceType: cartItem.priceType ? cartItem.priceType : PRODUCT_PRICE_TYPE.FIXED,
                                    unitType: cartItem.unitType ? cartItem.unitType : UNIT_TYPE.GRAM,

                                    itemCostPrice: cartItem.itemCostPrice ? cartItem.itemCostPrice : 0,
                                });

                                isOrderFullyPaid = false;
                            }
                        }

                        if (!isOrderFullyPaid) {
                            selectedOrderToPayListTemp.push(orderToProceedList[i]);
                        }
                    }
                }
            }

            if (
                selectedOrderToPayListTemp.reduce(
                    (accu, order) => accu + order.cartItems.length,
                    0,
                ) <= 0
            ) {
                window.confirm(
                    'No orders to pay for now',
                );
                return;
            }

            if (selectedOrdersCartItemsTemp.length <= 0) {
                window.confirm(
                    'Orders has been fully paid',
                );
                return;
            }

            const totalDiscAamTemp = selectedOrderToPayListTemp.reduce(
                (accum, order) => accum.concat(order.discAam || 0),
                [],
            );

            let totalPriceTemp = selectedOrdersCartItemsTemp.reduce(
                (accum, cartItem) => {
                    if (cartItem.tId === 'default' || cartItem.tId === undefined) {
                        if (tempTotalPriceTaxDict['default'] !== undefined) {
                            tempTotalPriceTaxDict['default'] += cartItem.price;
                        }
                        else {
                            tempTotalPriceTaxDict['default'] = cartItem.price;
                        }
                    }
                    else {
                        if (tempTotalPriceTaxDict[cartItem.tId] !== undefined) {
                            tempTotalPriceTaxDict[cartItem.tId] += cartItem.price;
                        }
                        else {
                            tempTotalPriceTaxDict[cartItem.tId] = cartItem.price;
                        }
                    }

                    return BigNumber(accum).plus(cartItem.price).toNumber();
                },
                0,
            );

            if (totalDiscAamTemp > 0) {
                totalPriceTemp = BigNumber(totalPriceTemp).minus(totalDiscAamTemp).toNumber();
            }

            let totalPriceTaxList = [{
                key: '',
                totalPrice: totalPriceTemp,
                tax: 0,
                tRate: 0,
                tCode: '',
                tName: 'SST',
            }];

            var taxTemp = 0;
            // if (outletsTaxDict[currOutletId]) {
            //   taxTemp = totalPriceTemp * outletsTaxDict[currOutletId].rate;
            // }
            if (checkToApplyTaxOrNot(selectedOutlet, ORDER_TYPE.DINEIN)) {
                // taxTemp = BigNumber(totalPriceTemp).multipliedBy(selectedOutlet.taxRate).toNumber();

                let discAamLeft = totalDiscAamTemp;

                let totalTaxTemp = 0;
                const tempTotalPriceTaxList = Object.entries(tempTotalPriceTaxDict).map(
                    ([key, value]) => {
                        let taxTemp = 0;
                        let tRate = selectedOutlet.taxRate;
                        let tCode = 'SST';
                        let tName = 'SST';

                        if (value > discAamLeft) {
                            value = value - discAamLeft;
                            discAamLeft = 0;
                        }
                        else {
                            value = 0;
                            discAamLeft = discAamLeft - value;
                        }

                        if (key === 'default') {
                            taxTemp = BigNumber(taxTemp).plus(
                                BigNumber(value).multipliedBy(tRate)
                            ).toNumber();
                        }
                        else {
                            // let foundCustomTax = outletCustomTaxList.find(customTax => customTax.uniqueId === key);
                            let foundCustomTaxCartItem = [...selectedOrdersCartItemsTemp.map(cartItem => (cartItem))]
                                .find(customTaxCartItem => customTaxCartItem.tId === key);

                            if (foundCustomTaxCartItem) {
                                tRate = foundCustomTaxCartItem.tRate;
                                tCode = foundCustomTaxCartItem.tCode;
                                tName = foundCustomTaxCartItem.tName;

                                taxTemp = BigNumber(taxTemp).plus(
                                    BigNumber(value).multipliedBy(foundCustomTaxCartItem.tRate)
                                ).toNumber();
                            }
                            else {
                                taxTemp = BigNumber(taxTemp).plus(
                                    BigNumber(value).multipliedBy(tRate)
                                ).toNumber();
                            }
                        }

                        totalTaxTemp = BigNumber(totalTaxTemp).plus(taxTemp).toNumber();

                        return {
                            key: key,
                            totalPrice: value,
                            tax: taxTemp,
                            tRate: tRate,
                            tCode: tCode,
                            tName: tName,
                        };
                    },
                );

                taxTemp = totalTaxTemp;

                totalPriceTaxList = tempTotalPriceTaxList;

                if (totalTaxTemp <= 0) {
                    let taxTemp = 0;
                    let tRate = selectedOutlet.taxRate;
                    let tCode = selectedOutlet.taxCode ? selectedOutlet.taxCode : 'SST';
                    let tName = selectedOutlet.taxName ? selectedOutlet.taxName : 'SST';

                    taxTemp = BigNumber(taxTemp).plus(
                        BigNumber(totalPriceTemp).multipliedBy(selectedOutlet.taxRate)
                    ).toNumber();
                }
            }

            var scTemp = 0;
            // if (outletsTaxDict[currOutletId]) {
            //   taxTemp = totalPriceTemp * outletsTaxDict[currOutletId].rate;
            // }
            // if (selectedOutlet.scActive && selectedOutlet.scOrderTypes.includes(orderType)) {
            if (selectedOutlet.scActive && checkToApplyScOrNot(selectedOutlet, ORDER_TYPE.DINEIN)) {
                // scTemp = BigNumber(totalPriceTemp).multipliedBy(selectedOutlet.scRate).toNumber();

                scTemp = excludeSkipScItems(totalPriceTemp * selectedOutlet.scRate, selectedOrdersCartItemsTemp, selectedOutlet.scRate);

                // setTotalSc(excludeSkipScItems(tempTotalPrice * selectedOutlet.scRate, tempCartItemsProcessed, selectedOutlet.scRate));
            }

            const tablePaxTemp = selectedOrderToPayListTemp.reduce(
                (accum, order) => accum + order.tablePax,
                0,
            );
            const totalPrepareTimeTemp = selectedOrderToPayListTemp.reduce(
                (accum, order) => accum + order.totalPrepareTime,
                0,
            );
            const estimatedPreparedDateTemp = moment(
                selectedOrderToPayListTemp[0].orderDate,
            )
                .add(totalPrepareTimeTemp, 'second')
                .valueOf();
            const remarksTemp = selectedOrderToPayListTemp
                .filter(order => order.remarks)
                .map((order) => order.remarks)
                .join('\n');

            const orderIdTemp = selectedOrderToPayListTemp
                .sort((a, b) => a.orderId.localeCompare(b.orderId))
                .map((order) => order.orderId)
                .join(', ');

            const totalPromotionIdListTemp = selectedOrderToPayListTemp.reduce(
                (accum, order) => accum.concat(order.promotionIdList || []),
                [],
            );

            const totalPromoCodePromotionIdListTemp = selectedOrderToPayListTemp.reduce(
                (accum, order) => accum.concat(order.promoCodePromotionIdList || []),
                [],
            );

            const totalDiscAamListTemp = selectedOrderToPayListTemp.reduce(
                (accum, order) => accum.concat(order.discAamList || []),
                [],
            );

            // might got multiple order that claimed same voucher (multiple customer claim voucher is allowed in configuration), thus no need use Set to ensure unique
            const totalTaggableVoucherIdListTemp = selectedOrderToPayListTemp.reduce(
                (accum, order) => {
                    if (order.taggableVoucherId) {
                        return ([...accum, order.taggableVoucherId]);
                    }
                    else {
                        return accum;
                    }
                },
                [],
            );

            const totalUserTaggableVoucherIdListTemp = selectedOrderToPayListTemp.reduce(
                (accum, order) => {
                    if (order.userTaggableVoucherId) {
                        return ([...accum, order.userTaggableVoucherId]);
                    }
                    else {
                        return accum;
                    }
                },
                [],
            );

            // const merchantSnapshot = await firestore().collection(Collections.Merchant)
            //   .where('uniqueId', '==', outlet.merchantId)
            //   .limit(1)
            //   .get();

            // var merchant = null;
            // if (!merchantSnapshot.empty) {
            //   merchant = merchantSnapshot.docs[0].data();
            // }

            const finalPrice =
                BigNumber(
                    Math.round(
                        BigNumber((BigNumber(totalPriceTemp).plus(taxTemp).plus(scTemp).toNumber())).multipliedBy(20).toNumber()
                    )
                ).dividedBy(20).toNumber();
            const finalPriceBefore = BigNumber(totalPriceTemp).plus(taxTemp).plus(scTemp).toNumber();

            // const selectedOrderToPayUser =
            //     selectedOrderToPayUserIdDict[selectedOrderToPayUserId];

            const selectedOrderToPayUser = currCrmUser;

            var toPaidUser = null;
            // if (selectedOrderToPayUserId.includes('@')) {
            //     // means is email

            //     toPaidUser = crmUsers.find(user => user.email === selectedOrderToPayUserId);
            // }
            // else {
            //     // means is firebaseuid

            //     toPaidUser = crmUsers.find(user => user.userId === selectedOrderToPayUserId);
            // }

            // if (!toPaidUser) {
            //     // if still not found

            //     toPaidUser = crmUsers.find(user => user.number === selectedOrderToPayUser.userPhone);
            // }
            toPaidUser = currCrmUser;

            ////////////////////////////////////////////////////

            // Herks - 2022/05/30 - Fixes

            if (selectedOrderToPayUser) {
                if (selectedOrderToPayUser.userPhone === '' &&
                    selectedOrderToPayUser.userId === '' &&
                    selectedOrderToPayUserId === '') {
                    toPaidUser = null;
                }
            }
            else {
                toPaidUser = null;
            }

            // if (toPaidUser === null || selectedOrderToPayUser === null) {
            // }

            ////////////////////////////////////////////////////

            // 2023-04-04 - To check for enforcing following conditions:
            // if the order already been used the promo code/vouchers, need paid under the same person first

            // UserOrder.promoCodePromotionIdList - store the promotion id list used, that use promo code
            // UserOrder.userPhone - phone number

            // var promoIdOrderIdDict = {};

            // var combinedPromoCodeObjList =
            //     selectedOrderToPayListTemp.filter(order => {
            //         if (order.promoCodePromotionIdList && order.promoCodePromotionIdList.length > 0) {
            //             return true;
            //         }
            //     })
            //         .map(order => {
            //             return {
            //                 orderId: order.orderId,
            //                 orderType: order.orderType,
            //                 uniqueId: order.uniqueId,

            //                 promoCodePromotionIdList: order.promoCodePromotionIdList,

            //                 userPhone: order.userPhone,
            //                 userName: order.userName,
            //             };
            //         })
            //         .reduce((accum, obj) => {
            //             return [...accum, obj];
            //         }, []);

            // var combinedTaggableVoucherObjList =
            //     selectedOrderToPayListTemp.filter(order => {
            //         if (order.userTaggableVoucherId && order.userTaggableVoucherId.length > 0) {
            //             return true;
            //         }
            //     })
            //         .map(order => {
            //             return {
            //                 orderId: order.orderId,
            //                 orderType: order.orderType,
            //                 uniqueId: order.uniqueId,

            //                 taggableVoucherId: order.taggableVoucherId,
            //                 userTaggableVoucherId: order.userTaggableVoucherId,

            //                 userPhone: order.userPhone,
            //                 userName: order.userName,
            //             };
            //         })
            //         .reduce((accum, obj) => {
            //             return [...accum, obj];
            //         }, []);

            // var combinedUserPhoneList = [
            //     ...new Set(
            //         selectedOrderToPayListTemp.filter(order => {
            //             if (order.userPhone && order.userPhone.length > 0) {
            //                 return true;
            //             }
            //         })
            //             // .map(order => {
            //             //   return {
            //             //     orderId: order.orderId,
            //             //     uniqueId: order.uniqueId,

            //             //     userPhone: order.userPhone,
            //             //     userName: order.userName,


            //             //   };
            //             // })
            //             .reduce((accum, obj) => {
            //                 return [...accum, obj.userPhone];
            //             }, [])
            //     )
            // ];

            // var combinedNormalOrderObjList = [
            //     ...new Set(
            //         selectedOrderToPayListTemp.filter(order => {
            //             var isNormalOrder = true;

            //             if (order.promoCodePromotionIdList && order.promoCodePromotionIdList.length > 0) {
            //                 isNormalOrder = false;
            //             }

            //             if (order.userTaggableVoucherId && order.userTaggableVoucherId.length > 0) {
            //                 isNormalOrder = false;
            //             }
            //             return isNormalOrder;
            //         })
            //             .map(order => {
            //                 return {
            //                     orderId: order.orderId,
            //                     uniqueId: order.uniqueId,
            //                 };
            //             })
            //             .reduce((accum, obj) => {
            //                 return [...accum, obj];
            //             }, [])
            //     )
            // ];

            // var currActionUserPhone = '';
            // if (toPaidUser && toPaidUser.number) {
            //     currActionUserPhone = toPaidUser.number;
            // }
            // else if (selectedOrderToPayUser && selectedOrderToPayUser.userPhone) {
            //     currActionUserPhone = selectedOrderToPayUser.userPhone;
            // }

            // console.log('toPaidUser');
            // console.log(toPaidUser);
            // console.log('currActionUserPhone');
            // console.log(currActionUserPhone);

            // if (currActionUserPhone) {
            //     //////////////////////////////////////////////////////////////////////
            //     //////////////////////////////////////////////////////////////////////
            //     //////////////////////////////////////////////////////////////////////

            //     // got choose user

            //     if (combinedPromoCodeObjList.length >= 2 &&
            //         combinedUserPhoneList.length >= 1) {
            //         window.confirm(
            //             `Info, there are ${combinedPromoCodeObjList.length} orders that used the promo code, please pay each order under the Individual tab:\n\n${combinedPromoCodeObjList.map((order, index) => {
            //                 return `${index + 1}. #${order.orderType ===
            //                     ORDER_TYPE.DINEIN
            //                     ? ''
            //                     : 'T'}${order.orderId} (${order.userName ? order.userName : 'N/A'} | ${order.userPhone ? order.userPhone : 'N/A'})\n`
            //             }).join('')}`,
            //         );
            //         return;
            //     }

            //     if (combinedTaggableVoucherObjList.length >= 2 &&
            //         combinedUserPhoneList.length >= 1) {
            //             window.confirm(`Info, there are ${combinedTaggableVoucherObjList.length} orders that used the voucher, please pay each order under the Individual tab:\n\n${combinedTaggableVoucherObjList.map((order, index) => {
            //                 return `${index + 1}. #${order.orderType ===
            //                     ORDER_TYPE.DINEIN
            //                     ? ''
            //                     : 'T'}${order.orderId} (${order.userName ? order.userName : 'N/A'} | ${order.userPhone ? order.userPhone : 'N/A'})\n`
            //             }).join('')}`,
            //         );
            //         return;
            //     }

            //     if (
            //         combinedPromoCodeObjList.length >= 1
            //         &&
            //         combinedNormalOrderObjList.length >= 1
            //         &&
            //         combinedUserPhoneList.length >= 1
            //     ) {
            //         window.confirm(`Info, there are ${combinedPromoCodeObjList.length} order that used the promo code, please pay the order under the Individual tab:\n\n${combinedPromoCodeObjList.map((order, index) => {
            //                 return `${index + 1}. #${order.orderType ===
            //                     ORDER_TYPE.DINEIN
            //                     ? ''
            //                     : 'T'}${order.orderId} (${order.userName ? order.userName : 'N/A'} | ${order.userPhone ? order.userPhone : 'N/A'})\n`
            //             }).join('')}`,
            //         );
            //         return;
            //     }

            //     if (
            //         combinedTaggableVoucherObjList.length >= 1
            //         &&
            //         combinedNormalOrderObjList.length >= 1
            //         &&
            //         combinedUserPhoneList.length >= 1
            //     ) {
            //         window.confirm(`Info, there are ${combinedTaggableVoucherObjList.length} order that used the voucher, please pay the order under the Individual tab:\n\n${combinedTaggableVoucherObjList.map((order, index) => {
            //                 return `${index + 1}. #${order.orderType ===
            //                     ORDER_TYPE.DINEIN
            //                     ? ''
            //                     : 'T'}${order.orderId} (${order.userName ? order.userName : 'N/A'} | ${order.userPhone ? order.userPhone : 'N/A'})\n`
            //             }).join('')}`,
            //         );
            //         return;
            //     }

            //     if (combinedPromoCodeObjList.length === 1 &&
            //         combinedUserPhoneList.length >= 1 &&
            //         !combinedUserPhoneList.includes(currActionUserPhone) &&
            //         combinedUserPhoneList[0].length > 0
            //     ) {
            //         window.confirm(`Info, unmatched to paid user that used the promo code, please select the same user and pay the order under the Individual tab:\n\n${combinedPromoCodeObjList.map((order, index) => {
            //                 return `${index + 1}. #${order.orderType ===
            //                     ORDER_TYPE.DINEIN
            //                     ? ''
            //                     : 'T'}${order.orderId} (${order.userName ? order.userName : 'N/A'} | ${order.userPhone ? order.userPhone : 'N/A'})\n`
            //             }).join('')}`,
            //         );
            //         return;
            //     }

            //     if (combinedTaggableVoucherObjList.length === 1 &&
            //         combinedUserPhoneList.length >= 1 &&
            //         !combinedUserPhoneList.includes(currActionUserPhone) &&
            //         combinedUserPhoneList[0].length > 0
            //     ) {
            //         window.confirm(`Info, unmatched to paid user that used the promo code, please select the same user and pay the order under the Individual tab:\n\n${combinedTaggableVoucherObjList.map((order, index) => {
            //                 return `${index + 1}. #${order.orderType ===
            //                     ORDER_TYPE.DINEIN
            //                     ? ''
            //                     : 'T'}${order.orderId} (${order.userName ? order.userName : 'N/A'} | ${order.userPhone ? order.userPhone : 'N/A'})\n`
            //             }).join('')}`,
            //         );
            //         return;
            //     }

            //     if (combinedPromoCodeObjList.length >= 1 &&
            //         combinedTaggableVoucherObjList.length >= 1
            //         // &&
            //         // combinedUserPhoneList.length >= 1 &&
            //         // !combinedUserPhoneList.includes(currActionUserPhone)
            //     ) {
            //         window.confirm(`Info, there are ${combinedPromoCodeObjList.length + combinedTaggableVoucherObjList.length} order that used the promo code/voucher, please pay the order under the Individual tab:\n\n${combinedPromoCodeObjList.map((order, index) => {
            //                 return `${index + 1}. #${order.orderType ===
            //                     ORDER_TYPE.DINEIN
            //                     ? ''
            //                     : 'T'}${order.orderId} (${order.userName} | ${order.userPhone}) (Promo)\n`
            //             })}${combinedTaggableVoucherObjList.map((order, index) => {
            //                 return `${index + 1}. #${order.orderType ===
            //                     ORDER_TYPE.DINEIN
            //                     ? ''
            //                     : 'T'}${order.orderId} (${order.userName} | ${order.userPhone}) (Voucher)\n`
            //             }).join('')}`,
            //         );
            //         return;
            //     }

            //     //////////////////////////////////////////////////////////////////////
            //     //////////////////////////////////////////////////////////////////////
            //     //////////////////////////////////////////////////////////////////////
            // }
            // else {
            //     //////////////////////////////////////////////////////////////////////
            //     //////////////////////////////////////////////////////////////////////
            //     //////////////////////////////////////////////////////////////////////

            //     // didn't choose user

            //     if (combinedPromoCodeObjList.length >= 2) {
            //         window.confirm(`Info, there are ${combinedPromoCodeObjList.length} orders that used the promo code, please pay each order under the Individual tab:\n\n${combinedPromoCodeObjList.map((order, index) => {
            //                 return `${index + 1}. #${order.orderType ===
            //                     ORDER_TYPE.DINEIN
            //                     ? ''
            //                     : 'T'}${order.orderId} (${order.userName ? order.userName : 'N/A'} | ${order.userPhone ? order.userPhone : 'N/A'})\n`
            //             }).join('')}`,
            //         );
            //         return;
            //     }

            //     if (combinedTaggableVoucherObjList.length >= 2) {
            //         window.confirm(`Info, there are ${combinedTaggableVoucherObjList.length} orders that used the voucher, please pay each order under the Individual tab:\n\n${combinedTaggableVoucherObjList.map((order, index) => {
            //                 return `${index + 1}. #${order.orderType ===
            //                     ORDER_TYPE.DINEIN
            //                     ? ''
            //                     : 'T'}${order.orderId} (${order.userName ? order.userName : 'N/A'} | ${order.userPhone ? order.userPhone : 'N/A'})\n`
            //             }).join('')}`,
            //         );
            //         return;
            //     }

            //     if (
            //         combinedPromoCodeObjList.length >= 1
            //         &&
            //         combinedNormalOrderObjList.length >= 1
            //     ) {
            //         window.confirm(`Info, there are ${combinedPromoCodeObjList.length} order that used the promo code, please pay the order under the Individual tab:\n\n${combinedPromoCodeObjList.map((order, index) => {
            //                 return `${index + 1}. #${order.orderType ===
            //                     ORDER_TYPE.DINEIN
            //                     ? ''
            //                     : 'T'}${order.orderId} (${order.userName ? order.userName : 'N/A'} | ${order.userPhone ? order.userPhone : 'N/A'})\n`
            //             }).join('')}`,
            //         );
            //         return;
            //     }

            //     if (
            //         combinedTaggableVoucherObjList.length >= 1
            //         &&
            //         combinedNormalOrderObjList.length >= 1
            //     ) {
            //         window.confirm(`Info, there are ${combinedTaggableVoucherObjList.length} order that used the voucher, please pay the order under the Individual tab:\n\n${combinedTaggableVoucherObjList.map((order, index) => {
            //                 return `${index + 1}. #${order.orderType ===
            //                     ORDER_TYPE.DINEIN
            //                     ? ''
            //                     : 'T'}${order.orderId} (${order.userName ? order.userName : 'N/A'} | ${order.userPhone ? order.userPhone : 'N/A'})\n`
            //             }).join('')}`,
            //         );
            //         return;
            //     }

            //     if (combinedPromoCodeObjList.length === 1 &&
            //         combinedUserPhoneList.length >= 1 &&
            //         combinedUserPhoneList[0].length > 0
            //         // &&
            //         // combinedUserPhoneList.length >= 1 &&
            //         // !combinedUserPhoneList.includes(currActionUserPhone)
            //     ) {
            //         window.confirm(`Info, unmatched to paid user that used the promo code, please select the same user and pay the order under the Individual tab:\n\n${combinedPromoCodeObjList.map((order, index) => {
            //                 return `${index + 1}. #${order.orderType ===
            //                     ORDER_TYPE.DINEIN
            //                     ? ''
            //                     : 'T'}${order.orderId} (${order.userName ? order.userName : 'N/A'} | ${order.userPhone ? order.userPhone : 'N/A'})\n`
            //             }).join('')}`,
            //         );
            //         return;
            //     }

            //     if (combinedTaggableVoucherObjList.length === 1 &&
            //         combinedUserPhoneList.length >= 1 &&
            //         combinedUserPhoneList[0].length > 0
            //         // &&
            //         // combinedUserPhoneList.length >= 1 &&
            //         // !combinedUserPhoneList.includes(currActionUserPhone)
            //     ) {
            //         window.confirm(`Info, unmatched to paid user that used the promo code, please select the same user and pay the order under the Individual tab:\n\n${combinedTaggableVoucherObjList.map((order, index) => {
            //                 return `${index + 1}. #${order.orderType ===
            //                     ORDER_TYPE.DINEIN
            //                     ? ''
            //                     : 'T'}${order.orderId} (${order.userName ? order.userName : 'N/A'} | ${order.userPhone ? order.userPhone : 'N/A'})\n`
            //             }).join('')}`,
            //         );
            //         return;
            //     }

            //     if (combinedPromoCodeObjList.length >= 1 &&
            //         combinedTaggableVoucherObjList.length >= 1
            //         // &&
            //         // combinedUserPhoneList.length >= 1 &&
            //         // !combinedUserPhoneList.includes(currActionUserPhone)
            //     ) {
            //         window.confirm(`Info, there are ${combinedPromoCodeObjList.length + combinedTaggableVoucherObjList.length} order that used the promo code/voucher, please pay the order under the Individual tab:\n\n${combinedPromoCodeObjList.map((order, index) => {
            //                 return `${index + 1}. #${order.orderType ===
            //                     ORDER_TYPE.DINEIN
            //                     ? ''
            //                     : 'T'}${order.orderId} (${order.userName} | ${order.userPhone}) (Promo)\n`
            //             })}${combinedTaggableVoucherObjList.map((order, index) => {
            //                 return `${index + 1}. #${order.orderType ===
            //                     ORDER_TYPE.DINEIN
            //                     ? ''
            //                     : 'T'}${order.orderId} (${order.userName} | ${order.userPhone}) (Voucher)\n`
            //             }).join('')}`,
            //         );
            //         return;
            //     }

            //     //////////////////////////////////////////////////////////////////////
            //     //////////////////////////////////////////////////////////////////////
            //     //////////////////////////////////////////////////////////////////////
            // }

            ////////////////////////////////////////////////////

            var currPendingOrderTemp = {
                uniqueId: uuidv4(),
                // userId: toPaidUser ? (toPaidUser.firebaseUid || toPaidUser.email) : selectedOrderToPayUser.userId,
                // userEmail: toPaidUser ? (toPaidUser.email) : selectedOrderToPayUser.userId,
                // crmUserId: toPaidUser ? (toPaidUser.uniqueId) : '',
                // userPhone: toPaidUser ? (toPaidUser.number) : selectedOrderToPayUser.userPhone,
                userId: toPaidUser ? (toPaidUser.firebaseUid || toPaidUser.email) : ((selectedOrderToPayUser && selectedOrderToPayUser.userId) ? selectedOrderToPayUser.userId : ''),
                userEmail: toPaidUser ? (toPaidUser.email) : '',
                crmUserId: toPaidUser ? (toPaidUser.uniqueId) : '',
                userPhone: toPaidUser ? (toPaidUser.number) : (selectedOrderToPayUser && selectedOrderToPayUser.userPhone ? selectedOrderToPayUser.userPhone : ''),

                userIdAnonymous: selectedOrderToPayListTemp[0].userIdAnonymous ? selectedOrderToPayListTemp[0].userIdAnonymous : '',

                // promotionIdList: totalPromotionIdListTemp,
                promoCodePromotionIdList: totalPromoCodePromotionIdListTemp,
                cartPromotionIdList: selectedOrderToPayListTemp.map(order => order.promotionIdList || []).reduce((accum, idList) => accum.concat([...idList]), []),

                discAam: totalDiscAamTemp,
                discAamList: totalDiscAamListTemp,

                taggableVoucherIdList: totalTaggableVoucherIdListTemp,
                userTaggableVoucherIdList: totalUserTaggableVoucherIdListTemp,

                cartItems: [...selectedOrdersCartItemsTemp.map(cartItem => ({
                    ...cartItem,
                    // priceOriginal: cartItem.price,

                    priceOriginal: cartItem.priceOriginal || cartItem.price,
                }))],
                orderType: ORDER_TYPE.DINEIN,
                paymentMethod: 'Offline',
                userVoucherId: null,
                userVoucherCode: null,
                userAddressId: null,
                orderDate: selectedOrderToPayListTemp[0].orderDate,
                totalPrice: +totalPriceTemp.toFixed(2),

                discountVoucher: 0,
                discount: selectedOrderToPayListTemp.reduce((accum, curr) => {
                    return BigNumber(accum).plus(curr.discount || 0).toNumber();
                }, 0), // for all discounts
                discountPromotionsTotal: selectedOrderToPayListTemp.reduce((accum, curr) => {
                    return BigNumber(accum).plus(curr.discountPromotionsTotal || 0).toNumber();
                }, 0),

                discAam: selectedOrderToPayListTemp.reduce((accum, curr) => {
                    return BigNumber(accum).plus(curr.discAam || 0).toNumber();
                }, 0),
                discAamFinal: selectedOrderToPayListTemp.reduce((accum, curr) => {
                    return BigNumber(accum).plus(curr.discAam || 0).toNumber();
                }, 0),

                tax: +taxTemp.toFixed(2),
                sc: +scTemp.toFixed(2),
                deliveryFee: 0,

                totalPriceTaxList: totalPriceTaxList,

                // tableId: !isCheckingOutTakeaway
                //     ? selectedOutletTable.uniqueId
                //         ? selectedOutletTable.uniqueId
                //         : ''
                //     : '',
                // tablePax: !isCheckingOutTakeaway
                //     ? tablePaxTemp
                //         ? tablePaxTemp
                //         : 0
                //     : 0,
                // tableCode: !isCheckingOutTakeaway
                //     ? selectedOutletTable.code
                //         ? selectedOutletTable.code
                //         : ''
                //     : 0,
                tableId: selectedOrderToPayListTemp[0].tableId,
                tablePax: selectedOrderToPayListTemp[0].tablePax,
                tableCode: selectedOrderToPayListTemp[0].tableCode,

                finalPrice: +finalPrice.toFixed(2),
                finalPriceBefore: +finalPriceBefore.toFixed(2),
                outletId: selectedOutlet.uniqueId,

                printDt: Date.now(), // to print receipt for pay later online

                merchantId: selectedOrderToPayListTemp[0].merchantId ? selectedOrderToPayListTemp[0].merchantId : '',
                outletCover: selectedOutlet ? selectedOutlet.cover : '',
                merchantLogo: selectedOrderToPayListTemp[0].merchantLogo ? selectedOrderToPayListTemp[0].merchantLogo : '',
                outletName: selectedOutlet ? selectedOutlet.name : '',
                merchantName: selectedOrderToPayListTemp[0].merchantName ? selectedOrderToPayListTemp[0].merchantName : '',

                outletAddress: selectedOutlet ? selectedOutlet.address : '',
                outletPhone: selectedOutlet ? selectedOutlet.phone : '',
                outletTaxId: '',
                outletTaxNumber: '',
                outletTaxName: '',
                // outletTaxRate: selectedOutlet
                //   ? outletsTaxDict[selectedOutlet.uniqueId].rate
                //   : 0.06,
                outletTaxRate: checkToApplyTaxOrNot(selectedOutlet, ORDER_TYPE.DINEIN) ? selectedOutlet.taxRate : 0,

                outletScRate:
                    checkToApplyScOrNot(selectedOutlet, ORDER_TYPE.DINEIN)
                        ? selectedOutlet.scRate
                        : 0,

                // orderStatus: userOrdersTableDict[selectedOutletTable.uniqueId], // this will store entire order as orderStatus
                // orderStatus: USER_ORDER_STATUS.ORDER_DELIVERED,
                orderStatus: USER_ORDER_STATUS.ORDER_PREPARED,

                courierCode: '',
                courierId: '',
                courierStatus: '',

                courierLink: '',
                driverId: '',
                driverDetails: null,

                scheduleAt: null,

                //////////////////////////////////////

                // append delivery info into user order itself, enable merchant later call courier again

                // cr stands for 'courier raw'

                crDeliveryCurrency: '',

                crOutletLat: null,
                crOutletLng: null,
                crOutletAddress: '',

                crOutletPhone: '',

                crUserLat: null,
                crUserLng: null,
                crUserAddress: '',

                crUserName: '',
                crUserPhone: '',
                crUserRemarks: '',

                crScheduleAt: '',

                crTotalWeightKg: null,
                crOutletRequiredStartDatetime: '',
                crOutletRequiredFinishDatetime: '',
                crUserRequiredStartDatetime: '',
                crUserRequiredFinishDatetime: '',

                //////////////////////////////////////

                isPrioritizedOrder: false,
                // waiterName: userName ? userName : '',
                waiterName: '',
                // waiterId: userId ? userId : '',
                waiterId: '',

                totalPrepareTime: totalPrepareTimeTemp,
                estimatedPreparedDate: estimatedPreparedDateTemp,

                remarks: remarksTemp,

                paymentDetails: null,

                ///////////////////////////////

                collectionDate: selectedOrderToPayListTemp[0].collectionDate,
                completedDate: null,

                paymentDate: null,

                priority: USER_ORDER_PRIORITY.NORMAL, // should use one of the joined order?

                userAddress: selectedOrderToPayUser && selectedOrderToPayUser.userAddress
                    ? selectedOrderToPayUser.userAddress
                    : '',
                // userName: selectedOrderToPayUser.userName
                //   ? selectedOrderToPayUser.userName
                //   : '',
                userName: toPaidUser ? (toPaidUser.name) : (selectedOrderToPayUser && selectedOrderToPayUser.userName ? selectedOrderToPayUser.userName : ''),
                // userPhone: selectedOrderToPayUser.userPhone
                //   ? selectedOrderToPayUser.userPhone
                //   : '',

                // orderId: nanoid(),
                receiptId: nanoid(),

                orderId: orderIdTemp,
                ...(selectedOrderToPayListTemp.length > 1) && {
                    orderIdJoined: orderIdTemp,
                },
                // receiptId: (outletOrderNumber.number + '').padStart(4, '0'),

                ///////////////////////////////

                preorderPackageId: null,
                preorderCollectionDate: null,
                preorderCollectionTime: null,

                ///////////////////////////////

                pointsToRedeem: 0,
                pointsToRedeemPackageIdList: [],
                pointsToRedeemAmountList: [],
                pointsToRedeemDiscount: 0,
                usePointsToRedeem: false,

                // splitAmountList: [], // for split bill

                tableQRUrl: selectedOrderToPayListTemp[0].tableQRUrl
                    ? selectedOrderToPayListTemp[0].tableQRUrl
                    : '',
                orderRegisterQRUrl: selectedOrderToPayListTemp[0].orderRegisterQRUrl
                    ? selectedOrderToPayListTemp[0].orderRegisterQRUrl
                    : '',

                ////////////////////

                deliveryPackagingFee: selectedOrderToPayListTemp.reduce((accum, order) => accum + order.deliveryPackagingFee || 0, 0),
                pickupPackagingFee: selectedOrderToPayListTemp.reduce((accum, order) => accum + order.pickupPackagingFee || 0, 0),

                // isReservationOrder: selectedOrderToPayListTemp[0].isReservationOrder ? selectedOrderToPayListTemp[0].isReservationOrder : false,

                // printedTokenList: [],
                printedTokenList: selectedOrderToPayListTemp.reduce((accum, order) => accum.concat(order.printedTokenList ? order.printedTokenList : []), []),

                isOnlineOrdering: selectedOrderToPayListTemp[0].isOnlineOrdering ? selectedOrderToPayListTemp[0].isOnlineOrdering : false,

                // appType: selectedOrderToPayListTemp[0].appType ? selectedOrderToPayListTemp[0].appType : APP_TYPE.WEB_ORDER,
                appType: APP_TYPE.WEB_ORDER,

                settlementDate: null,

                scName: selectedOutlet.scName ? selectedOutlet.scName : '',

                ////////////////////

                createdAt: moment().valueOf(),
                updatedAt: moment().valueOf(),
                deletedAt: null,
            };

            if (isNaN(currPendingOrderTemp.finalPrice)) {
                window.confirm('Invalid order(s) to proceed.');
            }
            else {
                // setCurrPendingOrderDiscount(0);
                // setCurrPendingOrder(currPendingOrderTemp);
                // setSelectedOrderToPayList(selectedOrderToPayListTemp);
                // setSelectedOrdersCartItems(selectedOrdersCartItemsTemp);
                // setRenderPaymentSummary(true);

                TableStore.update(s => {
                    s.currPendingOrderDiscount = 0;
                    s.currPendingOrder = currPendingOrderTemp;
                    s.selectedOrderToPayList = selectedOrderToPayListTemp;
                    s.selectedOrdersCartItems = selectedOrdersCartItemsTemp;
                    s.renderPaymentSummary = true;

                    // 2022-11-17 - changes
                    s.viewTableOrderModal = false;
                });

                var payHybridBodyTemp = {
                    orders: JSON.parse(JSON.prune(selectedOrderToPayListTemp)),
                    ordersCartItems: JSON.parse(JSON.prune(selectedOrdersCartItemsTemp)),
                    destOrder: {
                        ...currPendingOrderTemp,
                        discountPromotionsTotalLCC: 0,
                        // promotionIdList: promotionIdAppliedList,
                        promotionIdList: [],
                        promoCodePromotionIdList: [
                            ...new Set(
                                (currPendingOrderTemp.promoCodePromotionIdList ? currPendingOrderTemp.promoCodePromotionIdList : [])
                                // .filter(promotionId => {
                                //     if (availablePromoCodePromotions.find(promoCodePromotion => promoCodePromotion.uniqueId === promotionId)) {
                                //         return true;
                                //     }
                                //     else {
                                //         return false;
                                //     }
                                // })
                            )
                        ],

                        ...(selectedOutlet.multiTax) && { multiTax: selectedOutlet.multiTax },

                        taggableVoucherIdList: totalTaggableVoucherIdListTemp,
                        userTaggableVoucherIdList: totalUserTaggableVoucherIdListTemp,

                        loyaltyCampaignId: null,

                        taggableVoucherId: null,
                        userTaggableVoucherId: null,

                        cartItems: currPendingOrderTemp.cartItems,

                        discount: currPendingOrderTemp.discount,

                        totalPrice: currPendingOrderTemp.totalPrice,
                        tax: currPendingOrderTemp.tax,
                        sc: currPendingOrderTemp.sc,

                        totalPriceTaxList: currPendingOrderTemp.totalPriceTaxList ? currPendingOrderTemp.totalPriceTaxList : [{
                            key: '',
                            totalPrice: totalPriceTemp,
                            tax: 0,
                            tRate: 0,
                            tCode: '',
                            tName: 'SST',
                        }],
                    },
                    billType: OFFLINE_BILL_TYPE.SUMMARY,
                    paymentMethod: 'Offline-Cash',
                    paymentMethodRemarks: '',
                    amountReceived: currPendingOrderTemp.finalPrice,
                    amountBalance: 0,

                    outletData: selectedOutlet,
                };

                CommonStore.update(s => {
                    s.payHybridBody = payHybridBodyTemp;

                    s.orderType = ORDER_TYPE.DINEIN;
                });

                // global.currPageStack.push('OrderHistoryDetail');

                const subdomain = await AsyncStorage.getItem("latestSubdomain");

                if (subdomain === 'mykoodoo') {
                    linkTo(`${prefix}/outlet/pay-cart`);
                }
                else {
                    linkTo(`${prefix}/outlet/${subdomain}/pay-cart`);
                }
            }
        }
    };

    const forcePayAtCashier = CommonStore.useState(s => s.forcePayAtCashier);
    const unpaidOrder = useMemo(() => {
        return toRenderOrders.filter(order =>
            !(order.paymentDetails && order.paymentDetails.channel)
        );
    }, [toRenderOrders]);

    return (
        <View style={{ height: windowHeight * 0.925 }}>
            {
                (orderToShow && orderToShow.uniqueId)
                    ?
                    <>
                        <ScrollView style={styles.container} ref={scrollViewRef} showsVerticalScrollIndicator={false} contentContainerStyle={{
                            paddingBottom: 40,
                        }}>
                            {(forcePayAtCashier && unpaidOrder.length > 0) && (
                                <View style={{
                                    position: 'sticky',
                                    top: 0,
                                    backgroundColor: Colors.safetyOrange,
                                    paddingVertical: 5,
                                    zIndex: 1000,
                                }}>
                                    <Text style={{
                                        textAlign: 'center',
                                        color: Colors.whiteColor,
                                        fontFamily: "NunitoSans-SemiBold",
                                        fontSize: 16,
                                    }}>Please pay at cashier to confirm and complete order</Text>
                                </View>
                            )}

                            <View style={{ padding: 10 }}>
                                <View style={{ alignItems: 'center', flexDirection: 'row', justifyContent: 'center' }}>
                                    <View style={{ alignSelf: 'center' }}>
                                        <AsyncImage

                                            source={{ uri: orderToShow ? orderToShow.merchantLogo : '' }}
                                            // item={selectedUserOrder}
                                            style={{ width: 90, height: 90, borderRadius: 10 }}
                                        />
                                    </View>
                                </View>
                                <View style={styles.titleDetail}>
                                    <View style={{ flexDirection: 'row' }}>
                                        <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 18 }}>{orderToShow ? orderToShow.outletName : ''}</Text>
                                        {/* <TouchableOpacity
                    style={{
                        marginLeft: 10
                    }}
                    onPress={() => {

                        CommonStore.update(s => {
                            s.selectedUserOrder = selectedUserOrder;
                          });

                        navigation.navigate('ReceiptDetail');
                        proceedToReceiptDetails(selectedUserOrder);
                     }}>
                    <Ionicons name={'receipt-outline'} size={30} color={Colors.primaryColor}/>
                    </TouchableOpacity> */}
                                    </View>
                                    <Text
                                        style={{
                                            marginTop: 10,
                                            textAlign: 'center',
                                            fontFamily: "NunitoSans-Bold",
                                        }}>
                                        {orderOutlet.address}
                                    </Text>
                                    <Text style={{
                                        marginTop: 0, fontFamily: "NunitoSans-Bold",
                                    }}>
                                        Phone: {orderOutlet.phone}
                                    </Text>
                                </View>

                                <View style={[styles.orderTitle]}>
                                    <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                                        <View style={{ flexDirection: 'row', width: '50%' }}>
                                            <Text style={[orderCodeFontSize, {
                                                fontFamily: "NunitoSans-SemiBold",
                                                marginBottom: 5,
                                            }]}>
                                                Order # :{' '}
                                            </Text>
                                            <Text style={[orderCodeFontSize, {
                                                fontFamily: "NunitoSans-Bold",
                                                marginBottom: 5,
                                                width: '50%',
                                            }]} numberOfLines={2}>
                                                {orderToShow ? orderToShow.orderId : ''}
                                                {selectedUserOrderOthers.length > 0 ? `, ${(selectedUserOrderOthers.map(order => order.orderId).join(', '))}` : ''}
                                            </Text>
                                        </View>
                                        <View style={{ flexDirection: 'row' }}>
                                            <Text style={[orderCodeFontSize, {
                                                fontFamily: "NunitoSans-SemiBold",
                                                marginBottom: 5,
                                            }]}>
                                                Order Type :{' '}
                                            </Text>
                                            <Text style={[orderCodeFontSize, {
                                                fontFamily: "NunitoSans-Bold",
                                                marginBottom: 5,
                                                /* width: '70%' */
                                            }]} numberOfLines={2}>
                                                {ORDER_TYPE_PARSED[orderToShow ? orderToShow.orderType : '']}
                                            </Text>
                                        </View>
                                    </View>
                                    <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                                        <View style={{ flexDirection: 'row' }}>
                                            <Text style={[orderCodeFontSize, {
                                                fontFamily: "NunitoSans-SemiBold",
                                                marginBottom: 5,
                                            }]}>
                                                Date :{' '}
                                            </Text>
                                            <Text style={[orderCodeFontSize, {
                                                fontFamily: "NunitoSans-SemiBold",
                                                marginBottom: 5,
                                            }]}>
                                                {moment(orderToShow ? orderToShow.orderDate : '').format('DD-MMM-YYYY')}
                                            </Text>
                                        </View>
                                        <View style={{ flexDirection: 'row' }}>
                                            <Text style={[orderCodeFontSize, {
                                                fontFamily: "NunitoSans-SemiBold",
                                                marginBottom: 5,
                                            }]}>
                                                Time :{' '}
                                            </Text>
                                            <Text style={[orderCodeFontSize, {
                                                fontFamily: "NunitoSans-SemiBold",
                                                marginBottom: 5,
                                            }]}>
                                                {moment(orderToShow ? orderToShow.orderDate : '').format('hh:mm A')}
                                            </Text>
                                        </View>
                                    </View>

                                    <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                                        <View style={{ flexDirection: 'row' }}>
                                            <Text style={[orderCodeFontSize, {
                                                fontFamily: "NunitoSans-SemiBold",
                                                marginBottom: 5,
                                            }]}>
                                                Name :{' '}
                                            </Text>
                                            <Text style={[orderCodeFontSize, {
                                                fontFamily: "NunitoSans-SemiBold",
                                                marginBottom: 5,
                                            }]}>
                                                {userInfoName ? userInfoName : 'N/A'}
                                            </Text>
                                        </View>
                                        <View style={{ flexDirection: 'row' }}>
                                            <Text style={[orderCodeFontSize, {
                                                fontFamily: "NunitoSans-SemiBold",
                                                marginBottom: 5,
                                            }]}>
                                                Phone :{' '}
                                            </Text>
                                            <Text style={[orderCodeFontSize, {
                                                fontFamily: "NunitoSans-SemiBold",
                                                marginBottom: 5,
                                            }]}>
                                                {userInfoPhone ? userInfoPhone : 'N/A'}
                                            </Text>
                                        </View>
                                    </View>

                                    <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                                        <View style={{ flexDirection: 'row' }}>
                                            <Text style={[orderCodeFontSize, {
                                                fontFamily: "NunitoSans-SemiBold",
                                                marginBottom: 10,
                                            }]}>
                                                Table :{' '}
                                            </Text>
                                            <Text style={[orderCodeFontSize, {
                                                fontFamily: "NunitoSans-SemiBold",
                                                marginBottom: 10,
                                            }]}>
                                                {selectedOutletTableCode ? selectedOutletTableCode : 'N/A'}
                                            </Text>
                                        </View>
                                        <View style={{ flexDirection: 'row' }}>
                                            {/* <Text style={[orderCodeFontSize, {
                                            fontFamily: "NunitoSans-SemiBold",
                                            marginBottom: 10,
                                        }]}>
                                            Phone :{' '}
                                        </Text>
                                        <Text style={[orderCodeFontSize, {
                                            fontFamily: "NunitoSans-SemiBold",
                                            marginBottom: 10,
                                        }]}>
                                            {userInfoPhone ? userInfoPhone : 'N/A'}
                                        </Text> */}
                                        </View>
                                    </View>
                                </View>

                                <FlatList
                                    removeClippedSubviews={true}
                                    style={{
                                        // width: '100%',
                                        // minHeight: windowHeight * 0.05,
                                    }}
                                    contentContainerStyle={{
                                        // paddingHorizontal: 15,
                                    }}
                                    data={
                                        toRenderOrders.reduce(
                                            (accu, order) =>
                                                accu.concat(
                                                    order.cartItems
                                                        // .filter(
                                                        //     (cartItem) =>
                                                        //         cartItem.deliveredAt === null,
                                                        // )
                                                        .map(cartItem => ({
                                                            ...cartItem,
                                                            userOrderId: order.uniqueId,

                                                            userName: order.userName ? order.userName : 'N/A',
                                                            orderId: order.orderId,

                                                            checkBox: true,
                                                        })),
                                                ),
                                            [],
                                        )
                                    }
                                    renderItem={renderTableOrder}
                                    keyExtractor={(item, index) => String(index)}
                                />

                                {/* 2024-08-16 - For pending approval orders */}

                                {
                                    selectedTablePendingApprovalOrders.length > 0
                                        ?
                                        <View style={{
                                            flexDirection: 'column',

                                            borderTopWidth: StyleSheet.hairlineWidth,
                                            borderTopColor: Colors.fieldtTxtColor,

                                            paddingTop: 5,
                                        }}>
                                            <Text style={[orderCodeFontSize, {
                                                fontFamily: "NunitoSans-SemiBold",
                                                marginBottom: 2,
                                            }]}>
                                                Orders Pending Approval
                                            </Text>

                                            <FlatList
                                                removeClippedSubviews={true}
                                                style={{
                                                    // width: '100%',
                                                    // minHeight: windowHeight * 0.05,

                                                }}
                                                contentContainerStyle={{
                                                    // paddingHorizontal: 15,
                                                }}
                                                data={
                                                    selectedTablePendingApprovalOrders.reduce(
                                                        (accu, order) =>
                                                            accu.concat(
                                                                (order.cartItems ? order.cartItems : [])
                                                                    // .filter(
                                                                    //     (cartItem) =>
                                                                    //         cartItem.deliveredAt === null,
                                                                    // )
                                                                    .map(cartItem => ({
                                                                        ...cartItem,
                                                                        userOrderId: order.uniqueId,

                                                                        userName: order.userName ? order.userName : 'N/A',
                                                                        orderId: order.orderId,

                                                                        // isCancelledItem: true,
                                                                    })),
                                                            ),
                                                        [],
                                                    )
                                                }
                                                renderItem={renderTableOrder}
                                                keyExtractor={(item, index) => String(index)}
                                            />
                                        </View>
                                        :
                                        <></>
                                }

                                {/* 2024-08-16 - For other people's promo/voucher orders, need paid separately by original customer */}

                                {
                                    selectedTableOtherPeoplePromoVoucherOrders.length > 0
                                        ?
                                        <View style={{
                                            flexDirection: 'column',

                                            borderTopWidth: StyleSheet.hairlineWidth,
                                            borderTopColor: Colors.fieldtTxtColor,

                                            paddingTop: 5,
                                        }}>
                                            <Text style={[orderCodeFontSize, {
                                                fontFamily: "NunitoSans-SemiBold",
                                                marginBottom: 2,
                                            }]}>
                                                Promotional Orders From Other Customers
                                            </Text>

                                            <FlatList
                                                removeClippedSubviews={true}
                                                style={{
                                                    // width: '100%',
                                                    // minHeight: windowHeight * 0.05,

                                                }}
                                                contentContainerStyle={{
                                                    // paddingHorizontal: 15,
                                                }}
                                                data={
                                                    selectedTableOtherPeoplePromoVoucherOrders.reduce(
                                                        (accu, order) =>
                                                            accu.concat(
                                                                (order.cartItems ? order.cartItems : [])
                                                                    // .filter(
                                                                    //     (cartItem) =>
                                                                    //         cartItem.deliveredAt === null,
                                                                    // )
                                                                    .map(cartItem => ({
                                                                        ...cartItem,
                                                                        userOrderId: order.uniqueId,

                                                                        userName: order.userName ? order.userName : 'N/A',
                                                                        orderId: order.orderId,

                                                                        // isCancelledItem: true,
                                                                    })),
                                                            ),
                                                        [],
                                                    )
                                                }
                                                renderItem={renderTableOrder}
                                                keyExtractor={(item, index) => String(index)}
                                            />
                                        </View>
                                        :
                                        <></>
                                }

                                {/* 2023-02-23 - Show cancelled items */}

                                {
                                    filterOldRejectedCancelledOrders(toRenderOrders).find(order => {
                                        if (order.cartItemsCancelled && order.cartItemsCancelled.length > 0) {
                                            return true;
                                        }
                                        else {
                                            return false;
                                        }
                                    })
                                        ?
                                        <View style={{
                                            flexDirection: 'column',

                                            borderTopWidth: StyleSheet.hairlineWidth,
                                            borderTopColor: Colors.fieldtTxtColor,

                                            paddingTop: 5,
                                        }}>
                                            <Text style={[orderCodeFontSize, {
                                                fontFamily: "NunitoSans-SemiBold",
                                                marginBottom: 2,
                                            }]}>
                                                Rejected Items
                                            </Text>

                                            <FlatList
                                                removeClippedSubviews={true}
                                                style={{
                                                    // width: '100%',
                                                    // minHeight: windowHeight * 0.05,

                                                }}
                                                contentContainerStyle={{
                                                    // paddingHorizontal: 15,
                                                }}
                                                data={
                                                    filterOldRejectedCancelledOrders(toRenderOrders).reduce(
                                                        (accu, order) =>
                                                            accu.concat(
                                                                (order.cartItemsCancelled ? order.cartItemsCancelled : [])
                                                                    // .filter(
                                                                    //     (cartItem) =>
                                                                    //         cartItem.deliveredAt === null,
                                                                    // )
                                                                    .map(cartItem => ({
                                                                        ...cartItem,
                                                                        userOrderId: order.uniqueId,

                                                                        userName: order.userName ? order.userName : 'N/A',
                                                                        orderId: order.orderId,

                                                                        isCancelledItem: true,
                                                                    })),
                                                            ),
                                                        [],
                                                    )
                                                }
                                                renderItem={renderTableOrder}
                                                keyExtractor={(item, index) => String(index)}
                                            />
                                        </View>
                                        :
                                        <></>
                                }

                                {/* {selectedUserOrder && selectedUserOrder.cartItems != null
                                ? selectedUserOrder.cartItems.map((element, index) => {
                                    return (
                                        <View>
                                            <View style={{ flexDirection: 'row', justifyContent: 'space-between', paddingVertical: 5, }}>
                                                <View style={{ width: "60%" }}>
                                                    <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10 }}>{element.name}</Text>
                                                </View>
                                                <View style={{ width: "10%" }}>
                                                    <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10 }}>X{element.quantity}</Text>
                                                </View>
                                                <View style={{ width: "5%" }}>
                                                    <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10 }}></Text>
                                                </View>
                                                <View style={{ width: "25%" }}>
                                                    <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                                                        <Text style={{ fontSize: 10, alignSelf: 'flex-end', fontFamily: "NunitoSans-Bold", }}>RM </Text>
                                                        <Text style={{ alignSelf: 'flex-end', fontFamily: "NunitoSans-Bold", fontSize: 10 }}>{(element.price + (element.discountPromotions ? element.discountPromotions : 0)).toFixed(2)}</Text>
                                                    </View>
                                                </View>
                                            </View>

                                            {element.remarks && element.remarks.length > 0 ?
                                                <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                                                    <View style={{ width: "60%" }}>
                                                        <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10, }}><Text style={{ color: '#a8a8a8' }}>- </Text>{element.remarks}</Text>
                                                    </View>
                                                    <View style={{ width: "10%" }}>
                                                        <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10, }}></Text>
                                                    </View>
                                                    <View style={{ width: "5%" }}>
                                                        <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10, }}></Text>
                                                    </View>
                                                    <View style={{ width: "25%" }}>
                                                        <Text style={{ alignSelf: 'flex-end', fontFamily: "NunitoSans-Bold", fontSize: 10, }}></Text>
                                                    </View>
                                                </View>
                                                : <></>
                                            }

                                            {element.addOns.map((addOnChoice, i) => {
                                                return (
                                                    <View style={{ flexDirection: 'row', justifyContent: 'space-between', paddingVertical: 5, }}>
                                                        <View style={{ width: "60%", }}>
                                                            <View style={{ flexDirection: 'row', }}>
                                                                <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10, color: Colors.descriptionColor, width: '30%' }}>{`${addOnChoice.name}:`}</Text>
                                                                <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10, color: Colors.descriptionColor, width: '70%' }}>{`${addOnChoice.choiceNames[0]}`}</Text>
                                                            </View>
                                                        </View>
                                                        <View style={{ width: "10%" }}>
                                                            <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10, color: Colors.descriptionColor, }}>{`${addOnChoice.quantities
                                                                ? `x${addOnChoice.quantities[0]}`
                                                                : ''
                                                                }`}</Text>
                                                        </View>
                                                        <View style={{ width: "5%" }}>
                                                            <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10, color: Colors.descriptionColor, }}></Text>
                                                        </View>
                                                        <View style={{ width: "25%" }}>
                                                            <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                                                                <Text style={{ alignSelf: 'flex-end', fontFamily: "NunitoSans-Bold", fontSize: 10, color: Colors.descriptionColor, }}>RM </Text>
                                                                <Text style={{ alignSelf: 'flex-end', fontFamily: "NunitoSans-Bold", fontSize: 10, color: Colors.descriptionColor, }}>{addOnChoice.prices.reduce((accumAddOn, priceAddOn) => accumAddOn + priceAddOn, 0).toFixed(2)}</Text>
                                                            </View>
                                                        </View>
                                                    </View>
                                                )
                                            })}

                                        </View>
                                    );
                                })
                                : null}

                            {
                                selectedUserOrderOthers.map(order => {
                                    return (
                                        <>
                                            {order && order.cartItems != null
                                                ? order.cartItems.map((element, index) => {
                                                    return (
                                                        <View>
                                                            <View style={{ flexDirection: 'row', justifyContent: 'space-between', paddingVertical: 5, }}>
                                                                <View style={{ width: "60%" }}>
                                                                    <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10 }}>{element.name}</Text>
                                                                </View>
                                                                <View style={{ width: "10%" }}>
                                                                    <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10 }}>X{element.quantity}</Text>
                                                                </View>
                                                                <View style={{ width: "5%" }}>
                                                                    <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10 }}></Text>
                                                                </View>
                                                                <View style={{ width: "25%" }}>
                                                                    <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                                                                        <Text style={{ fontSize: 10, alignSelf: 'flex-end', fontFamily: "NunitoSans-Bold", }}>RM </Text>
                                                                        <Text style={{ alignSelf: 'flex-end', fontFamily: "NunitoSans-Bold", fontSize: 10 }}>{(element.price + (element.discountPromotions ? element.discountPromotions : 0)).toFixed(2)}</Text>
                                                                    </View>
                                                                </View>
                                                            </View>

                                                            {element.remarks && element.remarks.length > 0 ?
                                                                <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                                                                    <View style={{ width: "60%" }}>
                                                                        <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10, }}><Text style={{ color: '#a8a8a8' }}>- </Text>{element.remarks}</Text>
                                                                    </View>
                                                                    <View style={{ width: "10%" }}>
                                                                        <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10, }}></Text>
                                                                    </View>
                                                                    <View style={{ width: "5%" }}>
                                                                        <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10, }}></Text>
                                                                    </View>
                                                                    <View style={{ width: "25%" }}>
                                                                        <Text style={{ alignSelf: 'flex-end', fontFamily: "NunitoSans-Bold", fontSize: 10, }}></Text>
                                                                    </View>
                                                                </View>
                                                                : <></>
                                                            }

                                                            {element.addOns.map((addOnChoice, i) => {
                                                                return (
                                                                    <View style={{ flexDirection: 'row', justifyContent: 'space-between', paddingVertical: 5, }}>
                                                                        <View style={{ width: "60%", }}>
                                                                            <View style={{ flexDirection: 'row', }}>
                                                                                <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10, color: Colors.descriptionColor, width: '30%' }}>{`${addOnChoice.name}:`}</Text>
                                                                                <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10, color: Colors.descriptionColor, width: '70%' }}>{`${addOnChoice.choiceNames[0]}`}</Text>
                                                                            </View>
                                                                        </View>
                                                                        <View style={{ width: "10%" }}>
                                                                            <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10, color: Colors.descriptionColor, }}>{`${addOnChoice.quantities
                                                                                ? `x${addOnChoice.quantities[0]}`
                                                                                : ''
                                                                                }`}</Text>
                                                                        </View>
                                                                        <View style={{ width: "5%" }}>
                                                                            <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10, color: Colors.descriptionColor, }}></Text>
                                                                        </View>
                                                                        <View style={{ width: "25%" }}>
                                                                            <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                                                                                <Text style={{ alignSelf: 'flex-end', fontFamily: "NunitoSans-Bold", fontSize: 10, color: Colors.descriptionColor, }}>RM </Text>
                                                                                <Text style={{ alignSelf: 'flex-end', fontFamily: "NunitoSans-Bold", fontSize: 10, color: Colors.descriptionColor, }}>{addOnChoice.prices.reduce((accumAddOn, priceAddOn) => accumAddOn + priceAddOn, 0).toFixed(2)}</Text>
                                                                            </View>
                                                                        </View>
                                                                    </View>
                                                                )
                                                            })}

                                                        </View>
                                                    );
                                                })
                                                : null}
                                        </>
                                    );
                                })
                            } */}

                                <View style={styles.totalContainer}>
                                    <View style={{ flex: 1, width: '75%', paddingTop: 10, }}>
                                        <Text style={styles.description}>Subtotal</Text>
                                        <Text style={styles.description}>Discount (-)</Text>
                                        {/* <Text style={styles.description}> */}
                                        {/* Tax ( */}
                                        {/* {orderTax ? orderTax.rate : 0} */}
                                        {/* {orderOutlet.taxPercent} */}
                                        {/* %) */}
                                        {/* </Text> */}
                                        {
                                            selectedOutlet && selectedOutlet.taxActive
                                                ? (
                                                    orderToShow && orderToShow.totalPriceTaxList && orderToShow.totalPriceTaxList.length > 0
                                                        ? (
                                                            orderToShow.totalPriceTaxList.map((taxItem, index) => (
                                                                <Text
                                                                    key={index}
                                                                    style={[styles.description]}>
                                                                    {taxItem.tName
                                                                        ? `${taxItem.tName} (${(taxItem.tRate * 100).toFixed(0)}%)`
                                                                        : `Tax (${(taxItem.tRate * 100).toFixed(0)}%)`}
                                                                </Text>
                                                            ))
                                                        ) : (
                                                            <Text
                                                                style={[styles.description]}>
                                                                {`Tax (${(selectedOutlet.taxRate * 100).toFixed(0)}%) ${selectedOutlet.taxNum ? `(${selectedOutlet.taxNum})` : ''}`}
                                                            </Text>
                                                        )
                                                )
                                                :
                                                <></>
                                        }
                                        {
                                            selectedOutlet && selectedOutlet.scActive
                                                ?
                                                <Text
                                                    style={[
                                                        styles.description,
                                                    ]}>
                                                    {`${selectedOutlet.scName ? selectedOutlet.scName : 'Service Charge'} (${(selectedOutlet.scRate * 100).toFixed(0)}%)`}
                                                </Text>
                                                :
                                                <></>
                                        }
                                        {/* <Text style={styles.description}>
                            Delivery Fee
                        </Text>
                        {
                            (selectedUserOrder && selectedUserOrder.deliveryPackagingFee && selectedUserOrder.orderType === ORDER_TYPE.DELIVERY)
                                ?
                                <Text style={styles.description}>
                                    Delivery Packaging Fee
                                </Text>
                                :
                                <></>
                        } */}

                                        {
                                            (orderToShow && orderToShow.pickupPackagingFee && orderToShow.orderType === ORDER_TYPE.PICKUP)
                                                ?
                                                <Text style={styles.description}>
                                                    Takeaway Packaging Fee
                                                </Text>
                                                :
                                                <></>
                                        }

                                        <Text
                                            style={[
                                                styles.description,
                                            ]}>
                                            {`Roundings`}
                                        </Text>

                                        <Text style={styles.total}>Total</Text>
                                    </View>
                                    <View style={{ alignSelf: 'flex-end', width: '25%', }}>
                                        {/* <Text style={styles.price}>RM {selectedUserOrder.subtotal}</Text>
                        <Text style={styles.price}> {orderId.discount}</Text>
                        <Text style={styles.price}>
                            {' '}
                            {orderTax
                                ? (((parseFloat(orderId.subtotal) - parseFloat(orderId.discount)))
                                    * parseFloat(orderTax.rate)).toFixed(2)
                                : 0}
                        </Text>
                        <Text style={styles.totalPrice}>
                            RM {orderId.total}
                        </Text> */}
                                        <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                                            <Text style={styles.price}>RM </Text>
                                            {orderToShow ?
                                                <Text style={styles.price}>
                                                    {
                                                        (
                                                            orderToShow.totalPrice +
                                                            orderToShow.cartItems.reduce(
                                                                (accumCartItem, cartItem) =>
                                                                    accumCartItem +
                                                                    (cartItem.discount ? cartItem.discount : 0), 0
                                                            ) +
                                                            (orderToShow.discAam ? orderToShow.discAam : 0) +
                                                            (selectedUserOrderOthers.reduce(
                                                                (accum, order) => accum +
                                                                    (order.totalPrice
                                                                        +
                                                                        // order.discount +
                                                                        // (order.discountPromotionsTotal ? order.discountPromotionsTotal : 0)), 0)
                                                                        order.cartItems.reduce(
                                                                            (accumCartItem, cartItem) =>
                                                                                accumCartItem +
                                                                                (cartItem.discount ? cartItem.discount : 0), 0
                                                                        )
                                                                        +
                                                                        (order.discAam ? order.discAam : 0)
                                                                    ),
                                                                0
                                                            )
                                                            )
                                                            // selectedUserOrder.discount +
                                                            // (selectedUserOrder.discountPromotionsTotal ? selectedUserOrder.discountPromotionsTotal : 0) +
                                                            // (selectedUserOrderOthers.reduce(
                                                            //     (accum, order) => accum +
                                                            //         (order.totalPrice +
                                                            //             order.discount +
                                                            //             (order.discountPromotionsTotal ? order.discountPromotionsTotal : 0)), 0)
                                                            // )
                                                        ).toFixed(2)
                                                    }
                                                </Text> : <></>}
                                        </View>
                                        <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                                            <Text style={styles.price}>RM </Text>
                                            {orderToShow ? <Text style={styles.price}>
                                                {
                                                    (
                                                        // selectedUserOrder.discount +
                                                        //     selectedUserOrder.discountPromotionsTotal ? selectedUserOrder.discountPromotionsTotal :
                                                        //     selectedUserOrder.discount +
                                                        //         selectedUserOrder.preorderPackagePrice ? selectedUserOrder.totalPrice - selectedUserOrder.preorderPackagePrice :
                                                        //         0

                                                        // selectedUserOrder.discount +
                                                        // (selectedUserOrder.discountPromotionsTotal ? selectedUserOrder.discountPromotionsTotal : 0) +
                                                        // (selectedUserOrder.preorderPackagePrice ? selectedUserOrder.totalPrice - selectedUserOrder.preorderPackagePrice : 0) +

                                                        // (selectedUserOrderOthers.reduce((accum, order) => accum +
                                                        //     (order.discount +
                                                        //         (order.discountPromotionsTotal ? order.discountPromotionsTotal : 0) +
                                                        //         (order.preorderPackagePrice ? order.totalPrice - order.preorderPackagePrice : 0)),
                                                        //     0)
                                                        // )

                                                        orderToShow.cartItems.reduce(
                                                            (accumCartItem, cartItem) =>
                                                                accumCartItem +
                                                                (cartItem.discount ? cartItem.discount : 0), 0
                                                        ) +
                                                        (orderToShow.discAam ? orderToShow.discAam : 0) +
                                                        (selectedUserOrderOthers.reduce(
                                                            (accum, order) => accum +
                                                                // order.discount +
                                                                // (order.discountPromotionsTotal ? order.discountPromotionsTotal : 0)), 0)
                                                                order.cartItems.reduce(
                                                                    (accumCartItem, cartItem) =>
                                                                        accumCartItem +
                                                                        (cartItem.discount ? cartItem.discount : 0), 0
                                                                ) +
                                                                (order.discAam ? order.discAam : 0),
                                                            0
                                                        )
                                                        )

                                                        // selectedUserOrder.finalPrice +
                                                        // // (selectedUserOrder.discountPromotionsTotal ? selectedUserOrder.discountPromotionsTotal : 0) +
                                                        // (selectedUserOrderOthers.reduce(
                                                        //     (accum, order) => accum + order.finalPrice
                                                        //         // +
                                                        //         // (order.discountPromotionsTotal ? order.discountPromotionsTotal : 0)
                                                        //     , 0)
                                                        // )
                                                    ).toFixed(2)
                                                }
                                            </Text> : <></>}
                                        </View>
                                        {
                                            selectedOutlet && selectedOutlet.taxActive
                                                ? (
                                                    orderToShow && orderToShow.totalPriceTaxList && orderToShow.totalPriceTaxList.length > 0
                                                        ? (
                                                            orderToShow.totalPriceTaxList.map((taxItem, index) => (
                                                                <View key={index} style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                                                                    <Text style={styles.price}>RM </Text>
                                                                    <Text style={styles.price}>{(taxItem.tax || 0).toFixed(2)}</Text>
                                                                </View>
                                                            ))
                                                        ) : (
                                                            <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                                                                <Text style={styles.price}>RM </Text>
                                                                <Text style={styles.price}>{
                                                                    (
                                                                        ((orderToShow && orderToShow.tax) ? orderToShow.tax : 0) +
                                                                        (selectedUserOrderOthers.reduce((accum, order) => accum + ((order && order.tax) ? order.tax : 0), 0))
                                                                    ).toFixed(2)
                                                                }</Text>
                                                            </View>
                                                        )
                                                )
                                                :
                                                <></>
                                        }
                                        {
                                            selectedOutlet && selectedOutlet.scActive
                                                ?
                                                <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                                                    <Text style={styles.price}>RM </Text>
                                                    <Text style={styles.price}>{
                                                        // ((selectedUserOrder && selectedUserOrder.sc) ? selectedUserOrder.sc.toFixed(2) : (0).toFixed(2))
                                                        (
                                                            ((orderToShow && orderToShow.sc) ? orderToShow.sc : 0) +
                                                            (selectedUserOrderOthers.reduce((accum, order) => accum + ((order && order.sc) ? order.sc : 0), 0))
                                                        ).toFixed(2)
                                                    }</Text>
                                                </View>
                                                :
                                                <></>
                                        }
                                        {/* <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                            <Text style={styles.price}>RM </Text>                            
                            {selectedUserOrder ? <Text style={styles.price}>{(selectedUserOrder && selectedUserOrder.deliveryFee) ? selectedUserOrder.deliveryFee.toFixed(2) : (0).toFixed(2)}</Text> : <></>}
                        </View>
                        {
                            (selectedUserOrder && selectedUserOrder.deliveryPackagingFee && selectedUserOrder.orderType === ORDER_TYPE.DELIVERY)
                                ?
                                <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                                    <Text style={styles.price}>RM </Text>
                                    <Text style={styles.price}>{selectedUserOrder.deliveryPackagingFee.toFixed(2)}</Text>

                                </View>
                                :
                                <></>
                        } */}

                                        {console.log('selectedUserOrder')}
                                        {console.log(selectedUserOrder)}
                                        {console.log('selectedUserOrderOthers')}
                                        {console.log(selectedUserOrderOthers)}

                                        <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                                            <Text style={styles.price}>RM </Text>
                                            <Text style={styles.price}>{
                                                (
                                                    (
                                                        (
                                                            (orderToShow && orderToShow.finalPrice) ? orderToShow.finalPrice : 0
                                                        ) +
                                                        (selectedUserOrderOthers.reduce((accum, order) =>
                                                            accum + ((order && order.finalPrice) ? order.finalPrice : 0), 0)
                                                        )
                                                    )
                                                    -
                                                    (
                                                        (
                                                            (orderToShow && orderToShow.finalPriceBefore) ? orderToShow.finalPriceBefore : 0
                                                        ) +
                                                        (selectedUserOrderOthers.reduce((accum, order) =>
                                                            accum + ((order && order.finalPriceBefore) ? order.finalPriceBefore : 0), 0)
                                                        )
                                                    )
                                                ).toFixed(2)
                                            }</Text>
                                        </View>

                                        <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
                                            <Text style={styles.totalPrice}>RM </Text>
                                            {/* <Text style={styles.totalPrice}>{(Math.ceil((selectedUserOrder.totalPrice + selectedUserOrder.tax + (selectedUserOrder.sc ? selectedUserOrder.sc : 0) + selectedUserOrder.deliveryFee - selectedUserOrder.discount.toFixed(2)) * 20 - 0.05) / 20).toFixed(2)}</Text> */}
                                            {/*<Text style={styles.totalPrice}>{(selectedUserOrder.finalPrice).toFixed(2)}</Text>*/}
                                            <Text style={styles.totalPrice}>{
                                                // ((selectedUserOrder && selectedUserOrder.finalPrice) ? selectedUserOrder.finalPrice : 0)
                                                (
                                                    (
                                                        (orderToShow && orderToShow.finalPrice) ? orderToShow.finalPrice : 0
                                                    ) +
                                                    (selectedUserOrderOthers.reduce((accum, order) =>
                                                        accum + ((order && order.finalPrice) ? order.finalPrice : 0), 0)
                                                    )
                                                ).toFixed(2)
                                            }</Text>
                                        </View>
                                    </View>
                                </View>
                                <View style={{}}>
                                    <Text
                                        style={[
                                            {
                                                alignSelf: 'center',
                                                fontFamily: 'NunitoSans-Regular',
                                                fontSize: 20,
                                                marginTop: 30,
                                                marginBottom: 10,
                                            },
                                        ]}>
                                        Thank you for your order
                                    </Text>
                                    {/* <Barcode value={receiptInfo.id.toString() + checkOutTime.toString()} format="CODE128" width={1} height={50} />
                    <Barcode format="CODE128" width={1} height={50} /> */}
                                    <Image
                                        style={{
                                            width: 124,
                                            height: 26,
                                            alignSelf: 'center',
                                            //backgroundColor: Colors.primaryColor,
                                        }}
                                        resizeMode="contain"
                                        source={koodoo_logo}
                                    />
                                    <Text
                                        style={[
                                            {
                                                textAlign: 'center',
                                                fontSize: 16,
                                                fontWeight: 'bold',
                                            },
                                        ]}>
                                        Powered by KooDoo
                                    </Text>
                                </View>

                                <View style={{
                                    display: 'flex',
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    width: '100%',
                                    flexDirection: 'row',
                                    // marginTop: 20,
                                }}>
                                    {/* <TouchableOpacity style={{
                                    width: "25%",
                                    height: isMobile() ? 50 : 40,
                                    backgroundColor: Colors.primaryColor,
                                    borderRadius: 40, justifyContent: "center", alignItems: "center", borderWidth: 1, borderColor: Colors.descriptionColor,
                                    flexDirection: 'row',
                                    marginLeft: 10,
                                    zIndex: 1000,
                                }}
                                    disabled={
                                        isLoading ||
                                        !(toRenderOrders.filter((o) => o.paymentDetails === null))
                                    }
                                    onPress={async () => {
                                        TableStore.update(s => {
                                            s.selectedPaymentMethodRemarks = '';
                                        });

                                        proceedToPaymentPage(true, false, false);
                                    }}
                                >
                                    <Text style={{ color: Colors.whiteColor, fontFamily: "NunitoSans-Regular", marginHorizontal: 5, textAlign: 'center' }}>
                                        {isPay
                                            ? 'LOADING...'
                                            :
                                            toRenderOrders.filter((o) => o.paymentDetails === null)
                                                ? 'PAY FOR TABLE'
                                                : 'PAID'
                                        }
                                    </Text>
                                </TouchableOpacity> */}

                                    {/* 2025-01-30 - hide first, to be replaced by pay for selected orders */}
                                    {/* {
                                    toRenderOrders && toRenderOrders.length > 0
                                        ?
                                        <TouchableOpacity style={{
                                            width: "55%",
                                            height: isMobile() ? 50 : 40,
                                            backgroundColor: Colors.primaryColor,
                                            borderRadius: 40, justifyContent: "center", alignItems: "center", borderWidth: 1, borderColor: Colors.descriptionColor,
                                            flexDirection: 'row',
                                            marginLeft: 10,
                                            zIndex: 1000,

                                            marginTop: 15,
                                        }}
                                            disabled={
                                                isLoading ||
                                                !(toRenderOrders.filter((o) => o.paymentDetails === null))
                                            }
                                            onPress={async () => {
                                                TableStore.update(s => {
                                                    s.selectedPaymentMethodRemarks = '';
                                                });

                                                proceedToPaymentPage(false, false, true);
                                            }}
                                        >
                                            <Text style={{ color: Colors.whiteColor, fontFamily: "NunitoSans-Regular", marginHorizontal: 5, textAlign: 'center' }}>
                                                {isPay
                                                    ? 'LOADING...'
                                                    :
                                                    toRenderOrders.filter((o) => o.paymentDetails === null)
                                                        ? 'PAY FOR SELECTED'
                                                        : 'PAID'
                                                }
                                            </Text>
                                        </TouchableOpacity>
                                        :
                                        <></>
                                } */}
                                </View>

                                <View style={{
                                    display: 'flex',
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    width: '100%',
                                    flexDirection: 'row',
                                    marginTop: 20,
                                }}>
                                    {
                                        (selectedOutlet && selectedOutlet.subdomain &&
                                            (selectedOutlet.dso === undefined || selectedOutlet.dso === false)
                                            &&
                                            (
                                                global.selectedOutlet && global.selectedOutlet.subdomain &&
                                                (
                                                    global.selectedOutlet.dso === undefined ||
                                                    global.selectedOutlet.dso === false
                                                )
                                            )
                                        )
                                            ?
                                            <>
                                                {
                                                    toRenderOrders.reduce(
                                                        (accu, order) =>
                                                            accu.concat(
                                                                order.cartItems
                                                                    // .filter(
                                                                    //     (cartItem) =>
                                                                    //         cartItem.deliveredAt === null,
                                                                    // )
                                                                    .map(cartItem => ({
                                                                        ...cartItem,
                                                                        userOrderId: order.uniqueId,

                                                                        userName: order.userName ? order.userName : 'N/A',
                                                                        orderId: order.orderId,
                                                                    })),
                                                            ),
                                                        [],
                                                    )
                                                        .find(item => {
                                                            if (item.priceToPay !== undefined && item.priceToPay === item.price) {
                                                                return false;
                                                            }
                                                            else {
                                                                // check if the order is the orders that already joined with as bill/new order

                                                                if ((item.priceToPay === undefined ||
                                                                    (item.priceToPay !== undefined && item.priceToPay === item.price))) {
                                                                    return true;
                                                                }
                                                                else {
                                                                    return false;
                                                                }
                                                            }
                                                        })
                                                        ?
                                                        <TouchableOpacity style={{
                                                            width: "25%",
                                                            height: isMobile() ? 50 : 40,
                                                            backgroundColor: Colors.primaryColor,
                                                            borderRadius: 40, justifyContent: "center", alignItems: "center", borderWidth: 1, borderColor: Colors.descriptionColor,
                                                            flexDirection: 'row',
                                                            marginLeft: 10,
                                                            zIndex: 1000,
                                                        }}
                                                            disabled={
                                                                isLoading ||
                                                                !(toRenderOrders.filter((o) => o.paymentDetails === null))
                                                            }
                                                            onPress={async () => {
                                                                let dsoStored = false;
                                                                const dsoRaw = await idbGet(`dso`);
                                                                if (dsoRaw) {
                                                                    const dsoParsed = JSON.parse(dsoRaw);

                                                                    if (typeof dsoParsed === 'boolean') {
                                                                        dsoStored = dsoParsed;
                                                                    }
                                                                }

                                                                if (
                                                                    global.selectedOutlet && global.selectedOutlet.subdomain &&
                                                                    (
                                                                        global.selectedOutlet.dso === undefined ||
                                                                        global.selectedOutlet.dso === false
                                                                    )
                                                                    &&
                                                                    (
                                                                        dsoStored === undefined ||
                                                                        dsoStored === false
                                                                    )
                                                                ) {
                                                                    TableStore.update(s => {
                                                                        s.selectedPaymentMethodRemarks = '';
                                                                    });

                                                                    proceedToPaymentPage(false, true, false);
                                                                }
                                                                else {
                                                                    Alert.alert('Info', 'Online payment options is not available for now');
                                                                }
                                                            }}
                                                        >
                                                            <Text style={{ color: Colors.whiteColor, fontFamily: "NunitoSans-Regular", marginHorizontal: 5, textAlign: 'center' }}>
                                                                {isPay
                                                                    ? 'LOADING...'
                                                                    :
                                                                    toRenderOrders.filter((o) => o.paymentDetails === null)
                                                                        // ? 'PAY YOURSELF'
                                                                        ? 'PAY ALL'
                                                                        : 'PAID'
                                                                }
                                                            </Text>
                                                        </TouchableOpacity>
                                                        :
                                                        <></>
                                                }
                                            </>
                                            :
                                            <>
                                            </>
                                    }

                                    <TouchableOpacity style={{
                                        width: "25%",
                                        height: isMobile() ? 50 : 40,
                                        backgroundColor: Colors.primaryColor,
                                        borderRadius: 40, justifyContent: "center", alignItems: "center", borderWidth: 1, borderColor: Colors.descriptionColor,
                                        flexDirection: 'row',
                                        marginLeft: 10,
                                        zIndex: 1000,
                                    }}
                                        onPress={async () => {
                                            const subdomain = await AsyncStorage.getItem("latestSubdomain");

                                            if (!subdomain) {
                                                linkTo && linkTo(`${prefix}/outlet/menu`);
                                            } else {
                                                linkTo && linkTo(`${prefix}/outlet/${subdomain}/menu`);
                                            }
                                        }}
                                    >
                                        <Text style={{ color: Colors.whiteColor, fontFamily: "NunitoSans-Regular", marginHorizontal: 5, textAlign: 'center' }}>MENU</Text>
                                    </TouchableOpacity>

                                    {/* <TouchableOpacity style={{
                                    width: "25%",
                                    height: isMobile() ? 50 : 40,
                                    backgroundColor: Colors.primaryColor,
                                    borderRadius: 40, justifyContent: "center", alignItems: "center", borderWidth: 1, borderColor: Colors.descriptionColor,
                                    flexDirection: 'row',
                                    marginLeft: 10,
                                    zIndex: 1000,
                                }}
                                    onPress={() => {
                                        if (orderId && orderData) {
                                            // props.navigation.navigate("ConfirmOrder", {
                                            //     orderResult: selectedUserOrder,
                                            //     outletData: orderOutlet,
                                            // });

                                            // navigation.navigate("OrderStatus", { outletData: orderOutlet });
                                        }

                                        setExpandDetails(!expandDetails);

                                        setTimeout(() => {
                                            scrollViewRef && scrollViewRef.current && scrollViewRef.current.scrollToEnd({
                                                duration: 500,
                                                animated: true,
                                            });
                                        }, 100);
                                    }}
                                >
                                    <Text style={{ color: Colors.whiteColor, fontFamily: "NunitoSans-Regular", marginHorizontal: 5, textAlign: 'center' }}>STATUS</Text>
                                    <Feather name={expandDetails ? "chevron-up" : "chevron-down"} size={24} color={'#ffffff'} />                                    
                                </TouchableOpacity> */}

                                    {
                                        orderToShow && orderToShow.courierActionPending
                                            // true
                                            ?
                                            <TouchableOpacity style={{
                                                width: "22%",
                                                height: 40,
                                                backgroundColor: Colors.primaryColor,
                                                borderRadius: 40, justifyContent: "center", alignItems: "center", borderWidth: 1, borderColor: Colors.descriptionColor,
                                                flexDirection: 'row',
                                                zIndex: 1000,
                                                marginLeft: 10,
                                            }}
                                                onPress={() => {
                                                    setActionModalVisibility(true)
                                                }}
                                            >
                                                <Text style={{ color: Colors.whiteColor, fontFamily: "NunitoSans-Regular", marginRight: 5 }}>Action</Text>

                                            </TouchableOpacity>
                                            :
                                            <></>
                                    }

                                    {/* {
                        selectedUserOrder && selectedUserOrder.orderStatus !== USER_ORDER_STATUS.ORDER_COMPLETED && selectedUserOrder.orderStatus !== USER_ORDER_STATUS.ORDER_CANCELLED_BY_MERCHANT
                            ?

                            <TouchableOpacity style={{
                                width: "30%",
                                height: 40,
                                backgroundColor: Colors.primaryColor,
                                borderRadius: 40, justifyContent: "center", alignItems: "center", borderWidth: 1, borderColor: Colors.descriptionColor,
                                flexDirection: 'row',
                                zIndex: 1000,
                                marginLeft: 10,
                            }}
                                onPress={() => {
                                    // setCompleteModalVisibility(true)
                                    completeUserOrderByUser();
                                }}
                            >
                                <Text style={{ color: Colors.whiteColor, fontFamily: "NunitoSans-Regular", }}>Received</Text>

                            </TouchableOpacity>
                            :
                            <TouchableOpacity style={{
                                width: "30%",
                                height: 40,
                                backgroundColor: Colors.primaryColor,
                                borderRadius: 40, justifyContent: "center", alignItems: "center", borderWidth: 1, borderColor: Colors.descriptionColor,
                                flexDirection: 'row',
                                zIndex: 1000,
                                marginLeft: 10,
                            }}
                                onPress={() => {
                                    //This is just for showing how UI appear
                                }}
                            >
                                <Text style={{ color: Colors.whiteColor, fontFamily: "NunitoSans-Regular", }}>Received</Text>

                            </TouchableOpacity>
                    } */}
                                </View>

                                {/* {
                    selectedUserOrder && selectedUserOrder.orderType === ORDER_TYPE.DELIVERY
                        ?
                        <View style={{
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center',
                            width: '100%',
                            flexDirection: 'row',
                            marginTop: 10
                        }}>
                            <TouchableOpacity style={{
                                width: "30%",
                                height: 40,
                                backgroundColor: Colors.primaryColor,
                                borderRadius: 40, justifyContent: "center", alignItems: "center", borderWidth: 1, borderColor: Colors.descriptionColor,
                                flexDirection: 'row',
                                zIndex: 1000,
                                marginLeft: 10,
                            }}
                                onPress={() => {
                                    props.navigation.navigate("DeliveryInfo", { orderId: orderId });

                                }}
                            >
                                <Text style={{ color: Colors.whiteColor, fontFamily: "NunitoSans-Regular", }}>Delivery Info</Text>

                            </TouchableOpacity>

                        </View>
                        :
                        <></>
                } */}

                                <Modal
                                    style={{
                                        // flex: 1
                                    }}
                                    visible={receiptModal}
                                    transparent={true}
                                    animationType={'slide'}
                                >
                                    <View style={styles.modalContainerReceipt}>
                                        <ScrollView>
                                            <View style={[styles.modalViewReceipt, {
                                            }]}>
                                                <View style={{ borderWidth: 0, borderColor: Colors.fieldtBgColor, height: '90%', width: '90%' }}>
                                                    <View style={{ alignItems: 'center', flexDirection: 'row', justifyContent: 'center' }}>
                                                        <View style={{ alignSelf: 'center' }}>
                                                            <AsyncImage
                                                                source={{ uri: orderToShow ? orderToShow.merchantLogo : '' }}
                                                                // item={selectedUserOrder}
                                                                style={{ width: 70, height: 70, borderRadius: 10 }}
                                                            />
                                                        </View>
                                                    </View>
                                                    <View style={styles.titleDetail}>
                                                        <View style={{ flexDirection: 'row' }}>
                                                            <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 12 }}>{orderToShow && orderToShow.outletName}</Text>
                                                        </View>
                                                        <Text
                                                            style={{
                                                                marginTop: 10,
                                                                textAlign: 'center',
                                                                fontFamily: "NunitoSans-Bold",
                                                                fontSize: 10
                                                            }}>
                                                            {orderOutlet.address}
                                                        </Text>
                                                        <Text style={{
                                                            marginTop: 0, fontFamily: "NunitoSans-Bold", fontSize: 10
                                                        }}>
                                                            Phone: {orderOutlet.phone}
                                                        </Text>
                                                    </View>
                                                    <View style={[styles.orderTitle, { flexDirection: 'row', justifyContent: 'space-between' }]}>
                                                        <View style={{ flexDirection: 'row', width: '30%', justifyContent: 'flex-start' }}>
                                                            <Text style={[orderCodeFontSize1, {
                                                                fontFamily: "NunitoSans-SemiBold",
                                                                marginBottom: 10,
                                                            }]}>
                                                                Order # :{' '}
                                                            </Text>
                                                            <Text style={[orderCodeFontSize1, {
                                                                fontFamily: "NunitoSans-Bold",
                                                                marginBottom: 10,
                                                                /* width: '30%' */
                                                            }]} numberOfLines={2}>
                                                                {orderToShow && orderToShow.orderId}
                                                            </Text>
                                                        </View>
                                                        <View style={{ flexDirection: 'row', width: '56%', justifyContent: 'flex-end' }}>
                                                            <Text style={[orderCodeFontSize1, {
                                                                fontFamily: "NunitoSans-SemiBold",
                                                                marginBottom: 10,
                                                            }]}>
                                                                Order Type :{' '}
                                                            </Text>
                                                            <Text style={[orderCodeFontSize1, {
                                                                fontFamily: "NunitoSans-Bold",
                                                                marginBottom: 10,
                                                                /* width: '70%' */
                                                            }]} numberOfLines={2}>
                                                                {ORDER_TYPE_PARSED[orderToShow && orderToShow.orderType]}
                                                            </Text>
                                                        </View>
                                                    </View>
                                                    <View style={{ minHeight: Dimensions.get('screen').height * 0.2, }}>
                                                        {orderToShow && orderToShow.cartItems != null
                                                            ? orderToShow.cartItems.map((element, index) => {
                                                                return (
                                                                    <View>
                                                                        <View style={{ flexDirection: 'row', justifyContent: 'space-between', paddingVertical: 5, }}>
                                                                            <View style={{ width: "60%" }}>
                                                                                <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10 }}>{element.name}</Text>
                                                                            </View>
                                                                            <View style={{ width: "10%" }}>
                                                                                <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10 }}>X{element.quantity}</Text>
                                                                            </View>
                                                                            <View style={{ width: "5%" }}>
                                                                                <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10 }}></Text>
                                                                            </View>
                                                                            <View style={{ width: "25%" }}>
                                                                                <Text style={{ alignSelf: 'flex-end', fontFamily: "NunitoSans-Bold", fontSize: 10 }}>{element.price.toFixed(2)}</Text>
                                                                            </View>
                                                                        </View>

                                                                        {element.remarks && element.remarks.length > 0 ?
                                                                            <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                                                                                <View style={{ width: "60%" }}>
                                                                                    <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10 }}><Text style={{ color: '#a8a8a8' }}>- </Text>{element.remarks}</Text>
                                                                                </View>
                                                                                <View style={{ width: "10%" }}>
                                                                                    <Text style={{ fontFamily: "NunitoSans-Regular", fontSize: 10 }}></Text>
                                                                                </View>
                                                                                <View style={{ width: "5%" }}>
                                                                                    <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10 }}></Text>
                                                                                </View>
                                                                                <View style={{ width: "25%" }}>
                                                                                    <Text style={{ alignSelf: 'flex-end', fontFamily: "NunitoSans-Bold", fontSize: 10 }}></Text>
                                                                                </View>
                                                                            </View>
                                                                            : <></>
                                                                        }

                                                                    </View>
                                                                );
                                                            })
                                                            : null}
                                                    </View>
                                                    <View style={styles.totalContainer}>
                                                        <View style={{ flex: 1, width: '75%' }}>
                                                            <Text style={styles.PromotionTitleReceipt}>Voucher Applied</Text>
                                                            <Text style={styles.promotionDescriptionReceipt}> - 10% Discount OFF</Text>
                                                            <Text style={styles.descriptionReceipt}>Subtotal</Text>
                                                            <Text style={styles.descriptionReceipt}>Discount</Text>
                                                            {/* <Text style={styles.descriptionReceipt}> */}
                                                            {/* Tax ( */}
                                                            {/* {orderTax ? orderTax.rate : 0} */}
                                                            {/* {orderOutlet.taxPercent} */}
                                                            {/* %) */}
                                                            {/* </Text> */}
                                                            {
                                                                selectedOutlet && selectedOutlet.taxActive
                                                                    ?
                                                                    <Text
                                                                        style={[
                                                                            styles.description,
                                                                        ]}>
                                                                        {`Tax (${(selectedOutlet.taxRate * 100).toFixed(0)}%) ${selectedOutlet.taxNum ? `(${selectedOutlet.taxNum})` : ''}`}
                                                                    </Text>
                                                                    :
                                                                    <></>
                                                            }
                                                            {
                                                                selectedOutlet && selectedOutlet.scActive
                                                                    ?
                                                                    <Text
                                                                        style={[
                                                                            styles.description,
                                                                        ]}>
                                                                        {`${selectedOutlet.scName ? selectedOutlet.scName : 'Service Charge'} (${(selectedOutlet.scRate * 100).toFixed(0)}%)`}
                                                                    </Text>
                                                                    :
                                                                    <></>
                                                            }
                                                            {/* <Text style={styles.descriptionReceipt}>
                                                Delivery Fee
                                            </Text> */}
                                                            <Text style={styles.totalReceipt}>Total</Text>
                                                        </View>
                                                        <View style={{ alignSelf: 'flex-end', width: '25%' }}>
                                                            {/* <Text style={styles.price}>RM {selectedUserOrder.subtotal}</Text>
                        <Text style={styles.price}> {orderId.discount}</Text>
                        <Text style={styles.price}>
                            {' '}
                            {orderTax
                                ? (((parseFloat(orderId.subtotal) - parseFloat(orderId.discount)))
                                    * parseFloat(orderTax.rate)).toFixed(2)
                                : 0}
                        </Text>
                        <Text style={styles.totalPrice}>
                            RM {orderId.total}
                        </Text> */}
                                                            <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                                                                <Text style={styles.priceReceipt}>RM </Text>
                                                                {/*<Text style={styles.priceReceipt}>{selectedUserOrder.totalPrice.toFixed(2)}</Text>*/}
                                                                <Text style={styles.priceReceipt}>{(orderToShow && orderToShow.totalPrice) ? orderToShow.totalPrice.toFixed(2) : (0).toFixed(2)}</Text>
                                                            </View>
                                                            <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                                                                <Text style={styles.priceReceipt}>RM </Text>
                                                                {/*<Text style={styles.priceReceipt}>{(selectedUserOrder.discount).toFixed(2)}</Text>*/}
                                                                <Text style={styles.priceReceipt}>{(((orderToShow && orderToShow.discount) ? parseFloat(orderToShow.discount) : 0) + (orderToShow.discAam ? orderToShow.discAam : 0)).toFixed(2)}</Text>
                                                            </View>
                                                            <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                                                                <Text style={styles.priceReceipt}>RM </Text>
                                                                {/*<Text style={styles.priceReceipt}>{selectedUserOrder.tax.toFixed(2)}</Text>*/}
                                                                <Text style={styles.priceReceipt}>{(orderToShow && orderToShow.tax) ? orderToShow.tax.toFixed(2) : (0).toFixed(2)}</Text>
                                                            </View>
                                                            {/* <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                                                <Text style={styles.priceReceipt}>RM </Text>                                                
                                                <Text style={styles.priceReceipt}>{(selectedUserOrder && selectedUserOrder.deliveryFee) ? selectedUserOrder.deliveryFee.toFixed(2) : (0).toFixed(2)}</Text>
                                            </View> */}
                                                            <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
                                                                <Text style={styles.totalPriceReceipt}>RM </Text>
                                                                <Text style={styles.totalPriceReceipt}>
                                                                    {/* {(Math.ceil((selectedUserOrder.totalPrice + selectedUserOrder.tax + selectedUserOrder.deliveryFee - selectedUserOrder.discount.toFixed(2)) * 20 - 0.05) / 20).toFixed(2)} */}
                                                                    {/*(selectedUserOrder.finalPrice).toFixed(2)*/}
                                                                    {(orderToShow && orderToShow.finalPrice) ? orderToShow.finalPrice.toFixed(2) : (0).toFixed(2)}
                                                                </Text>
                                                            </View>
                                                        </View>
                                                    </View>
                                                </View>
                                                <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'center', padding: 20 }}>
                                                    <TouchableOpacity style={{
                                                        width: "45%",
                                                        height: 40,
                                                        backgroundColor: Colors.whiteColor,
                                                        borderRadius: 10,
                                                        justifyContent: "center",
                                                        alignItems: "center",
                                                        borderWidth: 1,
                                                        borderColor: Colors.descriptionColor,
                                                        flexDirection: 'row',
                                                        zIndex: 1000,
                                                    }}
                                                        onPress={() => {
                                                            setReceiptModal(false)
                                                        }}
                                                    >
                                                        <Text style={{ fontFamily: 'NunitoSans-Regular', color: Colors.primaryColor }}>CANCEL</Text>

                                                    </TouchableOpacity>
                                                    <TouchableOpacity style={{
                                                        width: "45%",
                                                        height: 40,
                                                        backgroundColor: Colors.primaryColor,
                                                        borderRadius: 10,
                                                        justifyContent: "center",
                                                        alignItems: "center",
                                                        borderWidth: 1,
                                                        borderColor: Colors.descriptionColor,
                                                        flexDirection: 'row',
                                                        zIndex: 1000,
                                                        marginLeft: 10,
                                                    }}

                                                        onPress={() => {

                                                        }}
                                                    >
                                                        <Text style={{ fontFamily: 'NunitoSans-Regular', color: Colors.whiteColor }}>DOWNLOAD</Text>

                                                    </TouchableOpacity>
                                                </View>
                                            </View>
                                        </ScrollView>
                                    </View>
                                </Modal>

                                <Modal
                                    style={{
                                        // flex: 1
                                    }}
                                    visible={actionModalVisibility}
                                    transparent={true}
                                    animationType={'slide'}
                                >
                                    <View style={styles.modalContainer}>
                                        <View style={[styles.modalView, {
                                        }]}>
                                            <View style={{ justifyContent: 'flex-end', alignItems: 'flex-end', padding: 6 }}>
                                                <TouchableOpacity style={{
                                                    width: "47%",
                                                    height: 40,
                                                    backgroundColor: '#EB5757',
                                                    borderRadius: 10,
                                                    justifyContent: "center",
                                                    alignItems: "center",
                                                    borderWidth: 1,
                                                    borderColor: Colors.descriptionColor,
                                                    flexDirection: 'row',
                                                    zIndex: 1000,
                                                    marginLeft: 10,
                                                }}
                                                    disabled={isLoading}
                                                    onPress={() => {
                                                        //cancelAndRefund()
                                                    }}
                                                >
                                                    {
                                                        isLoading
                                                            ?
                                                            <ActivityIndicator style={{
                                                            }} color={Colors.whiteColor} size={'small'} />
                                                            :
                                                            <Text style={{ color: Colors.whiteColor, fontFamily: "NunitoSans-Regular" }}>REFUND</Text>
                                                    }

                                                </TouchableOpacity>
                                            </View>

                                            <View style={{
                                                flexDirection: 'column',
                                                width: '100%',
                                                justifyContent: 'center',
                                                alignItems: 'center',
                                                marginTop: 15,
                                                borderBottomWidth: 1,
                                                borderBottomColor: Colors.fieldtTxtColor,
                                                height: '60%',
                                            }}>

                                                <TouchableOpacity style={{
                                                    width: "65%",
                                                    height: 45,
                                                    backgroundColor: Colors.primaryColor,
                                                    borderRadius: 10,
                                                    justifyContent: "center",
                                                    alignItems: "center",
                                                    borderWidth: 1,
                                                    borderColor: Colors.descriptionColor,
                                                    flexDirection: 'row',
                                                    zIndex: 1000,

                                                }}
                                                    disabled={isLoading}
                                                    onPress={() => {
                                                        waitForSender()
                                                    }}
                                                >
                                                    {
                                                        isLoading
                                                            ?
                                                            <ActivityIndicator style={{
                                                            }} color={Colors.whiteColor} size={'small'} />
                                                            :
                                                            <Text style={{ color: Colors.whiteColor, fontFamily: "NunitoSans-Regular" }}>Wait For Sender</Text>
                                                    }


                                                </TouchableOpacity>

                                                <View style={{
                                                    flexDirection: 'row',
                                                    alignItems: 'center',
                                                    width: '100%',
                                                    justifyContent: 'space-between',
                                                    marginTop: 20,
                                                }}>
                                                    <View style={{
                                                        width: '50%',
                                                    }}>
                                                        <Picker
                                                            placeholder={{}}
                                                            style={Styles.rnPickerSelectStyle}
                                                            items={SENDER_DROPDOWN_LIST}
                                                            onValueChange={value => {
                                                                setSelectedSender(value);
                                                            }}
                                                        />
                                                    </View>

                                                    <TouchableOpacity style={{
                                                        width: "45%",
                                                        height: 45,
                                                        backgroundColor: Colors.primaryColor,
                                                        borderRadius: 10,
                                                        justifyContent: "center",
                                                        alignItems: "center",
                                                        borderWidth: 1,
                                                        borderColor: Colors.descriptionColor,
                                                        flexDirection: 'row',
                                                        zIndex: 1000,
                                                        // marginTop: 10,
                                                    }}
                                                        disabled={isLoading}
                                                        onPress={() => {
                                                            topup();
                                                        }}
                                                    >
                                                        {
                                                            isLoading
                                                                ?
                                                                <ActivityIndicator style={{
                                                                }} color={Colors.whiteColor} size={'small'} />
                                                                :
                                                                orderToShow ? <Text style={{ color: Colors.whiteColor, fontFamily: "NunitoSans-Regular" }}>{`Top-Up (RM${(Math.max(deliveryQuotation.totalFee - orderToShow.deliveryFee, 0).toFixed(2))})`}</Text> : <></>
                                                        }
                                                    </TouchableOpacity>
                                                </View>
                                            </View>

                                            <View style={{
                                                flexDirection: 'row',
                                                alignItems: 'center',
                                                justifyContent: 'center',
                                                margin: 15,
                                            }}>

                                                <TouchableOpacity style={{
                                                    width: "47%",
                                                    height: 40,
                                                    backgroundColor: Colors.whiteColor,
                                                    borderRadius: 10,
                                                    justifyContent: "center",
                                                    alignItems: "center",
                                                    borderWidth: 1,
                                                    borderColor: Colors.descriptionColor,
                                                    flexDirection: 'row',
                                                    zIndex: 1000,

                                                }}
                                                    disabled={isLoading} celAnd
                                                    onPress={() => {
                                                        setActionModalVisibility(false);
                                                    }}
                                                >
                                                    <Text style={{ color: Colors.primaryColor, fontFamily: "NunitoSans-Regular" }}>CANCEL</Text>

                                                </TouchableOpacity>

                                                <TouchableOpacity style={{
                                                    width: "47%",
                                                    height: 40,
                                                    backgroundColor: '#EB5757',
                                                    borderRadius: 10,
                                                    justifyContent: "center",
                                                    alignItems: "center",
                                                    borderWidth: 1,
                                                    borderColor: Colors.descriptionColor,
                                                    flexDirection: 'row',
                                                    zIndex: 1000,
                                                    marginLeft: 10,
                                                }}
                                                    disabled={isLoading}
                                                    onPress={() => {
                                                        //cancelAndRefund()
                                                    }}
                                                >
                                                    {
                                                        isLoading
                                                            ?
                                                            <ActivityIndicator style={{
                                                            }} color={Colors.whiteColor} size={'small'} />
                                                            :
                                                            <Text style={{ color: Colors.whiteColor, fontFamily: "NunitoSans-Regular" }}>REFUND</Text>
                                                    }

                                                </TouchableOpacity>

                                            </View>
                                        </View>
                                    </View>

                                </Modal>


                                <Modal
                                    style={{
                                    }}
                                    visible={questionnaireVisibility}
                                    transparent={true}
                                    animationType={'slide'}
                                >
                                    <View style={styles.modalContainer}>
                                        <View style={[styles.modalView1]}>
                                            <FlatList
                                                style={{}}
                                                data={renderQuestionnaire}
                                                //extraData={renderQuestionnaire}
                                                renderItem={renderQuestionnaire}
                                                keyExtractor={(item, index) => String(index)}
                                            />

                                        </View>
                                    </View>
                                </Modal>

                                <Modal
                                    style={{
                                        // flex: 1
                                    }}
                                    visible={completeModalVisibility}
                                    transparent={true}
                                    animationType={'slide'}
                                >
                                    <View style={styles.modalContainer}>
                                        <View style={[styles.modalView1, {}]}>
                                            <View style={{ flexDirection: 'row', justifyContent: 'flex-end', alignItems: 'flex-end', padding: 10 }}>
                                                <TouchableOpacity
                                                    style={styles.closeButton}
                                                    onPress={() => {
                                                        setCompleteModalVisibility(false);
                                                    }}>
                                                    <AntDesign name="closecircle" size={25} color={Colors.fieldtTxtColor} />
                                                </TouchableOpacity>
                                            </View>
                                            <View style={{ flexDirection: 'column', alignItems: 'flex-start', justifyContent: 'flex-start' }}>
                                                <Text style={{
                                                    fontSize: 20,
                                                    fontFamily: "NunitoSans-Bold"
                                                }}>
                                                    Feedback
                                                </Text>
                                                <Text style={{
                                                    fontSize: 13,
                                                    fontFamily: "NunitoSans-Regular",
                                                    marginTop: 10
                                                }}>
                                                    How was your experience in {orderToShow ? orderToShow.outletName : ''}?
                                                </Text>
                                            </View>

                                            <View style={{ flexDirection: 'row', marginTop: 10 }}>
                                                {/* <TouchableOpacity
                    onPress={() => {
                       
                    }}
                    >
                    <AntDesign name="star" size={35} color={Colors.primaryColor}/>
                    </TouchableOpacity>

                    <TouchableOpacity
                    onPress={() => {
                        
                    }}
                    >
                    <AntDesign name="star" size={35} color={Colors.primaryColor} />
                    </TouchableOpacity>

                    <TouchableOpacity
                    onPress={() => {
                        
                    }}
                    >
                    <AntDesign name="star" size={35} color={Colors.primaryColor} />
                    </TouchableOpacity>

                    <TouchableOpacity
                    onPress={() => {
                        
                    }}
                    >
                    <AntDesign name="star" size={35} color={Colors.primaryColor} />
                    </TouchableOpacity>

                    <TouchableOpacity
                    onPress={() => {
                       
                    }}
                    >
                    <AntDesign name="star" size={35} color={Colors.primaryColor} />
                    </TouchableOpacity> */}
                                                <StarRatingView />
                                            </View>

                                            <View style={{ alignItems: 'flex-start', justifyContent: 'flex-start', marginTop: 20 }}>
                                                <Text style={{
                                                    fontSize: 13,
                                                    fontFamily: "NunitoSans-Regular"
                                                }}>
                                                    Give some feedback in words.
                                                </Text>
                                            </View>

                                            <View style={{ marginTop: 5 }}>
                                                <TextInput
                                                    //editable={}
                                                    multiline={true}
                                                    clearButtonMode="while-editing"
                                                    style={styles.textInput}
                                                    placeholder="Leave Your Comment..."
                                                    onChangeText={(text) => { setReviewComment(text) }}
                                                    defaultValue={reviewComment}
                                                />
                                            </View>
                                            {/* <View style={{ marginTop: 10, alignItems: 'center' }}>
                                <Text style={{
                                    fontSize: 12.5,
                                    fontFamily: "NunitoSans-Regular",
                                }}>
                                    ( {reviewTotal} ) Ratings
                                </Text>
                            </View> */}


                                            <View style={{ flexDirection: 'row', justifyContent: 'center', alignItems: 'center', marginTop: 25 }}>
                                                <TouchableOpacity style={{
                                                    width: "47%",
                                                    height: 40,
                                                    backgroundColor: Colors.primaryColor,
                                                    borderRadius: 10,
                                                    justifyContent: "center",
                                                    alignItems: "center",
                                                    borderWidth: 1,
                                                    borderColor: Colors.descriptionColor,
                                                    flexDirection: 'row',
                                                    zIndex: 1000,
                                                }}
                                                    disabled={isLoading}
                                                    onPress={() => {
                                                        submitReview()
                                                        //setQuestionnaireVisibility(true)
                                                    }}
                                                >
                                                    {
                                                        isLoading
                                                            ?
                                                            <ActivityIndicator style={{
                                                            }} color={Colors.whiteColor} size={'small'} />
                                                            :
                                                            <Text style={{ color: Colors.whiteColor, fontFamily: "NunitoSans-Regular" }}>SUBMIT</Text>
                                                    }

                                                </TouchableOpacity>
                                            </View>

                                        </View>
                                    </View>
                                </Modal>

                                {expandDetails &&
                                    <>
                                        {
                                            (
                                                !orderDetails
                                                // 2022-10-12 - Hide the following condition first
                                                // ||
                                                // (
                                                //     orderDetails &&
                                                //     orderDetails.orderStatus !== USER_ORDER_STATUS.ORDER_CANCELLED_BY_MERCHANT &&
                                                //     orderDetails.orderStatus !== USER_ORDER_STATUS.ORDER_CANCELLED_BY_USER
                                                // )
                                            )
                                                ?
                                                <View style={{
                                                    alignSelf: "center",
                                                    // marginTop: 80,
                                                    // height: Dimensions.get('screen').height * 0.75
                                                }}>
                                                    {/* <Image style={styles.headerLogo} resizeMode="contain" source={selectedUserOrder.orderType === ORDER_TYPE.DELIVERY ? ORDER_STATUS_IMAGES_DELIVERY[parsedOrderStatus] : ORDER_STATUS_IMAGES[parsedOrderStatus]} /> */}

                                                    <View style={[detailsTopSpacing, {
                                                        alignItems: "center",
                                                        justifyContent: 'center',
                                                        //top: '48%',
                                                    }]}>
                                                        <Text style={[detailsTextScale, {
                                                            //fontSize: 25,
                                                            // fontWeight: "700",
                                                            color: Colors.blackColor,
                                                            fontFamily: 'NunitoSans-SemiBold',
                                                            width: '100%',
                                                            textAlign: 'center',
                                                            // marginTop: 5,
                                                            // zIndex: 10,
                                                            // backgroundColor: 'red',
                                                            width: '100%',
                                                            marginTop: 0,
                                                        }]}>
                                                            {
                                                                `#${orderToShow.orderType === ORDER_TYPE.DINEIN ? '' : 'T'}${orderToShow.orderId}:\n${orderToShow.orderType === ORDER_TYPE.DELIVERY ?
                                                                    ORDER_STATUS_TEXT_DELIVERY[parsedOrderStatus] :
                                                                    ORDER_STATUS_TEXT[parsedOrderStatus]}`
                                                            }
                                                        </Text>

                                                        <>
                                                            {
                                                                selectedUserOrderOthers && selectedUserOrderOthers.map((order) => {
                                                                    return (
                                                                        <Text style={[detailsTextScale, {
                                                                            //fontSize: 25,
                                                                            // fontWeight: "700",
                                                                            color: Colors.blackColor,
                                                                            fontFamily: 'NunitoSans-SemiBold',
                                                                            width: '100%',
                                                                            textAlign: 'center',
                                                                            // marginTop: 5,
                                                                            // zIndex: 10,
                                                                            // backgroundColor: 'red',
                                                                            width: '100%',
                                                                            marginTop: 20,
                                                                        }]}>
                                                                            {
                                                                                `#${order.orderType === ORDER_TYPE.DINEIN ? '' : 'T'}${order.orderId}:\n${order.orderType === ORDER_TYPE.DELIVERY ?
                                                                                    ORDER_STATUS_TEXT_DELIVERY[parsedOrderStatusDict[order.uniqueId]] :
                                                                                    (ORDER_STATUS_TEXT[parsedOrderStatusDict[order.uniqueId]]) ? (ORDER_STATUS_TEXT[parsedOrderStatusDict[order.uniqueId]]) : (ORDER_STATUS_TEXT[ORDER_STATUS_PARSED.PLACED])}`
                                                                            }
                                                                        </Text>
                                                                    );
                                                                })
                                                            }
                                                        </>


                                                        {/* <Text style={[detailsTextScale2, {
                                                        //fontSize: 16,
                                                        marginTop: 30,
                                                        color: "#b5b5b5",
                                                        marginBottom: 50,
                                                        fontFamily: 'NunitoSans-Regular'
                                                    }]}>
                                                        {parsedOrderStatus !== ORDER_STATUS_PARSED.DELIVERED ? `Estimate time ${(((selectedUserOrderTakeaway.totalPrepareTime ? selectedUserOrderTakeaway.totalPrepareTime : 60) / 60) + 30).toFixed(0)}mins` : 'Delivered'}
                                                    </Text> */}
                                                    </View>

                                                    {/* {selectedUserOrder.orderType === ORDER_TYPE.DELIVERY &&
                                        // <View style={{
                                        //     // backgroundColor: 'blue',
                                        // }}>
                                        <>
                                            <Image style={{
                                                // width: Styles.width * 0.9,
                                                width: '100%',
                                                // backgroundColor: 'red',

                                                // height: Dimensions.get('screen').width * 2.5,

                                                position: 'absolute',
                                                //bottom: '-2%',
                                                alignSelf: 'center',
                                            }}
                                                resizeMode="contain"
                                                source={ORDER_STATUS_IMAGES_BOTTOM[parsedOrderStatus]}
                                            />

                                            <View style={{
                                                flexDirection: "row",
                                                alignItems: "center",
                                                position: "absolute",
                                                height: Dimensions.get('screen').width * 2.7,
                                                //bottom: '-105%',
                                                //backgroundColor: 'red',
                                                //position: 'absolute',
                                                alignSelf: 'center',
                                                width: '107%',
                                            }}>
                                                <Text style={{
                                                    fontSize: 12,
                                                    marginTop: 20,
                                                    color: Colors.primaryColor,
                                                    fontFamily: 'NunitoSans-Bold',
                                                    width: '20%',
                                                    textAlign: 'center',
                                                }}>Placed</Text>

                                                <Text style={{
                                                    fontSize: 12,
                                                    marginTop: 20,
                                                    color: Colors.primaryColor,
                                                    fontFamily: 'NunitoSans-Bold',
                                                    width: '20%',
                                                    textAlign: 'center',
                                                }}>Preparing</Text>

                                                <Text style={{
                                                    fontSize: 12,
                                                    marginTop: 20,
                                                    color: Colors.primaryColor,
                                                    fontFamily: 'NunitoSans-Bold',
                                                    width: '20%',
                                                    textAlign: 'center',
                                                }}>Picking-up</Text>

                                                <Text style={{
                                                    fontSize: 12,
                                                    marginTop: 20,
                                                    color: Colors.primaryColor,
                                                    fontFamily: 'NunitoSans-Bold',
                                                    width: '20%',
                                                    textAlign: 'center',
                                                }}>Delivering</Text>

                                                <Text style={{
                                                    fontSize: 12,
                                                    marginTop: 20,
                                                    color: Colors.primaryColor,
                                                    fontFamily: 'NunitoSans-Bold',
                                                    width: '20%',
                                                    textAlign: 'center',
                                                }}>Arrived</Text>
                                            </View>
                                        </>
                                        // </View>
                                    }

                                    {(selectedUserOrder.orderType === ORDER_TYPE.PICKUP || selectedUserOrder.orderType === ORDER_TYPE.DINEIN) &&
                                        <>

                                            <Image style={[styles.headerLogo1, {
                                                width: Dimensions.get('screen').width,
                                                // marginLeft: 20
                                                // backgroundColor: 'red'
                                            }]} resizeMode="contain" source={ORDER_STATUS_IMAGES_BOTTOM_TAKEAWAY[parsedOrderStatus]} />

                                            <View style={{
                                                flexDirection: "row",
                                                alignItems: "center",
                                                bottom: '-115%',
                                                // backgroundColor: 'red',

                                                justifyContent: 'center',
                                                // width: '100%',
                                                width: Dimensions.get('screen').width,
                                            }}>
                                                <Text style={{
                                                    fontSize: 14,
                                                    marginTop: 10,
                                                    color: Colors.primaryColor,
                                                    fontFamily: 'NunitoSans-Bold',
                                                    width: '33.3%',
                                                    textAlign: 'center',
                                                }}>Order Placed</Text>

                                                <Text style={{
                                                    fontSize: 14,
                                                    marginTop: 10,
                                                    color: Colors.primaryColor,
                                                    fontFamily: 'NunitoSans-Bold',
                                                    width: '33.3%',
                                                    textAlign: 'center',
                                                }}>Preparing</Text>

                                                <Text style={{
                                                    fontSize: 14,
                                                    marginTop: 10,
                                                    color: Colors.primaryColor,
                                                    fontFamily: 'NunitoSans-Bold',
                                                    width: '33.3%',
                                                    textAlign: 'center',
                                                }}>Done</Text>
                                            </View>
                                        </>
                                    } */}
                                                </View>
                                                :
                                                <></>
                                        }

                                        {
                                            orderDetails
                                                ?
                                                <View style={[{
                                                    width: '100%',
                                                }, (
                                                    orderDetails &&
                                                    orderDetails.orderStatus !== USER_ORDER_STATUS.ORDER_CANCELLED_BY_MERCHANT &&
                                                    orderDetails.orderStatus !== USER_ORDER_STATUS.ORDER_CANCELLED_BY_USER
                                                ) ? {

                                                } : {
                                                    marginTop: '10%',
                                                }]}>
                                                    <Text style={{
                                                        fontSize: 16,
                                                        // marginTop: 10,
                                                        color: Colors.secondaryColor,
                                                        fontFamily: 'NunitoSans-SemiBold',
                                                        textAlign: 'center',
                                                    }}>{
                                                            `Remark: ${orderDetails.message}`
                                                        }</Text>
                                                </View>
                                                :
                                                <></>
                                        }

                                        <View style={{ height: 120 }}></View>
                                    </>
                                }
                            </View>
                        </ScrollView>
                    </>
                    :
                    <View style={{
                        width: '100%',
                        height: '100%',
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                    }}>
                        <Text style={{
                            fontSize: 16,
                            color: Colors.blackColor,
                            fontFamily: 'NunitoSans-SemiBold',
                            textAlign: 'center',
                        }}>{
                                `No active orders found.`
                            }</Text>
                    </View>
            }

        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#ffffff',
        padding: 10,
        paddingTop: 5,
    },
    titleDetail: {
        marginTop: 5,
        justifyContent: 'center',
        alignContent: 'center',
        alignItems: 'center',
    },
    orderTitle: {
        marginTop: 10,
        borderBottomWidth: StyleSheet.hairlineWidth,
        borderBottomColor: Colors.fieldtTxtColor,
    },
    totalContainer: {
        flexDirection: 'row',
        borderTopWidth: StyleSheet.hairlineWidth,
        borderTopColor: Colors.fieldtTxtColor,
        // marginTop: 10,
    },
    description: {
        paddingVertical: 5,
        fontSize: 10,
        fontFamily: "NunitoSans-Bold",
    },
    descriptionReceipt: {
        paddingVertical: 5,
        fontSize: 12,
        fontFamily: "NunitoSans-Bold",
    },
    PromotionTitle: {
        paddingVertical: 5,
        fontSize: 13,
        fontFamily: "NunitoSans-Bold",
        color: Colors.fieldtTxtColor
    },
    PromotionTitleReceipt: {
        paddingVertical: 5,
        fontSize: 10,
        fontFamily: "NunitoSans-Bold",
        color: Colors.fieldtTxtColor
    },
    price: {
        paddingVertical: 5,
        fontSize: 10,
        alignSelf: 'flex-end',
        fontFamily: "NunitoSans-Bold",
    },
    priceReceipt: {
        paddingVertical: 5,
        fontSize: 10,
        alignSelf: 'flex-end',
        fontFamily: "NunitoSans-Bold",
    },
    promotionDescription: {
        paddingVertical: 5,
        fontSize: 13,
        fontFamily: "NunitoSans-Bold",
        color: Colors.fieldtTxtColor,
    },
    promotionDescriptionReceipt: {
        paddingVertical: 2,
        fontSize: 10,
        fontFamily: "NunitoSans-Bold",
        color: Colors.fieldtTxtColor,
    },
    totalPrice: {
        paddingVertical: 5,
        fontSize: 15,
        fontWeight: '700',
        marginTop: 5,
        fontFamily: "NunitoSans-Bold",
    },
    totalPriceReceipt: {
        paddingVertical: 5,
        fontSize: 15,
        fontWeight: '700',
        marginTop: 5,
        fontFamily: "NunitoSans-Bold",
    },
    total: {
        paddingVertical: 5,
        fontSize: 15,
        fontWeight: '700',
        marginTop: 5,
        fontFamily: "NunitoSans-Bold",
    },
    totalReceipt: {
        paddingVertical: 5,
        fontSize: 15,
        fontWeight: '700',
        marginTop: 5,
        fontFamily: "NunitoSans-Bold",
    },

    outletCover: {
        width: "100%",
        alignSelf: 'center',
        height: undefined,
        aspectRatio: 2,
        borderRadius: 5
    },
    infoTab: {
        backgroundColor: Colors.fieldtBgColor,
        padding: 16,
        justifyContent: 'center',
        alignContent: 'center',
        alignItems: 'center'
    },
    workingHourTab: {
        padding: 16,
        flexDirection: 'row'
    },
    outletAddress: {
        textAlign: 'center',
        color: Colors.mainTxtColor
    },
    outletName: {
        fontWeight: 'bold',
        fontSize: 20,
        marginBottom: 10
    },
    logo: {
        width: 100,
        height: 100
    },
    headerLogo: {
        width: Styles.width * 0.8,
        //height: '100%'
        // backgroundColor: 'red',
        position: 'absolute',
        top: '-95%',
        alignSelf: 'center',
    },
    headerLogo1: {
        // width: Styles.width * 0.9,
        width: '100%',
        // backgroundColor: 'red',
        position: 'absolute',
        bottom: '-2%',
        alignSelf: 'center',
    },
    actionTab: {
        flexDirection: 'row',
        marginTop: 20
    },
    actionView: {
        width: Styles.width / 4,
        height: Styles.width / 4,
        justifyContent: 'center',
        alignItems: 'center',
        alignContent: 'center'
    },
    actionBtn: {
        borderRadius: 50,
        width: 70,
        height: 70,
        borderColor: Colors.secondaryColor,
        borderWidth: StyleSheet.hairlineWidth,
        justifyContent: 'center',
        alignItems: 'center'
    },
    actionText: {
        fontSize: 12,
        marginTop: 10
    },
    modalContainer: {
        flex: 1,
        backgroundColor: Colors.modalBgColor,
        alignItems: 'center',
        justifyContent: 'center'
    },
    modalView: {
        height: Styles.width * 0.7,
        width: Styles.width * 0.8,
        backgroundColor: Colors.whiteColor,
        borderRadius: 12,
        padding: Styles.width * 0.03,
        //alignItems: 'center',
        //justifyContent: 'center'
    },
    modalContainerReceipt: {
        flex: 1,
        backgroundColor: Colors.modalBgColor,
        alignItems: 'center',
        justifyContent: 'center'
    },
    modalViewReceipt: {
        //height: Styles.height * 0.9,
        width: Styles.width * 0.85,
        backgroundColor: Colors.whiteColor,
        borderRadius: 12,
        //padding: Styles.width * 0.03,
        alignItems: 'center',
        justifyContent: 'center'
    },
    modalView1: {
        //height: Styles.width * 1,
        width: Styles.width * 0.85,
        backgroundColor: Colors.whiteColor,
        borderRadius: 12,
        padding: Styles.width * 0.03,
        //alignItems: 'center',
        //justifyContent: 'center'
    },
    closeButton: {
        position: 'absolute',
        right: Styles.width * 0.02,
        top: Styles.width * 0.02,

        elevation: 1000,
        zIndex: 1000,
    },
    textInput: {
        height: 150,
        paddingHorizontal: 5,
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 5,
        marginTop: 10,
        textAlignVertical: "top",
        fontSize: 13
    },
    starStyle: {
        width: 35,
        height: 35,
    }
});

export default OrderDetailScreen;
