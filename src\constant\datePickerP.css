.react-datepicker-wrapper,
.react-datepicker__input-container,
.react-datepicker-ignore-onclickoutside,
.react-datepicker__input-container input {
  border-width: 0 !important;

  border: none;
  font-family: "NunitoSans-Regular";
  border-radius: 10;
  border-width: 1px;
  height: 35px;
  padding-left: 0px;
  z-index: 1000;
}

.react-datepicker-wrapper input {
  border: black;
  border-width: 1px;
  /* min-width: none; */
  /* width: 29.5vw; */
  width: 99%;
  padding-left: 20px;
  background-color: #F7F7F7;
  z-index: 1000;
}

.react-datepicker__day--selected {
  background-color: #4E9F7D !important;
  /* border-top: 0px;
  cursor: pointer;
  text-align: center;
  font-weight: bold;
  padding: 5px 0;
  clear: left; */
}

.react-datepicker__header {
  background-color: #4E9F7D !important;
  /* text-align: center;
  border-bottom: 0px;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  padding-top: 8px;
  position: relative; */

}