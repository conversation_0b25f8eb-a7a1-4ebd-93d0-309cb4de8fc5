const urlBase64ToUint8Array = base64String => {
  const padding = '='.repeat((4 - (base64String.length % 4)) % 4);
  const base64 = (base64String + padding)
    .replace(/\-/g, '+')
    .replace(/_/g, '/');

  const rawData = atob(base64);
  const outputArray = new Uint8Array(rawData.length);

  for (let i = 0; i < rawData.length; ++i) {
    outputArray[i] = rawData.charCodeAt(i);
  }

  return outputArray;
}

// const saveSubscription = async (subscription) => {
//   const response = await fetch('http://localhost:3000/save-subscription', {
//       method: 'post',
//       headers: { 'Content-type': "application/json" },
//       body: JSON.stringify(subscription)
//   })

//   return response.json()
// }

self.addEventListener('install', e => {
  console.log('sw.js install event!');
  console.log(e.type);

  self.skipWaiting() // always activate updated SW immediately
});

self.addEventListener('message', e => {
  console.log('sw.js message event!');

  console.log(e.type, e.data);
})

self.addEventListener("activate", async (e) => {
  console.log('sw.js activate event!');

  const subscription = await self.registration.pushManager.subscribe({
    userVisibleOnly: true,
    applicationServerKey: urlBase64ToUint8Array('BNbBBLIJ1JuSSQJJDelk0cdhXfC-Xr6Qr9VRODG1kUSO4re5oHYM5TJajl2zidaAAqUUOlsvXeVquxrOSeROVyk')
  })

  // window.swSubscription = subscription;

  self.clients.matchAll().then(clients => {
    clients.forEach(client => client.postMessage({
      type: 'sw',
      swSubscription: JSON.stringify(subscription),
    }));
  });

  // const response = await saveSubscription(subscription)
  // console.log(response)
});

self.addEventListener("push", e => {
  console.log('sw.js push event!');
  console.log(e);

  try {
    const payload = e.data.text();

    console.log(payload);

    const data = JSON.parse(payload);

    self.registration.showNotification(data.title, {
      body: data.msg,
    });
  }
  catch (ex) {
    console.error(ex);
  }
});

self.addEventListener('pushsubscriptionchange', function(event) {
  event.waitUntil(
    self.clients.matchAll().then(clients => {
      clients.forEach(client => client.postMessage({
        type: 'swc',
        swSubscription: JSON.stringify(event.newSubscription),
      }));
    })
  );
});

self.addEventListener('notificationclick', function(event) {
  event.notification.close();

  const notificationData = event.notification.data;
  let url = '/';

  const urlRegex = /(https?:\/\/[^\s]+)/g;
  const bodyText = event.notification.body || '';
  const matches = bodyText.match(urlRegex);
  
  if (matches && matches.length > 0) {
    url = matches[0];
  }
  
  // try {
  //   const data = JSON.parse(notificationData);
  //   if (data && data.url) {
  //       url = data.url;
  //   }
  // } catch (err) {
  //   console.error('Error parsing notification data:', err);
  // }

  event.waitUntil(
    self.clients.matchAll({
      type: 'window',
      includeUncontrolled: true
    })
    .then(function(clientList) {
      for (let i = 0; i < clientList.length; i++) {
          const client = clientList[i];
          if (client.url === url && 'focus' in client) {
              return client.focus();
          }
      }
      if (self.clients.openWindow) {
          return self.clients.openWindow(url);
      }
    })
  );
});
