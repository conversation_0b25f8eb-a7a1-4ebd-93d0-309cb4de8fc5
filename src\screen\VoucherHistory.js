import React, { Component, useReducer, useState, useEffect, useRef } from 'react';
import {
  StyleSheet,
  ScrollView,
  Image,
  View,
  Text,
  TouchableOpacity,
  TextInput,
  FlatList,
  RefreshControl,
  Alert,
  Modal,
  Dimensions,
  Platform,
  ActivityIndicator,
} from 'react-native';
import Colors from '../constant/Colors';
import Styles from '../constant/Styles';
import ApiClient from '../util/ApiClient';
import API from '../constant/API';
import Ionicons from "react-native-vector-icons/Ionicons";
import AsyncImage from '../components/asyncImage';
import Icon from 'react-native-vector-icons/Ionicons';
import Icons from 'react-native-vector-icons/Feather';
// import { color } from 'react-native-reanimated';
//import QRCode from 'react-native-qrcode-svg';
import Close from "react-native-vector-icons/AntDesign";
import { CommonStore } from '../store/commonStore';
import { UserStore } from '../store/userStore';
import moment from 'moment';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
/////////////////////// Test for search bar ///////////////////////
import MaterialIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import { MERCHANT_VOUCHER_CODE_FORMAT } from '../constant/common';
import { Collections } from '../constant/firebase';
//import firestore from '@react-native-firebase/firestore';
import { useScrollToTop } from '@react-navigation/native';
import { removeOrdinalFromDate, getOrdinalFromDate, subText } from '../util/commonFuncs';
// import firebase from 'firebase';
import { getFirestore, collection, query, where, getDocs, limit } from "firebase/firestore";
import { signInAnonymously } from "firebase/auth";
import { useFocusEffect, useLinkTo } from "@react-navigation/native";
import { prefix } from "../constant/env";

/**
 * VoucherScreen
 * function
 * *display list of available vouchers
 * *redeem voucher
 */

const VoucherHistory = props => {
  const {
    navigation,
    route,
  } = props;

  var pageFromParam = null;
  if (route && route.params) {
    pageFromParam = route.params.pageFrom;
  }
  const linkTo = useLinkTo();

  const [loadingVoucher, setLoadingVoucher] = useState(false);
  const [cartItem, setCartItem] = useState([]);
  const [refreshing, setRefreshing] = useState(false);
  const [visible, setVisible] = useState(false);
  const [visible1, setVisible1] = useState(false);
  const [value, setValue] = useState('');
  const [voucher, setVoucher] = useState([]);
  const [qrCodeModalVisibility, setQrCodeModalVisibility] = useState(false);
  const [selectedItem, setSelectedItem] = useState({});
  const [pageFrom, setPageFrom] = useState(pageFromParam);

  const [merchantVoucherLoadingDict, setMerchantVoucherLoadingDict] = useState({});

  const setState = () => { };

  const selectedOutlet = CommonStore.useState(s => s.selectedOutlet);

  const merchantVouchersDict = CommonStore.useState(s => s.merchantVouchersDict);
  const voucherIdValidList = UserStore.useState(s => s.voucherIdValidList);
  const voucherIdRedemptionList = UserStore.useState(s => s.voucherIdRedemptionList);

  const selectedVoucher = CommonStore.useState(s => s.selectedVoucher);
  const isLoading = CommonStore.useState(s => s.isLoading);

  const firebaseUid = UserStore.useState(s => s.firebaseUid);

  const [searchText, setSearchText] = useState('');
  const [searchList, setSearchList] = useState(false);
  const [list, setList] = useState(true);

  const [redeemVoucherList, setRedeemVoucherList] = useState([])

  const ref = React.useRef(null);

  useScrollToTop(ref);

  navigation.setOptions({
    headerLeft: () => (
      <TouchableOpacity
        style={{
          opacity: 1,
        }}
        onPress={() => {
          linkTo && linkTo(`${prefix}/outlet/voucher-list`);
        }}
      >
        <View
          style={{
            marginLeft: 10,
            display: "flex",
            flexDirection: "row",
            alignItems: "center",
            justifyContent: "flex-start",
          }}
        >
          <Ionicons
            name="chevron-back"
            size={26}
            color={Colors.fieldtTxtColor}
            style={{}}
          />

          <Text
            style={{
              color: Colors.fieldtTxtColor,
              fontSize: 16,
              textAlign: "center",
              fontFamily: "NunitoSans-Regular",
              lineHeight: 22,
              marginTop: -1,
            }}
          >
            Back
          </Text>
        </View>
      </TouchableOpacity>
    ),
    headerRight: () => (
      <></>
    ),
    headerTitle: () => (
      <View
        style={{
          justifyContent: "center",
          alignItems: "center",
          bottom: -1,
        }}
      >
        <Text
          style={{
            fontSize: 20,
            lineHeight: 25,
            textAlign: "center",
            fontFamily: "NunitoSans-Bold",
            color: Colors.mainTxtColor,
          }}
        >
          Voucher
        </Text>
      </View>
    ),
  });

  useEffect(() => {
    var voucherListTemp = [];

    for (var i = 0; i < voucherIdRedemptionList.length; i++) {
      const record = voucherIdRedemptionList[i];

      // to-do: check for start/end date/time, min/max spend

      const merchantVoucher = merchantVouchersDict[record.voucherId];

      if (
        merchantVoucher
        //&& merchantVoucher.outletIdList.includes(selectedOutlet.uniqueId)
      ) {
        // check date

        if (
          moment().isSameOrAfter(merchantVoucher.startDate) &&
          moment().isBefore(merchantVoucher.endDate)
        ) {
          var voucherLogoSource = {
            uri: '',
          };

          if (merchantVouchersDict[record.voucherId]) {
            if (merchantVouchersDict[record.voucherId].customLogo) {
              voucherLogoSource = {
                uri: merchantVouchersDict[record.voucherId].customLogo,
              };
            } else if (merchantVouchersDict[record.voucherId].merchantLogo) {
              voucherLogoSource = {
                uri: merchantVouchersDict[record.voucherId].merchantLogo,
              };
            } else {
              voucherLogoSource = require('../asset/image/extend.png');
            }

            var merchantName = merchantVouchersDict[record.voucherId].merchantName;
            var startDate = merchantVouchersDict[record.voucherId].startDate;
            var endDate = merchantVouchersDict[record.voucherId].endDate;

          }

          voucherListTemp.push({
            label:
              (merchantVouchersDict[record.voucherId]
                ? merchantVouchersDict[record.voucherId].title
                : '') + ` [${record.code}]`,
            value: record.voucherId,
            code: record.code,
            usage: record.usageDate,
            img: voucherLogoSource,
            name: merchantName,
            validStartDate: startDate,
            validEndDate: endDate
          });
        }
      }

    }

    setRedeemVoucherList(voucherListTemp);
  }, [voucherIdRedemptionList, merchantVouchersDict]);

  const time = () => {
    if (voucher && voucher.startAt != null) {
      if (voucher.endAt != null) {
        return voucher.startAt + ' - ' + voucher.endAt;
      } else {
        return voucher.startAt;
      }
    } else {
      return 'Unlimited';
    }
  }

  const redeemVoucher = async item => {
    if (item.quantity > 0) {
      CommonStore.update(s => {
        s.isLoading = true;
      });

      setMerchantVoucherLoadingDict({
        ...merchantVoucherLoadingDict,
        [item.uniqueId]: true,
      });

      const body = {
        userId: firebaseUid,
        voucherId: item.uniqueId,
      };

      ApiClient.POST(API.redeemMerchantVoucher, body).then(async (result) => {
        if (result && result.status === 'success') {
          // get the latest one without delay

          // const merchantVoucherSnapshot = await firebase.firestore().collection(Collections.MerchantVoucher)
          //   .where('uniqueId', '==', item.uniqueId)
          //   .limit(1)
          //   .get();

          const merchantVoucherSnapshot = await getDocs(
            query(
              collection(global.db, Collections.MerchantVoucher),
              where('uniqueId', '==', item.uniqueId),
              limit(1),
            )
          );

          if (!merchantVoucherSnapshot.empty) {
            const record = merchantVoucherSnapshot.docs[0].data();

            CommonStore.update(s => {
              s.selectedVoucher = record;
              s.isLoading = false;
            });

            setMerchantVoucherLoadingDict({
              ...merchantVoucherLoadingDict,
              [item.uniqueId]: false,
            });

            props.navigation.navigate("VoucherDetails", { merchantVouchersDict: record });
          }
        }
        else {
          Alert.alert('Error', 'Sorry, please try again later.');

          CommonStore.update(s => {
            s.isLoading = false;
          });
        }
      });
    }
    else {
      CommonStore.update(s => {
        s.selectedVoucher = item;
      });

      props.navigation.navigate("VoucherDetails", { merchantVouchersDict: item });
    }
  };

  const isCloseToBottom = ({ layoutMeasurement, contentOffset, contentSize }) => {
    const paddingToBottom = 30;
    return layoutMeasurement.height + contentOffset.y >=
      contentSize.height - paddingToBottom;
  };

  var voucherDetailsFontSize = Platform.OS == 'ios' ? 13 : 15;

  if (Dimensions.get('screen').width <= 360) {
    voucherDetailsFontSize = Platform.OS == 'ios' ? 8 : 10;
    //console.log(Dimensions.get('screen').width)
  }

  const fontSizeCondition = {
    fontSize: voucherDetailsFontSize,
  };

  var tAndcFont = Platform.OS == 'ios' ? 11 : 12;

  if (Dimensions.get('screen').width <= 360) {
    tAndcFont = Platform.OS == 'ios' ? 8.5 : 9.5;
    //console.log(Dimensions.get('screen').width)
  }

  const tAndcFontCondition = {
    fontSize: tAndcFont,
  };



  if (Dimensions.get('screen').width <= 360) {
    var redeemStyleWidth = 80;
    //console.log(Dimensions.get('screen').width)
  }

  const redeemWidthStyling = {
    width: redeemStyleWidth,
  };


  if (Dimensions.get('screen').width <= 360) {
    var redeemStyleHeight = 40;
    //console.log(Dimensions.get('screen').width)
  }

  const redeemHeightStyling = {
    height: redeemStyleHeight,
  };


  const renderItemRedeemed = ({ item, index }) => {
    return (
      <View
        style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          // backgroundColor: 'red',
          height: '100%',
        }}>
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
          }}>
          { }
          <Image
            source={item.img}
            style={{
              marginRight: 10,
              width: 32,
              height: 32,
              borderRadius: 8,
              top: 1,
            }}
          />

          <Text
            style={{
              // paddingVertical: 14
              fontSize: 13,
              fontFamily: 'NunitoSans-Regular',
              color: Colors.fieldtTxtColor,
              width: '80%',
            }}>
            {item.label}
          </Text>
        </View>
      </View>
    )
  }


  // function end
  const renderItem = ({ item }) => {



    return (
      <View>
        <View style={[styles.card, { backgroundColor: item.usage !== null ? Colors.tabRed : '#416D5C' }]}>
          <View style={styles.cornerleft}></View>
          <View style={{ marginRight: 10, flex: 2 }}>
            <View style={styles.pic}>
              <Image
                source={item.img}
                style={{ width: 90, height: Platform.OS == 'ios' ? 90 : 90, borderRadius: 10 }}
              />
            </View>
          </View>
          <View
            style={{
              flex: 3,
              alignContent: 'center',
              alignItems: 'center',
              justifyContent: 'center',
            }}>
            <View style={{
              // alignSelf: 'flex-end',
              marginRight: '12%',
              // backgroundColor: 'red',
              // paddingLeft: 4,
            }}>
              <Text
                numberOfLines={2}
                style={[styles.title, Platform.OS === 'ios' ? {
                  fontSize: 22,
                  marginRight: '-15%',
                } : {
                  // backgroundColor: 'red',
                  paddingLeft: 0,
                  marginLeft: '0%',
                  fontSize: 20,
                }]}>
                {/* {item.GiftCard == null ? null : (item.GiftCard.merchantId !== 'legacy' ? item.GiftCard.title : item.GiftCard.description)} */}
                {item.label}
              </Text>
            </View>

            <View style={{
              alignSelf: 'flex-end',
              marginRight: Platform.OS == 'ios' ? '5%' : ' 12%',
              // marginTop: -5,
            }}>
              <Text
                style={{
                  color: Colors.whiteColor,
                  fontSize: Platform.OS == 'ios' ? 15 : 15,
                  fontFamily: 'NunitoSans-Regular',
                }}>
                {item.name}
              </Text>
            </View>
          </View>
          <View style={styles.cornerleft1}></View>
        </View>
        <View style={[styles.card1, {
          // backgroundColor: 'red',
        }]}>
          <View style={styles.cornerright}></View>

          <View
            style={{
              marginRight: 10,
              flex: 4,
              alignItems: 'flex-start',
              justifyContent: 'center',

              marginLeft: 10,
              marginTop: -10,
            }}>
            <Text style={styles.title1}>Valid Period</Text>
            {/* {
              subText(
                  removeOrdinalFromDate(
                  {
                    start: item.startDate,
                    end: item.endDate
                  }
                ),
                getOrdinalFromDate(
                  {
                    start: item.startDate,
                    end: item.endDate
                  }
                )
              )}         */}
            <Text style={[styles.title2, fontSizeCondition]}>
              {moment(item.validStartDate).format('Do MMMM')} to {moment(item.validEndDate).format('Do MMMM')}
            </Text>

            <Text style={[styles.title3, tAndcFontCondition]}>Terms & Conditions Applied</Text>

          </View>
          <View
            style={[redeemHeightStyling, {
              //flex: 2,
              width: Dimensions.get('screen').width * 0.25,
              //alignContent: 'center',
              alignItems: 'center',
              paddingTop: Dimensions.get('screen').width * 0.1,
              //justifyContent: 'center',
            }]}>
            <View
              style={{
                borderRadius: 10,
                backgroundColor: item.usage !== null ? Colors.tabRed : Colors.primaryColor,
                justifyContent: 'center',
                alignItems: 'center',
                width: Dimensions.get('screen').width * 0.2,
                paddingVertical: 2,
                paddingHorizontal: 5,
                marginRight: 5,
                // flexDirection: 'row',
              }}>
              <Text style={{
                color: Colors.whiteColor,
                fontSize: 14,
                fontFamily: 'NunitoSans-Regular'
              }}>
                {item.usage !== null ? 'Used' : 'Available'}
              </Text>
            </View>
          </View>
          <View style={styles.cornerright1}></View>

          <View style={{
            position: 'absolute',
            // backgroundColor: 'red',
            bottom: '2%',
            left: '1%',
            width: '98%',
            height: '80%',

            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }}>
            <Image
              source={require('../asset/image/voucher-border.png')}
              resizeMode={'stretch'}
              style={{
                width: '100%',
                height: '100%',
              }}
            />
          </View>
        </View>
      </View>
    );
  }
  // function end

  return (
    <>

      <View style={{
        flexDirection: 'row',
        alignItems: 'center',
        // marginLeft: 2,
        // marginRight: 2,
        marginTop: 10,
        marginBottom: 10,
        marginLeft: 5,
      }}>
        <Icon name="search" size={20} color={Colors.primaryColor} style={{
          position: 'absolute',
          left: 16,
          zIndex: 10,
        }} />

        <TextInput
          underlineColorAndroid={Colors.fieldtBgColor}
          clearButtonMode='while-editing'
          placeholder="search voucher"
          style={styles.textInput}
          onChangeText={text => {
            setSearchText(text);

          }}
          value={searchText}
        />

      </View>


      {/*************************** Finished for search bar ***************************/}

      {redeemVoucherList && redeemVoucherList.length > 0 &&
        <View style={{ flex: 1 }}>
          <View style={styles.container1}>
            <View style={styles.container}>
              <ScrollView
                ref={ref}>
                <FlatList
                  style={{ marginBottom: 10 }}
                  data={redeemVoucherList.filter(param => {
                    if (searchText !== '') {
                      const searchLowerCase = searchText.toLowerCase();

                      if (param.label.toLowerCase().includes(searchLowerCase)) {
                        return true;
                      }
                      else if (param.name.toLowerCase().includes(searchLowerCase)) {
                        return true;
                      }
                      else if ((moment(param.validStartDate).format('Do MMMM')).toString().toLowerCase().includes(searchLowerCase)) {
                        return true;
                      }
                      else if ((moment(param.validEndDate).format('Do MMMM')).toString().toLowerCase().includes(searchLowerCase)) {
                        return true;
                      }
                      return false;
                    }
                    else {
                      return true;
                    }
                  })}
                  renderItem={renderItem}
                  keyExtractor={(param, index) => String(index)}
                />

                <View style={{ height: 180 }}></View>
              </ScrollView>
            </View>
          </View>
        </View>
      }
    </>
  );
}


const styles = StyleSheet.create({
  container1: {
    backgroundColor: '#ffffff',
    // backgroundColor: 'red',
    height: Dimensions.get('window').height,
  },
  container: {
    backgroundColor: '#ffffff',
    padding: 16,
  },
  searchBar: {
    marginHorizontal: 16,
    backgroundColor: Colors.fieldtBgColor,
    flexDirection: 'row',
    padding: 12,
    borderRadius: 10,
    alignContent: 'center',
  },
  searchBarText: {
    color: Colors.fieldtTxtColor,
    marginLeft: 10,
  },
  card: {
    flex: 1,
    minWidth: Styles.width - 64,
    minHeight: 100,
    // backgroundColor: Colors.primaryColor,
    flexDirection: 'row',
    marginTop: 20,
    alignContent: 'center',
    alignItems: 'center',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  card1: {
    flex: 1,
    minWidth: Styles.width - 64,
    minHeight: 100,
    backgroundColor: Colors.fieldtBgColor,
    flexDirection: 'row',
    marginBottom: 20,
    alignContent: 'center',
    alignItems: 'center',
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
    marginBottom: 10,
  },
  cornerleft: {
    backgroundColor: 'white',
    height: 30,
    width: 30,
    alignSelf: 'flex-start',
    marginTop: '22%',
    borderTopRightRadius: 50,
  },
  cornerright: {
    backgroundColor: 'white',
    height: 30,
    width: 30,
    alignSelf: 'flex-start',
    borderBottomRightRadius: 50,

    // borderStyle: 'dashed',
    // borderWidth: 1,
    // borderColor: 'black',
  },

  cornerleft1: {
    backgroundColor: 'white',
    height: 30,
    width: 30,
    alignSelf: 'flex-start',
    marginTop: '22%',
    borderTopLeftRadius: 50,
  },
  cornerright1: {
    backgroundColor: 'white',
    height: 30,
    width: 30,
    alignSelf: 'flex-start',
    borderBottomLeftRadius: 50,
  },
  text: {
    fontSize: 13,
    color: Colors.fieldtTxtColor,
    fontFamily: 'NunitoSans-Bold'
  },
  title: {
    color: Colors.whiteColor,
    // fontSize: Platform.OS == 'ios' ? 30 : 20,
    fontSize: Platform.OS == 'ios' ? 30 : 22,
    fontFamily: 'NunitoSans-SemiBold',
    marginLeft: '1%',
    // marginTop: 10,
    marginBottom: 5,
    textAlign: 'right',
  },
  title1: {
    color: Colors.blackColor,
    fontSize: 16,
    fontFamily: 'NunitoSans-Bold'
  },
  title2: {
    color: Colors.primaryColor,
    fontFamily: 'NunitoSans-SemiBold',
    // fontWeight: 'bold',
    marginBottom: 8,
  },
  title3: {
    color: Colors.descriptionColor,
    fontFamily: 'NunitoSans-SemiBold',
    // fontWeight: 'bold',
  },
  viewContainer: {
    marginTop: 10,
    justifyContent: 'space-between',
    flexDirection: 'row',
    flex: 10,
  },
  total: {
    fontSize: 14,
    color: Colors.blackColor,
    fontFamily: 'NunitoSans-Bold'
  },
  price: {
    fontSize: 14,
    color: Colors.primaryColor,
    fontFamily: "NunitoSans-Regular",
  },
  totalBorder: {
    paddingTop: 5,
    justifyContent: 'space-between',
    flex: 1,
    flexDirection: 'row',
    borderTopWidth: StyleSheet.hairlineWidth,
    borderColor: Colors.blackColor,
    borderRadius: 1,
  },
  pic: {
    backgroundColor: Colors.secondaryColor,
    width: 90,
    height: 90,
    borderRadius: 10,
    alignSelf: 'center',
  },
  searchBar1: {
    backgroundColor: Colors.fieldtBgColor,
    flexDirection: 'row',
    padding: 12,
    borderRadius: 10,
    alignContent: 'center',
    marginBottom: '2%',
  },

  centerTextHolder: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },

  textInput: {
    height: 45,
    paddingHorizontal: 50,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 10,

    color: Colors.fieldtTxtColor,
    fontFamily: "NunitoSans-Regular",
    fontSize: 14,
    lineHeight: 20,

    // backgroundColor: 'blue',
    width: '98%',
  },
});

export default VoucherHistory;
