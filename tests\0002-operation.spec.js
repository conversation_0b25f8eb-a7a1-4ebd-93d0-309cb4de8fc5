const { test, expect } = require('@playwright/test');

test('Add 1 item with variant and addon to Cart Test', async ({ page, isMobile }) => {
    // await page.setViewportSize({ width: 1600, height: 900 }); //modify to adjust window size
    await page.goto('http://localhost:5400/web/outlet/hominsanttdi/takeaway');

    await page.waitForTimeout(3000);
    if (isMobile) {
        await page.evaluate(() => { window.scrollBy(0, 100); });
    }
    else {
        await page.mouse.wheel(0, 100);
    }

    await page.waitForTimeout(500);

    await page.getByTestId('startOrdering').click();
    // await page.getByTestId('closeButton').click();

    await page.waitForTimeout(500);
    await page.getByTestId('categoryName-1').click();

    await page.waitForTimeout(500);
    if (isMobile) {
        await page.evaluate(() => { window.scrollBy(0, 100); });
    }
    else {
        await page.mouse.wheel(0, 100);
    }

    await page.waitForTimeout(500);
    await page.getByTestId('productName-1').click();

    await page.waitForTimeout(500);
    await page.getByTestId('0-variantName-1').click();

    await page.waitForTimeout(500);
    await page.getByTestId('2-variantName-1').click();

    await page.waitForTimeout(500);
    await page.getByTestId('addOnPlus-0').click();

    await page.waitForTimeout(500);
    await page.getByTestId('addToCart').click();

    await page.waitForTimeout(500);
    await page.getByTestId('cartIcon').click();

    // await page.mouse.wheel(0, 700);

    // await page.waitForTimeout(500);
    // await page.getByTestId('cartPaymentMethodDropdown').click();

    // await page.waitForTimeout(500);
    // await page.getByTestId('Pay Later').click();

    // await page.waitForTimeout(500);
    // await page.getByTestId('cartPlaceOrder').click();

});