import {
  useState,
  useEffect,
} from "react";
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  Image,
  Animated,
  ScrollView,
  Linking,
  useWindowDimensions
} from "react-native";
import {
  isMobile as useIsMobile,
} from '../util/commonFuncs';
import { CommonStore } from "../store/commonStore";
import Colors from '../constant/Colors';
import { TempStore } from "../store/tempStore";
import imgLogo from "../asset/image/logo.png";
import { ReactComponent as Foodturkey } from "../asset/svg/Food-turkey.svg"
import FontAwesome from "react-native-vector-icons/FontAwesome";
import Entypo from "react-native-vector-icons/Entypo";
import AntDesign from "react-native-vector-icons/AntDesign";
import Toastify from 'toastify-js';
import "toastify-js/src/toastify.css";
import { CheckBox } from "react-native-web";

const ShowGreetingPopupModal = () => {
  const {
    width: windowWidth,
    height: windowHeight,
  } = useWindowDimensions();
  const isMobile = useIsMobile();
  const showGreetingPopup = TempStore.useState((s) => s.showGreetingPopup);

  const selectedOutlet = CommonStore.useState(s => s.selectedOutlet);
  const tickboxConsent = TempStore.useState(s => s.tickboxConsent);
  const showStartAsGuestButton = TempStore.useState((s) => s.showStartAsGuestButton);

  const [images, setImages] = useState(['', '', '']);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [opacity, setOpacity] = useState(new Animated.Value(1));

  // Load Images
  useEffect(() => {
    const loadImages = async () => {
      const modules = await Promise.all([
        import('../asset/image/img1.png'),
        import('../asset/image/img2.png'),
        import('../asset/image/img3.png'),
      ]);
      setImages(modules.map(m => m.default).filter(Boolean));
    };

    loadImages();
  }, []);

  useEffect(() => {
    const interval = setInterval(() => {
      Animated.timing(opacity, { toValue: 0, duration: 1000, useNativeDriver: true }).start(() => {
        setCurrentIndex(prev => (prev + 1) % images.length);
        Animated.timing(opacity, { toValue: 1, duration: 1000, useNativeDriver: true }).start();
      });
    }, 5000);
    return () => clearInterval(interval);
  }, [images.length]);
  
  ////////////////////////////////////////////////////////////

  if (!showGreetingPopup) {
    return null;
  };

  return (
    <Modal
      style={{
        width: windowWidth,
        height: windowHeight,
      }}
      visible={true}
      transparent={true}
    >
      <View style={{
        width: windowWidth,
        height: windowHeight,

        backgroundColor: isMobile ? Colors.whiteColor : Colors.modalBgColor,
        alignItems: "center",
        justifyContent: "center",
      }}>
        <View
          style={{
            width: windowWidth * 0.25,
            height: windowHeight * 0.8,
            backgroundColor: Colors.whiteColor,
            borderRadius: 8,
            padding: isMobile
              ? windowWidth * 0.03
              : windowWidth * 0.01,
            alignItems: "center",
            justifyContent: "space-between",
            paddingTop: isMobile
              ? windowWidth * 0.05
              : windowWidth * 0.01,
            paddingBottom: isMobile
              ? windowWidth * 0.05
              : windowWidth * 0.01,
            ...isMobile && {
              width: windowWidth,
              height: windowHeight,
            }
          }}
        >
          <ScrollView
            bounces={false}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{
              width: windowWidth * 0.25,
              paddingHorizontal: isMobile
                ? windowWidth * 0.03
                : windowWidth * 0.01,
              alignItems: "center",
              justifyContent: "space-between",
              ...isMobile && {
                width: '100%',
                height: '100%',
              },
            }}>
            <TouchableOpacity
              testID="closeButton"
              style={{
                position: 'absolute',
                elevation: 1000,
                zIndex: 1000,
                right: windowWidth * (isMobile ? 0.04 : 0.01),
                top: windowWidth * (isMobile ? 0.04 : 0.01),
              }}
              onPress={async () => {
                TempStore.update(s => {
                  s.showGreetingPopup = false;
                });

                CommonStore.update(s => {
                  s.currPageIframe = 'OutletMenu';
                });

                setTimeout(() => {
                  if (global.toShowSignUpMemberModal) {
                    TempStore.update(s => {
                      s.showSignUpMember = true;
                    });
                  }
                }, 3000);
              }}
            >
              <AntDesign
                name="closecircle"
                size={25}
                color={Colors.fieldtTxtColor}
              />
            </TouchableOpacity>

            <View style={{ alignItems: 'center', justifyContent: 'center', height: isMobile ? windowHeight * 0.5 : windowHeight * 0.4, width: "100%", }}>
              <Image
                style={{
                  width: windowWidth * 0.15,
                  height: windowHeight * 0.15,
                  alignSelf: "center",
                  marginBottom: 0,
                  ...isMobile && {
                    width: windowWidth * 0.5,
                    height: windowHeight * 0.15,
                  }
                }}
                resizeMode="contain"
                source={imgLogo}
              />
              <Animated.Image
                style={{
                  width: windowWidth * 0.25,
                  height: windowHeight * 0.25,
                  alignSelf: 'center',
                  marginBottom: 0,
                  opacity,
                  ...isMobile && {
                    width: windowWidth * 0.8,
                    height: windowHeight * 0.35,
                  },
                }}
                resizeMode="contain"
                source={images[currentIndex]}
              />
            </View>

            <View
              style={[
                {
                  alignItems: "center",
                },
                {
                  // backgroundColor: 'red'
                },
                isMobile ? {
                  marginTop: 0,
                } : {

                },
              ]}
            >
              {/* <Text style={[{
                fontFamily: "NunitoSans-Bold",
                // marginBottom: 10,
                fontSize: 20,
              }, {
                textAlign: 'center',
                marginTop: isMobile ? 0 : 10,
              }, isMobile ? {
                // width: '80%',
                // backgroundColor: 'red',
              } : {

              }]}>
                {
                  'Enter name/number to receive promotional products in the future'
                }
              </Text> */}
            </View>

            <View style={{ backgroundColor: Colors.highlightColor, width: '100%', borderRadius: 15, }}>
              <View
                style={[
                  {
                    // flex: 0.8,
                    alignItems: "center",
                    justifyContent: "center",
                  },
                  {
                    width: "95%",
                    alignItems: "center",
                    // alignItems: 'flex-start',
                    justifyContent: "flex-start",

                    paddingTop: 25,
                  },
                ]}
              >
                <Text style={{
                  fontFamily: 'NunitoSans-Bold',
                  fontSize: 20,
                  paddingBottom: 5,

                  textAlign: 'center',
                }}>
                  {'How to Order'}
                </Text>
                <Text style={{
                  fontFamily: 'NunitoSans-SemiBold',
                  fontSize: 17,
                  // paddingBottom: 5,

                  textAlign: 'center',
                }}>
                  {'Ordering food has never been so easy'}
                </Text>
              </View>

              <View
                style={{
                  alignItems: "center",
                  flexDirection: "row",
                  justifyContent: "space-around",
                  width: "100%",
                  paddingTop: 25,
                  paddingBottom: 25,
                }}
              >
                <View style={{ justifyContent: 'center', alignItems: 'center' }}>
                  <View style={{ height: 45 }}>
                    <FontAwesome name="cart-plus" size={40} />
                  </View>
                  <Text style={{
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: 17,
                    marginVertical: 5,

                    textAlign: 'center',
                  }}>
                    {'Add to cart'}
                  </Text>
                </View>
                <View style={{ justifyContent: 'center', alignItems: 'center' }}>
                  <View style={{ height: 45 }}>
                    <Entypo name="login" size={40} />
                  </View>
                  <Text style={{
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: 17,
                    marginVertical: 5,

                    textAlign: 'center',
                  }}>
                    {'Auto login'}
                  </Text>
                </View>
                <View style={{ justifyContent: 'center', alignItems: 'center' }}>
                  <Foodturkey width={45} height={45} />
                  <Text style={{
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: 17,
                    marginVertical: 5,

                    textAlign: 'center',
                  }}>
                    {'Place order'}
                  </Text>
                </View>
              </View>
            </View>

            <View
              style={{
                alignItems: "center",
                flexDirection: "row",
                justifyContent: 'center',
                width: "100%",
              }}
            >
              {
                showStartAsGuestButton
                  ?
                  <TouchableOpacity
                    testID="startOrdering"
                    // disabled={isLoading}
                    // disabled={!(!toWaitForQrLoading ? true : (qrGenericOutlet && qrGenericOutlet.uniqueId))}
                    style={[
                      {
                        // width: isMobile
                        //   ? Dimensions.get("window").width * 0.3
                        //   : Dimensions.get("window").width * 0.1,
                        backgroundColor: Colors.fieldtBgColor,
                        // height: 45,
                        height: 60,
                        alignItems: "center",
                        justifyContent: "center",
                        borderRadius: 8,

                        shadowOffset: {
                          width: 0,
                          height: 1,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 1,
                      },
                      {
                        // width: isMobile ? windowWidth * 0.3 : windowWidth * 0.1,
                        marginTop: isMobile ? 10 : windowHeight * 0.04,
                        marginBottom: 10,
                        paddingHorizontal: isMobile ? windowWidth * 0.05 : 25,

                        // width: windowWidth * 0.01,
                        // width: '40%',
                      },
                    ]}
                    onPress={() => {
                      // proceedOrderAfterUserInfo();
                      // registerUser();

                      // if (toWaitForQrLoading && !global.isUpdatedTablePax) {
                      //   updateTablePaxDirectly(
                      //     {
                      //       ...qrGenericParams,
                      //       // outletId: outlet.uniqueId,
                      //       outletId: qrGenericOutlet.uniqueId,
                      //       tableId: global.storedTableId,
                      //       tablePax: qrGenericOutlet.seated,
                      //       tableCode: qrGenericOutlet.code,
                      //     },
                      //     global.selectedOutlet,
                      //   );
                      // }

                      // if (toWaitForQrLoading) {
                      //   // means is generic qr

                      //   if (global.accessToken && qrGenericOutlet && qrGenericOutlet.uniqueId) {
                      //     const body = {
                      //       tableId: qrGenericJson.tableId,
                      //       // pax: seatingPax,
                      //       pax: 1,
                      //       // outletId: currOutlet.uniqueId,
                      //       outletId: qrGenericJson.outletId,
                      //     };

                      //     /////////////////////////////////////////////////

                      //     // go to menu first

                      //     const subdomain = global.subdomainOnly;

                      //     if (!subdomain) {
                      //       global.linkToFunc && global.linkToFunc(`${prefix}/outlet/menu`);
                      //     }
                      //     else {
                      //       global.linkToFunc && global.linkToFunc(`${prefix}/outlet/${subdomain}/menu`);
                      //     }

                      //     TempStore.update(s => {
                      //       s.showGreetingPopup = false;
                      //     });

                      //     /////////////////////////////////////////////////

                      //     ApiClient.POST(API.updateOutletTablePaxByUser, body).then(async (result) => {
                      //       if (result && result.status === 'success') {
                      //         console.log('ok');

                      //         // await AsyncStorage.setItem('latestOutletId', currOutlet.uniqueId);

                      //         CommonStore.update(s => {
                      //           // 2022-10-08 - Reset cart items
                      //           s.cartItems = [];
                      //           s.cartItemsProcessed = [];
                      //           s.cartOutletId = qrGenericOutlet.uniqueId;

                      //           s.selectedOutletItem = {};
                      //           s.selectedOutletItemAddOn = {};
                      //           s.selectedOutletItemAddOnChoice = {};
                      //           s.onUpdatingCartItem = null;

                      //           s.isLoading = false;

                      //           s.molpayResult = null;

                      //           s.payHybridBody = null;
                      //           s.paymentDetails = null;
                      //           s.orderIdCreated = '';
                      //         });

                      //         await updateUserCartGeneric({
                      //           ...qrGenericParams,
                      //           ...qrGenericJson,
                      //         }, qrGenericOutlet, firebaseUid);
                      //       }
                      //     });
                      //   }
                      // }
                      if (tickboxConsent !== true) {
                        Toastify({
                          text: 'Please agree to MyKoodoo Terms of Use and Privacy Policy before continuing.',
                          duration: 3000,
                          newWindow: true,
                          close: false,
                          gravity: "top", // top or bottom
                          position: "center", // left, center or right
                          stopOnFocus: true, // Prevents dismissing of toast on hover
                          style: {
                            background: "linear-gradient(to right, #4E9F7D, #75bd9f)",
                            color: 'white',

                            // marginLeft: '15px !important',
                            // marginRight: '15px !important',
                          },
                          onClick: function () { } // Callback after click
                        }).showToast();
                      } else {
                        TempStore.update(s => {
                          s.showGreetingPopup = false;
                          // s.claimingVoucher = true;
                          // s.showSignUpMember = true;
                        });

                        CommonStore.update(s => {
                          s.currPageIframe = 'OutletMenu';
                        });

                        // global.toShowSignUpMemberModal = true;
                        setTimeout(() => {
                          if (global.toShowSignUpMemberModal) {
                            TempStore.update(s => {
                              s.showSignUpMember = true;
                            });
                          }
                        }, 3000);
                      }
                    }}
                  >
                    <Text
                      style={[
                        {
                          fontFamily: "NunitoSans-SemiBold",
                          fontSize: 16,
                          // color:
                          //   (!toWaitForQrLoading ? true : (qrGenericOutlet && qrGenericOutlet.uniqueId))
                          //     ?
                          //     Colors.primaryColor
                          //     :
                          //     Colors.fieldtTxtColor,
                          color: Colors.primaryColor,

                          textAlign: 'center',
                        },
                      ]}
                    >
                      {'Start Ordering\nAs Guest'}
                    </Text>
                  </TouchableOpacity>
                  :
                  <></>
              }

              {/* 2024-03-25 - start ordering as member button */}

              <TouchableOpacity
                testID="startOrdering"
                // disabled={isLoading}
                // disabled={!(!toWaitForQrLoading ? true : (qrGenericOutlet && qrGenericOutlet.uniqueId))}
                style={[
                  {
                    // width: isMobile
                    //   ? Dimensions.get("window").width * 0.3
                    //   : Dimensions.get("window").width * 0.1,
                    backgroundColor: Colors.fieldtBgColor,
                    // height: 45,
                    height: 60,
                    alignItems: "center",
                    justifyContent: "center",
                    borderRadius: 8,

                    shadowOffset: {
                      width: 0,
                      height: 1,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 1,

                    marginLeft: showStartAsGuestButton ? 30 : 0,
                  },
                  {
                    // width: isMobile ? windowWidth * 0.3 : windowWidth * 0.1,
                    marginTop: isMobile ? 10 : windowHeight * 0.04,
                    marginBottom: 10,
                    paddingHorizontal: isMobile ? windowWidth * 0.05 : 25,

                    // width: windowWidth * 0.01,
                    // width: '40%',
                  },
                ]}
                onPress={() => {
                  if (tickboxConsent !== true) {
                    Toastify({
                      text: 'Please agree to MyKoodoo Terms of Use and Privacy Policy before continuing.',
                      duration: 3000,
                      newWindow: true,
                      close: false,
                      gravity: "top", // top or bottom
                      position: "center", // left, center or right
                      stopOnFocus: true, // Prevents dismissing of toast on hover
                      style: {
                        background: "linear-gradient(to right, #4E9F7D, #75bd9f)",
                        color: 'white',

                        // marginLeft: '15px !important',
                        // marginRight: '15px !important',
                      },
                      onClick: function () { } // Callback after click
                    }).showToast();
                  } else {
                    TempStore.update(s => {
                      s.showGreetingPopup = false;
                      // s.claimingVoucher = true;

                      s.startAsMember = true;
                    });

                    CommonStore.update(s => {
                      s.currPageIframe = 'OutletMenu';
                    });
                  }
                }}
              >
                <Text
                  style={[
                    {
                      fontFamily: "NunitoSans-SemiBold",
                      fontSize: 16,
                      // color:
                      //   (!toWaitForQrLoading ? true : (qrGenericOutlet && qrGenericOutlet.uniqueId))
                      //     ?
                      //     Colors.primaryColor
                      //     :
                      //     Colors.fieldtTxtColor,
                      color: Colors.primaryColor,

                      textAlign: 'center',
                    },
                  ]}
                >
                  {'Start Ordering\nAs Member'}
                </Text>
              </TouchableOpacity>
            </View>

            {(selectedOutlet && selectedOutlet.tickTerms && showStartAsGuestButton) && (
              <View
                style={{
                  margin: 10,
                  flexDirection: "row",
                  justifyContent: 'center',
                  width: "100%",
                }}
              >
                <CheckBox
                  style={{
                    marginVertical: 2,
                  }}
                  value={
                    tickboxConsent
                  }
                  onValueChange={() => {
                    if (tickboxConsent == true) {
                      TempStore.update(s => { s.tickboxConsent = false; })
                    } else {
                      TempStore.update(s => { s.tickboxConsent = true; })
                    }
                  }}
                />

                <Text style={{ paddingHorizontal: 10 }}>
                  {'By logging in, you agree to MyKoodoo '}
                  <Text style={{ color: 'blue', textDecorationLine: 'underline' }}
                    onPress={() => { Linking.openURL('https://mykoodoo.com/terms/') }}>{'Terms of Use'}</Text>
                  {' & '}
                  <Text style={{ color: 'blue', textDecorationLine: 'underline' }}
                    onPress={() => { Linking.openURL('https://mykoodoo.com/privacy-policy/') }}>{'Privacy Policy'}</Text>
                </Text>
              </View>
            )}
          </ScrollView>
        </View>
      </View>
    </Modal>
  );
};

export default ShowGreetingPopupModal;