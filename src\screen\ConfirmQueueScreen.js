import React, { Component } from 'react';
import {
  StyleSheet,
  ScrollView,
  Image,
  View,
  Text,
  TouchableOpacity,
  Alert,
  Dimensions,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import Icons from 'react-native-vector-icons/Feather';
import Colors from '../constant/Colors';
import Styles from '../constant/Styles';
import * as User from '../util/User';
import { UserStore } from '../store/userStore';
import API from '../constant/API';
import ApiClient from '../util/ApiClient';
//import * as Animatable from 'react-native-animatable';
import Entypo from 'react-native-vector-icons/Entypo';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { prefix } from "../constant/env";
import { useFocusEffect, useLinkTo } from "@react-navigation/native";
import { ReactComponent as DR } from "../asset/svg/Direction-run.svg";

/**
 * ComfirmQueueScreen
 * function
 * *display details of the queue for user to confirm
 *
 * route.params
 * *outletData: array of data of the outlet
 * *queueResult: array of data of the queue
 */
class ComfirmQueueScreen extends Component {
  constructor({ navigation, props, route }) {
    const { outletData, queueResult } = route.params;
    super(props);

    navigation.setOptions({
      headerLeft: () => (
        <TouchableOpacity
          style={{}}
          onPress={() => {
            this.props.navigation.navigate("Outlet - KooDoo Web Order");
          }}>
          <View
            style={{
              marginLeft: 10,
              display: 'flex',
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'flex-start',
            }}>
            <Ionicons
              name="chevron-back"
              size={26}
              color={Colors.fieldtTxtColor}
              style={{}}
            />

            <Text
              style={{
                color: Colors.fieldtTxtColor,
                fontSize: 16,
                textAlign: 'center',
                fontFamily: 'NunitoSans-Regular',
                lineHeight: 22,
                marginTop: -1,
              }}>
              Back
            </Text>
          </View>
        </TouchableOpacity>
      ),
      headerRight: () => (
        <TouchableOpacity
          onPress={() => {
            this.props.navigation.navigate('Profile');
          }}
          style={{}}>
          <View style={{ marginRight: 15 }}>
            <Ionicons name="menu" size={30} color={Colors.primaryColor} />
          </View>
        </TouchableOpacity>
      ),
      headerTitle: () => (
        <View
          style={{
            justifyContent: 'center',
            alignItems: 'center',
            bottom: -1,
          }}>
          <Text
            style={{
              fontSize: 20,
              lineHeight: 25,
              textAlign: 'center',
              fontFamily: 'NunitoSans-Bold',
              color: Colors.mainTxtColor,
            }}>
            Confirmation
          </Text>
        </View>
      ),
      headerTintColor: '#000000',
    });

    this.state = {
      outletData: outletData,
      queueResult: queueResult,
    };
  }

  componentDidMount() { }

  // function here
  cancelQueue() {
    var body = {
      // userId: User.getUserId(),
      queueId: this.state.queueResult.uniqueId,
      userId: this.state.queueResult.userId,
      pax: this.state.queueResult.pax,
      number: this.state.queueResult.number,
      outletName: this.state.queueResult.outletName,
    };

    console.log(body);

    ApiClient.POST(API.cancelQueue, body).then((result) => {
      console.log(result);
      // User.getRefreshCurrentAction();
      if (result.status) {
        if (window.confirm('Info: Queue cancelled successfully') == true) {
          this.props.navigation.navigate("Outlet - KooDoo Web Order")
        }
        else {
          return;
        }
      }
    });
  }
  // function end

  render() {
    return (
      <ScrollView style={styles.container}>
        <View style={{ alignItems: 'center', justifyContent: 'center', paddingBottom: Dimensions.get('screen').height * 0.05 }}>
          {this.state.outletData && this.state.outletData.uniqueId && (
            <Image
              source={{
                uri: this.state.outletData.cover
                  ? this.state.outletData.cover
                  : this.state.outletData.merchant.logo,
              }}
              style={{ width: 90, height: 90, borderRadius: 5 }}
            />
          )}
          {!(this.state.outletData && this.state.outletData.uniqueId) && (
            <Image
              source={{
                uri: this.state.queueResult.outletCover
                  ? this.state.queueResult.outletCover
                  : this.state.queueResult.merchantLogo,
              }}
              style={{ width: 90, height: 90, borderRadius: 5 }}
            />
          )}
          <Text
            style={{
              fontFamily: 'NunitoSans-SemiBold',
              marginVertical: 20,
              fontSize: 17,
            }}>
            {this.state.outletData && this.state.outletData.name
              ? this.state.outletData.name
              : this.state.queueResult.outletName}
          </Text>
          <Text
            style={{
              fontFamily: 'NunitoSans-SemiBold',
              marginVertical: 10,
              fontSize: 17,
            }}>
            Hi {this.state.queueResult.userName},
          </Text>
          <Text style={{ fontFamily: 'NunitoSans-SemiBold', fontSize: 17 }}>
            Thank you for waiting
          </Text>
          {/* <Animatable.View animation="bounceIn" delay={400}>
            <Entypo
              name="dots-three-horizontal"
              color={Colors.primaryColor}
              size={60}
            />
          </Animatable.View> */}
          <Text style={{ fontFamily: 'NunitoSans-SemiBold', fontSize: 17 }}>
            Here's your number
          </Text>
          <TouchableOpacity>
            <View
              style={{
                width: 200,
                height: 200,
                borderRadius: 100,
                backgroundColor: Colors.secondaryColor,
                marginVertical: 30,
                justifyContent: 'center',
                alignItems: 'center',
              }}>
              <Text style={{ fontSize: 70, fontFamily: 'NunitoSans-SemiBold' }}>
                {this.state.queueResult.number}
              </Text>
            </View>
          </TouchableOpacity>
          <View style={{ alignSelf: 'center', width: '70%' }}>
            <Text
              style={{
                fontFamily: 'NunitoSans-SemiBold',
                fontSize: 17,
                textAlign: 'center',
              }}>
              We'll notify you when we're almost ready to see you.
            </Text>
          </View>
          <TouchableOpacity
            onPress={() => {
              this.cancelQueue();
            }}>
            <View
              style={{
                flexDirection: 'row',
                backgroundColor: Colors.fieldtBgColor,
                paddingVertical: 22,
                paddingHorizontal: 10,
                margin: 30,
                alignItems: 'center',
                borderRadius: 10,
                height: 35
              }}>
              <DR color="red" size={20} />
              <Text
                style={{
                  color: 'red',
                  marginLeft: 10,
                  fontSize: 16,
                  fontFamily: 'NunitoSans-SemiBold',
                }}>
                Leave Queue
              </Text>
            </View>
          </TouchableOpacity>
        </View>
      </ScrollView>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FEFCFF',
    padding: 16,
  },
  outletCover: {
    width: '100%',
    alignSelf: 'center',
    height: undefined,
    aspectRatio: 2,
    borderRadius: 5,
  },
  infoTab: {
    backgroundColor: Colors.fieldtBgColor,
    padding: 16,
    justifyContent: 'center',
    alignContent: 'center',
    alignItems: 'center',
  },
  workingHourTab: {
    padding: 16,
    flexDirection: 'row',
  },
  outletAddress: {
    textAlign: 'center',
    color: Colors.mainTxtColor,
  },
  outletName: {
    fontWeight: 'bold',
    fontSize: 20,
    marginBottom: 10,
  },
  logo: {
    width: 100,
    height: 100,
  },
  actionTab: {
    flexDirection: 'row',
    marginTop: 20,
  },
  actionView: {
    width: Styles.width / 4,
    height: Styles.width / 4,
    justifyContent: 'center',
    alignItems: 'center',
    alignContent: 'center',
  },
  actionBtn: {
    borderRadius: 50,
    width: 70,
    height: 70,
    borderColor: Colors.secondaryColor,
    borderWidth: StyleSheet.hairlineWidth,
    justifyContent: 'center',
    alignItems: 'center',
  },
  actionText: {
    fontSize: 12,
    marginTop: 10,
  },
});
export default ComfirmQueueScreen;
