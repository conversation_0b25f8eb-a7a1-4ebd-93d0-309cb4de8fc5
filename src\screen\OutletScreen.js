import React, { Component, useState, useEffect, useCallback } from "react";
import {
  StyleSheet,
  ScrollView,
  Image,
  View,
  Text,
  TouchableOpacity,
  Linking,
  Share,
  Modal,
  Dimensions,
  Alert,
  useWindowDimensions,
} from "react-native";
import Colors from "../constant/Colors";
import ApiClient from "../util/ApiClient";
import * as Cart from "../util/Cart";
import * as User from "../util/User";
import API from "../constant/API";
import { getFirestore, collection, query, where, getDocs, limit } from "firebase/firestore";
import Feather from "react-native-vector-icons/Feather";
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";
import Icon2 from "react-native-vector-icons/FontAwesome";
import Icon3 from "react-native-vector-icons/EvilIcons";
import Ionicons from "react-native-vector-icons/Ionicons";
//import { FlatList } from 'react-native-gesture-handler';
import Styles from "../constant/Styles";
import Back from "react-native-vector-icons/EvilIcons";
// import Draggable from 'react-native-draggable';
// import DraggableFab from "react-native-draggable-fab";
import moment from "moment";
//import { openMap, createMapLink, createOpenLink } from 'react-native-open-maps';
import Close from "react-native-vector-icons/AntDesign";
import { CommonStore } from "../store/commonStore";
import {
  ORDER_TYPE,
  OUTLET_HOMEPAGE_SECTION,
  QR_SCANNING_TYPE,
  TABLE_QR_SALT,
  USER_ORDER_STATUS,
  WEEK,
} from "../constant/common";
import { DataStore } from "../store/dataStore";
import { UserStore } from "../store/userStore";
import AntDesign from "react-native-vector-icons/AntDesign";
import Fontisto from "react-native-vector-icons/Fontisto";
import MaterialIcons from "react-native-vector-icons/MaterialIcons";
import Entypo from "react-native-vector-icons/Entypo";
import AsyncImage from "../components/asyncImage";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { useFocusEffect, useLinkTo } from "@react-navigation/native";
import { prefix } from "../constant/env";
import { isMobile, signInWithPhoneForCRMUser } from "../util/commonFuncs";

import { ReactComponent as ScanOrder } from '../svg/scanorder.svg'
import { ReactComponent as Delivery } from '../svg/delivery.svg'
import { ReactComponent as Takeaway } from '../svg/takeaway.svg'
import { ReactComponent as Docket } from '../svg/docket.svg'
import { ReactComponent as Feedback } from '../svg/feedback.svg'
import { ReactComponent as Reservation } from '../svg/reservation.svg'
import { ReactComponent as Queue } from '../svg/queue.svg';

import { ReactComponent as Gshare } from '../svg/googleshare.svg'
import { ReactComponent as Lpin } from '../svg/locationpin.svg'
import { PaymentStore } from "../store/paymentStore";
import { TableStore } from "../store/tableStore";
import { idbGet } from "../util/db";

import "react-native-get-random-values";
import { v4 as uuidv4 } from "uuid";
import { signInAnonymously } from "firebase/auth";
import { Collections } from "../constant/firebase";

import QrScanner from 'qr-scanner';

import Hashids from 'hashids';
import { TempStore } from "../store/tempStore";
import { ANALYTICS, ANALYTICS_PARSED } from "../constant/analytics";
import { logEvent } from "@firebase/analytics";

const hashids = new Hashids(TABLE_QR_SALT);

/**
 * OutletScreen
 * function
 * *scan order
 * *takeaway order (takeaway delivery or pick up)
 * *create reservation
 * *send feedback
 * *create queue
 *
 * route.params
 * *outletData: array of data related to the current outlet
 */

// outlet screen
const OutletScreen = (props) => {
  const { navigation, route } = props;

  // const outletDataParam = route.params.outletData;

  const outletData = CommonStore.useState(s => s.selectedOutlet || {});

  const { width: windowWidth, height: windowHeight } = useWindowDimensions();
  const linkTo = useLinkTo();

  const [showGenericQRModal, setShowGenericQRModal] = useState(false);
  const [tableMaxPax, setTableMaxPax] = useState(0);
  const [tableCode, setTableCode] = useState('');
  const [seatingPax, setSeatingPax] = useState(1);

  navigation.setOptions({
    headerLeft: () => (
      // <TouchableOpacity
      //   style={{
      //     // display: 'none',
      //   }}
      //   onPress={() => {
      //     props.navigation.navigate("Home");
      //   }}
      // >
      //   <View
      //     style={{
      //       marginLeft: 10,
      //       display: "flex",
      //       flexDirection: "row",
      //       alignItems: "center",
      //       justifyContent: "flex-start",
      //     }}
      //   >
      //     <Ionicons
      //       name="chevron-back"
      //       size={26}
      //       color={Colors.fieldtTxtColor}
      //       style={{}}
      //     />

      //     <Text
      //       style={{
      //         color: Colors.fieldtTxtColor,
      //         fontSize: 16,
      //         textAlign: "center",
      //         fontFamily: "NunitoSans-Regular",
      //         lineHeight: 22,
      //         marginTop: -1,
      //       }}
      //     >
      //       Back
      //     </Text>
      //   </View>
      // </TouchableOpacity>
      <></>
    ),
    headerRight: () => (
      // <TouchableOpacity
      //   onPress={() => {
      //     props.navigation.navigate("Profile");
      //   }}
      //   style={{}}
      // >
      //   <View style={{ marginRight: 15 }}>
      //     <Ionicons name="menu" size={30} color={Colors.primaryColor} />
      //   </View>
      // </TouchableOpacity>
      <></>
    ),
    headerTitle: () => (
      <View
        style={{
          justifyContent: "center",
          alignItems: "center",
          bottom: -1,
        }}
      >
        <Text
          style={{
            fontSize: 20,
            lineHeight: 25,
            textAlign: "center",
            fontFamily: "NunitoSans-Bold",
            color: Colors.mainTxtColor,
          }}
        >
          {outletData ? outletData.name : ''}
        </Text>
      </View>
    ),
  });

  // useEffect(() => {
  //   global.currPageStack = [
  //     ...global.currPageStack,
  //     'OutletScreen',
  //   ];
  // }, []);

  useFocusEffect(
    useCallback(() => {
      // setTimeout(() => {
      //   global.currPageStack = [
      //     ...global.currPageStack,
      //     'OutletScreen',
      //   ];

      //   global.showingOutletScreen = true;

      //   // TempStore.update(s => {
      //   //   s.showGreetingPopup = false;
      //   // });
      // }, 100);

      global.currPageStack = [
        ...global.currPageStack,
        'OutletScreen',
      ];

      // global.showingOutletScreen = true;

      // history.replaceState({
      //   newState: true,
      // }, "", window.location.href);
    }, [])
  );

  useEffect(() => {
    // 2024-03-07 - should be no longer needed

    // window.addEventListener(
    //   "popstate",
    //   async (e) => {
    //     // e.preventDefault();
    //     // console.log('unload!');

    //     // linkTo(`${prefix}/outlet/menu`);

    //     const subdomain = await AsyncStorage.getItem("latestSubdomain");

    //     if (!subdomain) {
    //       linkTo && linkTo(`${prefix}/outlet/menu`);
    //     } else {
    //       linkTo && linkTo(`${prefix}/outlet/${subdomain}/menu`);
    //     }
    //   },
    //   false
    // );

    window.addEventListener(
      "load",
      (e) => {
        // e.preventDefault();
        // console.log('unload!');

        // linkTo('/web/outlet/menu');

        setTimeout(() => {
          readStates();
        }, 1000);

        setTimeout(() => {
          readCommonStates();
        }, 1000);
      },
      false
    );

    // window.onpopstate = e => {
    //   e.preventDefault();
    //   console.log('unload!');
    // }
  }, []);

  useEffect(() => {
    (async () => {
      if (linkTo) {
        DataStore.update((s) => {
          s.linkToFunc = linkTo;
        });

        global.linkToFunc = linkTo;
      }

      //////////////////////////////////

      // 2024-06-19 - helps to authenticate as well

      if (
        // route.params === undefined
        false
      ) {
        linkTo && linkTo(`${prefix}/error`);
      } else {
        let subdomain = '';

        try {
          const currUrl = window.location.href;

          const outletStr = '/outlet/';
          const outletStrPos = currUrl.indexOf(outletStr);

          let subdomainExtracted = currUrl.slice(outletStrPos + outletStr.length);

          if (subdomainExtracted.indexOf('/') > 0) {
            subdomainExtracted = subdomainExtracted.slice(0, subdomainExtracted.indexOf('/'));
          }

          ///////////////////////////////////////////////////////////////

          // 2025-04-29 - for payment link compatiblity

          try {
            if (currUrl.includes('/payment/')) {
              const outletStr2 = '/payment/';
              const outletStrPos2 = currUrl.indexOf(outletStr2);

              let subdomainExtracted2 = currUrl.slice(outletStrPos2 + outletStr2.length);

              if (subdomainExtracted2.indexOf('/') > 0) {
                subdomainExtracted = subdomainExtracted2.slice(0, subdomainExtracted2.indexOf('/'));
              }
            }
          }
          catch (ex) {
            console.error(ex);
          }

          ///////////////////////////////////////////////////////////////

          if (subdomainExtracted) {
            subdomain = subdomainExtracted;
          }
        } catch (ex) {
          console.error(ex);
        }

        //////////////////////////////////

        // 2024-11-28 - check if the Outlet.subdomain being saved in local or not, if no then can read from local

        if (!subdomain) {
          subdomain = await AsyncStorage.getItem('latestSubdomain');

          if (!subdomain) {
            // do nothing
          }
          else {
            // can redirect to /outlet url again, with the subdomain data

            linkTo && linkTo(`${prefix}/outlet/${subdomain}`);
          }
        }

        //////////////////////////////////

        // const subdomain = route.params.subdomain;

        // const subdomain = window.location.host.split('.')[1] ? window.location.host.split('.')[0] : false;

        if (subdomain) {
          // means all got, can login this user to check all info valid or not

          console.log("subdomain");
          console.log(subdomain);

          // TempStore.update(s => {
          //   s.showGreetingPopup = true;
          // });

          global.subdomain = subdomain;

          let outlet = {};

          ////////////////////////////////////////////////////////////////////////////////
          ////////////////////////////////////////////////////////////////////////////////
          ////////////////////////////////////////////////////////////////////////////////

          setTimeout(async () => {
            // for loading outlet data

            var outletSnapshot = null;

            if (subdomain) {
              if (subdomain === "192") {
                outletSnapshot = await getDocs(
                  query(
                    collection(global.db, Collections.Outlet),
                    where('uniqueId', '==', 'b422c1d9-d30b-4de7-ad49-2e601d950919'),
                    limit(1),
                  )
                );
              } else {
                outletSnapshot = await getDocs(
                  query(
                    collection(global.db, Collections.Outlet),
                    where('subdomain', '==', subdomain),
                    limit(1),
                  )
                );
              }
            } else {
              console.log("web scan 3");

              global.errorMessage = 'Link was expired, please scan the QR code again. (EO-1001)';

              linkTo && linkTo(`${prefix}/error/${global.errorMessage}`);
            }

            // var outlet = {};
            if (!outletSnapshot.empty) {
              outlet = outletSnapshot.docs[0].data();
            }

            if (
              outlet &&
              (outlet.subdomain === subdomain || subdomain === "192")
            ) {
              AsyncStorage.multiSet([
                ['latestOutletId', outlet.uniqueId],
                ['latestSubdomain', subdomain],
                // ['latestTableId', route.params.tableId],
                // ['latestUserId', result.userId],
              ]);

              document.title = outlet.name;
              document.getElementsByTagName("META")[2].content =
                outlet.address;

              global.selectedOutlet = outlet;

              // comment first, cause breaking changes

              // PaymentStore.update(s => {
              //   s.cartItemsPayment = [];
              // });

              // CommonStore.update(
              //   (s) => {
              //     s.selectedOutlet = outlet;

              //     // s.orderType = ORDER_TYPE.PICKUP;

              //     // 2022-10-08 - Reset cart items
              //     s.cartItems = [];
              //     s.cartItemsProcessed = [];
              //     s.cartOutletId = outlet.uniqueId;

              //     s.cartItemsReservation = [];
              //     s.cartItemsProcessedReservation = [];

              //     s.selectedOutletItem = {};
              //     s.selectedOutletItemAddOn = {};
              //     s.selectedOutletItemAddOnChoice = {};
              //     s.onUpdatingCartItem = null;

              //     s.cachedAddOnChoiceIdDict = {};

              //     s.isLoading = false;

              //     s.molpayResult = null;

              //     s.payHybridBody = null;
              //     s.paymentDetails = null;
              //     s.orderIdCreated = '';

              //     s.currPage = '';

              //     s.menuItemDetailModal = false;

              //     ////////////////////////////////////

              //     // 2023-06-06 - To clear reservation states

              //     s.selectedReservationId = '';
              //     s.selectedReservationStartTime = null;
              //     s.selectedReservationPax = '1';
              //     s.selectedReservationOutletSectionId = '';
              //     s.selectedReservationOutletSectionName = '';

              //     s.editUserReservation = {};

              //     // s.reservationIdTemp = '';

              //     s.selectedAddressUserPhone = '';
              //     s.selectedUserEmail = '';
              //     s.selectedUserFirstName = '';
              //     s.selectedUserLastName = '';
              //     s.selectedUserRemarks = '';
              //     s.selectedUserDiet = '';
              //     s.selectedUserOccasion = '';

              //     s.resFirstName = '';
              //     s.resLastName = '';
              //     s.resPhoneNum = '';
              //     s.resEmail = '';
              //     s.resRemarks = '';
              //     s.resDietaryRestrictions = '';
              //     s.resSpecialOccasions = '';

              //     s.isPlacingReservation = false;
              //     s.isPlacingReservationUpselling = false;

              //     s.isDepositOnly = false;

              //     s.resShowConfirmationPage = false;

              //     s.selectedTaggableVoucher = null;

              //     s.redeemableStackedLoyaltyStamps = [];
              //     s.toRedeemStackedLoyaltyStamp = {};

              //     ////////////////////////////////////
              //   },
              //   () => {
              //     // if (outlet && outlet.uniqueId) {
              //     //   linkTo &&
              //     //     linkTo(`${prefix}/outlet/${subdomain}/menu`);
              //     // }
              //   }
              // );
            }
            else {
              console.log("web scan 3");

              global.errorMessage = 'Link was expired, please scan the QR code again. (EO-1002)';

              linkTo && linkTo(`${prefix}/error/${global.errorMessage}`);

              // comment first
            }
          }, 10);

          ////////////////////////////////////////////////////////////////////////////////
          ////////////////////////////////////////////////////////////////////////////////
          ////////////////////////////////////////////////////////////////////////////////

          // comment first, cause breaking changes

          // PaymentStore.update(s => {
          //   s.cartItemsPayment = [];
          // });

          // CommonStore.update(
          //   (s) => {
          //     // s.orderType = ORDER_TYPE.PICKUP;

          //     s.cartItems = [];
          //     s.cartItemsProcessed = [];

          //     s.cartItemsReservation = [];
          //     s.cartItemsProcessedReservation = [];

          //     s.selectedOutletItem = {};
          //     s.selectedOutletItemAddOn = {};
          //     s.selectedOutletItemAddOnChoice = {};
          //     s.onUpdatingCartItem = null;

          //     s.cachedAddOnChoiceIdDict = {};

          //     s.isLoading = false;

          //     s.molpayResult = null;

          //     s.payHybridBody = null;
          //     s.paymentDetails = null;
          //     s.orderIdCreated = '';

          //     s.currPage = '';

          //     s.menuItemDetailModal = false;

          //     ////////////////////////////////////

          //     // 2023-06-06 - To clear reservation states

          //     s.selectedReservationId = '';
          //     s.selectedReservationStartTime = null;
          //     s.selectedReservationPax = '1';
          //     s.selectedReservationOutletSectionId = '';
          //     s.selectedReservationOutletSectionName = '';

          //     s.editUserReservation = {};

          //     // s.reservationIdTemp = '';

          //     s.selectedAddressUserPhone = '';
          //     s.selectedUserEmail = '';
          //     s.selectedUserFirstName = '';
          //     s.selectedUserLastName = '';
          //     s.selectedUserRemarks = '';
          //     s.selectedUserDiet = '';
          //     s.selectedUserOccasion = '';

          //     s.resFirstName = '';
          //     s.resLastName = '';
          //     s.resPhoneNum = '';
          //     s.resEmail = '';
          //     s.resRemarks = '';
          //     s.resDietaryRestrictions = '';
          //     s.resSpecialOccasions = '';

          //     s.isPlacingReservation = false;
          //     s.isPlacingReservationUpselling = false;

          //     s.isDepositOnly = false;

          //     s.resShowConfirmationPage = false;

          //     s.selectedTaggableVoucher = null;

          //     s.redeemableStackedLoyaltyStamps = [];
          //     s.toRedeemStackedLoyaltyStamp = {};

          //     ////////////////////////////////////
          //   },
          //   () => {
          //     // linkTo &&
          //     //   linkTo(`${prefix}/outlet/${subdomain}/menu`);
          //   }
          // );

          ////////////////////////////////////////////////////////////////////////////////
          ////////////////////////////////////////////////////////////////////////////////
          ////////////////////////////////////////////////////////////////////////////////

          // later first (for sign in, and get token)

          // firebase
          //   .auth()
          //   .signInAnonymously()

          try {
            setTimeout(() => {
              signInAnonymously(global.auth)
                .then((result) => {
                  // TempStore.update(s => {
                  //   s.firebaseAuth = true;
                  // });

                  const firebaseUid = result.user.uid;

                  ApiClient.GET(API.getTokenKWeb + firebaseUid).then(
                    async (result) => {
                      console.log("getTokenKWeb");
                      console.log(result);

                      if (result && result.token) {
                        // await AsyncStorage.setItem("accessToken", result.token);
                        // await AsyncStorage.setItem(
                        //   "refreshToken",
                        //   result.refreshToken
                        // );

                        AsyncStorage.multiSet([
                          ['accessToken', result.token],
                          ['refreshToken', result.refreshToken],
                        ]);

                        global.accessToken = result.token;

                        UserStore.update((s) => {
                          s.firebaseUid = result.userId;
                          s.userId = result.userId;
                          s.role = result.role;
                          s.refreshToken = result.refreshToken;
                          s.token = result.token;
                          s.name = '';
                          s.email = '';
                          s.number = '';
                        });

                        //////////////////////////////////

                        // 2024-06-19 - helps to auto sign in

                        await signInWithPhoneForCRMUser(outlet);

                        //////////////////////////////////
                      } else {
                        CommonStore.update((s) => {
                          s.alertObj = {
                            title: "Error",
                            message: "Unauthorized access",
                          };

                          // s.isAuthenticating = false;
                        });

                        console.log("web scan 4");

                        global.errorMessage = 'Link was expired, please scan the QR code again. (EO-1003)';

                        linkTo && linkTo(`${prefix}/error/${global.errorMessage}`);
                      }
                    }
                  );
                });
            }, 10);
          }
          catch (ex) {
            console.error(ex);
          }

          ////////////////////////////////////////////////////////////////////////////////
          ////////////////////////////////////////////////////////////////////////////////
          ////////////////////////////////////////////////////////////////////////////////

          // 2022-10-12 - Get and set user id anonymous

          setTimeout(async () => {
            const userIdAnonymousRaw = await AsyncStorage.getItem('userIdAnonymous');

            if (userIdAnonymousRaw) {
              // means existed

              UserStore.update(s => {
                s.userIdAnonymous = userIdAnonymousRaw;
              });
            }
            else {
              var userIdAnonymousTemp = uuidv4();
              UserStore.update(s => {
                s.userIdAnonymous = userIdAnonymousTemp;
              });

              await AsyncStorage.setItem('userIdAnonymous', userIdAnonymousTemp);
            }

            global.userIdAnonymousLoaded = true;
          }, 100);

          if (userIdAnonymous === 'none') {
            // means haven't set before
          }


        } else {
          global.errorMessage = 'Link was expired, please scan the QR code again. (EO-1004)';

          linkTo && linkTo(`${prefix}/error/${global.errorMessage}`);
        }
      }

      //////////////////////////////////
    })();
  }, [linkTo]);

  useEffect(() => {
    if (outletData === null) {
      readStates();
    }

    readCommonStates();
  }, [outletData]);

  const readStates = async () => {
    console.log('global.selectedoutlet = readStates (1) (==test==)');

    if (selectedOutlet === null) {
      console.log('global.selectedoutlet = readStates (2) (==test==)');

      // const commonStoreDataRaw = await AsyncStorage.getItem("@commonStore");
      const commonStoreDataRaw = await idbGet('@commonStore');
      if (commonStoreDataRaw !== undefined) {
        console.log('global.selectedoutlet = readStates (3) (==test==)');

        const commonStoreData = JSON.parse(commonStoreDataRaw);

        const latestOutletId = await AsyncStorage.getItem("latestOutletId");

        console.log('latestOutletId');
        console.log(latestOutletId);
        console.log('commonStoreData.selectedOutlet');
        console.log(commonStoreData.selectedOutlet);

        if (
          commonStoreData.selectedOutlet &&
          latestOutletId === commonStoreData.selectedOutlet.uniqueId
        ) {
          // check if it's the same outlet user scanned

          console.log('global.selectedoutlet = readStates (4) (==test==)');

          // if (isPlacingReservation) {
          // } else {
          //   // if (
          //   //   commonStoreData.orderType === ORDER_TYPE.DINEIN 
          //   //   // 2022-10-08 - Try to disable this
          //   //   // &&
          //   //   // commonStoreData.userCart.uniqueId === undefined
          //   // ) {
          //   //   // logout the user

          //   //   linkTo && linkTo(`${prefix}/scan`);
          //   // }
          // }

          console.log('global.selectedoutlet = commonStoreData.selectedOutlet (3) (==test==)');

          global.selectedOutlet = commonStoreData.selectedOutlet;

          CommonStore.replace(commonStoreData);

          // const userStoreDataRaw = await AsyncStorage.getItem("@userStore");
          const userStoreDataRaw = await idbGet('@userStore');
          if (userStoreDataRaw !== undefined) {
            const userStoreData = JSON.parse(userStoreDataRaw);

            UserStore.replace(userStoreData);
          }

          // const dataStoreDataRaw = await AsyncStorage.getItem('@dataStore');
          const dataStoreDataRaw = await idbGet('@dataStore');
          if (dataStoreDataRaw !== undefined) {
            const dataStoreData = JSON.parse(dataStoreDataRaw);

            DataStore.replace(dataStoreData);
            // DataStore.replace({
            //   ...dataStoreData,
            //   ...dataStoreData.linkToFunc !== undefined && {
            //     linkToFunc: dataStoreData.linkToFunc,
            //   },
            // });
          }

          // const tableStoreDataRaw = await AsyncStorage.getItem("@tableStore");
          const tableStoreDataRaw = await idbGet('@tableStore');
          if (tableStoreDataRaw !== undefined) {
            const tableStoreData = JSON.parse(tableStoreDataRaw);

            TableStore.replace(tableStoreData);
          }

          // const paymentStoreDataRaw = await AsyncStorage.getItem("@paymentStore");
          const paymentStoreDataRaw = await idbGet('@paymentStore');
          if (paymentStoreDataRaw !== undefined) {
            const paymentStoreData = JSON.parse(paymentStoreDataRaw);

            PaymentStore.replace(paymentStoreData);
          }

          // const dataStoreDataRaw = await AsyncStorage.getItem('@dataStore');
          // if (dataStoreDataRaw !== undefined) {
          //   const dataStoreData = JSON.parse(dataStoreDataRaw);

          //   DataStore.replace(dataStoreData);
          // }
        }
      }
    }
  };

  const readCommonStates = async () => {
    // if (!linkToFunc) {
    //   const dataStoreDataRaw = await AsyncStorage.getItem('@dataStore');
    //   if (dataStoreDataRaw !== undefined) {
    //     const dataStoreData = JSON.parse(dataStoreDataRaw);
    //     DataStore.replace(dataStoreData);
    //   }
    // }
  };

  // 2024-06-19 - scan qr support
  const [currSection, setCurrSection] = useState(OUTLET_HOMEPAGE_SECTION.HOME);

  // const [outletData, setOutletData] = useState(outletDataParam);
  const [allDay, setAllDay] = useState(false);
  const [icon, setIcon] = useState("heart-o");
  const [favo, setFavo] = useState(0);
  const [onStartVisible, setOnStartVisible] = useState(false);
  const [openToday, setOpenToday] = useState(false);

  const [showWeekOpenings, setShowWeekOpenings] = useState(false);

  const [activeDineInOrders, setActiveDineInOrders] = useState([]);

  const [isOpeningNowState, setIsOpeningNowState] = useState(true);

  const selectedOutlet = CommonStore.useState((s) => s.selectedOutlet);
  const merchantsDict = CommonStore.useState((s) => s.merchantsDict);
  const outletsOpeningDict = CommonStore.useState((s) => s.outletsOpeningDict);
  const userFavoriteOutletDict = CommonStore.useState(
    (s) => s.userFavoriteOutletDict
  );

  useEffect(() => {
    console.log('asasdasdaasdasd')
    console.log(outletsOpeningDict)
  }, [outletsOpeningDict])


  const selectedOutletReviewSummary = CommonStore.useState(
    (s) => s.selectedOutletReviewSummary
  );
  const selectedOutletReviews = CommonStore.useState(
    (s) => s.selectedOutletReviews
  );
  const selectedOutletReview = CommonStore.useState(
    (s) => s.selectedOutletReview
  );

  const userCart = CommonStore.useState((s) => s.userCart);

  const userOrders = CommonStore.useState((s) => s.userOrders);

  const userName = UserStore.useState((s) => s.name);
  const userNumber = UserStore.useState((s) => s.number);
  const firebaseUid = UserStore.useState((s) => s.firebaseUid);
  const userIdAnonymous = UserStore.useState((s) => s.userIdAnonymous);
  const email = UserStore.useState((s) => s.email);

  const [starRatingDefault, setStarRatingDefault] = useState(2);
  const [starRating, setStarRating] = useState([1, 2, 3, 4, 5]);

  const setState = () => { };

  useEffect(() => {
    var activeDineInOrdersTemp = [];

    for (var i = 0; i < userOrders.length; i++) {
      if (
        userOrders[i].orderStatus !== USER_ORDER_STATUS.ORDER_COMPLETED &&
        userOrders[i].orderStatus !==
        USER_ORDER_STATUS.ORDER_CANCELLED_BY_MERCHANT &&
        userOrders[i].orderStatus !== USER_ORDER_STATUS.ORDER_CANCELLED_BY_USER
      ) {
        if (userOrders[i].orderType === ORDER_TYPE.DINEIN) {
          if (userOrders[i].outletId === selectedOutlet.uniqueId) {
            activeDineInOrdersTemp.push(userOrders[i]);
          }
        }
      }
    }

    setActiveDineInOrders(activeDineInOrdersTemp);
  }, [userOrders]);

  useEffect(() => {
    // ApiClient.GET(API.getFavoriteOutlet + User.getUserId()).then((result) => {
    //   //console.log(result)
    //   setState({ favorite: result })
    //   if (result.find((obj) => obj.outletId == outletData.id)) {
    //     setState({ icon: 'heart', favo: 1 });
    //   } else {
    //     //return icon
    //   }
    // });
  }, []);

  // function here
  const favourite = () => {
    if (favo == 0) {
      return setState({ icon: "heart", favo: 1 }), addfavo();
    } else if (favo == 1) {
      return setState({ icon: "heart-o", favo: 0 }), removefavo();
    }
  };

  const addfavo = () => {
    const body = {
      userId: User.getUserId(),
      outletId: outletData.id,
    };
    ApiClient.POST(API.favoriteOutlet, body).then((result) => {
      //console.log(result)
      setState({ icon: "heart-o", favo: 1 });
    });
  };
  const removefavo = () => {
    const body = {
      userId: User.getUserId(),
      outletId: outletData.id,
    };
    ApiClient.POST(API.removeFavoriteOutlet, body).then((result) => {
      //console.log(result)
      setState({ icon: "heart", favo: 0 });
    });
  };

  const onShare = async () => {
    const text = outletData.name;
    const add = outletData.address;
    //const latlng = (outletData.latlng).split(",")
    //const maplink = createMapLink({ latitude: parseFloat(latlng[0]), longitude: parseFloat(latlng[1]), zoom: 30 })
    //console.log(`${text}, ${add} ${maplink}`)
    const result = await Share.share(
      {
        //title: `${text}, ${add}`,
        //message: `${text}, ${add}${maplink}`,
        title: `${text}, ${add}`,
        message: `${text}, ${add}`,
      },
      {}
    );
    //const text = outletData.name
    //const add = outletData.address
    //const latlng = (outletData.latlng).split(",")
    //const maplink = createMapLink({ latitude: parseFloat(latlng[0]), longitude: parseFloat(latlng[1]), zoom: 30 })
    //console.log(`${text}, ${add} ${maplink}`)

    console.log(result);
    if (result.action === Share.sharedAction) {
      if (result.activityType) {
        alert("Shared with " + result.activityType);
      } else if (result.action === Share.dismissedAction) {
        alert("Dismissed");
      } else {
        alert("Shared");
      }
    }
    //console.log(result)
  };

  const ringOutlet = async () => {
    if (userCart && userCart.uniqueId) {
      // means dine-in now

      const body = {
        tableId: userCart.tableId,
        tableCode: userCart.tableCode,
        userId: userCart.userId,
        outletId: userCart.outletId,
        userName: userName,
        userNumber: userNumber,
      };

      console.log('body', body)

      ApiClient.POST(API.createUserRing, body).then((result) => {
        if (result && result.status === "success") {
          alert("Success, Ring waiter successfully");
        } else {
          alert("Error, Failed to ring waiter");
        }
      });
    } else if (activeDineInOrders.length > 0) {
      const body = {
        tableId: activeDineInOrders[0].tableId,
        tableCode: activeDineInOrders[0].tableCode,
        userId: activeDineInOrders[0].userId,
        outletId: activeDineInOrders[0].outletId,
        userName: userName,
        userNumber: userNumber,
      };

      ApiClient.POST(API.createUserRing, body).then((result) => {
        if (result && result.status === "success") {
          alert("Success, Ring waiter successfully");
        } else {
          alert("Error, Failed to ring waiter");
        }
      });
    } else {
      alert("Info, Only available during dine-in");
    }
  };

  const addUserFavoriteOutlet = () => {
    const body = {
      userId: firebaseUid,
      outletId: selectedOutlet.uniqueId,
      merchantId: selectedOutlet.merchantId,
    };
    ApiClient.POST(API.addUserFavoriteOutlet, body).then((result) => {
      if (result && result.status === "success") {
        Alert.alert("Success", "Added to favorite");
      } else {
        Alert.alert("Error", "Failed to add favorite");
      }
    });
  };

  const removeUserFavoriteOutlet = () => {
    const body = {
      userId: firebaseUid,
      outletId: selectedOutlet.uniqueId,
      merchantId: selectedOutlet.merchantId,
    };
    ApiClient.POST(API.removeUserFavoriteOutlet, body).then((result) => {
      if (result && result.status === "success") {
        Alert.alert("Success", "Removed from favorite");
      } else {
        Alert.alert("Error", "Failed to remove favorite");
      }
    });
  };

  const showAllDay = () => {
    if (allDay == false) {
      setState({ allDay: true });
    } else {
      setState({ allDay: false });
    }
  };

  const goToMenu = (orderType, tableNo) => {
    Cart.setOrderType(orderType);
    Cart.setTableNumber(tableNo);
    props.navigation.navigate("OutletMenu", {
      outletData: outletData,
      orderType: orderType,
      test: 1,
      navFrom: "OUTLET",
    });
  };

  const parseScannedQr = async (qrStrResult) => {
    // console.log(qrStr);

    try {
      if (qrStrResult) {
        let qrStr = '';

        if (qrStrResult.data &&
          typeof qrStrResult.data === 'string') {
          qrStr = qrStrResult.data;
        }
        else if (typeof qrStrResult === 'string') {
          qrStr = qrStrResult;
        }

        if (qrStr.includes('/new-order/')) {
          // check qrDateTimeEncrypted, is it valid

          let route = {
            params: {},
          };

          try {
            const currUrl = qrStr;

            const cutStartStr = '/new-order/';
            const cutStartStrPos = currUrl.indexOf(cutStartStr);

            const extractedStr = currUrl.slice(cutStartStrPos + cutStartStr.length);

            const extractedStrArr = extractedStr.split('/');

            route.params.tableId = extractedStrArr[0];
            route.params.waiterId = extractedStrArr[1];
            route.params.qrDateTimeEncrypted = extractedStrArr[2];
          }
          catch (ex) {
            console.error(ex);

            route.params = {};
          }

          if (route.params &&
            route.params.tableId &&
            route.params.waiterId &&
            route.params.qrDateTimeEncrypted) {
            // can proceed

            var qrDateTimeEncrypted = route.params.qrDateTimeEncrypted;
            var qrDateTime = null;
            var qrDateTimeStr = '';
            var expiryMinutes = 60;

            if (qrDateTimeEncrypted) {
              qrDateTimeStr = hashids.decodeHex(qrDateTimeEncrypted);
              qrDateTime = parseInt(qrDateTimeStr);
            }

            console.log(`qrDateTime: ${qrDateTime}`);

            //////////////////////////////////////////

            // 2023-12-14 - custom table qr scan time

            if (qrDateTimeStr.length === 15) {
              // means is the new qr code from new build

              qrDateTime = parseInt(qrDateTimeStr.slice(0, 13));

              expiryMinutes = parseInt(qrDateTimeStr.slice(13, 15));
            }

            //////////////////////////////////////////

            if (qrDateTime && moment().diff(qrDateTime, 'minute') <= expiryMinutes) {
              // within 60 minutes still can use

              // means all got, can login this user to check all info valid or not

              ///////////////////////////////////////////////////////////////////////
              ///////////////////////////////////////////////////////////////////////
              ///////////////////////////////////////////////////////////////////////

              TempStore.update(s => {
                s.showGreetingPopup = true;
              });

              CommonStore.update(s => {
                // s.selectedOutletTableQRUrl = window.location.href;
                s.selectedOutletTableQRUrl = qrStr;
              });

              var tableId = hashids.decodeHex(route.params.tableId).toString().insert(8, '-').insert(13, '-').insert(18, '-').insert(23, '-');

              ///////////////////////////////////////////////////////////////////////
              ///////////////////////////////////////////////////////////////////////
              ///////////////////////////////////////////////////////////////////////

              // firebase.auth().signInAnonymously()

              ///////////////////////////////////////////////////////////////////////
              ///////////////////////////////////////////////////////////////////////
              ///////////////////////////////////////////////////////////////////////

              const outletTableSnapshot = await getDocs(
                query(
                  collection(global.db, Collections.OutletTable),
                  where('uniqueId', '==', tableId),
                  limit(1),
                )
              );

              var outletTable = null;

              if (!outletTableSnapshot.empty) {
                outletTable = outletTableSnapshot.docs[0].data();
              }

              if (outletTable) {
                if (outletTable.outletId === selectedOutlet.uniqueId) {
                  if (outletTable.seated <= 0) {
                    // linkTo && linkTo(`${prefix}/scan`);

                    Alert.alert('Info', 'This table is unseated already.');
                  }
                  else {
                    const outlet = selectedOutlet;

                    if (outlet) {
                      global.subdomainOnly = (outlet.subdomain || '');

                      AsyncStorage.multiSet([
                        ['latestOutletId', outlet.uniqueId],
                        ['latestSubdomain', (outlet.subdomain || '')],
                        ['latestTableId', route.params.tableId],
                        // ['latestUserId', result.userId],
                      ]);

                      PaymentStore.update(s => {
                        s.cartItemsPayment = [];
                      });

                      console.log('[stamp] clear');

                      CommonStore.update(s => {
                        // 2022-10-08 - Reset cart items
                        s.cartItems = [];
                        s.cartItemsProcessed = [];
                        s.cartOutletId = outlet.uniqueId;

                        s.cartItemsReservation = [];
                        s.cartItemsProcessedReservation = [];

                        s.tcCartItems = [];
                        s.tcCartItemsProcessed = [];

                        s.selectedOutletItem = {};
                        s.selectedOutletItemAddOn = {};
                        s.selectedOutletItemAddOnChoice = {};
                        s.onUpdatingCartItem = null;

                        s.cachedAddOnChoiceIdDict = {};

                        s.isLoading = false;

                        s.molpayResult = null;

                        s.payHybridBody = null;
                        s.paymentDetails = null;
                        s.orderIdCreated = '';

                        s.payTopupCreditBody = null;

                        s.currPage = '';

                        s.menuItemDetailModal = false;

                        ////////////////////////////////////

                        // 2023-06-06 - To clear reservation states

                        s.selectedReservationId = '';
                        s.selectedReservationStartTime = null;
                        s.selectedReservationPax = '1';
                        s.selectedReservationOutletSectionId = '';
                        s.selectedReservationOutletSectionName = '';

                        // s.reservationIdTemp = '';

                        s.editUserReservation = {};

                        s.selectedAddressUserPhone = '';
                        s.selectedUserEmail = '';
                        s.selectedUserFirstName = '';
                        s.selectedUserLastName = '';
                        s.selectedUserRemarks = '';
                        s.selectedUserDiet = '';
                        s.selectedUserOccasion = '';

                        s.resFirstName = '';
                        s.resLastName = '';
                        s.resPhoneNum = '';
                        s.resEmail = '';
                        s.resRemarks = '';
                        s.resDietaryRestrictions = '';
                        s.resSpecialOccasions = '';

                        s.isPlacingReservation = false;
                        s.isPlacingReservationUpselling = false;

                        s.isDepositOnly = false;

                        s.resShowConfirmationPage = false;

                        s.selectedTaggableVoucher = null;
                        s.selectedBundleTaggableVoucher = null;

                        s.userLoyaltyStamps = [];
                        s.redeemableStackedLoyaltyStamps = [];
                        s.toRedeemStackedLoyaltyStamp = {};

                        ////////////////////////////////////
                      });

                      await updateUserCartDynamicQR(
                        {
                          ...route.params,
                          outletId: outlet.uniqueId,
                          tableId: tableId,
                          tablePax: outletTable.seated,
                          tableCode: outletTable.code,
                        },
                        outlet,
                        // firebaseUid,
                        '',
                      );

                      logEvent(global.analytics, ANALYTICS.EVENT_QR_DINEIN_U, {
                        eventNameParsed: ANALYTICS_PARSED.EVENT_QR_DINEIN_U,

                        // merchantName: global.merchantName,
                        // outletName: outlet.name,
                        outletName: global.outletName ? `${global.outletName} (Web)` : '',

                        merchantId: outlet.merchantId,
                        outletId: outlet.uniqueId,
                        // userId: global.userId,

                        webUserInfo: `${moment().format('YYYYMMDD-HHmm')}>${global.userName ? global.userName : 'N/A'} (${global.userIdAnonymous ? global.userIdAnonymous : ''})`,
                      });

                      //////////////////////////////////////////////////////

                      // 2022-12-28 - Get and set user id anonymous

                      // setTimeout(async () => {
                      //   const userIdAnonymousRaw = await AsyncStorage.getItem('userIdAnonymous');

                      //   if (userIdAnonymousRaw) {
                      //     // means existed

                      //     UserStore.update(s => {
                      //       s.userIdAnonymous = userIdAnonymousRaw;
                      //     });
                      //   }
                      //   else {
                      //     var userIdAnonymousTemp = uuidv4();
                      //     UserStore.update(s => {
                      //       s.userIdAnonymous = userIdAnonymousTemp;
                      //     });

                      //     await AsyncStorage.setItem('userIdAnonymous', userIdAnonymousTemp);
                      //   }

                      //   global.userIdAnonymousLoaded = true;

                      //   // 2023-05-23 - Track QR info

                      //   await uploadScanQrInfo(
                      //     outlet.uniqueId,
                      //     outlet.merchantId,

                      //     '',

                      //     route.params.tableId,
                      //     route.params.waiterId,
                      //     route.params.qrDateTimeEncrypted,

                      //     userIdAnonymous,
                      //   );
                      // }, 100);

                      // if (userIdAnonymous === 'none') {
                      //   // means haven't set before
                      // }

                      //////////////////////////////////////////////////////                            
                    }
                    else {
                      // linkTo && linkTo(`${prefix}/scan`);

                      Alert.alert('Info', 'Unable to find the outlet data.');
                    }
                  }
                }
                else {
                  Alert.alert('Info', 'The QR code comes from another outlet.')
                }
              }
              else {
                // linkTo && linkTo(`${prefix}/scan`);
                Alert.alert('Info', 'The table is not existed.');
              }
            }
            else {
              global.errorMsg = 'This QR code is expired already.';

              linkTo && linkTo(`${prefix}/scan`);
            }
          }
          else {
            Alert.alert('Info', 'Invalid dynamic QR.');
          }
        }
        else if (qrStr.includes('/new-order-generic/')) {
          // Alert.alert('Info', 'Generic QR.');

          let route = {
            params: {},
          };

          try {
            const currUrl = qrStr;

            const cutStartStr = '/new-order-generic/';
            const cutStartStrPos = currUrl.indexOf(cutStartStr);

            const extractedStr = currUrl.slice(cutStartStrPos + cutStartStr.length);

            const extractedStrArr = extractedStr.split('/');

            route.params.tableId = extractedStrArr[0];
          }
          catch (ex) {
            console.error(ex);

            route.params = {};
          }

          if (route.params &&
            route.params.tableId) {
            // can proceed

            ///////////////////////////////////////////////////////////////////////
            ///////////////////////////////////////////////////////////////////////
            ///////////////////////////////////////////////////////////////////////

            TempStore.update(s => {
              s.showGreetingPopup = true;
              s.toWaitForQrLoading = true;
            });

            // means all got, can login this user to check all info valid or not

            var tableId = hashids.decodeHex(route.params.tableId).toString().insert(8, '-').insert(13, '-').insert(18, '-').insert(23, '-');

            ///////////////////////////////////////////////////////////////////////
            ///////////////////////////////////////////////////////////////////////
            ///////////////////////////////////////////////////////////////////////

            const outletTableSnapshot = await getDocs(
              query(
                collection(global.db, Collections.OutletTable),
                where('uniqueId', '==', tableId),
                limit(1),
              )
            );

            var outletTable = null;

            if (!outletTableSnapshot.empty) {
              outletTable = outletTableSnapshot.docs[0].data();
            }

            ///////////////////////////////////////////////////////////////////////////////

            // 2023-01-09 - Search for joined tables

            if (!outletTable) {
              // if still not found

              // const outletTableSnapshot = await firebase.firestore().collection(Collections.OutletTable)
              //     .where('joinedTableIdList', 'array-contains', tableId)
              //     .limit(1)
              //     .get();

              const outletTableSnapshot = await getDocs(
                query(
                  collection(global.db, Collections.OutletTable),
                  where('joinedTableIdList', 'array-contains', tableId),
                  limit(1),
                )
              );

              if (!outletTableSnapshot.empty) {
                outletTable = outletTableSnapshot.docs[0].data();

                tableId = outletTable.uniqueId;
              }
            }

            ///////////////////////////////////////////////////////////////////////////////

            if (outletTable) {
              if (outletTable.outletId === selectedOutlet.uniqueId) {
                ///////////////////////////////////////////////////////////////////////
                ///////////////////////////////////////////////////////////////////////
                ///////////////////////////////////////////////////////////////////////

                setTimeout(async () => {
                  const outlet = selectedOutlet;

                  if (outlet) {
                    // setCurrOutlet(outlet);
                    TempStore.update(s => {
                      s.qrGenericOutlet = outlet;
                    });

                    CommonStore.update(s => {
                      s.selectedOutlet = outlet;
                    });

                    global.selectedOutlet = outlet;

                    global.subdomainOnly = (outlet.subdomain || '');

                    if (outlet && outlet.uniqueId && global.accessToken) {
                      updateTablePaxDirectly(
                        {
                          ...route.params,
                          // outletId: outlet.uniqueId,
                          outletId: outletTable.uniqueId,
                          tableId: tableId,
                          tablePax: outletTable.seated,
                          tableCode: outletTable.code,
                        },
                        outlet,
                      );
                    }

                    AsyncStorage.multiSet([
                      ['latestOutletId', outlet.uniqueId],
                      ['latestSubdomain', (outlet.subdomain || '')],
                      ['latestTableId', route.params.tableId],
                      // ['latestUserId', result.userId],
                    ]);

                    logEvent(global.analytics, ANALYTICS.EVENT_QR_DINEIN_G, {
                      eventNameParsed: ANALYTICS_PARSED.EVENT_QR_DINEIN_G,

                      // merchantName: global.merchantName,
                      // outletName: outlet.name,
                      outletName: global.outletName ? `${global.outletName} (Web)` : '',

                      merchantId: outlet.merchantId,
                      outletId: outlet.uniqueId,
                      // userId: global.userId,

                      webUserInfo: `${moment().format('YYYYMMDD-HHmm')}>${global.userName ? global.userName : 'N/A'} (${global.userIdAnonymous ? global.userIdAnonymous : ''})`,
                    });
                  }
                  else {
                    console.log('web scan 3');
                    // linkTo && linkTo(`${prefix}/scan`);

                    Alert.alert('Info', 'Unable to find the outlet data.');
                  }
                }, 10);

                ///////////////////////////////////////////////////////////////////////
                ///////////////////////////////////////////////////////////////////////
                ///////////////////////////////////////////////////////////////////////

                // show pax modal before proceed                                    

                // need check if got active order on this table first

                global.storedTableId = tableId;

                var isEmptyTable = true;

                if (isEmptyTable) {
                  setTableCode(outletTable.code);
                  setTableMaxPax(outletTable.capacity);

                  // setCurrQRJson({
                  //     ...route.params,
                  //     // outletId: outlet.uniqueId,
                  //     outletId: outletTable.uniqueId,
                  //     tableId: tableId,
                  //     tablePax: outletTable.seated,
                  //     tableCode: outletTable.code,
                  // });

                  TempStore.update(s => {
                    s.qrGenericJson = {
                      ...route.params,
                      // outletId: outlet.uniqueId,
                      outletId: outletTable.uniqueId,
                      tableId: tableId,
                      tablePax: outletTable.seated,
                      tableCode: outletTable.code,
                    };
                    s.qrGenericParams = route.params;
                  });

                  // setShowGenericQRModal(true);

                  //////////////////////////////////////////////////////

                  // 2022-12-28 - Get and set user id anonymous

                  // setTimeout(async () => {
                  //   const userIdAnonymousRaw = await AsyncStorage.getItem('userIdAnonymous');

                  //   if (userIdAnonymousRaw) {
                  //     // means existed

                  //     UserStore.update(s => {
                  //       s.userIdAnonymous = userIdAnonymousRaw;
                  //     });
                  //   }
                  //   else {
                  //     var userIdAnonymousTemp = uuidv4();
                  //     UserStore.update(s => {
                  //       s.userIdAnonymous = userIdAnonymousTemp;
                  //     });

                  //     await AsyncStorage.setItem('userIdAnonymous', userIdAnonymousTemp);
                  //   }

                  //   global.userIdAnonymousLoaded = true;
                  // }, 100);

                  // if (userIdAnonymous === 'none') {
                  //   // means haven't set before

                  // }

                  //////////////////////////////////////////////////////
                }
                else {
                  console.log('web scan 2');

                  global.errorMsg = 'Invalid table.';

                  linkTo && linkTo(`${prefix}/scan`);
                }
              }
              else {
                Alert.alert('Info', 'The QR code comes from another outlet.')
              }
            }
            else {
              console.log('web scan 3');
              // linkTo && linkTo(`${prefix}/scan`);

              Alert.alert('Info', 'The table is not existed.');
            }
          }
        }
        else if (qrStr.includes('/takeaway') &&
          qrStr.includes('/outlet/')) {
          // Alert.alert('Info', 'Takeaway QR');

          let route = {
            params: {},
          };

          try {
            const currUrl = qrStr;

            const cutStartStr = '/outlet/';
            const cutStartStrPos = currUrl.indexOf(cutStartStr);

            const extractedStr = currUrl.slice(cutStartStrPos + cutStartStr.length);

            const extractedStrArr = extractedStr.split('/');

            route.params.subdomain = extractedStrArr[0];
          }
          catch (ex) {
            console.error(ex);

            route.params = {};
          }

          if (route.params &&
            route.params.subdomain) {
            // can proceed

            const subdomain = route.params.subdomain;

            // const subdomain = window.location.host.split('.')[1] ? window.location.host.split('.')[0] : false;

            if (subdomain) {
              if (subdomain === selectedOutlet.subdomain) {
                // means all got, can login this user to check all info valid or not

                console.log("subdomain");
                console.log(subdomain);

                // await AsyncStorage.setItem(
                //   "latestOutletId",
                //   outlet.uniqueId
                // );
                // await AsyncStorage.setItem("latestSubdomain", subdomain);          

                // setCurrOutlet(outlet);

                // setShowGenericQRModal(true);

                TempStore.update(s => {
                  s.showGreetingPopup = true;
                });

                global.subdomain = subdomain;

                ////////////////////////////////////////////////////////////////////////////////
                ////////////////////////////////////////////////////////////////////////////////
                ////////////////////////////////////////////////////////////////////////////////

                setTimeout(async () => {
                  // for loading outlet data

                  const outlet = selectedOutlet;

                  if (
                    outlet &&
                    (outlet.subdomain === subdomain || subdomain === "192")
                  ) {
                    AsyncStorage.multiSet([
                      ['latestOutletId', outlet.uniqueId],
                      ['latestSubdomain', subdomain],
                      // ['latestTableId', route.params.tableId],
                      // ['latestUserId', result.userId],
                    ]);

                    document.title = outlet.name;
                    document.getElementsByTagName("META")[2].content =
                      outlet.address;

                    global.selectedOutlet = outlet;

                    PaymentStore.update(s => {
                      s.cartItemsPayment = [];
                    });

                    console.log('[stamp] clear');

                    CommonStore.update(
                      (s) => {
                        s.selectedOutlet = outlet;

                        // s.scannedQrData = json;

                        s.orderType = ORDER_TYPE.PICKUP;

                        // s.selectedOutletTableId = json.tableId;
                        // s.selectedOutletWaiterId = json.waiterId;
                        // s.selectedOutletTablePax = json.tablePax;
                        // s.selectedOutletTableCode = json.tableCode;

                        // 2022-10-08 - Reset cart items
                        s.cartItems = [];
                        s.cartItemsProcessed = [];
                        s.cartOutletId = outlet.uniqueId;

                        s.cartItemsReservation = [];
                        s.cartItemsProcessedReservation = [];

                        s.tcCartItems = [];
                        s.tcCartItemsProcessed = [];

                        s.selectedOutletItem = {};
                        s.selectedOutletItemAddOn = {};
                        s.selectedOutletItemAddOnChoice = {};
                        s.onUpdatingCartItem = null;

                        s.cachedAddOnChoiceIdDict = {};

                        s.isLoading = false;

                        s.molpayResult = null;

                        s.payHybridBody = null;
                        s.paymentDetails = null;
                        s.orderIdCreated = '';

                        s.payTopupCreditBody = null;

                        s.currPage = '';

                        s.menuItemDetailModal = false;

                        ////////////////////////////////////

                        // 2023-06-06 - To clear reservation states

                        s.selectedReservationId = '';
                        s.selectedReservationStartTime = null;
                        s.selectedReservationPax = '1';
                        s.selectedReservationOutletSectionId = '';
                        s.selectedReservationOutletSectionName = '';

                        s.editUserReservation = {};

                        // s.reservationIdTemp = '';

                        s.selectedAddressUserPhone = '';
                        s.selectedUserEmail = '';
                        s.selectedUserFirstName = '';
                        s.selectedUserLastName = '';
                        s.selectedUserRemarks = '';
                        s.selectedUserDiet = '';
                        s.selectedUserOccasion = '';

                        s.resFirstName = '';
                        s.resLastName = '';
                        s.resPhoneNum = '';
                        s.resEmail = '';
                        s.resRemarks = '';
                        s.resDietaryRestrictions = '';
                        s.resSpecialOccasions = '';

                        s.isPlacingReservation = false;
                        s.isPlacingReservationUpselling = false;

                        s.isDepositOnly = false;

                        s.resShowConfirmationPage = false;

                        s.selectedTaggableVoucher = null;
                        s.selectedBundleTaggableVoucher = null;

                        s.userLoyaltyStamps = [];
                        s.redeemableStackedLoyaltyStamps = [];
                        s.toRedeemStackedLoyaltyStamp = {};

                        ////////////////////////////////////
                      },
                      () => {
                        if (outlet && outlet.uniqueId) {
                          stopQrScanner();

                          linkTo &&
                            linkTo(`${prefix}/outlet/${subdomain}/menu`);

                          window.history.pushState({
                            page: 'outletMenuPage',
                          }, '');
                        }
                      }
                    );

                    // firebase.analytics().logEvent(ANALYTICS.EVENT_QR_TAKEAWAY, {
                    //   eventNameParsed: ANALYTICS_PARSED.EVENT_QR_TAKEAWAY,

                    //   // merchantName: global.merchantName,
                    //   outletName: outlet.name,

                    //   merchantId: outlet.merchantId,
                    //   outletId: outlet.uniqueId,
                    //   // userId: global.userId,
                    // });

                    logEvent(global.analytics, ANALYTICS.EVENT_QR_TAKEAWAY, {
                      eventNameParsed: ANALYTICS_PARSED.EVENT_QR_TAKEAWAY,

                      // merchantName: global.merchantName,
                      outletName: outlet.name,

                      merchantId: outlet.merchantId,
                      outletId: outlet.uniqueId,
                      // userId: global.userId,

                      webUserInfo: `${moment().format('YYYYMMDD-HHmm')}>${global.userName ? global.userName : 'N/A'} (${global.userIdAnonymous ? global.userIdAnonymous : ''})`,
                    });
                  }
                  else {
                    console.log("web scan 3");
                    // linkTo && linkTo(`${prefix}/error`);

                    Alert.alert('Info', 'Invalid takeaway QR');
                  }
                }, 10);

                ////////////////////////////////////////////////////////////////////////////////
                ////////////////////////////////////////////////////////////////////////////////
                ////////////////////////////////////////////////////////////////////////////////

                PaymentStore.update(s => {
                  s.cartItemsPayment = [];
                });

                console.log('[stamp] clear');

                CommonStore.update(
                  (s) => {
                    s.orderType = ORDER_TYPE.PICKUP;

                    s.cartItems = [];
                    s.cartItemsProcessed = [];

                    s.cartItemsReservation = [];
                    s.cartItemsProcessedReservation = [];

                    s.tcCartItems = [];
                    s.tcCartItemsProcessed = [];

                    s.selectedOutletItem = {};
                    s.selectedOutletItemAddOn = {};
                    s.selectedOutletItemAddOnChoice = {};
                    s.onUpdatingCartItem = null;

                    s.cachedAddOnChoiceIdDict = {};

                    s.isLoading = false;

                    s.molpayResult = null;

                    s.payHybridBody = null;
                    s.paymentDetails = null;
                    s.orderIdCreated = '';

                    s.payTopupCreditBody = null;

                    s.currPage = '';

                    s.menuItemDetailModal = false;

                    ////////////////////////////////////

                    // 2023-06-06 - To clear reservation states

                    s.selectedReservationId = '';
                    s.selectedReservationStartTime = null;
                    s.selectedReservationPax = '1';
                    s.selectedReservationOutletSectionId = '';
                    s.selectedReservationOutletSectionName = '';

                    s.editUserReservation = {};

                    // s.reservationIdTemp = '';

                    s.selectedAddressUserPhone = '';
                    s.selectedUserEmail = '';
                    s.selectedUserFirstName = '';
                    s.selectedUserLastName = '';
                    s.selectedUserRemarks = '';
                    s.selectedUserDiet = '';
                    s.selectedUserOccasion = '';

                    s.resFirstName = '';
                    s.resLastName = '';
                    s.resPhoneNum = '';
                    s.resEmail = '';
                    s.resRemarks = '';
                    s.resDietaryRestrictions = '';
                    s.resSpecialOccasions = '';

                    s.isPlacingReservation = false;
                    s.isPlacingReservationUpselling = false;

                    s.isDepositOnly = false;

                    s.resShowConfirmationPage = false;

                    s.selectedTaggableVoucher = null;
                    s.selectedBundleTaggableVoucher = null;

                    s.userLoyaltyStamps = [];
                    s.redeemableStackedLoyaltyStamps = [];
                    s.toRedeemStackedLoyaltyStamp = {};

                    ////////////////////////////////////
                  },
                  () => {
                    linkTo &&
                      linkTo(`${prefix}/outlet/${subdomain}/menu`);
                  }
                );

                ////////////////////////////////////////////////////////////////////////////////
                ////////////////////////////////////////////////////////////////////////////////
                ////////////////////////////////////////////////////////////////////////////////

                // 2022-10-12 - Get and set user id anonymous

                // setTimeout(async () => {
                //   const userIdAnonymousRaw = await AsyncStorage.getItem('userIdAnonymous');

                //   if (userIdAnonymousRaw) {
                //     // means existed

                //     UserStore.update(s => {
                //       s.userIdAnonymous = userIdAnonymousRaw;
                //     });
                //   }
                //   else {
                //     var userIdAnonymousTemp = uuidv4();
                //     UserStore.update(s => {
                //       s.userIdAnonymous = userIdAnonymousTemp;
                //     });

                //     await AsyncStorage.setItem('userIdAnonymous', userIdAnonymousTemp);
                //   }

                //   global.userIdAnonymousLoaded = true;
                // }, 100);

                // if (userIdAnonymous === 'none') {
                //   // means haven't set before
                // }
              }
              else {
                Alert.alert('Info', 'The QR code comes from another outlet.')
              }
            } else {
              // linkTo && linkTo(`${prefix}/error`);

              Alert.alert('Info', 'Invalid takeaway QR.');
            }
          }
        }
      }
    }
    catch (ex) {
      console.error(ex);

      console.error(qrStrResult);
    }

  };

  const updateUserCartDynamicQR = async (json, outlet, firebaseUid) => {
    const body = {
      // userId: firebaseUid,

      userId: '',

      // outletId: outlet.uniqueId,
      outletId: json.outletId,
      tableId: json.tableId,
      tableCode: json.tableCode,
      tablePax: json.tablePax,
      cartItems: [],

      waiterId: json.waiterId,
    };

    global.selectedOutlet = outlet;

    CommonStore.update(s => {
      s.selectedOutlet = outlet;

      s.scannedQrData = json;

      s.orderType = ORDER_TYPE.DINEIN;

      s.selectedOutletTableId = json.tableId;
      s.selectedOutletWaiterId = json.waiterId;
      s.selectedOutletTablePax = json.tablePax;
      s.selectedOutletTableCode = json.tableCode;

      s.timestampOutletCategory = Date.now();

      // 2022-10-08 - Reset cart items
      // s.cartItems = [];
      // s.cartItemsProcessed = [];
      // s.cartOutletId = outlet.uniqueId;

      // s.selectedOutletItem = {};
      // s.selectedOutletItemAddOn = {};
      // s.selectedOutletItemAddOnChoice = {};
      // s.onUpdatingCartItem = null;

      // s.isLoading = false;

      // s.molpayResult = null;
    }, async () => {
      if (outlet && outlet.uniqueId) {
        // navigation.navigate('OutletMenu', { 
        //     outletData: outlet, 
        //     orderType: 0, 
        //     test: 1 
        // });

        stopQrScanner();

        // const subdomain = await AsyncStorage.getItem('latestSubdomain');
        const subdomain = global.subdomainOnly;

        if (!subdomain) {
          linkTo(`${prefix}/outlet/menu`);
        }
        else {
          linkTo(`${prefix}/outlet/${subdomain}/menu`);
        }
        // linkTo && linkTo(`${prefix}/outlet/menu`);

        window.history.pushState({
          page: 'outletMenuPage',
        }, '');
      }

      // if (scanner && scanner.current) {
      //     scanner.current.reactivate();
      // }
    });
  };

  const updateTablePaxDirectly = async (json, outlet, firebaseUid) => {
    const body = {
      tableId: json.tableId,
      // pax: seatingPax,
      pax: 1,
      // outletId: currOutlet.uniqueId,
      outletId: json.outletId,
    };

    stopQrScanner();

    const subdomain = global.subdomainOnly;

    if (!subdomain) {
      linkTo(`${prefix}/outlet/menu`);
    }
    else {
      linkTo(`${prefix}/outlet/${subdomain}/menu`);
    }

    window.history.pushState({
      page: 'outletMenuPage',
    }, '');

    ApiClient.POST(API.updateOutletTablePaxByUser, body).then(async (result) => {
      if (result && result.status === 'success') {
        console.log('ok');

        global.isUpdatedTablePax = true;

        // await AsyncStorage.setItem('latestOutletId', currOutlet.uniqueId);

        PaymentStore.update(s => {
          s.cartItemsPayment = [];
        });

        console.log('[stamp] clear');

        CommonStore.update(s => {
          // 2022-10-08 - Reset cart items
          s.cartItems = [];
          s.cartItemsProcessed = [];
          s.cartOutletId = outlet.uniqueId;

          s.cartItemsReservation = [];
          s.cartItemsProcessedReservation = [];

          s.tcCartItems = [];
          s.tcCartItemsProcessed = [];

          s.selectedOutletItem = {};
          s.selectedOutletItemAddOn = {};
          s.selectedOutletItemAddOnChoice = {};
          s.onUpdatingCartItem = null;

          s.cachedAddOnChoiceIdDict = {};

          s.isLoading = false;

          s.molpayResult = null;

          s.payHybridBody = null;
          s.paymentDetails = null;
          s.orderIdCreated = '';

          s.payTopupCreditBody = null;

          s.currPage = '';

          s.menuItemDetailModal = false;

          ////////////////////////////////////

          // 2023-06-06 - To clear reservation states

          s.selectedReservationId = '';
          s.selectedReservationStartTime = null;
          s.selectedReservationPax = '1';
          s.selectedReservationOutletSectionId = '';
          s.selectedReservationOutletSectionName = '';

          // s.reservationIdTemp = '';

          s.editUserReservation = {};

          s.selectedAddressUserPhone = '';
          s.selectedUserEmail = '';
          s.selectedUserFirstName = '';
          s.selectedUserLastName = '';
          s.selectedUserRemarks = '';
          s.selectedUserDiet = '';
          s.selectedUserOccasion = '';

          s.selectedAddressUserPhone = '';
          s.selectedUserEmail = '';
          s.selectedUserFirstName = '';
          s.selectedUserLastName = '';
          s.selectedUserRemarks = '';
          s.selectedUserDiet = '';
          s.selectedUserOccasion = '';

          s.resFirstName = '';
          s.resLastName = '';
          s.resPhoneNum = '';
          s.resEmail = '';
          s.resRemarks = '';
          s.resDietaryRestrictions = '';
          s.resSpecialOccasions = '';

          s.isPlacingReservation = false;
          s.isPlacingReservationUpselling = false;

          s.isDepositOnly = false;

          s.resShowConfirmationPage = false;

          s.selectedTaggableVoucher = null;
          s.selectedBundleTaggableVoucher = null;

          s.userLoyaltyStamps = [];
          s.redeemableStackedLoyaltyStamps = [];
          s.toRedeemStackedLoyaltyStamp = {};

          ////////////////////////////////////
        });

        CommonStore.update(s => {
          // s.selectedOutlet = qrGenericOutlet;

          s.scannedQrData = json;

          s.orderType = ORDER_TYPE.DINEIN;

          s.selectedOutletTableId = json.tableId;
          s.selectedOutletWaiterId = json.waiterId;
          s.selectedOutletTablePax = json.tablePax;
          s.selectedOutletTableCode = json.tableCode;

          s.timestampOutletCategory = Date.now();
        });
      }
    });
  };

  const stopQrScanner = () => {
    if (global.outletQrScanner &&
      typeof global.outletQrScanner.stop === 'function') {
      global.outletQrScanner.stop();
    }

    setCurrSection(OUTLET_HOMEPAGE_SECTION.HOME);
  };

  // function end

  var iconFontSize = 16;

  if (Dimensions.get("screen").width <= 360) {
    iconFontSize = 11;
    //console.log(Dimensions.get('screen').width)
  }

  const iconTextScale = {
    fontSize: iconFontSize,
  };

  var weekFontSize = 16;

  if (Dimensions.get("screen").width <= 360) {
    weekFontSize = 11;
    //console.log(Dimensions.get('screen').width)
  }

  const weekTextScale = {
    fontSize: weekFontSize,
  };

  var statusFontSize = 16;

  if (Dimensions.get("screen").width <= 360) {
    statusFontSize = 11;
    //console.log(Dimensions.get('screen').width)
  }

  const statusTextScale = {
    fontSize: statusFontSize,
  };

  var timeFontSize = 16;

  if (Dimensions.get("screen").width <= 360) {
    timeFontSize = 10;
    //console.log(Dimensions.get('screen').width)
  }

  const timeTextScale = {
    fontSize: timeFontSize,
  };

  var phoneFontSize = 13;

  if (Dimensions.get("screen").width <= 360) {
    phoneFontSize = 11;
    //console.log(Dimensions.get('screen').width)
  }

  const phoneTextScale = {
    fontSize: phoneFontSize,
  };

  var iconWidth = "26%";

  if (Dimensions.get("screen").width <= 360) {
    iconWidth = "28%";
    //console.log(Dimensions.get('screen').width)
  }

  const iconWidthScale = {
    width: iconWidth,
  };

  var marginRightSpace = 0;

  if (Dimensions.get("screen").width <= 360) {
    marginRightSpace = -1;
    //console.log(Dimensions.get('screen').width)
  }

  const marginRightSpaceScale = {
    marginRight: marginRightSpace,
  };

  var marginTopSpace = 20;

  if (Dimensions.get("screen").width <= 360) {
    marginTopSpace = 10;
    //console.log(Dimensions.get('screen').width)
  }

  const marginTopSpaceScale = {
    marginTop: marginTopSpace,
  };

  const StarRatingView = () => {
    return (
      <>
        {starRating.map((item, key) => {
          return (
            <>
              <TouchableOpacity
                style={{ marginLeft: 3 }}
                key={item}
                onPress={() => setStarRatingDefault(item)}
              >
                <AntDesign
                  name={"star"}
                  size={25}
                  color={
                    item <=
                      Math.round(selectedOutletReviewSummary.ratingsAverage)
                      ? Colors.primaryColor
                      : Colors.fieldtTxtColor
                  }
                />
              </TouchableOpacity>
            </>
          );
        })}
      </>
    );
  };

  const outletAddress = { latitude: outletData ? outletData.lat : 0, longitude: outletData ? outletData.lng : 0 };
  //const openOutletLocation = createOpenLink({ outletAddress, zoom: 30 });

  return (
    <View
      style={[
        styles.container1,
        {
          width: windowWidth,
          height: windowHeight,
        },
      ]}
    >
      <Modal
        style={{ flex: 1 }}
        visible={onStartVisible}
        transparent={true}
        animationType="slide"
      >
        <View
          style={{
            backgroundColor: "rgba(0,0,0,0.5)",
            flex: 1,
            justifyContent: "center",
            alignItems: "center",
            minHeight: Dimensions.get("window").height,
          }}
        >
          <View style={styles.confirmBox}>
            <TouchableOpacity
              onPress={() => {
                setState({ onStartVisible: false });
              }}
            >
              <View
                style={{
                  alignSelf: "flex-start",
                  padding: 14,
                }}
              >
                <Close name="close" size={35} color={"#b0b0b0"} />
              </View>
            </TouchableOpacity>
            <View>
              <Text
                style={{
                  textAlign: "center",
                  fontWeight: "700",
                  fontSize: 20,
                }}
              >
                Choose Takeaway
              </Text>
            </View>
            <View
              style={{
                alignSelf: "center",
                marginTop: 20,
                justifyContent: "center",
                alignItems: "center",
                width: 250,
                height: 40,
                alignContent: "center",
                marginTop: 40,
              }}
            >
              <TouchableOpacity
                style={{
                  backgroundColor: Colors.primaryColor,
                  width: "100%",
                  justifyContent: "center",
                  alignItems: "center",
                  alignContent: "center",
                  borderRadius: 10,
                  height: 50,
                  marginTop: 30,
                  alignSelf: "center",
                }}
                onPress={() => {
                  // setState({ onStartVisible: false, }),
                  // goToMenu(1, null)
                }}
              >
                <Text style={{ fontSize: 20, color: Colors.whiteColor }}>
                  Delivery Menu
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => {
                  setState({ visible: false });
                }}
                style={{
                  backgroundColor: Colors.secondaryColor,
                  width: "100%",
                  justifyContent: "center",
                  alignItems: "center",
                  alignContent: "center",
                  borderRadius: 10,
                  height: 50,
                  marginTop: 20,
                }}
              >
                <Text style={{ fontSize: 20, color: Colors.whiteColor }}>
                  Takeaway Menu
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      <ScrollView style={styles.container} showsHorizontalScrollIndicator={false}>
        {/* <View> */}
        {!(
          outletData.cover ||
          (outletData.merchant && outletData.merchant.logo)
        ) && (
            <View
              style={[
                styles.outletCover,
                {
                  backgroundColor: Colors.secondaryColor,
                  width: '100%',
                  height: windowHeight * 0.5,
                },
              ]}
            ></View>
          )}
        {!outletData.cover &&
          outletData.merchant &&
          outletData.merchant.logo && (
            <AsyncImage
              source={{ uri: outletData.merchant.logo }}
              style={styles.outletCover}
            />
          )}
        {outletData.cover ? (
          <AsyncImage
            source={{ uri: outletData.cover }}
            style={styles.outletCover}
          />
        ) : (
          <></>
        )}

        {/* {outletData.cover &&
          <Image source={{ uri: outletData.cover }} style={styles.outletCover} />
        } */}

        {!isOpeningNowState ? (
          <View
            style={[
              styles.outletCover,
              {
                position: "absolute",
                top: 0,
                left: 0,
                backgroundColor: "black",
                opacity: 0.5,
                // width: Dimensions.get('screen').width * 0.3,
                // height: Dimensions.get('screen').width * 0.3,
                // borderRadius: 13,
              },
            ]}
          ></View>
        ) : (
          <></>
        )}

        {/* <View
          style={{
            position: "absolute",
            right: 24,
            top: 12,
            padding: 8,
            backgroundColor: "#ffffff",
            borderRadius: 25,
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          <TouchableOpacity
            style={{ alignSelf: "center" }}
            onPress={() => {
              if (userFavoriteOutletDict[selectedOutlet.uniqueId]) {
                removeUserFavoriteOutlet();
              } else {
                addUserFavoriteOutlet();
              }
            }}
          >
            <Icon2
              name={
                selectedOutlet &&
                  userFavoriteOutletDict[selectedOutlet.uniqueId]
                  ? "heart"
                  : "heart-o"
              }
              color={Colors.primaryColor}
              size={24}
              style={{
                top: 1,
              }}
            />
          </TouchableOpacity>
        </View> */}
        {/* </View> */}
        <View style={styles.infoTab}>
          <View
            style={{
              flexDirection: "row",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              marginBottom: 10,
            }}
          >
            <Text style={styles.outletName}>{outletData.name}</Text>
            {/* <TouchableOpacity
              style={{ marginLeft: 2 }}
              onPress={() => {
                onShare();
              }}
            >
              <Gshare
                width={25}
                height={25}
              />
              {/* <Icon3 name="share-google" color={Colors.primaryColor} size={30} /> *
            </TouchableOpacity> */}
          </View>
          {/* <View
            style={{
              flexDirection: "column",
              justifyContent: "center",
              alignItems: "center",
            }}
          > */}
          {/* <View
              style={{
                flexDirection: "row",
                justifyContent: "center",
                alignItems: "center",
              }}
            > */}
          <View
            style={{
              flexDirection: "row",
              justifyContent: "center",
              alignItems: "center",
              width: isMobile() ? '90%' : "70%",
            }}
          >
            <TouchableOpacity
              style={{
                flexDirection: "row",
                justifyContent: "center",
                alignItems: "center",
                width: '100%',
              }}
              onPress={() => {
                //openOutletLocation()
              }}
            >
              <Lpin
                height={25}
                width={25}
              />
              {/* <MaterialIcons
                  name={"location-pin"}
                  size={30}
                  color={Colors.primaryColor}
                /> */}
              <Text
                style={[styles.outletAddress, { marginLeft: 5 }]}
                numberOfLines={5}
              >
                {outletData.address}
              </Text>
            </TouchableOpacity>
          </View>
          {/* </View> */}

          {/* <View
              style={{
                flexDirection: "row",
                marginTop: 10,
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <Text
                style={{
                  textAlign: "center",
                  fontSize: 18,
                  color: Colors.primaryColor,
                  fontFamily: "NunitoSans-Bold",
                  marginRight: 5,
                }}
              >
                {selectedOutletReviewSummary.ratingsAverage.toFixed(1)}
              </Text>
              <StarRatingView />

              <TouchableOpacity
                onPress={() => {
                  CommonStore.update((s) => {
                    s.selectedOutletReview = selectedOutletReviewSummary;
                  });

                  navigation.navigate("ReviewList", {
                    selectedOutletReviewSummary: selectedOutlet,
                  });
                }}
              >
                <Text
                  style={{
                    textAlign: "center",
                    fontSize: 14,
                    color: Colors.fieldtTxtColor,
                    fontFamily: "NunitoSans-Regular",
                    marginLeft: 10,
                    textDecorationLine: "underline",
                  }}
                >{`${selectedOutletReviewSummary.ratingsCount} Review${selectedOutletReviewSummary.ratingsCount > 1 ? "s" : ""
                  }`}</Text>
              </TouchableOpacity>
            </View> */}
          {/* </View> */}
        </View>

        <View style={styles.workingHourTab}>
          {/* <View
            style={[
              iconWidthScale,
              {
                //width: '26%',
                marginLeft: 8,
                backgroundColor: "red"
              },
            ]}
          >
            {selectedOutlet &&
              merchantsDict[selectedOutlet.merchantId] &&
              merchantsDict[selectedOutlet.merchantId].logo && (
                <AsyncImage
                  source={{ uri: merchantsDict[selectedOutlet.merchantId].logo }}
                  style={[styles.logo, {}]}
                />
              )}
          </View> */}
          <View
            style={{
              width: isMobile() ? '100%' : "55%",
              marginLeft: isMobile() ? windowWidth * 0.1 : "9.8%",
              //backgroundColor: 'red',
              marginVertical: isMobile() ? 10 : 20,
              //alignSelf: 'center'
              ...isMobile() && {
                marginBottom: 20,
              }
            }}
          >
            <View
              style={{
                flexDirection: "row",
                display: "flex",
                alignItems: "center",
                //backgroundColor: 'red',
              }}
            >
              <Feather
                name="calendar"
                size={20}
                color={Colors.primaryColor}
                style={{
                  //top: -2,
                  marginRight: isMobile() ? -10 : "-1%",
                }}
              />

              <View style={{ marginLeft: 10 }}>
                {outletsOpeningDict &&
                  selectedOutlet &&
                  outletsOpeningDict[selectedOutlet.uniqueId] &&
                  [selectedOutlet].map((itemParam, index) => {
                    const item = {
                      ...itemParam,
                    };

                    var isOpeningNow = false;

                    const currDay = moment().day();

                    var outletOpeningToday = null;

                    var startTimeStr = null;
                    var endTimeStr = null;

                    ////////////////////////////////////////////////////////

                    var outletOpeningWeek = [];
                    const weekOrders = [
                      "sunday",
                      "monday",
                      "tuesday",
                      "wednesday",
                      "thursday",
                      "friday",
                      "saturday",
                    ];

                    try {
                      const outletOpening = outletsOpeningDict[item.uniqueId];
                      if (outletOpening) {
                        outletOpeningToday = outletOpening[WEEK[currDay]];
                      }

                      if (outletOpeningToday) {
                        startTimeStr = outletOpeningToday.split("-")[0];
                        endTimeStr = outletOpeningToday.split("-")[1];

                        const startTime = moment(startTimeStr, "HHmm");
                        const endTime = moment(endTimeStr, "HHmm");

                        isOpeningNow =
                          moment().isSameOrAfter(startTime) &&
                          moment().isBefore(endTime);
                      }

                      ////////////////////////////////////////////////////////
                      // do for all days

                      for (var i = 0; i < weekOrders.length; i++) {
                        const tempOpeningDay = outletOpening[weekOrders[i]];

                        var startTimeStrDay = "";
                        var endTimeStrDay = "";
                        var isOpeningNowDay = false;

                        if (tempOpeningDay && tempOpeningDay.length > 0) {
                          startTimeStrDay = tempOpeningDay.split("-")[0];
                          endTimeStrDay = tempOpeningDay.split("-")[1];

                          const startTime = moment(startTimeStrDay, "HHmm");
                          const endTime = moment(endTimeStrDay, "HHmm");

                          isOpeningNow =
                            moment().isSameOrAfter(startTime) &&
                            moment().isBefore(endTime);
                        }

                        outletOpeningWeek.push({
                          startTimeStr: startTimeStrDay,
                          endTimeStr: endTimeStrDay,
                          isOpeningNow: isOpeningNowDay,
                          day: i,
                        });
                      }

                      if (isOpeningNowState !== isOpeningNow) {
                        setIsOpeningNowState(isOpeningNow);
                      }
                    } catch (ex) {
                      console.error(ex);
                    }

                    // if (!isOpeningNow) {
                    //   return;
                    // }

                    return (
                      <View
                        style={{
                          display: "flex",
                          alignItems: "center",
                          //backgroundColor: 'red',
                        }}
                      >
                        <View
                          style={{
                            //marginBottom: 5,
                            flexDirection: "row",
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "flex-start",
                            width: "100%",
                            //backgroundColor: 'blue',
                            paddingLeft: 16,

                          }}
                        >
                          <View
                            style={{
                              width: "100%",
                              //backgroundColor: 'yellow',
                              //marginLeft: '100%'
                              marginTop: 5,
                            }}
                          >
                            <Text
                              style={[
                                statusTextScale,
                                {
                                  color: "black",
                                  fontFamily: "NunitoSans-Bold",
                                  letterSpacing: 0,
                                },
                              ]}
                            >
                              {isOpeningNow ? "OPEN" : "CLOSED"}
                            </Text>
                          </View>
                        </View>
                      </View>
                    );
                  })}
              </View>
            </View>

            <View
              style={{
                flexDirection: "row",
                display: "flex",
                alignItems: "center",
                marginTop: 2,
                //justifyContent: 'space-between',
                //backgroundColor: 'purple',
              }}
            >
              <Feather
                name="clock"
                size={20}
                color={Colors.primaryColor}
                style={{
                  //top: -2,
                  marginTop: 10,
                  marginRight: isMobile() ? -5 : "-1%",
                }}
              />

              <View style={{ marginLeft: 10, width: isMobile() ? '50%' : "75%", }}>
                {outletsOpeningDict &&
                  selectedOutlet &&
                  outletsOpeningDict[selectedOutlet.uniqueId] &&
                  [selectedOutlet].map((itemParam, index) => {
                    const item = {
                      ...itemParam,
                    };

                    var isOpeningNow = false;

                    const currDay = moment().day();
                    console.log("current dat", currDay);

                    var outletOpeningToday = null;

                    var startTimeStr = null;
                    var endTimeStr = null;

                    ////////////////////////////////////////////////////////

                    var outletOpeningWeek = [];
                    const weekOrders = [
                      "sunday",
                      "monday",
                      "tuesday",
                      "wednesday",
                      "thursday",
                      "friday",
                      "saturday",
                    ];

                    try {
                      const outletOpening = outletsOpeningDict[item.uniqueId];
                      if (outletOpening) {
                        outletOpeningToday = outletOpening[WEEK[currDay]];
                      }

                      if (outletOpeningToday) {
                        startTimeStr = outletOpeningToday.split("-")[0];
                        endTimeStr = outletOpeningToday.split("-")[1];

                        const startTime = moment(startTimeStr, "HHmm");
                        const endTime = moment(endTimeStr, "HHmm");

                        isOpeningNow =
                          moment().isSameOrAfter(startTime) &&
                          moment().isBefore(endTime);
                      }

                      ////////////////////////////////////////////////////////
                      // do for all days

                      for (var i = 0; i < weekOrders.length; i++) {
                        const tempOpeningDay = outletOpening[weekOrders[i]];

                        var startTimeStrDay = "";
                        var endTimeStrDay = "";
                        var isOpeningNowDay = false;

                        if (tempOpeningDay && tempOpeningDay.length > 0) {
                          startTimeStrDay = tempOpeningDay.split("-")[0];
                          endTimeStrDay = tempOpeningDay.split("-")[1];

                          const startTime = moment(startTimeStrDay, "HHmm");
                          const endTime = moment(endTimeStrDay, "HHmm");

                          isOpeningNow =
                            moment().isSameOrAfter(startTime) &&
                            moment().isBefore(endTime);
                        }

                        outletOpeningWeek.push({
                          startTimeStr: startTimeStrDay,
                          endTimeStr: endTimeStrDay,
                          isOpeningNow: isOpeningNowDay,
                          day: i,
                        });
                      }
                    } catch (ex) {
                      console.error(ex);
                    }

                    // if (!isOpeningNow) {
                    //   return;
                    // }

                    return (
                      <View
                        style={{
                          display: "flex",
                          alignItems: "center",
                          //width: '100%',
                          //backgroundColor: 'red',
                        }}
                      >
                        <View
                          style={{
                            marginBottom: 5,
                            flexDirection: "row",
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "flex-start",
                            width: "100%",
                            //backgroundColor: 'blue',

                            paddingLeft: 6,
                            // marginLeft: '2.5%',
                            marginTop: 15,
                          }}
                        >
                          <View
                            style={{
                              width: windowWidth * 0.22,
                              marginLeft: 5,
                              //backgroundColor: 'green',
                            }}
                          >
                            <Text
                              style={[
                                weekTextScale,
                                {
                                  //fontWeight: "bold",
                                  //fontSize: 14,
                                  fontFamily: "NunitoSans-Bold",
                                  // letterSpacing: 0,
                                },
                              ]}
                            >
                              {WEEK[moment().day()][0].toUpperCase() +
                                WEEK[moment().day()].slice(1)}
                            </Text>
                          </View>

                          <View
                            style={{
                              width: "4%",
                              // marginRight: 1,
                            }}
                          >
                            <Text
                              style={{
                                fontSize: 12,
                                fontWeight: "bold",
                                color: Colors.descriptionColor,
                                fontFamily: "NunitoSans-Regular",
                              }}
                            >
                              {":"}
                            </Text>
                          </View>

                          <View
                            style={[
                              marginRightSpaceScale,
                              {
                                //marginRight: -4,
                                //width: '30%',
                                //backgroundColor: 'yellow',
                                ...isMobile() && {
                                  // width: '45%'
                                }
                              },
                            ]}
                          >
                            <Text
                              style={[
                                timeTextScale,
                                {
                                  //fontSize: 12,
                                  color: Colors.descriptionColor,
                                  fontFamily: "NunitoSans-SemiBold",
                                  // color: 'black',
                                  // fontWeight: 'bold',
                                },
                              ]}
                            >
                              {startTimeStr == null
                                ? "null"
                                : moment(startTimeStr, "HHmm").format("h:mma")}
                              -
                              {endTimeStr == null
                                ? "null"
                                : moment(endTimeStr, "HHmm").format("h:mma")}
                            </Text>
                          </View>

                          <TouchableOpacity
                            onPress={() => {
                              setShowWeekOpenings(!showWeekOpenings);
                            }}
                            style={{
                              marginLeft: isMobile() ? 20 : 30,
                            }}
                          >
                            <Ionicons
                              name={
                                showWeekOpenings
                                  ? "chevron-up-sharp"
                                  : "chevron-down-sharp"
                              }
                              size={23}
                              color={Colors.primaryColor}
                              style={{
                                marginLeft: 1,
                              }}
                            />
                          </TouchableOpacity>
                        </View>

                        {showWeekOpenings &&
                          outletOpeningWeek.map((item, index) => {
                            // show all weeks

                            return (
                              <View
                                style={{
                                  marginBottom: 5,
                                  flexDirection: "row",
                                  display: "flex",
                                  alignItems: "center",
                                  justifyContent: "flex-start",
                                  width: "100%",

                                  paddingLeft: 6,
                                  marginLeft: '2.5%'
                                }}
                              >
                                <View
                                  style={{
                                    width: isMobile() ? '46%' : "13%",
                                  }}
                                >
                                  <Text
                                    style={[
                                      weekTextScale,
                                      {
                                        // fontWeight: "bold",
                                        //fontSize: 13,
                                        fontFamily: "NunitoSans-Regular",
                                      },
                                    ]}
                                  >
                                    {WEEK[item.day][0].toUpperCase() +
                                      WEEK[item.day].slice(1)}
                                  </Text>
                                </View>

                                <View
                                  style={{
                                    width: "4%",
                                    // marginRight: 1,
                                  }}
                                >
                                  <Text
                                    style={{
                                      fontSize: 12,
                                      color: Colors.descriptionColor,
                                      fontFamily: "NunitoSans-Regular",
                                    }}
                                  >
                                    {":"}
                                  </Text>
                                </View>

                                {/* <View style={{
                                width: '18%',
                              }}>
                                <Text style={[statusTextScale, {
                                  //fontSize: 13,
                                  color: Colors.descriptionColor,
                                  fontFamily: "NunitoSans-Regular",
                                  letterSpacing: 0,
                                }]}>
                                  {item.isOpeningNow ? 'OPEN' : 'CLOSE'}
                                </Text>
                              </View> */}

                                <View
                                  style={{
                                    // width: "45%",
                                  }}
                                >
                                  <Text
                                    style={[
                                      timeTextScale,
                                      {
                                        //fontSize: 12,
                                        fontFamily: "NunitoSans-Regular",
                                        color: Colors.descriptionColor,
                                      },
                                    ]}
                                  >
                                    {item.startTimeStr == null
                                      ? "null"
                                      : moment(item.startTimeStr, "HHmm").format(
                                        "h:mma"
                                      )}
                                    -
                                    {item.endTimeStr == null
                                      ? "null"
                                      : moment(item.endTimeStr, "HHmm").format(
                                        "h:mma"
                                      )}
                                  </Text>
                                </View>

                                {/* <TouchableOpacity onPress={() => { setShowWeekOpenings(!showWeekOpenings) }}>
                                <Ionicons
                                  name={showWeekOpenings ? 'chevron-up-sharp' : 'chevron-down-sharp'}
                                  size={26}
                                  color={Colors.primaryColor}
                                  style={{
                                    marginLeft: 2,
                                  }}
                                />
                              </TouchableOpacity>       */}
                              </View>
                            );
                          })}
                      </View>
                    );
                  })}
                {/* {
                outletsOpeningDict && outletsOpeningDict[item.uniqueId] && outletData.openings.map((item, index) => {
                  const item = {
                    ...itemParam,
                  };

                  var isOpeningNow = false;

                  const currDay = moment().day();

                  var outletOpeningToday = null;

                  var startTimeStr = null;
                  var endTimeStr = null;

                  ////////////////////////////////////////////////////////

                  var outletOpeningWeek = [];
                  const weekOrders = [
                    'sunday',
                    'monday',
                    'tuesday',
                    'wednesday',
                    'thursday',
                    'friday',
                    'saturday',
                  ];

                  try {
                    const outletOpening = outletsOpeningDict[item.uniqueId];
                    if (outletOpening) {
                      outletOpeningToday = outletOpening[WEEK[currDay]];
                    }

                    if (outletOpeningToday) {
                      startTimeStr = outletOpeningToday.split('-')[0];
                      endTimeStr = outletOpeningToday.split('-')[1];

                      const startTime = moment(startTimeStr, 'HHmm');
                      const endTime = moment(endTimeStr, 'HHmm');

                      isOpeningNow = moment().isSameOrAfter(startTime) && moment().isBefore(endTime);
                    };

                    ////////////////////////////////////////////////////////
                    // do for all days

                    for (var i = 0; i < weekOrders.length; i++) {
                      const tempOpeningDay = outletOpening[weekOrders[i]];

                      var startTimeStrDay = '';
                      var endTimeStrDay = '';
                      var isOpeningNowDay = false;

                      if (tempOpeningDay && tempOpeningDay.length > 0) {
                        startTimeStrDay = tempOpeningDay.split('-')[0];
                        endTimeStrDay = tempOpeningDay.split('-')[1];

                        const startTime = moment(startTimeStrDay, 'HHmm');
                        const endTime = moment(endTimeStrDay, 'HHmm');

                        isOpeningNow = moment().isSameOrAfter(startTime) && moment().isBefore(endTime);
                      }

                      outletOpeningWeek.push({
                        startTimeStr: startTimeStrDay,
                        endTimeStr: endTimeStrDay,
                        isOpeningNow: isOpeningNowDay,
                      });
                    }
                  }
                  catch (ex) {
                    console.error(ex);
                  }

                  // if (!isOpeningNow) {
                  //   return;
                  // }

                  const today = currentDate;
                  const day = moment(today).format("dddd");
                  //console.log(day);
                  if (item.week == day) {
                    return (
                      <View>
                        <View style={{ marginBottom: 5, flexDirection: 'row' }}>
                          <View style={{ width: 65 }}>
                            <Text style={{ fontWeight: "bold", fontSize: 12, fontFamily: "NunitoSans-Regular" }}>{item.week}</Text>
                          </View>
                          <View style={{ width: 42 }}>
                            <Text style={{ fontSize: 12, color: Colors.descriptionColor, fontFamily: "NunitoSans-Regular" }}>: OPEN </Text>
                          </View>
                          <View style={{ width: 120 }}>
                            <Text style={{ fontSize: 12, fontFamily: "NunitoSans-Regular" }}>{item.startTime}-{item.endTime}</Text>
                          </View>
                          <TouchableOpacity onPress={() => { showAllDay() }}>
                            <Ionicons name="chevron-down-sharp" size={20} color={Colors.primaryColor} />
                          </TouchableOpacity>

                        </View>

                        {allDay ? outletData.openings.map((item, index) => {
                          const today = currentDate;
                          const day = moment(today).format("dddd");
                          //console.log(day);
                          if (item.week !== day) {
                            return (
                              <View style={{ marginBottom: 5, flexDirection: 'row', justifyContent: 'space-between' }}>
                                <View style={{ width: 65 }}>
                                  <Text style={{ fontWeight: "bold", fontSize: 12, fontFamily: "NunitoSans-Regular" }}>{item.week}</Text>
                                </View>
                                <View style={{ width: 42 }}>
                                  <Text style={{ fontSize: 12, color: Colors.descriptionColor, fontFamily: "NunitoSans-Regular" }}>: OPEN </Text>
                                </View>
                                <View style={{ width: 120 }}>
                                  <Text style={{ fontSize: 12, fontFamily: "NunitoSans-Regular" }}>{item.startTime}-{item.endTime}</Text>
                                </View>
                                <View style={{ width: "10%" }}>

                                </View>
                              </View>

                            )
                          }
                        }) : null}
                      </View>
                    )
                  }
                })
              } */}
                {/* {outletData.openings != undefined && !openToday ?
                (
                  <View>
                    <View style={{ marginBottom: 5, flexDirection: 'row' }}>
                      <View style={{ width: 65 }}>
                        <Text style={{ fontWeight: "bold", fontSize: 12, fontFamily: "NunitoSans-Regular" }}>{moment(currentDate).format("dddd")}</Text>
                      </View>
                      <View style={{ width: 44 }}>
                        <Text style={{ fontSize: 12, color: Colors.descriptionColor, fontFamily: "NunitoSans-Regular" }}>: CLOSE </Text>
                      </View>
                      <View style={{ width: 118 }}>
                        <Text style={{ fontSize: 12, fontFamily: "NunitoSans-Regular" }}></Text>
                      </View>
                      <TouchableOpacity onPress={() => { showAllDay() }}>
                        <Ionicons name="chevron-down-sharp" size={20} color={Colors.primaryColor} />
                      </TouchableOpacity>

                    </View>

                    {allDay ? outletData.openings.map((item, index) => {
                      const today = currentDate;
                      const day = moment(today).format("dddd");
                      //console.log(day);
                      if (item.week !== day) {
                        return (
                          <View style={{ marginBottom: 5, flexDirection: 'row', justifyContent: 'space-between' }}>
                            <View style={{ width: 65 }}>
                              <Text style={{ fontWeight: "bold", fontSize: 12, fontFamily: "NunitoSans-Regular" }}>{item.week}</Text>
                            </View>
                            <View style={{ width: 42 }}>
                              <Text style={{ fontSize: 12, color: Colors.descriptionColor, fontFamily: "NunitoSans-Regular" }}>: OPEN </Text>
                            </View>
                            <View style={{ width: 120 }}>
                              <Text style={{ fontSize: 12, fontFamily: "NunitoSans-Regular" }}> {item.startTime}-{item.endTime}</Text>
                            </View>
                            <View style={{ width: "10%" }}>

                            </View>
                          </View>

                        )
                      }
                    }) : null}
                  </View>
                ) : null} */}
              </View>
            </View>

            <View
              style={{
                flexDirection: "row",
                marginTop: 10,
                display: "flex",
                alignItems: "center",
                // backgroundColor: 'green',
              }}
            >
              <TouchableOpacity
                style={{ flexDirection: "row" }}
                onPress={() => {
                  Linking.openURL(`tel:${outletData.phone}`);
                }}
              >
                <Feather
                  name="phone-call"
                  size={20}
                  color={Colors.primaryColor}
                />
                <View
                  style={{
                    //marginBottom: 5,
                    flexDirection: "row",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "flex-start",
                    width: "100%",

                    paddingLeft: 6,
                  }}
                >
                  <View
                    style={{
                      width: windowWidth * 0.22,
                      marginLeft: 10,
                    }}
                  >
                    <Text
                      style={[
                        weekTextScale,
                        {
                          // fontWeight: "bold",
                          //fontSize: 13,
                          fontFamily: "NunitoSans-Bold",
                          color: "black",
                        },
                      ]}
                    >
                      Phone
                    </Text>
                  </View>

                  <View
                    style={{
                      width: "4%",
                      // marginRight: 1,
                    }}
                  >
                    <Text
                      style={{
                        fontSize: 12,
                        fontWeight: "bold",
                        color: Colors.descriptionColor,
                        fontFamily: "NunitoSans-Regular",
                      }}
                    >
                      {":"}
                    </Text>
                  </View>

                  <View
                    style={{
                      // width: "45%",
                    }}
                  >
                    <Text
                      style={[
                        timeTextScale,
                        {
                          //fontSize: 12,
                          fontFamily: "NunitoSans-Regular",
                          color: Colors.descriptionColor,
                        },
                      ]}
                    >
                      {outletData.phone}
                    </Text>
                  </View>
                </View>
              </TouchableOpacity>
            </View>
          </View>
        </View>

        {/* bottom buttons tab */}
        {
          currSection === OUTLET_HOMEPAGE_SECTION.HOME
            ?
            <View
              style={{
                //display: "flex",
                alignItems: "center",
                width: "100%",
                justifyContent: "space-evenly"
              }}
            >
              <View style={{ width: '90%', flexDirection: 'row', }}>
                {/* scan to order */}
                <View style={{ width: '25%', height: windowHeight * 0.18, alignItems: "center", }}>
                  <TouchableOpacity
                    onPress={async () => {
                      const hasCamera = await QrScanner.hasCamera();

                      if (hasCamera) {
                        if (selectedOutlet && selectedOutlet.uniqueId) {
                          setCurrSection(OUTLET_HOMEPAGE_SECTION.SCAN_ORDER);

                          setTimeout(() => {
                            const qrScannerElem = document.getElementById('qrScanner');

                            if (qrScannerElem) {
                              // To enforce the use of the new api with detailed scan results, call the constructor with an options object, see below.
                              global.outletQrScanner = new QrScanner(
                                qrScannerElem,
                                result => {
                                  console.log('decoded qr code:', result);

                                  parseScannedQr(result);
                                },
                                { /* your options or returnDetailedScanResult: true if you're not specifying any other options */ },
                              );

                              global.outletQrScanner.start();

                              // for test scan qr code without camera
                              // fetch("https://koodooprod.s3.ap-southeast-1.amazonaws.com/test/generic-WhatsApp+Image+2024-06-19+at*****.05+PM.jpeg")
                              //   .then(response => response.blob())
                              //   .then(blob => {
                              //     const url = URL.createObjectURL(blob);

                              //     QrScanner.scanImage(url)
                              //       .then(result => {
                              //         console.log(result);

                              //         parseScannedQr(result);
                              //       })
                              //       .catch(error => {
                              //         console.log(error || 'No QR code found.');
                              //       });
                              //   });
                            }
                            else {
                              Alert.alert('Info', 'Unable to initialize the scanner.');
                            }
                          }, 500);
                        } else {
                          Alert.alert('Info', 'Scanner is being initialized, please try again later.');
                        }
                      }
                      else {
                        Alert.alert('Info', `This device didn't had the camera support.`);
                      }
                    }}
                  >
                    <View style={styles.actionBtn}>
                      <ScanOrder
                        width={25}
                        height={25}
                        color={Colors.tabGrey}
                      />
                    </View>
                  </TouchableOpacity>
                  <Text style={{ fontFamily: "NunitoSans-Regular", fontSize: 14, marginTop: 5, color: Colors.descriptionColor, textAlign: 'center' }}>
                    {'Scan\nOrder'}
                  </Text>
                </View>

                {/* delivery */}
                {/* <View style={[styles.actionView], { width: windowWidth / 4, height: windowHeight / 4.5 }}>
              <TouchableOpacity
                onPress={() => {
                  if (selectedOutlet.isDeliveryAccepted) {
                    CommonStore.update((s) => {
                      s.orderType = ORDER_TYPE.DELIVERY;
                    });

                    navigation.navigate("OutletMenu", {
                      outletData: selectedOutlet,
                      test: 1,
                      navFrom: "TAKEAWAY",
                    });
                  } else {
                    Alert.alert(
                      "Info",
                      "This outlet is not accepting delivery orders."
                    );
                  }
                }}
              >
                <View style={styles.actionBtn}>
                  <Delivery
                    width={30}
                    height={30}
                    color={Colors.tabGrey}
                  />
                </View>
              </TouchableOpacity>
              <Text style={[iconTextScale, styles.actionText], { marginLeft: 5, marginTop: 3, color: Colors.descriptionColor, }}>Delivery</Text>
            </View> */}

                {/* takeaway */}
                <View style={{ width: '25%', height: windowHeight * 0.18, alignItems: "center", }}>
                  <TouchableOpacity
                    onPress={async () => {
                      //goToMenu(1, null)
                      // setState({ onStartVisible: true, })

                      const subdomain = selectedOutlet.subdomain;

                      linkTo && linkTo(`${prefix}/outlet/${subdomain}/takeaway`);

                      // if (selectedOutlet.isPickupAccepted) {
                      //   // const subdomain = await AsyncStorage.getItem("latestSubdomain");
                      //   const subdomain = selectedOutlet.subdomain;

                      //   linkTo && linkTo(`${prefix}/outlet/${subdomain}/takeaway`);

                      //   // navigation.navigate("OutletMenu", {
                      //   //   outletData: selectedOutlet,
                      //   //   test: 1,
                      //   //   navFrom: "TAKEAWAY",
                      //   // });
                      // } else {
                      //   Alert.alert(
                      //     "Info",
                      //     "This outlet is not accepting takeaway orders."
                      //   );
                      // }
                    }}>
                    <View style={[styles.actionBtn]}>
                      <View style={{ bottom: 1, }}>
                        <Takeaway
                          width={35}
                          height={35}
                          color={Colors.tabGrey}
                        />
                      </View>
                    </View>
                  </TouchableOpacity>
                  <Text style={{ fontFamily: "NunitoSans-Regular", fontSize: 14, marginTop: 5, color: Colors.descriptionColor, textAlign: 'center' }}>Takeaway</Text>
                </View>

                {/* reservation */}
                <View style={{ width: '25%', height: windowHeight * 0.18, alignItems: "center", }}>
                  <TouchableOpacity
                    onPress={async () => {
                      if (selectedOutlet.reservationStatus) {
                        // navigation.navigate("Reservation - KooDoo Web Order", {
                        //   outletData: outletData,
                        // });

                        // const subdomain = await AsyncStorage.getItem("latestSubdomain");

                        const subdomain = selectedOutlet.subdomain;

                        linkTo && linkTo(`${prefix}/outlet/${subdomain}/reservation`);
                      } else {
                        // alert(
                        //   "Info: This outlet is not accepting reservation at the moment."
                        // );

                        Alert.alert(
                          "Info",
                          "This outlet is not accepting reservations."
                        );
                      }
                    }}
                  >
                    <View style={styles.actionBtn}>
                      {/* <Image
                  source={require("../asset/image/reserve.png")}
                  style={{ width: 26, height: 26 }}
                /> */}
                      <Reservation
                        width={35}
                        height={35}
                        color={Colors.tabGrey}
                      />
                    </View>
                  </TouchableOpacity>
                  <Text style={{ fontFamily: "NunitoSans-Regular", fontSize: 14, marginTop: 5, color: Colors.descriptionColor, textAlign: 'center' }}>Reservation</Text>
                </View>

                {/* queue */}
                <View style={{ width: '25%', height: windowHeight * 0.18, alignItems: "center", }}>
                  <TouchableOpacity
                    style={styles.actionBtn}
                    onPress={async () => {
                      // console.log('outletData', outletData)
                      // navigation.navigate("Queue - KooDoo Web Order", {
                      //   //outletData: outletData,
                      //   //cId: outletData.uniqueId,
                      // });
                      // CommonStore.update(
                      //   (s) => {
                      //     s.selectedOutlet = outletData;
                      //   }
                      // );
                      // console.log('selectedOutlet', selectedOutlet)

                      const subdomain = await AsyncStorage.getItem("latestSubdomain");

                      linkTo && linkTo(`${prefix}/outlet/${subdomain}/queue`);
                    }}
                  >
                    <View>
                      {/* <Image
                  source={require("../asset/image/line.png")}
                  resizeMode={"contain"}
                  style={{ width: 28, height: 28 }}
                /> */}
                      <Queue
                        width={35}
                        height={35}
                        color={Colors.tabGrey}
                      />
                    </View>
                  </TouchableOpacity>
                  <Text style={{ fontFamily: "NunitoSans-Regular", fontSize: 14, marginTop: 5, color: Colors.descriptionColor, textAlign: 'center' }}>Queue</Text>
                </View>

                {/* {outletData.reservationStatus ? <View style={styles.actionView}>
          <TouchableOpacity onPress={() => { props.navigation.navigate('CreateReservation', { outletData: outletData }) }}>
            <View style={styles.actionBtn}>
              <Image source={require('../../asset/image/reserve.png')} />
            </View>
          </TouchableOpacity>
          <Text style={styles.actionText}>Reservation</Text>

        </View> : null} */}

                {/* pre-order */}
                {/* <View style={[styles.actionView], { width: windowWidth / 4, height: windowHeight / 4.5 }}>
              <TouchableOpacity
                onPress={() => {
                  navigation.navigate("PreorderList", { outletData: outletData });
                }}
              >
                <View style={styles.actionBtn}>
                  <AntDesign
                    name={"clockcircleo"}
                    size={33}
                    color={Colors.tabGrey}
                  />
                </View>
              </TouchableOpacity>
              <Text style={[iconTextScale, styles.actionText], { marginLeft: 1, marginTop: 3, color: Colors.descriptionColor, }}>Pre-Order</Text>
            </View> */}
              </View>

              <View style={{ width: '90%', flexDirection: 'row', }}>
                {/* rewards */}
                {
                  (email || userNumber)
                    ?
                    <View style={{ width: '25%', height: windowHeight * 0.15, alignItems: "center", }}>
                      <TouchableOpacity
                        style={styles.actionBtn}
                        onPress={async () => {
                          // navigation.navigate("RewardList");

                          if (
                            // isMobile() &&
                            (email || userNumber)
                          ) {
                            const subdomain = await AsyncStorage.getItem("latestSubdomain");

                            linkTo && linkTo(`${prefix}/outlet/${subdomain}/outlet-rewards`);
                          }
                          else {
                            Alert.alert('Info', 'This page is available for registered user only.');
                          }
                        }}
                      >
                        <View>
                          <Ionicons
                            name={"ribbon-outline"}
                            size={32}
                            color={Colors.tabGrey}
                          />
                        </View>
                      </TouchableOpacity>
                      <Text style={{ fontFamily: "NunitoSans-Regular", fontSize: 14, marginTop: 5, color: Colors.descriptionColor, textAlign: 'center' }}>{'Outlet\nRewards'}</Text>
                    </View>
                    :
                    <></>
                }

                <View style={{ width: '25%', height: windowHeight * 0.18, alignItems: "center", }}>
                  <TouchableOpacity
                    style={styles.actionBtn}
                    onPress={async () => {
                      // navigation.navigate("RewardList");

                      // window.open(__DEV__ ? `http://localhost:5400$/rewards/login/${selectedOutlet.uniqueId}` : `https://mykoodoo.com/rewards/login/${selectedOutlet.uniqueId}`);
                      // window.open(`http://localhost:5600/rewards/login/${selectedOutlet.uniqueId}`);
                      window.open(`https://mykoodoo.com/rewards/login/${selectedOutlet.uniqueId}`);
                    }}
                  >
                    <View>
                      <Feather
                        name={"gift"}
                        size={30}
                        color={Colors.tabGrey}
                      />
                    </View>
                  </TouchableOpacity>
                  <Text style={{ fontFamily: "NunitoSans-Regular", fontSize: 14, marginTop: 5, color: Colors.descriptionColor, textAlign: 'center' }}>{'My\nRewards'}</Text>
                </View>

                {/* {outletData.queueStatus ?
          <View style={styles.actionView}>
            <TouchableOpacity style={styles.actionBtn} onPress={() => { props.navigation.navigate('CreateQueue', { outletData: outletData }) }}>
              <View>
                <Image source={require('../../asset/image/line.png')} />
              </View>
            </TouchableOpacity>
            <Text style={styles.actionText}>Queue</Text>

          </View> : null} */}

                {/* docket */}
                {/* <View style={[styles.actionView], { width: windowWidth / 4, height: windowHeight / 4.5 }}>
              <TouchableOpacity
                style={styles.actionBtn}
                onPress={() => {
                  props.navigation.navigate("DocketList", {
                    outletData: outletData,
                  });
                }}
              >
                <View>
                  <Docket
                    width={35}
                    height={35}
                    color={Colors.tabGrey}
                  />
                </View>
              </TouchableOpacity>
              <Text style={[iconTextScale, styles.actionText], { marginLeft: 9, marginTop: 3, color: Colors.descriptionColor, }}>Docket</Text>
            </View> */}

                {/* feedback */}
                {/* <View style={[styles.actionView], { width: windowWidth / 4, height: windowHeight / 4.5 }}>
              <TouchableOpacity
                onPress={() => {
                  navigation.navigate("CreateFeedback", {
                    outletData: outletData,
                  });
                }}
              >
                <View style={styles.actionBtn}>
                  <Feedback
                    width={35}
                    height={35}
                    color={Colors.tabGrey}
                  />
                </View>
              </TouchableOpacity>
              <Text style={[iconTextScale, styles.actionText], { marginLeft: 1, marginTop: 3, color: Colors.descriptionColor, }}>Feedback</Text>
            </View> */}

                {/* <View style={styles.actionView}>
            <TouchableOpacity style={styles.actionBtn} onPress={() => {
              ringOutlet();
            }}>
              <View>
                <Icon3 name={'bell'} size={45} color={Colors.tabGrey} />
              </View>
            </TouchableOpacity>
            <Text style={[iconTextScale, styles.actionText]}>Ring</Text>
          </View> */}
              </View>

              <View style={{ width: '90%', flexDirection: 'row', }}>
                {/* ring */}
                {/* <View style={[styles.actionView], { width: windowWidth / 4, height: windowHeight / 4.5 }}>
              <TouchableOpacity
                style={styles.actionBtn}
                onPress={() => {
                  ringOutlet();
                }}
              >
                <View>
                  <Icon3 name={"bell"} size={45} color={Colors.tabGrey} />
                </View>
              </TouchableOpacity>
              <Text style={[iconTextScale, styles.actionText], { marginLeft: 16, marginTop: 3, color: Colors.descriptionColor, }}>Ring</Text>
            </View> */}

                {/* promotion */}
                {/* <View style={[styles.actionView], { width: windowWidth / 4, height: windowHeight / 4.5 }}>
              <TouchableOpacity
                style={styles.actionBtn}
                onPress={() => {
                  navigation.navigate("PromotionList");
                }}
              >
                <View>
                  <Feather name={"percent"} size={30} color={Colors.tabGrey} />
                </View>
              </TouchableOpacity>
              <Text style={[iconTextScale, styles.actionText], { marginLeft: -2, marginTop: 3, color: Colors.descriptionColor, }}>Promotion</Text>
            </View> */}

                <View style={{ width: '25%', height: windowHeight * 0.15, alignItems: "center", }}></View>
              </View>
            </View>
            :
            <></>
        }

        {/* scan qr section */}
        {
          currSection === OUTLET_HOMEPAGE_SECTION.SCAN_ORDER
            ?
            <View
              style={{
                //display: "flex",
                alignItems: "center",
                width: "100%",
                justifyContent: "space-evenly"
              }}
            >
              <View style={{
                width: '90%',
                height: '100%',
                flexDirection: 'row',

                position: 'relative',

                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}>
                <video id='qrScanner'></video>

                <TouchableOpacity
                  testID="closeButton"
                  style={[
                    styles.closeButton,
                    {
                      right: isMobile()
                        ? windowWidth * 0.04
                        : windowWidth * 0.01,
                      top: isMobile()
                        ? windowWidth * 0.04
                        : windowWidth * 0.01,
                    },
                  ]}
                  onPress={async () => {
                    stopQrScanner();
                  }}
                >
                  <AntDesign
                    name="closecircle"
                    size={25}
                    color={Colors.fieldtTxtColor}
                  />
                </TouchableOpacity>
              </View>
            </View>
            :
            <></>
        }
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  confirmBox: {
    width: 350,
    height: 280,
    borderRadius: 30,
    backgroundColor: Colors.whiteColor,
  },
  container: {
    flex: 1,
    //backgroundColor: 'blue',
    backgroundColor: Colors.fieldtBgColor,
    // display: 'none',
  },
  container1: {
    // flex: 1,
    width: Dimensions.get("window").width,
    height: Dimensions.get("window").height,
    backgroundColor: "#ffffff",
    position: "relative",
  },
  outletCover: {
    width: isMobile() ? "100%" : "auto",
    alignSelf: "center",
    height: isMobile() ? undefined : Dimensions.get("window").height * 0.5,
    aspectRatio: isMobile() ? 2 : 2,
    resizeMode: isMobile() ? "stretch" : "stretch",
    ...(!isMobile() && {}),
    // borderRadius: 5,
  },
  infoTab: {
    backgroundColor: Colors.highlightColor,
    padding: 16,
    justifyContent: "center",
    alignContent: "center",
    alignItems: "center",
  },
  workingHourTab: {
    padding: 15,
    flexDirection: "row",
    paddingLeft: 0,
    paddingRight: 5,
    backgroundColor: Colors.fieldtBgColor,
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "flex-start",
  },
  outletAddress: {
    //textAlign: 'center',
    fontSize: 14,
    color: Colors.descriptionColor,
    fontFamily: "NunitoSans-Regular",
  },
  outletReview: {
    textAlign: "center",
    fontSize: 14,
    color: "#000000",
    fontFamily: "NunitoSans-Regular",
    marginLeft: 5,
  },
  outletName: {
    // fontFamily: "NunitoSans-Bold",
    // fontSize: 20,
    // marginBottom: 10,

    fontSize: 20,
    lineHeight: 25,
    textAlign: "center",
    fontFamily: "NunitoSans-Bold",
    color: Colors.mainTxtColor,

    top: 2,
  },
  logo: {
    borderRadius: 10,
    width: 90,
    height: 90,
  },
  actionTab: {
    flexDirection: "row",
    //marginTop: 20,
    marginBottom: 10,
    //width: "100%",
    justifyContent: "center",
    alignItems: "center",
    alignContent: "center",
    //backgroundColor: 'red',
  },
  actionView: {
    // width: Dimensions.get("window").width / 4.0,
    // height: Dimensions.get("window").height / 4.5,
    justifyContent: "center",
    alignItems: "center",
    alignContent: "center",
  },
  actionBtn: {
    borderRadius: 50,
    width: 60,
    height: 60,
    borderColor: Colors.secondaryColor,
    borderWidth: 1.5,
    justifyContent: "center",
    alignItems: "center",
  },
  actionText: {
    //fontSize: 14,
    marginTop: 5,
    fontFamily: "NunitoSans-Regular",
    color: Colors.descriptionColor,
    //textAlign: "center"
  },

  viewAddress: {
    //height: Styles.width * 1,
    width: Styles.width * 0.85,
    backgroundColor: Colors.whiteColor,
    borderRadius: 12,
    padding: Styles.width * 0.03,
    //alignItems: 'center',
    //justifyContent: 'center'
  },

  closeButton: {
    position: 'absolute',
    right: 10,
    top: 10,
    elevation: 1000,
    zIndex: 1000,
  },
});

export default OutletScreen;
