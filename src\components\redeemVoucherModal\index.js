
import React, { useState, useEffect } from 'react';
import {
    View,
    Image,
    ActivityIndicator,
    Text,
    Modal,
    useWindowDimensions,
    TouchableOpacity,
    TextInput,
    Dimensions,
    StyleSheet,
    ScrollView
} from 'react-native';
import Colors from '../../constant/Colors';
import { getCachedUrlContent, getImageFromFirebaseStorage, isMobile, signInWithPhoneForCRMUser, updateWebTokenAnonymous } from '../../util/commonFuncs';
import AntDesign from "react-native-vector-icons/AntDesign";
import { UserStore } from '../../store/userStore';
import { TempStore } from '../../store/tempStore';
// import firebase from 'firebase';
import { getFirestore, collection, query, where, getDocs, limit } from "firebase/firestore";
import { Collections } from '../../constant/firebase';
import { CommonStore } from '../../store/commonStore';
import ApiClient from '../../util/ApiClient';
import API from '../../constant/API';
import moment from 'moment';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { GR_STATUS, ORDER_TYPE } from '../../constant/common';
import { apiUrl, prefix } from '../../constant/env';
import FontAwesome from "react-native-vector-icons/FontAwesome";

import Toastify from 'toastify-js';
import "toastify-js/src/toastify.css";
import AsyncImage from '../asyncImage';
import Ionicons from 'react-native-vector-icons/Ionicons';
import Entypo from "react-native-vector-icons/Entypo";
import { LOYALTY_CAMPAIGN_TYPE, LOYALTY_PROMOTION_TYPE } from '../../constant/loyalty';
import Marquee from 'react-fast-marquee';

const VOUCHER_PROMOTION_INFO_PAGE = {
    REGISTER_USER: 'REGISTER_USER',
    CART_INFO: 'CART_INFO',
};

const onBeforeUnload = () => {
    if (global.createUserOrderBody) {
        // means still existed, help to create order

        var createUserOrderBodyLocal = {
            ...global.createUserOrderBody,
        };

        global.createUserOrderBody = null;

        // ApiClient.POST(API.createUserOrder, createUserOrderBodyLocal, {
        //     timeout: 100000,
        // }).then(async (result) => {
        //     if (createUserOrderBodyLocal.orderType === ORDER_TYPE.PICKUP) {
        //         global.takeawayOrders.push(result);
        //     }
        // });        

        // const headers = {
        //     type: 'application/json',
        //     'Authorization': `Bearer ${global.accessToken}`,            
        // };
        // const blob = new Blob([JSON.stringify(createUserOrderBodyLocal)], headers);
        // navigator.sendBeacon(apiUrl + API.createUserOrderBeacon, blob);

        ////////////////////////////////////////////////////////////////////////

        // fetch(apiUrl + API.createUserOrderBeacon, {
        //     method: 'POST', // *GET, POST, PUT, DELETE, etc.
        //     mode: 'cors', // no-cors, *cors, same-origin
        //     cache: 'no-cache', // *default, no-cache, reload, force-cache, only-if-cached
        //     // credentials: 'same-origin', // include, *same-origin, omit
        //     headers: {
        //         'Content-Type': 'application/json',
        //         'Authorization': `Bearer ${global.accessToken}`,            
        //     },
        //     redirect: 'follow', // manual, *follow, error
        //     referrerPolicy: 'no-referrer', // no-referrer, *no-referrer-when-downgrade, origin, origin-when-cross-origin, same-origin, strict-origin, strict-origin-when-cross-origin, unsafe-url
        //     body: JSON.stringify(createUserOrderBodyLocal) // body data type must match "Content-Type" header
        // });

        ////////////////////////////////////////////////////////////////////////

        ApiClient.POST(API.createUserOrderBeacon, createUserOrderBodyLocal, {
            // timeout: 100000,
        }).then(async (result) => {
            if (createUserOrderBodyLocal.orderType === ORDER_TYPE.PICKUP) {
                global.takeawayOrders.push(result);
            }
        });

        ///////////////////////////////////////////        

        // ApiClient.POST(API.deleteUserCart, global.deleteUserCartBody || null).then((result) => {
        //     if (result && result.status === "success") {
        //         console.log("ok");
        //     }
        // });

        global.deleteUserCartBody = null;

        ////////////////////////////////////////////////////////////////////////
    }
};

window.addEventListener('beforeUnload', onBeforeUnload);
window.addEventListener('beforeunload', onBeforeUnload);
window.addEventListener('unload', onBeforeUnload);
// window.addEventListener('visibilitychange', onBeforeUnload);
// window.addEventListener('pagehide', onBeforeUnload);

var cooldownTimerTime = 10; // 10

const RedeemVoucherModal = props => {
    const {
        linkToFunc,
    } = props;

    const {
        width: windowWidth,
        height: windowHeight,
    } = useWindowDimensions();

    const [showVoucherPromotionInfoModal, setShowVoucherPromotionInfoModal] = useState(false);

    const [currVoucherPromotionInfoPage, setCurrVoucherPromotionInfoPage] = useState(VOUCHER_PROMOTION_INFO_PAGE.REGISTER_USER);

    const [verifySMSModal, setVerifySMSModal] = useState(false);
    const [verificationCode, setVerificationCode] = useState('');
    const [isVerified, setIsVerified] = useState(false); // herks test

    const [name, setName] = useState('');
    const [phone, setPhone] = useState('');
    const [rating, setRating] = useState(5);
    const [review, setReview] = useState('A very good enjoyable experience.');
    const [birthday, setBirthday] = useState(moment(Date.now()));
    const [address, setAddress] = useState('');
    const [lat, setLat] = useState(0);
    const [lng, setLng] = useState(0);

    const [sentCode, setSentCode] = useState(true);
    const [existingCustomer, setExistingCustomer] = useState(false); //For testing UI purpose

    const [outletId, setOutletId] = useState('');
    const [outletName, setOutletName] = useState('');
    const [outletCover, setOutletCover] = useState('');
    const [merchantId, setMerchantId] = useState('');
    const [merchantName, setMerchantName] = useState('');

    // const registerUserOrder = CommonStore.useState(s => s.registerUserOrder);
    const [registerUserOrder, setRegisterUserOrder] = useState({});

    const [cooldownTimer, setCooldownTimer] = useState(null);
    const [cooldownTimerTimeState, setCooldownTimerTimeState] = useState(cooldownTimerTime);
    const [cooldownActive, setCooldownActive] = useState(true);

    const [submittedGoogleReview, setSubmittedGoogleReview] = useState(false);
    const [showGRIframe, setShowGRIframe] = useState(false);
    const [GRStatus, setGRStatus] = useState(GR_STATUS.PENDING);

    const isLoading = CommonStore.useState(s => s.isLoading);

    const selectedOutlet = CommonStore.useState(s => s.selectedOutlet);
    const selectedMerchant = CommonStore.useState(s => s.selectedMerchant);

    const redeemVoucherModal = TempStore.useState(s => s.redeemVoucherModal);
    const redeemVoucher = TempStore.useState(s => s.redeemVoucher);

    const showVoucherInfo = TempStore.useState(s => s.showVoucherInfo);
    const showVoucherPromotionInterestedInfo = TempStore.useState(s => s.showVoucherPromotionInterestedInfo);
    const isPaidFirstOrder = TempStore.useState(s => s.isPaidFirstOrder);

    const cartItems = TempStore.useState(s => s.cartItemsT);
    const cartItemsProcessed = TempStore.useState(s => s.cartItemsProcessedT);
    const cartOutletId = TempStore.useState(s => s.cartOutletIdT);

    const userEmail = UserStore.useState((s) => s.email);
    const userName = UserStore.useState((s) => s.name);
    const userNumber = UserStore.useState((s) => s.number);

    const gReview = UserStore.useState((s) => s.gReview);

    const userIdAnonymous = UserStore.useState(s => s.userIdAnonymous);

    const claimVoucherAnonymous = TempStore.useState(s => s.claimVoucherAnonymous);
    const [isTNCExpand, setIsTNCExpand] = useState(false);
    const [voucherPopupFullScreen, setVoucherPopupFullScreen] = useState(false);

    // const userCart = CommonStore.useState((s) => s.userCart);

    // useEffect(() => {
    //     return () => {
    //         if (global.createUserOrderBody) {
    //             var createUserOrderBodyLocal = {
    //                 ...global.createUserOrderBody,
    //             };

    //             global.createUserOrderBody = null;

    //             ApiClient.POST(API.createUserOrderBeacon, createUserOrderBodyLocal, {
    //                 timeout: 100000,
    //             }).then(async (result) => {
    //                 if (createUserOrderBodyLocal.orderType === ORDER_TYPE.PICKUP) {
    //                     global.takeawayOrders.push(result);
    //                 }
    //             });

    //             ///////////////////////////////////////////        

    //             ApiClient.POST(API.deleteUserCart, global.deleteUserCartBody || null).then((result) => {
    //                 if (result && result.status === "success") {
    //                     console.log("ok");
    //                 }
    //             });

    //             global.deleteUserCartBody = null;
    //         }
    //     };
    // }, []);

    /////////////////////////////////////////

    // 2023-12-05 - for google review stuff

    // useEffect(() => {
    //     if (showGRIframe && GRStatus === GR_STATUS.PENDING &&
    //         selectedOutlet && selectedOutlet.placeUrl) {
    //         // setTimeout(() => {
    //         //     global.intervalGR = setInterval(() => {
    //         //         // var pageGoogleSearch = document.querySelectorAll('[style="font-family: Verdana, sans; cursor: pointer; font-size: 16px; text-anchor: start; font-weight: normal;"]');

    //         //         const idGR = document.querySelector("#idGR");

    //         //         console.log(idGR);
    //         //         console.log(idGR.document);

    //         //         // Find the 'a' element
    //         //         const aElement = idGR.document.querySelector('a[data-index="2"][role="tab"]');

    //         //         // Check if the 'a' element exists
    //         //         if (aElement) {
    //         //             // Check if the third child has the attribute 'data-vt'
    //         //             const childElement = aElement.querySelector(':third-child[data-vt="1"]');

    //         //             // Check if the child has a <span> element with the text 'Reviews'
    //         //             const spanElement = childElement.querySelector('span');

    //         //             if (spanElement && spanElement.textContent.trim() === 'Reviews') {
    //         //                 console.log('Found the desired element:', aElement);
    //         //             } else {
    //         //                 console.log('No matching element found (1)');
    //         //             }
    //         //         } else {
    //         //             console.log('No matching element found (2)');
    //         //         }
    //         //     }, 100);
    //         // }, 5000);
    //     }
    //     else {
    //         if (global.intervalGR !== null) {
    //             clearInterval(global.intervalGR);
    //         }
    //     }
    // }, [GRStatus, showGRIframe, selectedOutlet]);

    /////////////////////////////////////////

    useEffect(() => {
        if (userName && userNumber) {
            setName(userName);
            setPhone(userNumber);
            setIsVerified(true);
        }
    }, [userName, userNumber]);

    useEffect(() => {
        if (selectedOutlet && selectedOutlet.uniqueId && selectedMerchant && selectedMerchant.uniqueId) {
            setOutletId(selectedOutlet.uniqueId);
            setOutletName(selectedOutlet.name);
            setOutletCover(selectedOutlet.cover);
            setMerchantId(selectedOutlet.merchantId);
            setMerchantName(selectedMerchant.name);
        }
    }, [selectedOutlet, selectedMerchant]);

    const redeemVoucherFunc = async () => {
        const currentPath = window.location.pathname;
        if (currentPath.includes('/error') || currentPath.includes('/scan')) {
            if (window.confirm('Info\nThis table QR code has expired. Please scan another QR code to continue.')) {
                window.location.reload();
            }
            return;
        }
        let isValidToUse = true;

        if (redeemVoucher.activationDate && moment().isBefore(redeemVoucher.activationDate)) {
            // means can't use first

            window.confirm(`Info\nThis voucher can only be used starting from ${moment(redeemVoucher.activationDate).format('Do MMM YYYY')} onwards.`);

            isValidToUse = false;
        }

        if (redeemVoucher.loyaltyCampaignId) {
            const loyaltyCampaignSnapshot = await getDocs(
                query(
                    collection(global.db, Collections.LoyaltyCampaign),
                    where('uniqueId', '==', redeemVoucher.loyaltyCampaignId),
                    limit(1),
                )
            );

            if (!loyaltyCampaignSnapshot.empty) {
                const loyaltyCampaign = loyaltyCampaignSnapshot.docs[0].data();

                if (loyaltyCampaign.loyaltyCampaignType === LOYALTY_CAMPAIGN_TYPE.BIRTHDAY) {
                    if (global.crmUser) {
                        console.log('test logs');
                        console.log(moment(global.crmUser.dob).isSame(global.crmUser.createdAt, 'day'));
                        console.log(!moment(global.crmUser.dob).isSame(moment(), 'date'));
                        console.log(!moment(global.crmUser.dob).isSame(moment(), 'month'));
                        
                        if (
                            moment(global.crmUser.dob).isSame(global.crmUser.createdAt, 'day') ||
                            !moment(global.crmUser.dob).isSame(moment(), 'date') ||
                            !moment(global.crmUser.dob).isSame(moment(), 'month')) {
                            window.confirm(`Info\nThis voucher can only be used on birthday ${moment(global.crmUser.dob).format('Do MMM')}.`);

                            isValidToUse = false;
                        }
                    }
                    else {
                        window.confirm(`Info\nThis voucher can only be used on birthday ${moment(global.crmUser.dob).format('Do MMM')}.`);

                        isValidToUse = false;
                    }
                }
                else {
                    // do nothing
                }
            }
        }

        if (isValidToUse) {
            if (redeemVoucher.voucherType === LOYALTY_PROMOTION_TYPE.ZUS_BUNDLE) {
                // pop new bundle page
                TempStore.update((s) => {
                    s.redeemVoucherModal = false;
                    s.rewardsModalEU = false;
                });

                CommonStore.update((s) => {
                    s.selectedBundleTaggableVoucher = redeemVoucher;
                    s.selectedTaggableVoucher = redeemVoucher;
                });
                TempStore.update((s) => {
                    s.voucherBundle = true;
                })

                TempStore.update((s) => {
                    s.voucherBundle = true;
                })
            } else if (redeemVoucher.voucherType !== LOYALTY_PROMOTION_TYPE.FREE_ITEM) {
                redeemVoucherFreeItem();
            } else {

                CommonStore.update((s) => {
                    s.selectedBundleTaggableVoucher = null;
                    s.selectedTaggableVoucher = redeemVoucher;
                });

                // alert('Please proceed with the payment method of Pay Now to redeem your voucher');

                const subdomain = await AsyncStorage.getItem("latestSubdomain");

                if (!subdomain) {
                    global.linkToFunc && global.linkToFunc(`${prefix}/outlet/cart`)
                } else {
                    global.linkToFunc && global.linkToFunc(`${prefix}/outlet/${subdomain}/cart`)
                }

                CommonStore.update((s) => {
                    s.isFromCartPage = false;
                });

                TempStore.update((s) => {
                    s.redeemVoucherModal = false;
                });

                global.isRedeemVoucherModalShownOnce = true;
            }
        }
    };

    const redeemVoucherFreeItem = async () => {
        console.log("Redeemed");

        if (redeemVoucher.voucherType !== LOYALTY_PROMOTION_TYPE.FREE_ITEM) {

            if (redeemVoucher.criteriaList.length > 0) {
                const firstCriteria = redeemVoucher.criteriaList[0];

                if (firstCriteria.variation === 'SPECIFIC_PRODUCTS') {
                    // check variationItemsSku which product is required
                    //variationItemsSku is using outletitem.sku - outletitem = selectedoutletitems 

                    // console.log('first criteria list', firstCriteria.variationItemsSku[0])

                    CommonStore.update((s) => {
                        s.selectedBundleTaggableVoucher = null;
                        s.selectedTaggableVoucher = redeemVoucher;
                    })
                    global.linkToFunc && global.linkToFunc(`${prefix}/outlet/menu`)
                }
                else { // mean is PRODUCT_OF_CATEGORY
                    //variationItemsSku is OutletItemCategory.name

                    // console.log('first criteria list', firstCriteria.variationItemsSku[0])

                    CommonStore.update((s) => {
                        s.selectedBundleTaggableVoucher = null;
                        s.selectedTaggableVoucher = redeemVoucher;
                    })

                    global.linkToFunc && global.linkToFunc(`${prefix}/outlet/menu`);
                }

                TempStore.update((s) => {
                    s.redeemVoucherModal = false;
                });

                global.isRedeemVoucherModalShownOnce = true;
            }
            else {
                window.confirm('Info\nSomething went wrong.');
            }
        }
    };

    console.log('=====================test=====================');
    console.log(name);
    console.log(phone);
    console.log('=====================test=====================');

    return (
        <>
            {
                redeemVoucherModal
                    ?
                    <Modal
                        style={{
                            // width: windowWidth,
                            // height: windowHeight,
                        }}
                        visible={redeemVoucherModal}
                        transparent={voucherPopupFullScreen ? false : true}
                        animationType={"none"}
                    >
                        <View
                            style={{
                                flex: 1,
                                // backgroundColor: 'rgba(0, 0, 0, 0.7)',
                                backgroundColor: Colors.secondaryColor,
                                justifyContent: 'center',
                                alignItems: 'center',
                            }}
                        >
                            <TouchableOpacity
                                onPress={() => {
                                    TempStore.update(s => {
                                        s.redeemVoucherModal = false;
                                    });
                                }}
                                style={{ justifyContent: 'flex-start', width: '100%', height: windowHeight * 0.055, opacity: 0, backgroundColor: 'transparent' }}>
                            </TouchableOpacity>

                            <TouchableOpacity
                                testID="closeButton"
                                style={[
                                    styles.closeButton,
                                    {
                                        right: isMobile()
                                            ? windowWidth * 0.04
                                            : windowWidth * 0.01,
                                        top: isMobile()
                                            ? windowWidth * 0.04
                                            : windowWidth * 0.01,
                                    },
                                ]}
                                onPress={() => {
                                    TempStore.update(s => {
                                        s.redeemVoucherModal = false;
                                    });
                                }}
                            >
                                <AntDesign
                                    name="closecircle"
                                    size={25}
                                    color={Colors.blackColor}
                                />
                            </TouchableOpacity>

                            <View style={{ backgroundColor: 'white', borderRadius: 20, height: windowHeight * 0.88, width: windowWidth * 0.9, overflow: 'hidden' }}>

                                <ScrollView showsVerticalScrollIndicator={false} contentContainerStyle={{ alignItems: 'center', justifyContent: 'center', }}>
                                    {/* {!isLoading ? ( */}
                                    <>
                                        <View style={{ justifyContent: 'center', alignItems: 'center', marginTop: 25, marginBottom: 10, }}>
                                            <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: 26, }}>
                                                Hi {userName ? userName : 'Guest'}!
                                            </Text>
                                        </View>
                                        <View
                                            style={{
                                                alignItems: 'center',
                                                backgroundColor: '#ffffff',
                                                justifyContent: 'center',
                                                borderColor: Colors.fieldtTxtColor,
                                            }}>
                                            {(redeemVoucher && redeemVoucher.image) ?
                                                <>
                                                    <AsyncImage
                                                        source={{ uri: redeemVoucher.image }}
                                                        item={redeemVoucher}
                                                        style={{
                                                            width: windowWidth * 0.82,
                                                            height: voucherPopupFullScreen ? windowWidth * 0.75 : windowHeight * 0.32,
                                                            marginBottom: 10,
                                                            borderRadius: 20,
                                                        }}
                                                    />
                                                </>
                                                :
                                                <>
                                                    <View style={{
                                                        backgroundColor: Colors.secondaryColor,
                                                        width: windowWidth * 0.82,
                                                        height: voucherPopupFullScreen ? windowWidth * 0.75 : windowHeight * 0.32,
                                                        marginBottom: 10,
                                                        alignSelf: 'center',
                                                        justifyContent: 'center',
                                                        alignItems: 'center',
                                                        borderRadius: 20,
                                                    }}>
                                                        <Ionicons name="fast-food-outline" size={voucherPopupFullScreen ? 200 : 80} />
                                                    </View>
                                                </>
                                            }
                                            <Text
                                                numberOfLines={2}
                                                style={{
                                                    width: windowWidth * 0.82,
                                                    fontSize: redeemVoucher && redeemVoucher.campaignName && redeemVoucher.campaignName.length > 15 ? '2em' : "2.5em",
                                                    fontFamily: "NunitoSans-Bold",
                                                    marginBottom: 10,
                                                    textAlign: 'center',
                                                }}>
                                                {redeemVoucher && redeemVoucher.campaignName ? redeemVoucher.campaignName : 'N/A'}
                                            </Text>
                                            <Marquee style={{ marginBottom: 10, }}>
                                                <Text
                                                    numberOfLines={1}
                                                    style={{
                                                        width: windowWidth * 0.82,
                                                        fontSize: 16,
                                                        fontFamily: "NunitoSans-SemiBold",
                                                        color: Colors.blackColor,
                                                        marginBottom: 10,
                                                        textAlign: 'center',
                                                    }}>
                                                    {redeemVoucher && redeemVoucher.campaignDescription ? redeemVoucher.campaignDescription : 'N/A'}
                                                </Text>
                                            </Marquee>
                                            <View style={{
                                                marginBottom: 15,
                                                ...!voucherPopupFullScreen && {
                                                    width: windowWidth * 0.9,
                                                    paddingHorizontal: 20,
                                                    justifyContent: "center",
                                                    alignItems: 'center'
                                                }
                                            }}>
                                                <Text style={{
                                                    fontSize: 16,
                                                    fontFamily: "NunitoSans-SemiBold",
                                                    marginBottom: 5,
                                                }}>
                                                    Available to redeem for
                                                </Text>

                                                <View style={{
                                                    flexDirection: 'row',
                                                    justifyContent: 'center',
                                                    alignItems: 'center',
                                                    marginBottom: 0,
                                                }}>
                                                    <View style={{
                                                        flexDirection: 'row',
                                                        alignItems: 'center',
                                                        justifyContent: 'center',
                                                        width: '100%',
                                                    }}>
                                                        <View
                                                            style={{
                                                                paddingHorizontal: 20,
                                                                width: windowWidth * 0.6,
                                                                height: 45,
                                                                textAlign: 'center',
                                                                justifyContent: 'center',
                                                                alignItems: 'center',
                                                                marginTop: 10,
                                                                marginBottom: 10,
                                                            }}
                                                        >
                                                            <Text
                                                                numberOfLines={1}
                                                                style={{
                                                                    fontSize: 16,
                                                                    fontFamily: "NunitoSans-Bold",
                                                                    color: Colors.blackColor,
                                                                }}>
                                                                {userName ? userName : 'Guest'}
                                                            </Text>
                                                        </View>
                                                    </View>
                                                </View>
                                                <View style={{
                                                    flexDirection: 'row',
                                                    justifyContent: 'center',
                                                    justifyContent: 'space-between',
                                                    alignItems: 'center',
                                                    marginBottom: 0,
                                                }}>
                                                    <View style={{
                                                        flexDirection: 'row',
                                                        alignItems: 'center',
                                                        width: '100%',
                                                    }}>
                                                        <View
                                                            style={{
                                                                paddingHorizontal: 20,
                                                                width: windowWidth * 0.6,
                                                                height: 45,
                                                                textAlign: 'center',
                                                                justifyContent: 'center',
                                                                alignItems: 'center',
                                                                marginTop: 10,
                                                                marginBottom: 10,
                                                            }}
                                                        >
                                                            <Text
                                                                numberOfLines={1}
                                                                style={{
                                                                    fontSize: 16,
                                                                    fontFamily: "NunitoSans-Bold",
                                                                    color: Colors.blackColor,
                                                                }}>
                                                                {userNumber ? userNumber : 'N/A'}
                                                            </Text>
                                                        </View>
                                                    </View>
                                                    <View style={{}}>

                                                    </View>
                                                </View>
                                            </View>
                                            {/* {(redeemVoucher && redeemVoucher.voucherTerms) ? */}
                                            <View style={{
                                                marginHorizontal: 30, marginBottom: 10,
                                                ...!voucherPopupFullScreen && {
                                                    paddingHorizontal: 20,
                                                    width: windowWidth * 0.9,
                                                    alignItems: 'center',
                                                    justifyContent: 'center',
                                                    opacity: (redeemVoucher && redeemVoucher.voucherTerms) ? 100 : 0,
                                                }
                                            }}>
                                                {/* <Text
                                                            style={{
                                                                fontSize: 16,
                                                                fontFamily: "NunitoSans-SemiBold",
                                                                marginBottom: 5,
                                                            }}>
                                                            Terms and Conditions
                                                        </Text> */}
                                                <TouchableOpacity
                                                    disabled={!(redeemVoucher && redeemVoucher.voucherTerms)}
                                                    onPress={() => {
                                                        alert(`${(redeemVoucher && redeemVoucher.voucherTerms) ? redeemVoucher.voucherTerms : 'N/A'}`);
                                                    }}>
                                                    <Text numberOfLines={1}
                                                        style={{
                                                            fontSize: 16,
                                                            fontFamily: "NunitoSans-SemiBold",
                                                            marginBottom: 5,
                                                            color: Colors.blackColor,
                                                        }}>
                                                        {(redeemVoucher && redeemVoucher.voucherTerms) ? redeemVoucher.voucherTerms : 'N/A'}
                                                    </Text>
                                                </TouchableOpacity>
                                            </View>
                                            {/* : null} */}

                                            < View style={{ flexDirection: 'row', marginBottom: 5 }}>
                                                <Text style={{
                                                    fontSize: "0.8em",
                                                    fontFamily: "NunitoSans-SemiBold",
                                                    color: Colors.blackColor,
                                                }}>
                                                    {'Valid from '}
                                                </Text>
                                                <Text style={{
                                                    fontSize: "0.8em",
                                                    fontFamily: "NunitoSans-SemiBold",
                                                    color: Colors.blackColor,
                                                }}>
                                                    {
                                                        redeemVoucherModal
                                                            ?
                                                            (
                                                                (
                                                                    redeemVoucher.activationDays !== undefined
                                                                    &&
                                                                    redeemVoucher.expirationDays !== undefined
                                                                )
                                                                    ?
                                                                    `${moment().add(redeemVoucher.activationDays, 'day').format('DD MMM YY')} to ${moment().add(redeemVoucher.activationDays + redeemVoucher.expirationDays, 'day').format('DD MMM YY')}`
                                                                    :
                                                                    `${moment(redeemVoucher.promoDateStart).format('DD MMM YY')} to ${moment(redeemVoucher.promoDateEnd).format('DD MMM YY')}`
                                                            )
                                                            :
                                                            'N/A'
                                                    }
                                                </Text>
                                            </View>
                                            <View>
                                            </View>
                                        </View>
                                        {/* Left semi-circle */}
                                        <View style={{
                                            width: 70,
                                            height: 70,
                                            backgroundColor: Colors.primaryColor,
                                            borderRadius: 120,
                                            position: 'absolute',
                                            left: -35, // Adjust as needed
                                            bottom: '19%' // Adjust as needed //30%
                                        }}></View>

                                        {/* Right semi-circle */}
                                        <View style={{
                                            width: 70,
                                            height: 70,
                                            backgroundColor: Colors.primaryColor,
                                            borderRadius: 120,
                                            position: 'absolute',
                                            right: -35, // Adjust as needed
                                            bottom: '23%' // Adjust as needed //76%
                                        }}></View>
                                    </>
                                    {/* ) : (
                                        <>
                                            <View style={{ justifyContent: 'center', alignSelf: 'center', marginTop: windowHeight * 0.35, alignItems: 'center' }}>
                                                <ActivityIndicator color={Colors.primaryColor} size={60} />
                                            </View>
                                        </>
                                    )} */}

                                </ScrollView>

                                <View style={{ position: 'relative', bottom: 0, justifyContent: 'center', alignItems: 'center' }}>
                                    <View style={{ width: windowWidth * 0.9, flexDirection: 'row', alignItems: 'center', justifyContent: 'center', marginTop: 5, marginBottom: 5, }}>
                                        <View style={{
                                            borderTop: "2px dashed #bbbbbb",
                                            color: "#fff",
                                            width: windowWidth * 0.9,
                                            height: "0",
                                        }}></View>
                                        <View style={{
                                            width: 50,
                                            height: 50,
                                            backgroundColor: Colors.secondaryColor,
                                            borderRadius: 50,
                                            position: 'absolute',
                                            left: -25,
                                        }}></View>
                                        <View style={{
                                            width: 50,
                                            height: 50,
                                            backgroundColor: Colors.secondaryColor,
                                            borderRadius: 40,
                                            position: 'absolute',
                                            right: -25,
                                        }}></View>
                                    </View>

                                    <TouchableOpacity
                                        disabled={isLoading}
                                        onPress={async () => {
                                            // proceedOrderAfterUserInfo();
                                            redeemVoucherFunc();
                                        }}>
                                        <View
                                            style={{
                                                backgroundColor: Colors.primaryColor,
                                                borderRadius: 20,
                                                cursor: "pointer",
                                                justifyContent: 'center',
                                                alignItems: 'center',
                                                border: "none",
                                                marginBottom: 15,
                                                marginTop: 20,
                                            }}
                                        >
                                            {isLoading ?
                                                <View style={{
                                                    justifyContent: 'center', alignSelf: 'center', alignItems: 'center', paddingHorizontal: 70,
                                                    paddingVertical: 10,
                                                }}>
                                                    <ActivityIndicator color={Colors.whiteColor} size={'small'} />
                                                </View>
                                                :
                                                <Text style={{
                                                    fontSize: 16,
                                                    fontFamily: "NunitoSans-SemiBold",
                                                    color: Colors.whiteColor,
                                                    paddingHorizontal: 70,
                                                    paddingVertical: 10,
                                                }}>{isLoading ? `PROCESSING...` : `REDEEM NOW`}</Text>
                                            }

                                        </View>
                                    </TouchableOpacity>
                                </View>
                            </View>
                            <TouchableOpacity
                                onPress={() => {
                                    TempStore.update(s => {
                                        s.redeemVoucherModal = false;
                                    });
                                }}
                                style={{ justifyContent: 'flex-start', width: '100%', height: windowHeight * 0.055, opacity: 0, backgroundColor: 'transparent' }}>
                            </TouchableOpacity>
                        </View>
                    </Modal>
                    :
                    <></>
            }
        </>
    );
};

const styles = StyleSheet.create({
    modalContainer: {
        flex: 1,
        backgroundColor: Colors.modalBgColor,
        alignItems: "center",
        justifyContent: "center",
    },
    modalView: {
        height: Dimensions.get("window").width * 1,
        width: Dimensions.get("window").width * 0.8,
        backgroundColor: Colors.whiteColor,
        borderRadius: Dimensions.get("window").width * 0.07,
        padding: Dimensions.get("window").width * 0.07,
        alignItems: "center",
        justifyContent: "space-between",
    },
    closeButton: {
        position: "absolute",
        right: isMobile()
            ? Dimensions.get("window").width * 0.04
            : Dimensions.get("window").width * 0.01,
        top: isMobile()
            ? Dimensions.get("window").width * 0.04
            : Dimensions.get("window").width * 0.01,
    },
    modalTitle: {
        alignItems: "center",
    },
    modalBody: {
        flex: 0.8,
        alignItems: "center",
        justifyContent: "center",
    },
    modalTitleText: {
        fontFamily: "NunitoSans-Bold",
        // marginBottom: 10,
        fontSize: 20,
    },
    modalDescText: {
        fontFamily: "NunitoSans-SemiBold",
        fontSize: 16,
        color: Colors.fieldtTxtColor,
    },
    modalBodyText: {
        // flex: 1,
        fontFamily: "NunitoSans-SemiBold",
        fontSize: 16,
        width: "20%",
    },
    modalSaveButton: {
        width: isMobile()
            ? Dimensions.get("window").width * 0.3
            : Dimensions.get("window").width * 0.1,
        backgroundColor: Colors.fieldtBgColor,
        height: 45,
        alignItems: "center",
        justifyContent: "center",
        borderRadius: 8,

        shadowOffset: {
            width: 0,
            height: 1,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 1,
    },
    textInput: {
        height: 50,
        paddingHorizontal: 20,
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 5,
        marginTop: 10,
        marginBottom: 10,

        shadowColor: "#000",
        shadowOffset: {
            width: 0,
            height: 1,
        },
        shadowOpacity: 0.22,
        shadowRadius: 2.22,
        elevation: 2,
    },
});

export default RedeemVoucherModal;
