const { test, expect } = require('@playwright/test');

test('Add 2 items with same Category w/o variant and addon to Cart Test', async ({ page, isMobile }) => {
    // await page.setViewportSize({ width: 1600, height: 900 }); //modify to adjust window size
    await page.goto('http://localhost:5400/web/outlet/hominsanttdi/takeaway');

    await page.waitForTimeout(3000);
    if (isMobile) {
        await page.evaluate(() => { window.scrollBy(0, 100); });
    }
    else {
        await page.mouse.wheel(0, 100);
    }

    await page.waitForTimeout(500);

    await page.getByTestId('startOrdering').click();
    // await page.getByTestId('closeButton').click();

    await page.waitForTimeout(500);
    await page.getByTestId('categoryName-3').click();

    await page.waitForTimeout(500);
    if (isMobile) {
        await page.evaluate(() => { window.scrollBy(0, 100); });
    }
    else {
        await page.mouse.wheel(0, 100);
    }

    await page.waitForTimeout(500);
    await page.getByTestId('productName-0').click();
    const storeProductName = await page.getByTestId('productName-0');
    const storedName = await storeProductName.textContent();

    await page.waitForTimeout(500);
    const storeProductQuantity = await page.getByTestId('menuItemDetailQuantity');
    const storedQuantity = await storeProductQuantity.textContent();

    await page.getByTestId('addToCart').click();

    await page.waitForTimeout(500);
    await page.getByTestId('productName-1').click();
    const storeProductName2 = await page.getByTestId('productName-1');
    const storedName2 = await storeProductName2.textContent();

    await page.waitForTimeout(500);
    const storeProductQuantity2 = await page.getByTestId('menuItemDetailQuantity');
    const storedQuantity2 = await storeProductQuantity2.textContent();
    await page.getByTestId('addToCart').click();

    await page.waitForTimeout(500);
    await page.getByTestId('cartIcon').click();

    await page.waitForTimeout(500);

    const cartProductName = await page.getByTestId('cartProductName-0');
    const storedCartName = await cartProductName.textContent();

    const cartProductQuantity = await page.getByTestId('cartQuantity-0');
    const storedCartQuantity = await cartProductQuantity.textContent();

    const cartProductName2 = await page.getByTestId('cartProductName-1');
    const storedCartName2 = await cartProductName2.textContent();

    const cartProductQuantity2 = await page.getByTestId('cartQuantity-1');
    const storedCartQuantity2 = await cartProductQuantity2.textContent();

    if (storedName === storedCartName && storedName2 === storedCartName2 &&
        storedQuantity === storedCartQuantity && storedQuantity2 === storedCartQuantity2) {

        console.log('match with order product passed')
    }
});