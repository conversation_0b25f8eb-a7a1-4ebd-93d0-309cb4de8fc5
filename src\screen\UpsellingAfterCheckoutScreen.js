import React, { Component, useCallback, useEffect, useState } from "react";
import {
  StyleSheet,
  ScrollView,
  Image,
  View,
  Text,
  TouchableOpacity,
  Alert,
  TextInput,
  Modal,
  Dimensions, ActivityIndicator,
  FlatList,
  useWindowDimensions,
} from "react-native";
import Colors from "../constant/Colors";
import ApiClient from "../util/ApiClient";
import API from "../constant/API";
import AntDesign from "react-native-vector-icons/AntDesign";
import Icon from "react-native-vector-icons/Ionicons";
import Ionicons from "react-native-vector-icons/Ionicons";
import FontAwesome from "react-native-vector-icons/FontAwesome";
import * as Cart from "../util/Cart";
import Styles from "../constant/Styles";
import Icons from "react-native-vector-icons/Feather";
import Entypo from "react-native-vector-icons/Entypo";
import Close from "react-native-vector-icons/AntDesign";
// import NumericInput from "react-native-numeric-input";
import Draggable from "react-native-draggable";
import Icon1 from "react-native-vector-icons/Feather";
import { CommonStore } from "../store/commonStore";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { UserStore } from "../store/userStore";
import AsyncImage from "../components/asyncImage";
import { useFocusEffect, useLinkTo } from "@react-navigation/native";
import { prefix } from "../constant/env";
import { APP_TYPE, CHARGES_TYPE, ORDER_TYPE, PRODUCT_PRICE_TYPE, UNIT_TYPE, UNIT_TYPE_SHORT, UPSELLING_SECTION, WEB_ORDER_VARIANT_LAYOUT } from "../constant/common";
import { isMobile, parseValidPriceText } from "../util/commonFuncs";
import { DataStore } from "../store/dataStore";
import { TempStore } from "../store/tempStore";
import { ReactComponent as DefaultImage } from '../svg/DefaultImage.svg';
import { PaymentStore } from "../store/paymentStore";
import RecommendedItems from "../components/recommendedItems";
import { HeaderHeightContext } from '@react-navigation/stack';

import { ReactComponent as ArrowForward } from "../asset/svg/Arrow-forward-ios.svg";
import { ReactComponent as ArrowBack } from "../asset/svg/Arrow-back-ios.svg";

import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { idbGet } from "../util/db";
import { TableStore } from "../store/tableStore";

/**
 * MenuItemDetailScreen
 * function
 * *select the quantity, size, add-on, variation and remarks of the item
 * 
 * route.params
 * *outletData: array of data of the current outlet
 * *menuItem: data of the current item
 * *orderType: type of current order (unused?)
 * *refresh: boolean value to determine if page need refreshing
 * *cartItem: data of items currently in cart
 */

// var quantity2;
var total;
// var options;
var refreshAction = null;

const UpsellingAfterCheckoutScreen = props => {
  const {
    navigation,
    route,
  } = props;

  // const { orderType, refresh } = route.params;
  // const outletDataParam = route.params.outletData;
  // const menuItemParam = route.params.menuItem;
  // const cartItemParam = route.params.cartItem;

  const {
    width: windowWidth,
    height: windowHeight,
  } = useWindowDimensions();

  const linkTo = useLinkTo();

  const insets = useSafeAreaInsets();

  const useHeaderHeight = () => {
    const height = React.useContext(HeaderHeightContext);

    if (height === undefined) {
      return 0;
    }

    return height;
  };

  const headerHeight = useHeaderHeight();

  // const headerHeight = insets.bottom;

  navigation.setOptions({
    headerLeft: () => (
      <TouchableOpacity style={{
      }} onPress={async () => {
        // props.navigation.goBack();

        CommonStore.update(s => {
          s.currPage = '';
        });

        const subdomain = await AsyncStorage.getItem('latestSubdomain');

        if (!subdomain) {
          linkTo && linkTo(`${prefix}/outlet/menu`);
        }
        else {
          linkTo && linkTo(`${prefix}/outlet/${subdomain}/menu`);
        }

        CommonStore.update(s => {
          s.currPageIframe = 'OutletMenu';
        });

        // linkTo(`${prefix}/outlet/menu`);
      }}>
        <View style={{
          marginLeft: 10,
          display: 'flex',
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'flex-start',
        }}>
          <Icon
            name="chevron-back"
            size={26}
            color={Colors.fieldtTxtColor}
            style={{
            }}
          />

          <Text
            style={{
              color: Colors.fieldtTxtColor,
              fontSize: 16,
              textAlign: 'center',
              fontFamily: 'NunitoSans-Regular',
              lineHeight: 22,
              marginTop: -1,
            }}>
            Back
          </Text>
        </View>
      </TouchableOpacity>
    ),
    headerRight: () => (
      <TouchableOpacity onPress={async () => {
        // props.navigation.navigate('Profile')

        const subdomain = await AsyncStorage.getItem('latestSubdomain');

        if (!subdomain) {
          linkTo && linkTo(`${prefix}/outlet/cart`);
        }
        else {
          linkTo && linkTo(`${prefix}/outlet/${subdomain}/cart`);
        }
      }} style={{
        // opacity: 0,

        paddingRight: 15,
      }}>
        {/* <View style={{ marginRight: 15 }}>
          <Icon name="menu" size={30} color={Colors.primaryColor} />
        </View> */}

        <AntDesign
          name="closecircle"
          size={25}
          color={Colors.fieldtTxtColor}
        />
      </TouchableOpacity>
    ),
    headerTitle: () => (
      <View style={{
        justifyContent: 'center',
        alignItems: 'center',
        bottom: -1,
      }}>
        {selectedOutlet &&
          <Text
            style={{
              fontSize: 20,
              lineHeight: 25,
              textAlign: 'center',
              fontFamily: 'NunitoSans-Bold',
              color: Colors.mainTxtColor,
            }}
          >
            {selectedOutlet.name}
          </Text>
        }
      </View>
    ),
  });

  // const [menuItem, setMenuItem] = useState(menuItemParam);
  // const [totalState, setTotalState] = useState(menuItemParam.price);
  // const [totalState, setTotalState] = useState(0);

  const [remark, setRemark] = useState('');
  // const [outletData, setOutletData] = useState(outletDataParam);
  const [visible, setVisible] = useState(false);
  const [optional, setOptional] = useState(0);
  const [optional1, setOptional1] = useState(1);
  const [quantity1, setQuantity1] = useState(1);
  const [qty, setQty] = useState(null);
  const [detail, setDetail] = useState([]);
  // const [cartItem, setCartItem] = useState([]);
  const [choice, setChoice] = useState(null);
  // const [cartItem, setCartItem] = useState(cartItemParam);
  const [size, setSize] = useState("small");
  const [clicked, setClicked] = useState(1);
  const [clicked1, setClicked1] = useState(0);
  const [clicked2, setClicked2] = useState(0);
  const [cartIcon, setCartIcon] = useState(false);
  const [expandChoice, setExpandChoice] = useState(false);

  // const menuItemParam = route.params.menuItem;
  // const [menuItem, setMenuItem] = useState(menuItemParam);
  // const [totalState, setTotalState] = useState(menuItemParam.price);
  // const [quantity2, setQuantity2] = useState(cartItem ? cartItem.options : []);
  // const [options, setOptions] = useState(cartItem ? cartItem.quantity : quantity);

  const [quantity, setQuantity] = useState(1);

  const [totalPrice, setTotalPrice] = useState(0);
  const [addOnPrice, setAddOnPrice] = useState(0);

  //////////////////////////////////////////////////////////////////////////////////////

  const [temp, setTemp] = useState('');

  const [variablePrice, setVariablePrice] = useState('0.00');

  //////////////////////////////////////////////////////////////////////////////////////

  const firebaseUid = UserStore.useState(s => s.firebaseUid);

  const selectedOutlet = CommonStore.useState(s => s.selectedOutlet);
  const outletsItemAddOnDict = CommonStore.useState(s => s.outletsItemAddOnDict);
  const outletsItemAddOnChoiceDict = CommonStore.useState(s => s.outletsItemAddOnChoiceDict);
  const selectedOutletItem = CommonStore.useState(s => s.selectedOutletItem);
  const selectedOutletItemAddOn = CommonStore.useState(s => s.selectedOutletItemAddOn);
  const selectedOutletItemAddOnChoice = CommonStore.useState(s => s.selectedOutletItemAddOnChoice);

  const selectedOutletItemCategoriesDict = CommonStore.useState(s => s.selectedOutletItemCategoriesDict);

  const cartItems = CommonStore.useState(s => s.cartItems);
  const cartItemChoices = CommonStore.useState(s => s.cartItemChoices);

  const onUpdatingCartItem = CommonStore.useState(s => s.onUpdatingCartItem);
  const userCart = CommonStore.useState(s => s.userCart);

  const [qualifiedLoyaltyStamps, setQualifiedLoyaltyStamps] = useState([]);
  const [qualifiedLoyaltyStampBuy, setQualifiedLoyaltyStampBuy] = useState({});

  const orderType = CommonStore.useState(s => s.orderType);

  const loyaltyStampBuyItemSkuDict = CommonStore.useState(s => s.loyaltyStampBuyItemSkuDict);
  const loyaltyStampBuyCategoryNameDict = CommonStore.useState(s => s.loyaltyStampBuyCategoryNameDict);
  const selectedOutletLoyaltyStamps = CommonStore.useState(s => s.selectedOutletLoyaltyStamps);

  const selectedOutletTableId = CommonStore.useState(s => s.selectedOutletTableId);
  const selectedOutletTablePax = CommonStore.useState(s => s.selectedOutletTablePax);
  const selectedOutletWaiterId = CommonStore.useState(s => s.selectedOutletWaiterId);

  const overrideItemPriceSkuDict = CommonStore.useState(s => s.overrideItemPriceSkuDict);
  const amountOffItemSkuDict = CommonStore.useState(s => s.amountOffItemSkuDict);

  const overrideCategoryPriceNameDict = CommonStore.useState(s => s.overrideCategoryPriceNameDict);
  const amountOffCategoryNameDict = CommonStore.useState(s => s.amountOffCategoryNameDict);

  //////////////////////////////////////////////////////////////////////////////////////

  const [newSelectedOutletItemAddOn, setNewSelectedOutletItemAddOn] = useState({});
  const [newSelectedOutletItemAddOnDetails, setNewSelectedOutletItemAddOnDetails] = useState({});

  const isOrdering = CommonStore.useState((s) => s.isOrdering);

  //////////////////////////////////////////////////////////////////////////////////////

  const isPlacingReservation = CommonStore.useState((s) => s.isPlacingReservation);

  //////////////////////////////////////////////////////////////////////////////////////

  const upsellingCampaignsAfterCheckout = DataStore.useState((s) => s.upsellingCampaignsAfterCheckout);
  const upsellingCampaignsAfterCheckoutRecommendation = DataStore.useState((s) => s.upsellingCampaignsAfterCheckoutRecommendation);

  //////////////////////////////////////////////////////////////////////////////////////

  const setState = () => { };

  // useFocusEffect(
  //   useCallback(() => {
  //     CommonStore.update(s => {
  //       s.currPageIframe = 'UpsellingAfterCheckout';
  //     });
  //   }, [])
  // );

  useEffect(() => {
    global.currPageStack = [
      ...global.currPageStack,
      'UpsellingAfterCheckoutScreen',
    ];
  }, []);

  useEffect(() => {
    if (linkTo) {
      DataStore.update(s => {
        s.linkToFunc = linkTo;
      });

      global.linkToFunc = linkTo;
    }
  }, [linkTo]);

  useEffect(() => {
    if (selectedOutlet === null) {
      readStates();
    }

    readCommonStates();
  }, [selectedOutlet]);

  const readStates = async () => {
    console.log('global.selectedoutlet = readStates (1) (==test==)');

    if (selectedOutlet === null) {
      console.log('global.selectedoutlet = readStates (2) (==test==)');

      // const commonStoreDataRaw = await AsyncStorage.getItem("@commonStore");
      const commonStoreDataRaw = await idbGet('@commonStore');
      if (commonStoreDataRaw !== undefined) {
        console.log('global.selectedoutlet = readStates (3) (==test==)');

        const commonStoreData = JSON.parse(commonStoreDataRaw);

        const latestOutletId = await AsyncStorage.getItem("latestOutletId");

        console.log('latestOutletId');
        console.log(latestOutletId);
        console.log('commonStoreData.selectedOutlet');
        console.log(commonStoreData.selectedOutlet);

        if (
          commonStoreData.selectedOutlet &&
          latestOutletId === commonStoreData.selectedOutlet.uniqueId
        ) {
          // check if it's the same outlet user scanned

          console.log('global.selectedoutlet = readStates (4) (==test==)');

          if (isPlacingReservation) {
          } else {
            // if (
            //   commonStoreData.orderType === ORDER_TYPE.DINEIN 
            //   // 2022-10-08 - Try to disable this
            //   // &&
            //   // commonStoreData.userCart.uniqueId === undefined
            // ) {
            //   // logout the user

            //   linkTo && linkTo(`${prefix}/scan`);
            // }
          }

          console.log('global.selectedoutlet = commonStoreData.selectedOutlet (3) (==test==)');

          global.selectedOutlet = commonStoreData.selectedOutlet;

          CommonStore.replace(commonStoreData);

          // const userStoreDataRaw = await AsyncStorage.getItem("@userStore");
          const userStoreDataRaw = await idbGet('@userStore');
          if (userStoreDataRaw !== undefined) {
            const userStoreData = JSON.parse(userStoreDataRaw);

            UserStore.replace(userStoreData);
          }

          // const dataStoreDataRaw = await AsyncStorage.getItem('@dataStore');
          const dataStoreDataRaw = await idbGet('@dataStore');
          if (dataStoreDataRaw !== undefined) {
            const dataStoreData = JSON.parse(dataStoreDataRaw);

            DataStore.replace(dataStoreData);
            // DataStore.replace({
            //   ...dataStoreData,
            //   ...dataStoreData.linkToFunc !== undefined && {
            //     linkToFunc: dataStoreData.linkToFunc,
            //   },
            // });
          }

          // const tableStoreDataRaw = await AsyncStorage.getItem("@tableStore");
          const tableStoreDataRaw = await idbGet('@tableStore');
          if (tableStoreDataRaw !== undefined) {
            const tableStoreData = JSON.parse(tableStoreDataRaw);

            TableStore.replace(tableStoreData);
          }

          // const paymentStoreDataRaw = await AsyncStorage.getItem("@paymentStore");
          const paymentStoreDataRaw = await idbGet('@paymentStore');
          if (paymentStoreDataRaw !== undefined) {
            const paymentStoreData = JSON.parse(paymentStoreDataRaw);

            PaymentStore.replace(paymentStoreData);
          }

          // const dataStoreDataRaw = await AsyncStorage.getItem('@dataStore');
          // if (dataStoreDataRaw !== undefined) {
          //   const dataStoreData = JSON.parse(dataStoreDataRaw);

          //   DataStore.replace(dataStoreData);
          // }
        }
      }
    }
  };

  const readCommonStates = async () => {
    // if (!linkToFunc) {
    //   const dataStoreDataRaw = await AsyncStorage.getItem('@dataStore');
    //   if (dataStoreDataRaw !== undefined) {
    //     const dataStoreData = JSON.parse(dataStoreDataRaw);
    //     DataStore.replace(dataStoreData);
    //   }
    // }
  };

  return (
    <View style={[styles.container, {
      // flex: 1
      width: windowWidth,
      height: windowHeight,

      position: 'relative',
    }]}>
      {/* <View
        style={{
          position: "absolute",
          alignItems: "center",
          paddingVertical: 15,
          paddingLeft: 6,
          width: 20,
          // height: 47,
          // backgroundColor: Colors.highlightColor,
          // opacity: 0.85,
          opacity: 1,
          // justifyContent: "center",
          // shadowColor: "#000",
          // shadowOffset: {
          //   width: 1,
          //   height: 0.5,
          // },
          // marginLeft: 2,
          // shadowOpacity: 0.12,
          // shadowRadius: 3.22,

          top: 100
        }}

      >
        <TouchableOpacity
          onPress={() => { }}
        >
          {/* <MaterialIcons
                  name="arrow-back-ios"
                  size={20}
                  color={Colors.primaryColor}
                  style={{ height: '120%', zIndex: 3, elevation: 1 }}
                /> */}
      {/* <ArrowBack
            name="arrow-back-ios"
            size={40}
            color={Colors.darkBgColor}
            style={{ height: '120%', zIndex: 3, elevation: 1 }}
          />
        </TouchableOpacity>
      </View> */}
      {/* <View
        style={{
          position: 'absolute',
          alignItems: "center",
          alignSelf: "flex-end",
          // marginRight: -60,
          paddingVertical: 15,
          paddingRight: 1,
          width: 20,
          // height: (windowHeight - headerHeight) * 0.9,
          // backgroundColor: Colors.highlightColor,
          // opacity: 0.85,
          opacity: 1,
          // justifyContent: "center",
          // shadowColor: "#000",
          // shadowOffset: {
          //   width: 1,
          //   height: 0.5,
          // },
          // shadowOpacity: 0.12,
          // shadowRadius: 3.22,

          top: 100
        }}
      >
        <TouchableOpacity
          onPress={() => { }}
        >
          {/* <MaterialIcons
                  name="arrow-forward-ios"
                  size={20}
                  color={Colors.primaryColor}
                  style={{ height: '120%', zIndex: 3, elevation: 1, }}
                /> */}
      {/* <ArrowForward
            name="arrow-forward-ios"
            size={40}
            color={Colors.darkBgColor}
            style={{ height: '120%', zIndex: 3, elevation: 1, }}
          />
        </TouchableOpacity>
      </View> */}

      <ScrollView style={[{
        flex: 1,

        //height: (windowHeight - headerHeight) * 0.9,

        padding: 16,
      }]}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{
          paddingLeft: 10,
          paddingRight: 10,
          paddingTop: 5,
          paddingVertical: 10
          //paddingBottom: (windowHeight - headerHeight) * 0.10,
        }}
        nestedScrollEnabled={true}
      >

        {
          upsellingCampaignsAfterCheckout.slice(0, 1).map((campaign, campaignIndex) => {
            return (
              <RecommendedItems style={{
                // marginBottom: 10,
              }} navigation={props.navigation} upsellingSection={UPSELLING_SECTION.AFTER_CHECKOUT}
                campaignData={campaign} campaignIndex={campaignIndex}
              />
            );
          })
        }

        {
          upsellingCampaignsAfterCheckoutRecommendation.map((campaign, campaignIndex) => {
            return (
              <RecommendedItems style={{
                // marginBottom: 10,
              }} navigation={props.navigation} upsellingSection={UPSELLING_SECTION.AFTER_CHECKOUT_RECOMMENDATION}
                campaignData={campaign} campaignIndex={campaignIndex}
              />
            );
          })
        }

        {/* <View style={{ minHeight: 100 }} /> */}
      </ScrollView>

      <View style={{
        // position: 'absolute',
        bottom: headerHeight + insets.bottom,
        width: '100%',
        height: (windowHeight - headerHeight) * 0.10,
        backgroundColor: Colors.whiteColor,

        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-around',
      }}>
        {/* <View style={{
          width: '45%',
        }}>

        </View> */}

        <TouchableOpacity
          onPress={async () => {
            const subdomain = await AsyncStorage.getItem('latestSubdomain');

            if (!subdomain) {
              linkTo && linkTo(`${prefix}/outlet/cart`);
            }
            else {
              linkTo && linkTo(`${prefix}/outlet/${subdomain}/cart`);
            }

            CommonStore.update(s => {
              s.currPageIframe = 'Cart';
            });
          }}
          style={{
            // width: '45%',
          }}
        >
          <View style={[
            Styles.button, {
              borderRadius: 2,
              padding: 7,
              // paddingVertical: 12,
              justifyContent: 'center',
              alignItems: 'center',
              width: windowWidth * 0.9,
              marginVertical: 0,
            }]}>
            <Text
              style={{
                color: "#ffffff",
                fontSize: 18,
                // fontWeight: "bold",
                fontFamily: 'NunitoSans-SemiBold',
              }}
            >
              {'NEXT'}
            </Text>
          </View>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: "#ffffff",
    // padding: 16,
  },
  floatCartBtn: {
    position: "absolute",
    bottom: 30,
    right: 30,
    width: 60,
    height: 60,
    borderRadius: 60 / 2,
    backgroundColor: Colors.secondaryColor,
    justifyContent: "center",
    alignContent: "center",
    alignItems: "center",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 3,
  },
  textInput: {
    // height: 100,
    height: isMobile() ? Dimensions.get('window').width * 0.35 : Dimensions.get('window').width * 0.08,
    paddingHorizontal: 20,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 12,

    fontSize: 14,
    fontFamily: 'NunitoSans-Regular',
    // color: Colors.descriptionColor,
    textAlignVertical: 'top',
    paddingVertical: 15,
  },
  addBtn: {
    backgroundColor: Colors.primaryColor,
    width: 40,
    height: 45,

    display: 'flex',
    justifyContent: "center",
    alignItems: "center",

    borderColor: 'transparent',
  },
  addBtnSmall: {
    backgroundColor: Colors.primaryColor,
    width: 25,
    height: 25,
    justifyContent: "center",
    alignItems: "center",
  },
  cartCount: {
    position: "absolute",
    top: -12,
    right: -10,
    backgroundColor: Colors.primaryColor,
    width: 30,
    height: 30,
    borderRadius: 30 / 2,
    alignContent: "center",
    alignItems: "center",
    justifyContent: "center",
  },
  confirmBox: {
    width: "70%",
    height: "40%",
    borderRadius: 10,
    backgroundColor: Colors.whiteColor,
  },
  textFieldInput: {
    height: "100%",
    width: "100%",
    paddingHorizontal: 20,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 10,
  },
  addBtn1: {
    backgroundColor: Colors.primaryColor,
    width: 20,
    height: 20,
    justifyContent: "center",
    alignItems: "center",
  },
});
export default UpsellingAfterCheckoutScreen;
