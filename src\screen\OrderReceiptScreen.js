import React, { Component, useReducer, useState, useEffect, useRef } from 'react';
import {
    StyleSheet,
    ScrollView,
    Image,
    View,
    Text,
    TouchableOpacity,
    TextInput,
    FlatList,
    RefreshControl,
    Alert,
    Modal,
    Dimensions,
    ActivityIndicator,
} from 'react-native';
import Colors from '../constant/Colors';
import * as Cart from '../util/Cart';
import * as User from '../util/User';
import Styles from '../constant/Styles';
import ApiClient from '../util/ApiClient';
import API from '../constant/API';
import Feather from 'react-native-vector-icons/Feather';
import Icons from 'react-native-vector-icons/EvilIcons';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { CommonStore } from '../store/commonStore';
import { Collections } from '../constant/firebase';
//import firestore from '@react-native-firebase/firestore';
// import firebase from "firebase";
import { getFirestore, collection, query, where, getDocs, limit } from "firebase/firestore";
import { ORDER_TYPE_PARSED, USER_ORDER_STATUS, COURIER_CODE, LALAMOVE_STATUS, ORDER_TYPE, MRSPEEDY_STATUS, COURIER_INFO_DICT, SENDER_DROPDOWN_LIST, ORDER_REGISTER_QR_SALT } from '../constant/common';
import AsyncImage from '../components/asyncImage';
import AntDesign from 'react-native-vector-icons/AntDesign';
import { Picker } from "react-native";
import { UserStore } from '../store/userStore';
import { CommonActions, useLinkTo } from "@react-navigation/native";
//import molpay from "molpay-mobile-xdk-reactnative-beta";
import moment from 'moment';
//import DropDownPicker from 'react-native-dropdown-picker';
//import RNPickerSelect from 'react-native-picker-select';
import { prefix } from "../constant/env";
import AsyncStorage from "@react-native-async-storage/async-storage";
import Hashids from 'hashids';
import { DataStore } from '../store/dataStore';
import { signInAnonymously } from 'firebase/auth';
import { TempStore } from '../store/tempStore';

const hashids = new Hashids(ORDER_REGISTER_QR_SALT);

/**
 * OrderDetailScreen
 * function
 * *display detail of the order
 * 
 * route.params
 * *orderId: id of the selected order to be displayed
 */

const ORDER_STATUS_PARSED = {
    PLACED: 'PLACED',
    PREPARING: 'PREPARING',
    PICKING_UP: 'PICKING_UP',
    DELIVERING: 'DELIVERING',
    DELIVERED: 'DELIVERED',
};

const ORDER_STATUS_IMAGES_DELIVERY = {
    PLACED: require('../asset/image/order-status-placed.png'),
    PREPARING: require('../asset/image/order-status-preparing.png'),
    PICKING_UP: require('../asset/image/order-status-picking-up.png'),
    DELIVERING: require('../asset/image/order-status-delivering.png'),
    DELIVERED: require('../asset/image/order-status-delivered.png'),
};

const ORDER_STATUS_IMAGES = {
    PLACED: require('../asset/image/order-status-placed.png'),
    PREPARING: require('../asset/image/order-status-preparing.png'),
    PICKING_UP: require('../asset/image/order-status-preparing.png'),
    DELIVERING: require('../asset/image/order-status-preparing.png'),
    DELIVERED: require('../asset/image/order-status-delivered.png'),
};

const ORDER_STATUS_IMAGES_BOTTOM = {
    PLACED: require('../asset/image/order-status-placed-bottom.png'),
    PREPARING: require('../asset/image/order-status-preparing-bottom.png'),
    PICKING_UP: require('../asset/image/order-status-picking-up-bottom.png'),
    DELIVERING: require('../asset/image/order-status-delivering-bottom.png'),
    DELIVERED: require('../asset/image/order-status-delivered-bottom.png'),
};

const ORDER_STATUS_TEXT_DELIVERY = {
    PLACED: 'Your order placed successfully!',
    PREPARING: 'Restaurant is currently preparing your order!',
    PICKING_UP: 'Your driver is picking up your order!',
    DELIVERING: 'Driver on the way delivering your order!',
    DELIVERED: 'Your order has been delivered.\nEnjoy your meal!',
};

const ORDER_STATUS_TEXT = {
    PLACED: 'Your order placed successfully!',
    PREPARING: 'Restaurant is currently preparing your order!',
    PICKING_UP: 'Restaurant is currently preparing your order!',
    DELIVERING: 'Restaurant is currently preparing your order!',
    DELIVERED: 'Your order is ready.\nEnjoy your meal!',
};

/////////////////////////////////////////////////////////////////////////

const ORDER_STATUS_IMAGES_BOTTOM_TAKEAWAY = {
    PLACED: require('../asset/image/t-received.png'),
    PREPARING: require('../asset/image/t-preparing.png'),
    DELIVERED: require('../asset/image/t-delivered.png'),
    PICKED_UP: require('../asset/image/order-status-picking-up.png'),
};

const OrderReceiptScreen = props => {
    const {
        navigation,
        route,
    } = props;

    const linkTo = useLinkTo();

    navigation.setOptions({
        headerLeft: () => (
            <TouchableOpacity style={{
            }} onPress={async () => {
                // props.navigation.goBack();
                const subdomain = await AsyncStorage.getItem('latestSubdomain');

                if (!subdomain) {
                    linkTo && linkTo(`${prefix}/outlet/menu`);
                }
                else {
                    linkTo && linkTo(`${prefix}/outlet/${subdomain}/menu`);
                }
            }}>
                <View style={{
                    marginLeft: 10,
                    display: 'flex',
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'flex-start',
                }}>
                    <Ionicons
                        name="chevron-back"
                        size={26}
                        color={Colors.fieldtTxtColor}
                        style={{
                        }}
                    />

                    <Text
                        style={{
                            color: Colors.fieldtTxtColor,
                            fontSize: 16,
                            textAlign: 'center',
                            fontFamily: 'NunitoSans-Regular',
                            lineHeight: 22,
                            marginTop: -1,
                        }}>
                        Back
                    </Text>
                </View>
            </TouchableOpacity>
        ),
        headerRight: () => (
            <TouchableOpacity onPress={() => { props.navigation.navigate('Profile') }} style={{
            }}>
                <View style={{ marginRight: 15 }}>
                    <Ionicons name="menu" size={30} color={Colors.primaryColor} />
                </View>
            </TouchableOpacity>
        ),
        headerTitle: () => (
            <View style={{
                justifyContent: 'center',
                alignItems: 'center',
                bottom: -1,
            }}>
                <Text
                    style={{
                        fontSize: 20,
                        lineHeight: 25,
                        textAlign: 'center',
                        fontFamily: 'NunitoSans-Bold',
                        color: Colors.mainTxtColor,
                    }}>
                    Order Details
                </Text>
            </View>
        ),
        headerTintColor: "#000000",
    });

    var orderIdParam = null;
    if (route && route.params) {
        const orderIdParam = route.params.orderId;
    }

    const [orderId, setOrderId] = useState(orderIdParam);
    const [orderData, setOrderData] = useState([]);
    const [orderMerchant, setOrderMerchant] = useState([]);
    const [orderItems, setOrderItems] = useState(null);
    const [orderTax, setOrderTax] = useState([]);

    const [expandDetails, setExpandDetails] = useState(false);
    const [expandCompleteQuestionnaire, setExpandCompleteQuestionnaire] = useState(false);
    const [questionnaireIndex, setQuestionnaireIndex] = useState(1);
    //const current = question[questionnaireIndex];

    const [reviewComment, setReviewComment] = useState('');
    const [reviewTotal, setReviewTotal] = useState(18);

    const [starRatingDefault, setStarRatingDefault] = useState(5);
    const [starRating, setStarRating] = useState([1, 2, 3, 4, 5]);

    const selectedOutlet = CommonStore.useState(s => s.selectedOutlet);

    const setState = () => { };

    const [orderOutlet, setOrderOutlet] = useState({});

    const [orderDetails, setOrderDetails] = useState(null);

    const scrollViewRef = useRef();
    const [actionModalVisibility, setActionModalVisibility] = useState(false);
    const [completeModalVisibility, setCompleteModalVisibility] = useState(false);
    const [questionnaireVisibility, setQuestionnaireVisibility] = useState(false);
    const [receiptModal, setReceiptModal] = useState(false);

    const [selectedSender, setSelectedSender] = useState(SENDER_DROPDOWN_LIST[0].value);

    const [deliveryQuotation, setDeliveryQuotation] = useState({
        totalFee: 0,
    });

    const selectedUserOrder = CommonStore.useState(s => s.selectedUserOrder);
    const isLoading = CommonStore.useState(s => s.isLoading);

    /////////////////////////////////////////////////////////

    const selectedTableOrders = CommonStore.useState(s => s.selectedTableOrders);

    const selectedUserOrderOthers = CommonStore.useState(s => s.selectedUserOrderOthers);

    /////////////////////////////////////////////////////////

    const userEmail = UserStore.useState(s => s.email);
    const userName = UserStore.useState(s => s.name);
    const userAvatar = UserStore.useState(s => s.avatar);
    const userAddresses = UserStore.useState(s => s.userAddresses);
    const firebaseUid = UserStore.useState(s => s.firebaseUid);
    const userNumber = UserStore.useState(s => s.number);

    ////////////////////////////////////////////////////////

    useEffect(() => {
        if (linkTo) {
            DataStore.update(s => {
                s.linkToFunc = linkTo;
            });

            global.linkToFunc = linkTo;
        }

        console.log('route');
        console.log(route);
        console.log('window.location.href');
        console.log(window.location.href);

        if (route.params === undefined ||
            // route.params.outletId === undefined ||
            route.params.orderId === undefined
            // route.params.tableCode === undefined ||
            // route.params.tablePax === undefined ||
            // route.params.waiterId === undefined ||
            // route.params.outletId.length !== 36 ||
            // route.params.tableId.length !== 36 ||
            // route.params.qrDateTimeEncrypted === undefined
        ) {
            // still valid, can proceed to register user

            // linkTo && linkTo(`${prefix}/scan`);     

            console.log('general');

            linkTo && linkTo(`${prefix}/error`);
        }
        else {
            try {
                // firebase.auth().signInAnonymously()
                signInAnonymously(global.auth)
                    .then((result) => {
                        // TempStore.update(s => {
                        //     s.firebaseAuth = true;
                        // });

                        const firebaseUid = result.user.uid;

                        ApiClient.GET(API.getTokenKWeb + firebaseUid).then(async (result) => {
                            console.log('getTokenKWeb');
                            console.log(result);

                            if (result && result.token) {
                                await AsyncStorage.setItem('accessToken', result.token);
                                await AsyncStorage.setItem('refreshToken', result.refreshToken);

                                global.accessToken = result.token;

                                UserStore.update(s => {
                                    s.firebaseUid = result.userId;
                                    s.userId = result.userId;
                                    s.role = result.role;
                                    s.refreshToken = result.refreshToken;
                                    s.token = result.token;
                                    s.name = '';
                                    s.email = '';
                                    s.number = '';
                                });

                                // CommonStore.update(s => {
                                //   s.selectedOutletTableQRUrl = window.location.href;
                                // });

                                var orderId = hashids.decodeHex(route.params.orderId).toString().insert(8, '-').insert(13, '-').insert(18, '-').insert(23, '-');

                                // const userOrderSnapshot = await firebase.firestore().collection(Collections.UserOrder)
                                //     .where('uniqueId', '==', orderId)
                                //     .limit(1)
                                //     .get();

                                const userOrderSnapshot = await getDocs(
                                    query(
                                        collection(global.db, Collections.UserOrder),
                                        where('uniqueId', '==', orderId),
                                        limit(1),
                                    )
                                );

                                var userOrder = null;

                                if (!userOrderSnapshot.empty) {
                                    userOrder = userOrderSnapshot.docs[0].data();
                                }

                                if (userOrder) {
                                    if (userOrder.isCashbackClaimed) {
                                        linkTo && linkTo(`${prefix}/error`);
                                    }

                                    // const outletSnapshot = await firebase.firestore().collection(Collections.Outlet)
                                    //     .where('uniqueId', '==', userOrder.outletId)
                                    //     .limit(1)
                                    //     .get();

                                    const outletSnapshot = await getDocs(
                                        query(
                                            collection(global.db, Collections.Outlet),
                                            where('uniqueId', '==', userOrder.outletId),
                                            limit(1),
                                        )
                                    );

                                    var outlet = {};
                                    if (!outletSnapshot.empty) {
                                        outlet = outletSnapshot.docs[0].data();
                                    }

                                    if (outlet) {
                                        // valid, can proceed to register user

                                        // setOutletId(outlet.uniqueId);
                                        // setOutletName(outlet.name);
                                        // setOutletCover(outlet.cover);
                                        // setMerchantId(outlet.merchantId);
                                        // setMerchantName(userOrder.merchantName);

                                        CommonStore.update(s => {
                                            s.selectedUserOrder = userOrder;

                                            s.selectedUserOrderOthers = [];
                                        });

                                        setOrderOutlet(outlet);

                                        // CommonStore.update(s => {
                                        //     s.registerUserOrder = userOrder;
                                        // });

                                        // await AsyncStorage.setItem('latestOutletId', outlet.uniqueId);

                                        // await updateUserCart(
                                        //   {
                                        //     ...route.params,
                                        //     outletId: outlet.uniqueId,
                                        //     tableId: tableId,
                                        //     tablePax: outletTable.seated,
                                        //     tableCode: outletTable.code,
                                        //   }
                                        //   , outlet, firebaseUid);

                                        // linkTo && linkTo('/web/outlet');
                                    }
                                    else {
                                        linkTo && linkTo(`${prefix}/error`);
                                    }
                                }
                                else {
                                    linkTo && linkTo(`${prefix}/error`);
                                }
                            }
                            else {
                                CommonStore.update(s => {
                                    s.alertObj = {
                                        title: 'Error',
                                        message: 'Unauthorized access',
                                    };

                                    // s.isAuthenticating = false;
                                });

                                linkTo && linkTo(`${prefix}/error`);
                            }
                        });
                    });
            }
            catch (ex) {
                console.error(ex);
            }
        }
    }, [linkTo, route]);

    // useEffect(() => {
    //     if (linkTo) {
    //         DataStore.update(s => {
    //             s.linkToFunc = linkTo;
    //         });
    //     }
    // }, [linkTo]);

    useEffect(() => {
        CommonStore.update(s => {
            s.routeName = route.name;
        });
    }, [route]);

    ////////////////////////////////////////////////////////

    // useEffect(() => {
    //     if (selectedTableOrders && selectedTableOrders.length > 0) {
    //         CommonStore.update(s => {
    //             s.selectedUserOrder = selectedTableOrders[0];

    //             s.selectedUserOrderOthers = selectedTableOrders.slice(1);
    //         });
    //     }
    // }, [selectedTableOrders]);

    // useEffect(() => {
    //     if (selectedOutlet && selectedOutlet.uniqueId) {
    //         setOrderOutlet(selectedOutlet);
    //     }
    //     else {

    //     }
    // }, [selectedOutlet]);

    // console.log('selectedTableOrders');
    // console.log(selectedTableOrders);
    // console.log('selectedUserOrder');
    // console.log(selectedUserOrder);
    // console.log('selectedUserOrderOthers');
    // console.log(selectedUserOrderOthers);

    ////////////////////////////////////////////////////////   

    // useEffect(() => {
    //     if (!selectedOutlet && selectedUserOrder && selectedUserOrder.outletId) {
    //         retrieveOutletData();
    //     }
    // }, [selectedOutlet, selectedUserOrder]);

    // const retrieveOutletData = async () => {
    //     const outletSnapshot = await firebase.firestore().collection(Collections.Outlet)
    //         .where('uniqueId', '==', selectedUserOrder.outletId)
    //         .limit(1)
    //         .get();

    //     if (!outletSnapshot.empty) {
    //         CommonStore.update(s => {
    //             s.selectedOutlet = outletSnapshot.docs[0].data();
    //         });
    //     }
    // };

    // useEffect(() => {
    //     if (selectedUserOrder && selectedUserOrder.uniqueId) {
    //         if (orderOutlet.uniqueId === undefined) {
    //             retrieveOrderOutlet();
    //         }
    //     }
    // }, [selectedUserOrder, orderOutlet]);

    // const retrieveOrderOutlet = async () => {
    //     const outletSnapshot = await firebase.firestore().collection(Collections.Outlet)
    //         .where('uniqueId', '==', selectedUserOrder.outletId)
    //         .limit(1)
    //         .get();

    //     if (!outletSnapshot.empty) {
    //         const outlet = outletSnapshot.docs[0].data();

    //         setOrderOutlet(outlet);
    //     }
    // };

    const [parsedOrderStatus, setParsedOrderStatus] = useState(ORDER_STATUS_PARSED.PLACED)

    // const selectedUserOrder = CommonStore.useState(s => s.selectedUserOrder)

    useEffect(() => {

        if (selectedUserOrder && selectedUserOrder.uniqueId) {
            if (selectedUserOrder.orderStatus === USER_ORDER_STATUS.ORDER_PREPARING ||
                selectedUserOrder.orderStatus === USER_ORDER_STATUS.ORDER_AUTHORIZED) {
                // means is still cooking

                setParsedOrderStatus(ORDER_STATUS_PARSED.PREPARING);
            }

            if (selectedUserOrder.orderStatus === USER_ORDER_STATUS.ORDER_PREPARED) {
                // means done cooking

                if (selectedUserOrder.courierCode === COURIER_CODE.LALAMOVE) {
                    // means is delivery and lalamove

                    if (selectedUserOrder.courierStatus === LALAMOVE_STATUS.REJECTED ||
                        selectedUserOrder.courierStatus === LALAMOVE_STATUS.CANCELED ||
                        selectedUserOrder.courierStatus === LALAMOVE_STATUS.EXPIRED) {
                        setParsedOrderStatus(ORDER_STATUS_PARSED.PICKING_UP);
                    }

                    if (selectedUserOrder.courierStatus === LALAMOVE_STATUS.ASSIGNING_DRIVER ||
                        selectedUserOrder.courierStatus === LALAMOVE_STATUS.ON_GOING) {
                        setParsedOrderStatus(ORDER_STATUS_PARSED.PICKING_UP);
                    }
                    else if (selectedUserOrder.courierStatus === LALAMOVE_STATUS.PICKED_UP) {
                        setParsedOrderStatus(ORDER_STATUS_PARSED.DELIVERING);
                    }
                    else if (selectedUserOrder.courierStatus === LALAMOVE_STATUS.COMPLETED) {
                        setParsedOrderStatus(ORDER_STATUS_PARSED.DELIVERED);
                    }
                }
                else if (selectedUserOrder.courierCode === COURIER_CODE.MRSPEEDY) {
                    // means is delivery and mrspeedy

                    if (selectedUserOrder.courierStatus === MRSPEEDY_STATUS.reactivated ||
                        selectedUserOrder.courierStatus === MRSPEEDY_STATUS.canceled ||
                        selectedUserOrder.courierStatus === MRSPEEDY_STATUS.delayed ||
                        selectedUserOrder.courierStatus === MRSPEEDY_STATUS.draft) {
                        setParsedOrderStatus(ORDER_STATUS_PARSED.PICKING_UP);
                    }

                    if (selectedUserOrder.courierStatus === MRSPEEDY_STATUS.new ||
                        selectedUserOrder.courierStatus === MRSPEEDY_STATUS.available) {
                        setParsedOrderStatus(ORDER_STATUS_PARSED.PICKING_UP);
                    }
                    else if (selectedUserOrder.courierStatus === MRSPEEDY_STATUS.active) {
                        setParsedOrderStatus(ORDER_STATUS_PARSED.DELIVERING);
                    }
                    else if (selectedUserOrder.courierStatus === MRSPEEDY_STATUS.completed) {
                        setParsedOrderStatus(ORDER_STATUS_PARSED.DELIVERED);
                    }
                }

                if (selectedUserOrder.orderType === ORDER_TYPE.PICKUP) {
                    setParsedOrderStatus(ORDER_STATUS_PARSED.DELIVERED);
                }
            }

            if (selectedUserOrder.orderStatus === USER_ORDER_STATUS.ORDER_DELIVERED ||
                selectedUserOrder.orderStatus === USER_ORDER_STATUS.ORDER_COMPLETED) {
                setParsedOrderStatus(ORDER_STATUS_PARSED.DELIVERED);
            }

            if (selectedUserOrder.orderStatus === USER_ORDER_STATUS.ORDER_CANCELLED_BY_MERCHANT) {
                setParsedOrderStatus(ORDER_STATUS_PARSED.DELIVERED);

                setOrderDetails({
                    message: 'Order cancelled by merchant.',

                    orderStatus: USER_ORDER_STATUS.ORDER_CANCELLED_BY_MERCHANT,
                });
            }

            if (selectedUserOrder.orderStatus === USER_ORDER_STATUS.ORDER_CANCELLED_BY_USER) {
                setParsedOrderStatus(ORDER_STATUS_PARSED.DELIVERED);

                setOrderDetails({
                    message: 'Order cancelled by user.',

                    orderStatus: USER_ORDER_STATUS.ORDER_CANCELLED_BY_USER,
                });
            }

            if (selectedUserOrder.orderStatus === USER_ORDER_STATUS.ORDER_SENDER_REJECTED) {
                setParsedOrderStatus(ORDER_STATUS_PARSED.DELIVERED);

                setOrderDetails({
                    message: `Order rejected by ${COURIER_INFO_DICT[selectedUserOrder.courierCode].name} side.`,

                    orderStatus: USER_ORDER_STATUS.ORDER_SENDER_REJECTED,
                });
            }

            if (selectedUserOrder.orderStatus === USER_ORDER_STATUS.ORDER_SENDER_CANCELED) {
                setParsedOrderStatus(ORDER_STATUS_PARSED.DELIVERED);

                setOrderDetails({
                    message: 'Order canceled.',

                    orderStatus: USER_ORDER_STATUS.ORDER_SENDER_CANCELED,
                });
            }

            if (selectedUserOrder.orderStatus === USER_ORDER_STATUS.ORDER_SENDER_EXPIRED) {
                setParsedOrderStatus(ORDER_STATUS_PARSED.DELIVERED);

                setOrderDetails({
                    message: `Order expired in ${COURIER_INFO_DICT[selectedUserOrder.courierCode].name} side.`,

                    orderStatus: USER_ORDER_STATUS.ORDER_SENDER_EXPIRED,
                });
            }

            ////////////////////////////////////////

            setDeliveryQuotation({
                totalFee: selectedUserOrder.deliveryFee,
            });
        }
    }, [selectedUserOrder]);

    useEffect(() => {
        if (actionModalVisibility) {
            if (selectedUserOrder.crUserAddress) {
                // valid order to proceed

                if (selectedSender === COURIER_CODE.LALAMOVE) {
                    var body = {
                        outletLat: selectedUserOrder.crOutletLat,
                        outletLng: selectedUserOrder.crOutletLng,
                        outletAddress: selectedUserOrder.crOutletAddress,

                        outletPhone: selectedUserOrder.crOutletPhone,
                        outletName: selectedUserOrder.outletName,

                        userLat: selectedUserOrder.crUserLat,
                        userLng: selectedUserOrder.crUserLng,
                        userAddress: selectedUserOrder.crUserAddress,

                        userName: selectedUserOrder.crUserName,
                        userPhone: selectedUserOrder.crUserPhone,
                        userRemarks: selectedUserOrder.crUserRemarks,

                        // scheduleAt: moment().add(totalPrepareTime, 'second').add(15, 'minute').utc().toISOString(),
                    };

                    console.log('quotation body');
                    console.log(body);

                    ApiClient.POST(API.lalamoveQuotation, body).then((result) => {
                        console.log("lalamove quotation result");
                        console.log(result);

                        if (result === undefined) {
                            // means lalamove can't deliver to this address

                            Alert.alert(
                                'Info',
                                'Sorry, we unable to deliver to this address, please try another one.',
                            );
                        }
                        else if (result && result.totalFee) {
                            // { totalFee: "0.00", totalFeeCurrency: "MYR" }

                            setDeliveryQuotation({
                                totalFee: parseFloat(result.totalFee),
                                totalFeeCurrency: result.totalFeeCurrency,
                                courierCode: COURIER_CODE.LALAMOVE,
                            });
                        }
                    });
                }
                else if (selectedSender === COURIER_CODE.MRSPEEDY) {
                    var body = {
                        outletLat: selectedUserOrder.crOutletLat,
                        outletLng: selectedUserOrder.crOutletLng,
                        outletAddress: selectedUserOrder.crOutletAddress,

                        outletPhone: selectedUserOrder.crOutletPhone,
                        outletName: selectedUserOrder.outletName,

                        userLat: selectedUserOrder.crUserLat,
                        userLng: selectedUserOrder.crUserLng,
                        userAddress: selectedUserOrder.crUserAddress,

                        userName: selectedUserOrder.crUserName,
                        userPhone: selectedUserOrder.crUserPhone,
                        userRemarks: selectedUserOrder.crUserRemarks,

                        totalWeightKg: selectedUserOrder.totalWeightKg,
                        // outletRequiredStartDatetime: moment().add(totalPrepareTime, 'second').add(5, 'minute').utc().toISOString(),
                        // outletRequiredFinishDatetime: moment().add(totalPrepareTime, 'second').add(10, 'minute').utc().toISOString(),
                        // userRequiredStartDatetime: moment().add(totalPrepareTime, 'second').add(15, 'minute').utc().toISOString(),
                        // userRequiredFinishDatetime: moment().add(totalPrepareTime, 'second').add(30, 'minute').utc().toISOString(),
                    };

                    console.log('quotation body');
                    console.log(body);

                    ApiClient.POST(API.mrSpeedyCalculateOrder, body).then((result) => {
                        console.log("mr speedy quotation result");
                        console.log(result);

                        if (!result || !result.is_successful) {
                            // means lalamove can't deliver to this address

                            Alert.alert(
                                'Info',
                                'Sorry, we unable to deliver to this address, please try another one.',
                            );
                        }
                        else if (result.is_successful && result.order && result.order.payment_amount) {
                            // { totalFee: "0.00", totalFeeCurrency: "MYR" }

                            setDeliveryQuotation({
                                totalFee: parseFloat(result.order.payment_amount),
                                totalFeeCurrency: 'MYR',
                                courierCode: COURIER_CODE.MRSPEEDY,
                            });
                        }
                    });
                }
            }
            else {
                // do nothing for now
            }
        }
    }, [actionModalVisibility, selectedUserOrder, selectedSender]);

    // componentDidMount() {
    //     ApiClient.GET(API.order2 + orderId).then((result) => {
    //         console.log(result);
    //         setState({
    //             orderId: result,
    //             orderItems: result.orderItems,
    //             orderData: result.outlet,
    //             orderMerchant: result.outlet.merchant,
    //             orderTax: result.outlet.orderTax,
    //         });
    //     });
    // }
    // function end

    var detailsFontSize = 25;

    if (Dimensions.get('screen').width <= 360) {
        detailsFontSize = 13;
        //console.log(Dimensions.get('screen').width)
    }

    const detailsTextScale = {
        fontSize: detailsFontSize,
    };

    var detailsFontSize2 = 16;

    if (Dimensions.get('screen').width <= 360) {
        detailsFontSize2 = 13;
        //console.log(Dimensions.get('screen').width)
    }

    const detailsTextScale2 = {
        fontSize: detailsFontSize2,
    };

    var detailsTopSpace = '48%';

    if (Dimensions.get('screen').width <= 360) {
        detailsTopSpace = '56.5%';
        //console.log(Dimensions.get('screen').width)
    }

    const detailsTopSpacing = {
        top: detailsTopSpace,
    };

    const waitForSender = () => {
        const body = {
            orderId: selectedUserOrder.uniqueId,
        };

        ApiClient.POST(API.waitForSender, body).then(async (result) => {
            // Alert.alert(
            //     'Success',
            //     'The user of this order had been notified.',
            //     [{ text: 'OK', onPress: () => { } }],
            //     { cancelable: false },
            // );

            // const userOrderSnapshot = await firebase().firestore()
            //     .collection(Collections.UserOrder)
            //     .where('uniqueId', '==', selectedUserOrder.uniqueId)
            //     .limit(1)
            //     .get();

            const userOrderSnapshot = await getDocs(
                query(
                    collection(global.db, Collections.UserOrder),
                    where('uniqueId', '==', selectedUserOrder.uniqueId),
                    limit(1),
                )
            );

            var userOrder = null;
            if (!userOrderSnapshot.empty) {
                userOrder = userOrderSnapshot.docs[0].data();
            }

            CommonStore.update(s => {
                s.selectedUserOrder = userOrder;
            }, () => {
                setActionModalVisibility(false);
            });
        });
    }

    // const getTestData = async () => {
    //     const userOrderSnapshot = await firebase.firestore().collection(Collections.UserOrder)

    //         .where('uniqueId', '==', orderId)
    //         .limit(1)
    //         .get();

    //     var userOrder = null;

    //     if (!userOrderSnapshot.empty) {
    //         userOrder = userOrderSnapshot.docs[0].data();
    //     }

    //     CommonStore.update(s => {
    //         s.selectedUserOrder = userOrder;
    //     });
    // };

    // useEffect(() => {
    //     getTestData()
    // }, [])


    const startMolPay = () => {
        // need get readable order id from api first

        const body = {
            outletId: selectedUserOrder.outletId,
        };

        var amountToPay = Math.max(deliveryQuotation.totalFee - selectedUserOrder.deliveryFee, 0);

        // Testing

        amountToPay = amountToPay > 5 ? 5 : amountToPay;

        var paymentDetails = {
            // Optional, REQUIRED when use online Sandbox environment and account credentials.
            'mp_dev_mode': true,

            // Mandatory String. Values obtained from Razer Merchant Services.
            'mp_username': 'api_SB_mykoodoo',
            'mp_password': 'WaaU1IeZ*&(%%',
            'mp_merchant_ID': 'SB_mykoodoo',
            'mp_app_name': 'mykoodoo',
            'mp_verification_key': '0bd0efac623106b4c19bc28b8e9a0d8d',

            // Mandatory String. Payment values.
            'mp_amount': amountToPay, // Minimum 1.01
            'mp_order_ID': `#${selectedUserOrder.orderType === ORDER_TYPE.DINEIN ? '' : 'T'}${selectedUserOrder.orderId}`,
            'mp_currency': 'MYR',
            'mp_country': 'MY',

            // Optional String.
            'mp_channel': 'multi', // Use 'multi' for all available channels option. For individual channel seletion, please refer to https://github.com/RazerMS/rms-mobile-xdk-examples/blob/master/channel_list.tsv.
            'mp_bill_description': selectedUserOrder.outletName + ' delivery topup RM' + parseFloat(amountToPay).toFixed(2),
            'mp_bill_name': selectedUserOrder.userName,
            'mp_bill_email': userEmail,
            'mp_bill_mobile': selectedUserOrder.userPhone,

            'mp_sandbox_mode': true,
            'mp_tcctype': 'AUTH',
        }

        window.startMolpay(paymentDetails, (data) => {
            //callback after payment success

            console.log("razer result v2");
            console.log(data);

            const result = JSON.parse(data);
            console.log(result);

            if (result.error_code || result.Error) {
                console.log('razer error');

                Alert.alert(
                    "Error",
                    "Failed to process your payment",
                    [
                        {
                            text: "OK",
                            onPress: () => {
                                // navigation.jumpTo('Home')
                                // navigation.navigate('Home');
                                navigation.dispatch(
                                    CommonActions.reset({
                                        index: 0,
                                        routes: [{ name: "Home" }]
                                    }));
                            },
                        },
                    ],
                    { cancelable: false }
                );

                CommonStore.update(s => {
                    s.isLoading = false;
                });
            }
            else {
                console.log("payment success v2");

                topupUpdateCourier(result);
            }

            // placeUserOrder();
        });
    }

    const topup = () => {
        CommonStore.update(s => {
            s.isLoading = true;
        });

        // think should record down the topup payment details

        const deliveryFeeDiff = Math.max(deliveryQuotation.totalFee - selectedUserOrder.deliveryFee, 0);

        if (deliveryFeeDiff > 0) {
            startMolPay();
        }
        else {
            topupUpdateCourier();
        }
    }

    const topupUpdateCourier = (paymentDetails) => {
        var body = {
            orderId: selectedUserOrder.uniqueId,
            merchantId: selectedUserOrder.merchantId,

            deliveryFeeDiff: Math.max(deliveryQuotation.totalFee - selectedUserOrder.deliveryFee, 0),

            deliveryFee: deliveryQuotation.totalFee,
            courierCode: deliveryQuotation.courierCode,
            deliveryCurrency: deliveryQuotation.totalFeeCurrency,

            paymentDetailsCourierAction: paymentDetails ? paymentDetails : null,
        };

        ApiClient.POST(API.updateUserOrderCourierByUser, body, {
            timeout: 10000,
        }).then(async (result) => {
            console.log("updateUserOrderCourierByUser");
            console.log(result)

            if (result) {
                if (result.status === 'success') {
                    Alert.alert(
                        "Success",
                        "Sender for this order changed successfully.",
                        [
                            {
                                text: "OK",
                                onPress: async () => {
                                    // const userOrderSnapshot = await firebase.firestore()
                                    //     .collection(Collections.UserOrder)
                                    //     .where('uniqueId', '==', selectedUserOrder.uniqueId)
                                    //     .limit(1)
                                    //     .get();

                                    const userOrderSnapshot = await getDocs(
                                        query(
                                            collection(global.db, Collections.UserOrder),
                                            where('uniqueId', '==', selectedUserOrder.uniqueId),
                                            limit(1),
                                        )
                                    );

                                    var userOrder = null;
                                    if (!userOrderSnapshot.empty) {
                                        userOrder = userOrderSnapshot.docs[0].data();
                                    }

                                    CommonStore.update(s => {
                                        s.selectedUserOrder = userOrder;
                                    }, () => {
                                        setActionModalVisibility(false);
                                    });
                                },
                            },
                        ],
                        { cancelable: false }
                    );
                }
                else {
                    Alert.alert(
                        "Error",
                        result.message,
                        [
                            {
                                text: "OK",
                                onPress: () => {
                                },
                            },
                        ],
                        { cancelable: false }
                    );
                }
            }
            else {
                Alert.alert(
                    "Error",
                    "Failed to change the sender for this order.",
                    [
                        {
                            text: "OK",
                            onPress: () => {
                            },
                        },
                    ],
                    { cancelable: false }
                );
            }

            CommonStore.update(s => {
                s.isLoading = false;
            });
        });
    };

    const completeUserOrderByUser = () => {
        CommonStore.update(s => {
            s.isLoading = true;
        });

        const body = {
            orderId: selectedUserOrder.uniqueId,
        };

        ApiClient.POST(API.completeUserOrderByUser, body).then(async (result) => {
            // const userOrderSnapshot = await firebase.firestore()
            //     .collection(Collections.UserOrder)
            //     .where('uniqueId', '==', selectedUserOrder.uniqueId)
            //     .limit(1)
            //     .get();

            const userOrderSnapshot = await getDocs(
                query(
                    collection(global.db, Collections.UserOrder),
                    where('uniqueId', '==', selectedUserOrder.uniqueId),
                    limit(1),
                )
            );

            var userOrder = null;
            if (!userOrderSnapshot.empty) {
                userOrder = userOrderSnapshot.docs[0].data();
            }

            CommonStore.update(s => {
                s.selectedUserOrder = userOrder;

                s.isLoading = false;
            }, () => {
                // setActionModalVisibility(false);

                setCompleteModalVisibility(true);
            });
        });
    }

    const submitReview = () => {
        CommonStore.update(s => {
            s.isLoading = true;
        });

        const body = {
            orderId: selectedUserOrder.uniqueId,
            userId: selectedUserOrder.userId,
            userName: userName,
            userImage: userAvatar,
            outletId: selectedUserOrder.outletId,
            outletName: selectedUserOrder.outletName,
            merchantId: selectedUserOrder.merchantId,
            merchantName: selectedUserOrder.merchantName,
            ratings: starRatingDefault,
            comments: reviewComment,
        };

        console.log(body);

        ApiClient.POST(API.submitOrderReviewByUser, body).then(async (result) => {
            // const userOrderSnapshot = await firebase.firestore()
            //     .collection(Collections.UserOrder)
            //     .where('uniqueId', '==', selectedUserOrder.uniqueId)
            //     .limit(1)
            //     .get();

            const userOrderSnapshot = await getDocs(
                query(
                    collection(global.db, Collections.UserOrder),
                    where('uniqueId', '==', selectedUserOrder.uniqueId),
                    limit(1),
                )
            );

            var userOrder = null;
            if (!userOrderSnapshot.empty) {
                userOrder = userOrderSnapshot.docs[0].data();
            }

            CommonStore.update(s => {
                s.selectedUserOrder = userOrder;

                s.isLoading = false;
            }, () => {
                // setActionModalVisibility(false);

                setCompleteModalVisibility(false);
            });
        });
    };

    //const starFilled = 'https://raw.githubusercontent.com/tranhonghan/images/main/star_filled.png'
    //const starUnfilled = 'https://raw.githubusercontent.com/tranhonghan/images/main/star_corner.png'

    //const starFilled = 'https://lh3.googleusercontent.com/proxy/DhwXgAC-E6rS9vYy3DIgsv8d9_G7b7hGCi5Aa3-LF0rCnWE3yhGoAoM_2ucnL-BEAHtWhAq-RHyho_LnDskAXBA0jA5OlCuISQ'
    //const starUnfilled = 'https://raw.githubusercontent.com/tranhonghan/images/main/star_corner.png'

    const StarRatingView = () => {

        return (
            <>
                {
                    starRating.map((item, key) => {
                        return (
                            <>
                                <TouchableOpacity
                                    style={{ marginLeft: 3 }}
                                    key={item}
                                    onPress={() =>
                                        setStarRatingDefault(item)
                                    }
                                >
                                    {/* <Image
                    style={styles.starStyle}
                    source={
                        item <= starRatingDefault
                        ? {uri: starFilled}
                        : {uri: starUnfilled}
                    }
                    /> */}
                                    <AntDesign name={'star'} size={35}
                                        color={
                                            item <=
                                                starRatingDefault
                                                ? Colors.primaryColor
                                                : Colors.fieldtTxtColor
                                        } />
                                </TouchableOpacity>
                            </>
                        )
                    }
                    )
                }
            </>
        )
    }


    const renderQuestionnaire = ({ item, index }) => (
        <>
            <View style={{ flexDirection: 'row', justifyContent: 'flex-end', alignItems: 'flex-end', padding: 10 }}>
                <TouchableOpacity
                    style={styles.closeButton}
                    onPress={() => {
                        setQuestionnaireVisibility(false);
                    }}>
                    <AntDesign name="closecircle" size={25} color={Colors.fieldtTxtColor} />
                </TouchableOpacity>
            </View>
            <View style={{ flexDirection: 'column', alignItems: 'flex-start', justifyContent: 'flex-start' }}>
                <Text style={{
                    fontSize: 20,
                    fontFamily: "NunitoSans-Bold"
                }}>
                    Questionnaire
                </Text>
                <Text style={{
                    fontSize: 13,
                    fontFamily: "NunitoSans-Regular",
                    marginTop: 5
                }}>
                    Help us serve you better
                </Text>
            </View>

            <Text style={{
                fontSize: 13,
                fontFamily: "NunitoSans-Regular",
                marginTop: 10
            }}>
                Are you a vegetarian?
            </Text>

            <Picker style={{ marginTop: 5, borderWidth: 1, borderColor: '#E5E5E5', borderRadius: 5, height: 30, width: 200, paddingVertical: 0, }}
                dropDownStyle={{ borderWidth: 1, borderColor: '#E5E5E5', borderRadius: 5, width: 200, paddingVertical: 0, marginHorizontal: 10 }}
                arrowSize={17}
                arrowColor={'black'}
                arrowStyle={{ paddingVertical: 0 }}
                itemStyle={{ justifyContent: 'flex-start', marginLeft: 5 }}
                placeholderStyle={{ marginLeft: 0 }}
                placeholder={" Answer"}
                items={[{ label: 'Yes', value: 'Yes' }, { label: 'No', value: 'No' }]}

            />

            {/* <Text style={{
                fontSize: 13,
                fontFamily: "NunitoSans-Regular",
                marginTop: 10
            }}>
                Are you allergic to any food or ingredient?
        </Text>
        <DropDownPicker style={{ marginTop: 5, borderWidth: 1, borderColor: '#E5E5E5', borderRadius: 5, height: 30, width: 200, paddingVertical: 0,  }}
            
            dropDownStyle={{ borderWidth: 1, borderColor: '#E5E5E5', borderRadius: 5, width: 200, paddingVertical: 0, marginHorizontal: 10 }}
            arrowSize={17}
            arrowColor={'black'}
            arrowStyle={{ paddingVertical: 0 }}
            itemStyle={{ justifyContent: 'flex-start', marginLeft: 5 }}
            placeholderStyle={{ marginLeft: 0 }}
            placeholder={" Answer"}
            items={[{ label: 'Yes', value: 'Yes' }, { label: 'No', value: 'No' }]}

        />

        
        <Text style={{
                fontSize: 13,
                fontFamily: "NunitoSans-Regular",
                marginTop: 10
            }}>
                What foods or ingredients are you allergic to?
        </Text>
        <TextInput
            underlineColorAndroid={Colors.fieldtBgColor}
            clearButtonMode='while-editing'
            placeholder={"Answer"}
            style={styles.questionInput}
            />

        <Text style={{
                fontSize: 13,
                fontFamily: "NunitoSans-Regular",
                marginTop: 10
            }}>
                Do you like fast food or healthy food?
        </Text>
        <DropDownPicker style={{ marginTop: 5, borderWidth: 1, borderColor: '#E5E5E5', borderRadius: 5, height: 30, width: 200, paddingVertical: 0,  }}
            
            dropDownStyle={{ borderWidth: 1, borderColor: '#E5E5E5', borderRadius: 5, width: 200, paddingVertical: 0, marginHorizontal: 10 }}
            arrowSize={17}
            arrowColor={'black'}
            arrowStyle={{ paddingVertical: 0 }}
            itemStyle={{ justifyContent: 'flex-start', marginLeft: 5 }}
            placeholderStyle={{ marginLeft: 0 }}
            placeholder={" Answer"}
            items={[{ label: 'Fast Food', value: 'Fast Food' }, { label: 'Healthy Food', value: 'Healthy Food' }]}

        />

        <Text style={{
                fontSize: 13,
                fontFamily: "NunitoSans-Regular",
                marginTop: 10
            }}>
                Do you like dessert?
        </Text>
        <DropDownPicker style={{ marginTop: 5, borderWidth: 1, borderColor: '#E5E5E5', borderRadius: 5, height: 30, width: 200, paddingVertical: 0,  }}
            
            dropDownStyle={{ borderWidth: 1, borderColor: '#E5E5E5', borderRadius: 5, width: 200, paddingVertical: 0, marginHorizontal: 10 }}
            arrowSize={17}
            arrowColor={'black'}
            arrowStyle={{ paddingVertical: 0 }}
            itemStyle={{ justifyContent: 'flex-start', marginLeft: 5 }}
            placeholderStyle={{ marginLeft: 0 }}
            placeholder={" Answer"}
            items={[{ label: 'Yes', value: 'Yes' }, { label: 'No', value: 'No' }]}

        /> */}
            {expandCompleteQuestionnaire ? (
                <View style={{ flexDirection: 'column', alignItems: 'flex-start', justifyContent: 'flex-start' }}>
                    <Text style={{
                        fontSize: 20,
                        fontFamily: "NunitoSans-Bold"
                    }}>
                        Thank you !
                    </Text>
                    <Text style={{
                        fontSize: 13,
                        fontFamily: "NunitoSans-Regular",
                        marginTop: 5
                    }}>
                        Hope to see you again
                    </Text>
                </View>
            ) : null}
            <View style={{ flexDirection: 'row', marginTop: 25, justifyContent: 'flex-end', alignItems: 'flex-end', padding: 10, }}>
                <TouchableOpacity style={{
                    width: "40%",
                    height: 40,
                    backgroundColor: Colors.primaryColor,
                    borderRadius: 10,
                    justifyContent: "center",
                    alignItems: "center",
                    borderWidth: 1,
                    borderColor: Colors.descriptionColor,
                    zIndex: 1000,
                    marginRight: 10
                }}
                    onPress={() => {
                        setExpandCompleteQuestionnaire(true);
                    }}
                >

                    <Text style={{ color: Colors.whiteColor, fontFamily: "NunitoSans-Regular" }}>SUBMIT</Text>

                </TouchableOpacity>
                <TouchableOpacity style={{
                    width: "40%",
                    height: 40,
                    backgroundColor: Colors.primaryColor,
                    borderRadius: 10,
                    justifyContent: "center",
                    alignItems: "center",
                    borderWidth: 1,
                    borderColor: Colors.descriptionColor,
                    zIndex: 1000,
                }}
                >

                    <Text style={{ color: Colors.whiteColor, fontFamily: "NunitoSans-Regular" }}>NEXT</Text>

                </TouchableOpacity>
            </View>
        </>
    );

    var orderCodeFont = 16;

    if (Dimensions.get('screen').width <= 360) {
        orderCodeFont = 14;
        //console.log(Dimensions.get('screen').width)
    }

    const orderCodeFontSize = {
        fontSize: orderCodeFont,
    };

    var orderCodeFont1 = 16;

    if (Dimensions.get('screen').width <= 360) {
        orderCodeFont1 = 12;
        //console.log(Dimensions.get('screen').width)
    }

    const orderCodeFontSize1 = {
        fontSize: orderCodeFont1,
    };

    const proceedToReceiptDetails = async (item) => {
        // const outletSnapshot = await firebase().collection(Collections.Outlet)
        //     .firestore
        //     .where('uniqueId', '==', item.outletId)
        //     .limit(1)
        //     .get();

        const outletSnapshot = await getDocs(
            query(
                collection(global.db, Collections.Outlet),
                where('uniqueId', '==', item.outletId),
                limit(1),
            )
        );

        var outlet = null;

        if (!outletSnapshot.empty) {
            outlet = outletSnapshot.docs[0].data();
        }

        CommonStore.update(s => {
            s.selectedUserOrder = item;
            s.selectedOutlet = outlet;
        });

        var tempUserAddress = null;
        for (var i = 0; i < userAddresses.length; i++) {
            if (userAddresses[i].uniqueId === item.userAddressId) {
                tempUserAddress = userAddresses[i];
            }
        }

        props.navigation.navigate('ReceiptDetail', {
            orderId: item.uniqueId,
            order: item,
            outlet: outlet,

            userDetails: {
                address: tempUserAddress.address,
                name: userName,
                phone: userNumber,
                email: userEmail,
                userId: firebaseUid,
            },
        });
    }

    return (
        <View style={{ flex: 1 }}>
            <ScrollView style={styles.container} ref={scrollViewRef} contentContainerStyle={{
                // paddingBottom: 40,
            }}>
                <View style={{ alignItems: 'center', flexDirection: 'row', justifyContent: 'center' }}>
                    <View style={{ alignSelf: 'center' }}>
                        <AsyncImage

                            source={{ uri: selectedUserOrder ? selectedUserOrder.merchantLogo : '' }}
                            // item={selectedUserOrder}
                            style={{ width: 90, height: 90, borderRadius: 10 }}
                        />
                    </View>
                </View>
                <View style={styles.titleDetail}>
                    <View style={{ flexDirection: 'row' }}>
                        <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 18 }}>{selectedUserOrder ? selectedUserOrder.outletName : ''}</Text>
                        {/* <TouchableOpacity
                    style={{
                        marginLeft: 10
                    }}
                    onPress={() => {

                        CommonStore.update(s => {
                            s.selectedUserOrder = selectedUserOrder;
                          });

                        navigation.navigate('ReceiptDetail');
                        proceedToReceiptDetails(selectedUserOrder);
                     }}>
                    <Ionicons name={'receipt-outline'} size={30} color={Colors.primaryColor}/>
                    </TouchableOpacity> */}
                    </View>
                    <Text
                        style={{
                            marginTop: 10,
                            textAlign: 'center',
                            fontFamily: "NunitoSans-Bold",
                        }}>
                        {orderOutlet.address}
                    </Text>
                    <Text style={{
                        marginTop: 0, fontFamily: "NunitoSans-Bold",
                    }}>
                        Phone: {orderOutlet.phone}
                    </Text>
                </View>

                <View style={[styles.orderTitle]}>
                    <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                        <View style={{ flexDirection: 'row' }}>
                            <Text style={[orderCodeFontSize, {
                                fontFamily: "NunitoSans-SemiBold",
                                marginBottom: 5,
                            }]}>
                                Order # :{' '}
                            </Text>
                            <Text style={[orderCodeFontSize, {
                                fontFamily: "NunitoSans-Bold",
                                marginBottom: 5,
                                /* width: '30%' */
                            }]} numberOfLines={2}>
                                {selectedUserOrder ? selectedUserOrder.orderId : ''}
                                {selectedUserOrderOthers.length > 0 ? `, ${(selectedUserOrderOthers.map(order => order.orderId).join(', '))}` : ''}
                            </Text>
                        </View>
                        <View style={{ flexDirection: 'row' }}>
                            <Text style={[orderCodeFontSize, {
                                fontFamily: "NunitoSans-SemiBold",
                                marginBottom: 5,
                            }]}>
                                Order Type :{' '}
                            </Text>
                            <Text style={[orderCodeFontSize, {
                                fontFamily: "NunitoSans-Bold",
                                marginBottom: 5,
                                /* width: '70%' */
                            }]} numberOfLines={2}>
                                {ORDER_TYPE_PARSED[selectedUserOrder ? selectedUserOrder.orderType : '']}
                            </Text>
                        </View>
                    </View>
                    <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                        <View style={{ flexDirection: 'row' }}>
                            <Text style={[orderCodeFontSize, {
                                fontFamily: "NunitoSans-SemiBold",
                                marginBottom: 5,
                            }]}>
                                Date :{' '}
                            </Text>
                            <Text style={[orderCodeFontSize, {
                                fontFamily: "NunitoSans-SemiBold",
                                marginBottom: 5,
                            }]}>
                                {moment(selectedUserOrder ? selectedUserOrder.orderDate : '').format('DD-MMM-YYYY')}
                            </Text>
                        </View>
                        <View style={{ flexDirection: 'row' }}>
                            <Text style={[orderCodeFontSize, {
                                fontFamily: "NunitoSans-SemiBold",
                                marginBottom: 10,
                            }]}>
                                Time :{' '}
                            </Text>
                            <Text style={[orderCodeFontSize, {
                                fontFamily: "NunitoSans-SemiBold",
                                marginBottom: 10,
                            }]}>
                                {moment(selectedUserOrder ? selectedUserOrder.orderDate : '').format('hh:mm A')}
                            </Text>
                        </View>
                    </View>
                </View>


                {/* <View style={{ minHeight: Dimensions.get('screen').height * 0.2, }}> */}
                {selectedUserOrder && selectedUserOrder.cartItems != null
                    ? selectedUserOrder.cartItems.map((element, index) => {
                        return (
                            <View>
                                <View style={{ flexDirection: 'row', justifyContent: 'space-between', paddingVertical: 5, }}>
                                    <View style={{ width: "60%" }}>
                                        <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10 }}>{element.name}</Text>
                                    </View>
                                    <View style={{ width: "10%" }}>
                                        <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10 }}>X{element.quantity}</Text>
                                    </View>
                                    <View style={{ width: "5%" }}>
                                        <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10 }}></Text>
                                    </View>
                                    <View style={{ width: "25%" }}>
                                        <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                                            <Text style={{ fontSize: 10, alignSelf: 'flex-end', fontFamily: "NunitoSans-Bold", }}>RM </Text>
                                            <Text style={{ alignSelf: 'flex-end', fontFamily: "NunitoSans-Bold", fontSize: 10 }}>{(element.price + (element.discountPromotions ? element.discountPromotions : 0)).toFixed(2)}</Text>
                                        </View>
                                    </View>
                                </View>

                                {element.remarks && element.remarks.length > 0 ?
                                    <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                                        <View style={{ width: "60%" }}>
                                            <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10, }}><Text style={{ color: '#a8a8a8' }}>- </Text>{element.remarks}</Text>
                                        </View>
                                        <View style={{ width: "10%" }}>
                                            <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10, }}></Text>
                                        </View>
                                        <View style={{ width: "5%" }}>
                                            <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10, }}></Text>
                                        </View>
                                        <View style={{ width: "25%" }}>
                                            <Text style={{ alignSelf: 'flex-end', fontFamily: "NunitoSans-Bold", fontSize: 10, }}></Text>
                                        </View>
                                    </View>
                                    : <></>
                                }

                                {element.addOns.map((addOnChoice, i) => {
                                    return (
                                        <View style={{ flexDirection: 'row', justifyContent: 'space-between', paddingVertical: 5, }}>
                                            <View style={{ width: "60%", }}>
                                                <View style={{ flexDirection: 'row', }}>
                                                    <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10, color: Colors.descriptionColor, width: '30%' }}>{`${addOnChoice.name}:`}</Text>
                                                    <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10, color: Colors.descriptionColor, width: '70%' }}>{`${addOnChoice.choiceNames[0]}`}</Text>
                                                </View>
                                            </View>
                                            <View style={{ width: "10%" }}>
                                                <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10, color: Colors.descriptionColor, }}>{`${addOnChoice.quantities
                                                    ? `x${addOnChoice.quantities[0]}`
                                                    : ''
                                                    }`}</Text>
                                            </View>
                                            <View style={{ width: "5%" }}>
                                                <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10, color: Colors.descriptionColor, }}></Text>
                                            </View>
                                            <View style={{ width: "25%" }}>
                                                <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                                                    <Text style={{ alignSelf: 'flex-end', fontFamily: "NunitoSans-Bold", fontSize: 10, color: Colors.descriptionColor, }}>RM </Text>
                                                    <Text style={{ alignSelf: 'flex-end', fontFamily: "NunitoSans-Bold", fontSize: 10, color: Colors.descriptionColor, }}>{addOnChoice.prices.reduce((accumAddOn, priceAddOn) => accumAddOn + priceAddOn, 0).toFixed(2)}</Text>
                                                </View>
                                            </View>
                                        </View>
                                    )
                                })}

                            </View>
                        );
                    })
                    : null}

                {
                    selectedUserOrderOthers.map(order => {
                        return (
                            <>
                                {order && order.cartItems != null
                                    ? order.cartItems.map((element, index) => {
                                        return (
                                            <View>
                                                <View style={{ flexDirection: 'row', justifyContent: 'space-between', paddingVertical: 5, }}>
                                                    <View style={{ width: "60%" }}>
                                                        <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10 }}>{element.name}</Text>
                                                    </View>
                                                    <View style={{ width: "10%" }}>
                                                        <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10 }}>X{element.quantity}</Text>
                                                    </View>
                                                    <View style={{ width: "5%" }}>
                                                        <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10 }}></Text>
                                                    </View>
                                                    <View style={{ width: "25%" }}>
                                                        <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                                                            <Text style={{ fontSize: 10, alignSelf: 'flex-end', fontFamily: "NunitoSans-Bold", }}>RM </Text>
                                                            <Text style={{ alignSelf: 'flex-end', fontFamily: "NunitoSans-Bold", fontSize: 10 }}>{(element.price + (element.discountPromotions ? element.discountPromotions : 0)).toFixed(2)}</Text>
                                                        </View>
                                                    </View>
                                                </View>

                                                {element.remarks && element.remarks.length > 0 ?
                                                    <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                                                        <View style={{ width: "60%" }}>
                                                            <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10, }}><Text style={{ color: '#a8a8a8' }}>- </Text>{element.remarks}</Text>
                                                        </View>
                                                        <View style={{ width: "10%" }}>
                                                            <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10, }}></Text>
                                                        </View>
                                                        <View style={{ width: "5%" }}>
                                                            <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10, }}></Text>
                                                        </View>
                                                        <View style={{ width: "25%" }}>
                                                            <Text style={{ alignSelf: 'flex-end', fontFamily: "NunitoSans-Bold", fontSize: 10, }}></Text>
                                                        </View>
                                                    </View>
                                                    : <></>
                                                }

                                                {element.addOns.map((addOnChoice, i) => {
                                                    return (
                                                        <View style={{ flexDirection: 'row', justifyContent: 'space-between', paddingVertical: 5, }}>
                                                            <View style={{ width: "60%", }}>
                                                                <View style={{ flexDirection: 'row', }}>
                                                                    <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10, color: Colors.descriptionColor, width: '30%' }}>{`${addOnChoice.name}:`}</Text>
                                                                    <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10, color: Colors.descriptionColor, width: '70%' }}>{`${addOnChoice.choiceNames[0]}`}</Text>
                                                                </View>
                                                            </View>
                                                            <View style={{ width: "10%" }}>
                                                                <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10, color: Colors.descriptionColor, }}>{`${addOnChoice.quantities
                                                                    ? `x${addOnChoice.quantities[0]}`
                                                                    : ''
                                                                    }`}</Text>
                                                            </View>
                                                            <View style={{ width: "5%" }}>
                                                                <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10, color: Colors.descriptionColor, }}></Text>
                                                            </View>
                                                            <View style={{ width: "25%" }}>
                                                                <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                                                                    <Text style={{ alignSelf: 'flex-end', fontFamily: "NunitoSans-Bold", fontSize: 10, color: Colors.descriptionColor, }}>RM </Text>
                                                                    <Text style={{ alignSelf: 'flex-end', fontFamily: "NunitoSans-Bold", fontSize: 10, color: Colors.descriptionColor, }}>{addOnChoice.prices.reduce((accumAddOn, priceAddOn) => accumAddOn + priceAddOn, 0).toFixed(2)}</Text>
                                                                </View>
                                                            </View>
                                                        </View>
                                                    )
                                                })}

                                            </View>
                                        );
                                    })
                                    : null}
                            </>
                        );
                    })
                }

                {/* </View> */}
                <View style={styles.totalContainer}>
                    <View style={{ flex: 1, width: '75%', }}>
                        <Text style={styles.description}>Subtotal</Text>
                        <Text style={styles.description}>Discount (-)</Text>
                        {/* <Text style={styles.description}> */}
                        {/* Tax ( */}
                        {/* {orderTax ? orderTax.rate : 0} */}
                        {/* {orderOutlet.taxPercent} */}
                        {/* %) */}
                        {/* </Text> */}
                        {
                            selectedOutlet && selectedOutlet.taxActive
                                ?
                                <Text
                                    style={[
                                        styles.description,
                                    ]}>
                                    {`Tax (${(selectedOutlet.taxRate * 100).toFixed(0)}%)`}
                                </Text>
                                :
                                <></>
                        }
                        {
                            selectedOutlet && selectedOutlet.scActive
                                ?
                                <Text
                                    style={[
                                        styles.description,
                                    ]}>
                                    {`${selectedOutlet.scName ? selectedOutlet.scName : 'Service Charge'} (${(selectedOutlet.scRate * 100).toFixed(0)}%)`}
                                </Text>
                                :
                                <></>
                        }
                        <Text style={styles.description}>
                            Delivery Fee
                        </Text>
                        {
                            (selectedUserOrder && selectedUserOrder.deliveryPackagingFee && selectedUserOrder.orderType === ORDER_TYPE.DELIVERY)
                                ?
                                <Text style={styles.description}>
                                    Delivery Packaging Fee
                                </Text>
                                :
                                <></>
                        }

                        {
                            (selectedUserOrder && selectedUserOrder.pickupPackagingFee && selectedUserOrder.orderType === ORDER_TYPE.PICKUP)
                                ?
                                <Text style={styles.description}>
                                    Takeaway Packaging Fee
                                </Text>
                                :
                                <></>
                        }
                        <Text style={styles.total}>Total</Text>
                    </View>
                    <View style={{ alignSelf: 'flex-end', width: '25%', }}>
                        {/* <Text style={styles.price}>RM {selectedUserOrder.subtotal}</Text>
                        <Text style={styles.price}> {orderId.discount}</Text>
                        <Text style={styles.price}>
                            {' '}
                            {orderTax
                                ? (((parseFloat(orderId.subtotal) - parseFloat(orderId.discount)))
                                    * parseFloat(orderTax.rate)).toFixed(2)
                                : 0}
                        </Text>
                        <Text style={styles.totalPrice}>
                            RM {orderId.total}
                        </Text> */}
                        <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                            <Text style={styles.price}>RM </Text>
                            {selectedUserOrder ?
                                <Text style={styles.price}>{
                                    (
                                        selectedUserOrder.totalPrice + (selectedUserOrder.discountPromotionsTotal ? selectedUserOrder.discountPromotionsTotal : 0) +
                                        (selectedUserOrderOthers.reduce((accum, order) => accum + order.totalPrice + (order.discountPromotionsTotal ? order.discountPromotionsTotal : 0), 0))
                                    ).toFixed(2)
                                }</Text> : <></>}
                        </View>
                        <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                            <Text style={styles.price}>RM </Text>
                            {selectedUserOrder ? <Text style={styles.price}>{(
                                // selectedUserOrder.discount +
                                //     selectedUserOrder.discountPromotionsTotal ? selectedUserOrder.discountPromotionsTotal :
                                //     selectedUserOrder.discount +
                                //         selectedUserOrder.preorderPackagePrice ? selectedUserOrder.totalPrice - selectedUserOrder.preorderPackagePrice :
                                //         0
                                selectedUserOrder.discount +
                                (selectedUserOrder.discountPromotionsTotal ? selectedUserOrder.discountPromotionsTotal : 0) +
                                (selectedUserOrder.preorderPackagePrice ? selectedUserOrder.totalPrice - selectedUserOrder.preorderPackagePrice : 0) +

                                (selectedUserOrderOthers.reduce((accum, order) => accum +
                                    order.discount +
                                    (order.discountPromotionsTotal ? order.discountPromotionsTotal : 0) +
                                    (order.preorderPackagePrice ? order.totalPrice - order.preorderPackagePrice : 0),
                                    0)
                                )
                            ).toFixed(2)}</Text> : <></>}
                        </View>
                        {
                            selectedOutlet && selectedOutlet.taxActive
                                ?
                                <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                                    <Text style={styles.price}>RM </Text>
                                    <Text style={styles.price}>{
                                        (
                                            ((selectedUserOrder && selectedUserOrder.tax) ? selectedUserOrder.tax : 0) +
                                            (selectedUserOrderOthers.reduce((accum, order) => accum + (order && order.tax) ? order.tax : 0, 0))
                                        ).toFixed(2)
                                    }</Text>
                                </View>
                                :
                                <></>
                        }
                        {
                            selectedOutlet && selectedOutlet.scActive
                                ?
                                <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                                    <Text style={styles.price}>RM </Text>
                                    <Text style={styles.price}>{
                                        // ((selectedUserOrder && selectedUserOrder.sc) ? selectedUserOrder.sc.toFixed(2) : (0).toFixed(2))
                                        (
                                            ((selectedUserOrder && selectedUserOrder.sc) ? selectedUserOrder.sc : 0) +
                                            (selectedUserOrderOthers.reduce((accum, order) => accum + (order && order.sc) ? order.sc : 0, 0))
                                        ).toFixed(2)
                                    }</Text>
                                </View>
                                :
                                <></>
                        }
                        <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                            <Text style={styles.price}>RM </Text>
                            {/*<Text style={styles.price}>{selectedUserOrder.deliveryFee.toFixed(2)}</Text>*/}
                            {selectedUserOrder ? <Text style={styles.price}>{(selectedUserOrder && selectedUserOrder.deliveryFee) ? selectedUserOrder.deliveryFee.toFixed(2) : (0).toFixed(2)}</Text> : <></>}
                        </View>
                        {
                            (selectedUserOrder && selectedUserOrder.deliveryPackagingFee && selectedUserOrder.orderType === ORDER_TYPE.DELIVERY)
                                ?
                                <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                                    <Text style={styles.price}>RM </Text>
                                    <Text style={styles.price}>{selectedUserOrder.deliveryPackagingFee.toFixed(2)}</Text>

                                </View>
                                :
                                <></>
                        }

                        {
                            (selectedUserOrder && selectedUserOrder.pickupPackagingFee && selectedUserOrder.orderType === ORDER_TYPE.PICKUP)
                                ?
                                <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                                    <Text style={styles.price}>RM </Text>
                                    <Text style={styles.price}>{selectedUserOrder.pickupPackagingFee.toFixed(2)}</Text>
                                </View>
                                :
                                <></>
                        }
                        <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
                            <Text style={styles.totalPrice}>RM </Text>
                            {/* <Text style={styles.totalPrice}>{(Math.ceil((selectedUserOrder.totalPrice + selectedUserOrder.tax + (selectedUserOrder.sc ? selectedUserOrder.sc : 0) + selectedUserOrder.deliveryFee - selectedUserOrder.discount.toFixed(2)) * 20 - 0.05) / 20).toFixed(2)}</Text> */}
                            {/*<Text style={styles.totalPrice}>{(selectedUserOrder.finalPrice).toFixed(2)}</Text>*/}
                            <Text style={styles.totalPrice}>{
                                // ((selectedUserOrder && selectedUserOrder.finalPrice) ? selectedUserOrder.finalPrice : 0)
                                (
                                    ((selectedUserOrder && selectedUserOrder.finalPrice) ? selectedUserOrder.finalPrice : 0) +
                                    (selectedUserOrderOthers.reduce((accum, order) => accum + (order && order.finalPrice) ? order.finalPrice : 0, 0))
                                ).toFixed(2)
                            }</Text>
                        </View>
                    </View>
                </View>







                <View style={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    width: '100%',
                    flexDirection: 'row',
                    marginTop: 10
                }}>

                    {/* <TouchableOpacity style={{
                        width: "30%",
                        height: 40,
                        backgroundColor: Colors.primaryColor,
                        borderRadius: 40, justifyContent: "center", alignItems: "center", borderWidth: 1, borderColor: Colors.descriptionColor,
                        flexDirection: 'row',
                        zIndex: 1000,

                    }} onPress={() => {
                        // props.navigation.goBack();
                        // props.navigation.navigate('Home');

                        props.navigation.reset({
                            index: 0,
                            routes: [{
                                name: 'Home',
                            }],
                        });
                    }}>

                        <Text
                            style={{
                                textAlign: 'center',
                                color: Colors.whiteColor, fontFamily: "NunitoSans-Regular",
                            }}>
                            Home
                        </Text>

                    </TouchableOpacity> */}

                    {/* <TouchableOpacity style={{
                        width: "30%",
                        height: 40,
                        backgroundColor: Colors.primaryColor,
                        borderRadius: 40, justifyContent: "center", alignItems: "center", borderWidth: 1, borderColor: Colors.descriptionColor,
                        flexDirection: 'row',
                        marginLeft: 10,
                        zIndex: 1000,
                    }}
                        onPress={() => {
                            if (orderId && orderData) {
                                // props.navigation.navigate("ConfirmOrder", {
                                //     orderResult: selectedUserOrder,
                                //     outletData: orderOutlet,
                                // });

                                // navigation.navigate("OrderStatus", { outletData: orderOutlet });
                            }

                            setExpandDetails(!expandDetails);

                            setTimeout(() => {
                                scrollViewRef && scrollViewRef.current && scrollViewRef.current.scrollToEnd({
                                    duration: 500,
                                    animated: true,
                                });
                            }, 100);
                        }}
                    >
                        <Text style={{ color: Colors.whiteColor, fontFamily: "NunitoSans-Regular", marginHorizontal: 5 }}>Status</Text>
                        <Feather name={expandDetails ? "chevron-up" : "chevron-down"} size={24} color={'#ffffff'} />
                    </TouchableOpacity> */}

                    {
                        selectedUserOrder && selectedUserOrder.courierActionPending
                            // true
                            ?
                            <TouchableOpacity style={{
                                width: "30%",
                                height: 40,
                                backgroundColor: Colors.primaryColor,
                                borderRadius: 40, justifyContent: "center", alignItems: "center", borderWidth: 1, borderColor: Colors.descriptionColor,
                                flexDirection: 'row',
                                zIndex: 1000,
                                marginLeft: 10,
                            }}
                                onPress={() => {
                                    setActionModalVisibility(true)
                                }}
                            >
                                <Text style={{ color: Colors.whiteColor, fontFamily: "NunitoSans-Regular", marginRight: 5 }}>Action</Text>

                            </TouchableOpacity>
                            :
                            <></>
                    }

                    {/* {
                        selectedUserOrder && selectedUserOrder.orderStatus !== USER_ORDER_STATUS.ORDER_COMPLETED && selectedUserOrder.orderStatus !== USER_ORDER_STATUS.ORDER_CANCELLED_BY_MERCHANT
                            ?

                            <TouchableOpacity style={{
                                width: "30%",
                                height: 40,
                                backgroundColor: Colors.primaryColor,
                                borderRadius: 40, justifyContent: "center", alignItems: "center", borderWidth: 1, borderColor: Colors.descriptionColor,
                                flexDirection: 'row',
                                zIndex: 1000,
                                marginLeft: 10,
                            }}
                                onPress={() => {
                                    // setCompleteModalVisibility(true)
                                    completeUserOrderByUser();
                                }}
                            >
                                <Text style={{ color: Colors.whiteColor, fontFamily: "NunitoSans-Regular", }}>Received</Text>

                            </TouchableOpacity>
                            :
                            <TouchableOpacity style={{
                                width: "30%",
                                height: 40,
                                backgroundColor: Colors.primaryColor,
                                borderRadius: 40, justifyContent: "center", alignItems: "center", borderWidth: 1, borderColor: Colors.descriptionColor,
                                flexDirection: 'row',
                                zIndex: 1000,
                                marginLeft: 10,
                            }}
                                onPress={() => {
                                    //This is just for showing how UI appear
                                }}
                            >
                                <Text style={{ color: Colors.whiteColor, fontFamily: "NunitoSans-Regular", }}>Received</Text>

                            </TouchableOpacity>
                    } */}
                </View>

                {/* {
                    selectedUserOrder && selectedUserOrder.orderType === ORDER_TYPE.DELIVERY
                        ?
                        <View style={{
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center',
                            width: '100%',
                            flexDirection: 'row',
                            marginTop: 10
                        }}>
                            <TouchableOpacity style={{
                                width: "30%",
                                height: 40,
                                backgroundColor: Colors.primaryColor,
                                borderRadius: 40, justifyContent: "center", alignItems: "center", borderWidth: 1, borderColor: Colors.descriptionColor,
                                flexDirection: 'row',
                                zIndex: 1000,
                                marginLeft: 10,
                            }}
                                onPress={() => {
                                    props.navigation.navigate("DeliveryInfo", { orderId: orderId });

                                }}
                            >
                                <Text style={{ color: Colors.whiteColor, fontFamily: "NunitoSans-Regular", }}>Delivery Info</Text>

                            </TouchableOpacity>

                        </View>
                        :
                        <></>
                } */}

                <Modal
                    style={{
                        // flex: 1
                    }}
                    visible={receiptModal}
                    transparent={true}
                    animationType={'slide'}
                >
                    <View style={styles.modalContainerReceipt}>
                        <ScrollView>
                            <View style={[styles.modalViewReceipt, {
                            }]}>
                                <View style={{ borderWidth: 0, borderColor: Colors.fieldtBgColor, height: '90%', width: '90%' }}>
                                    <View style={{ alignItems: 'center', flexDirection: 'row', justifyContent: 'center' }}>
                                        <View style={{ alignSelf: 'center' }}>
                                            <AsyncImage
                                                source={{ uri: selectedUserOrder ? selectedUserOrder.merchantLogo : '' }}
                                                // item={selectedUserOrder}
                                                style={{ width: 70, height: 70, borderRadius: 10 }}
                                            />
                                        </View>
                                    </View>
                                    <View style={styles.titleDetail}>
                                        <View style={{ flexDirection: 'row' }}>
                                            <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 12 }}>{selectedUserOrder && selectedUserOrder.outletName}</Text>
                                        </View>
                                        <Text
                                            style={{
                                                marginTop: 10,
                                                textAlign: 'center',
                                                fontFamily: "NunitoSans-Bold",
                                                fontSize: 10
                                            }}>
                                            {orderOutlet.address}
                                        </Text>
                                        <Text style={{
                                            marginTop: 0, fontFamily: "NunitoSans-Bold", fontSize: 10
                                        }}>
                                            Phone: {orderOutlet.phone}
                                        </Text>
                                    </View>
                                    <View style={[styles.orderTitle, { flexDirection: 'row', justifyContent: 'space-between' }]}>
                                        <View style={{ flexDirection: 'row', width: '30%', justifyContent: 'flex-start' }}>
                                            <Text style={[orderCodeFontSize1, {
                                                fontFamily: "NunitoSans-SemiBold",
                                                marginBottom: 10,
                                            }]}>
                                                Order # :{' '}
                                            </Text>
                                            <Text style={[orderCodeFontSize1, {
                                                fontFamily: "NunitoSans-Bold",
                                                marginBottom: 10,
                                                /* width: '30%' */
                                            }]} numberOfLines={2}>
                                                {selectedUserOrder && selectedUserOrder.orderId}
                                            </Text>
                                        </View>
                                        <View style={{ flexDirection: 'row', width: '56%', justifyContent: 'flex-end' }}>
                                            <Text style={[orderCodeFontSize1, {
                                                fontFamily: "NunitoSans-SemiBold",
                                                marginBottom: 10,
                                            }]}>
                                                Order Type :{' '}
                                            </Text>
                                            <Text style={[orderCodeFontSize1, {
                                                fontFamily: "NunitoSans-Bold",
                                                marginBottom: 10,
                                                /* width: '70%' */
                                            }]} numberOfLines={2}>
                                                {ORDER_TYPE_PARSED[selectedUserOrder && selectedUserOrder.orderType]}
                                            </Text>
                                        </View>
                                    </View>
                                    <View style={{ minHeight: Dimensions.get('screen').height * 0.2, }}>
                                        {selectedUserOrder && selectedUserOrder.cartItems != null
                                            ? selectedUserOrder.cartItems.map((element, index) => {
                                                return (
                                                    <View>
                                                        <View style={{ flexDirection: 'row', justifyContent: 'space-between', paddingVertical: 5, }}>
                                                            <View style={{ width: "60%" }}>
                                                                <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10 }}>{element.name}</Text>
                                                            </View>
                                                            <View style={{ width: "10%" }}>
                                                                <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10 }}>X{element.quantity}</Text>
                                                            </View>
                                                            <View style={{ width: "5%" }}>
                                                                <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10 }}></Text>
                                                            </View>
                                                            <View style={{ width: "25%" }}>
                                                                <Text style={{ alignSelf: 'flex-end', fontFamily: "NunitoSans-Bold", fontSize: 10 }}>{element.price.toFixed(2)}</Text>
                                                            </View>
                                                        </View>

                                                        {element.remarks && element.remarks.length > 0 ?
                                                            <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                                                                <View style={{ width: "60%" }}>
                                                                    <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10 }}><Text style={{ color: '#a8a8a8' }}>- </Text>{element.remarks}</Text>
                                                                </View>
                                                                <View style={{ width: "10%" }}>
                                                                    <Text style={{ fontFamily: "NunitoSans-Regular", fontSize: 10 }}></Text>
                                                                </View>
                                                                <View style={{ width: "5%" }}>
                                                                    <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10 }}></Text>
                                                                </View>
                                                                <View style={{ width: "25%" }}>
                                                                    <Text style={{ alignSelf: 'flex-end', fontFamily: "NunitoSans-Bold", fontSize: 10 }}></Text>
                                                                </View>
                                                            </View>
                                                            : <></>
                                                        }

                                                    </View>
                                                );
                                            })
                                            : null}
                                    </View>
                                    <View style={styles.totalContainer}>
                                        <View style={{ flex: 1, width: '75%' }}>
                                            <Text style={styles.PromotionTitleReceipt}>Voucher Applied</Text>
                                            <Text style={styles.promotionDescriptionReceipt}> - 10% Discount OFF</Text>
                                            <Text style={styles.descriptionReceipt}>Subtotal</Text>
                                            <Text style={styles.descriptionReceipt}>Discount</Text>
                                            {/* <Text style={styles.descriptionReceipt}> */}
                                            {/* Tax ( */}
                                            {/* {orderTax ? orderTax.rate : 0} */}
                                            {/* {orderOutlet.taxPercent} */}
                                            {/* %) */}
                                            {/* </Text> */}
                                            {
                                                selectedOutlet && selectedOutlet.taxActive
                                                    ?
                                                    <Text
                                                        style={[
                                                            styles.description,
                                                        ]}>
                                                        {`Tax (${(selectedOutlet.taxRate * 100).toFixed(0)}%)`}
                                                    </Text>
                                                    :
                                                    <></>
                                            }
                                            {
                                                selectedOutlet && selectedOutlet.scActive
                                                    ?
                                                    <Text
                                                        style={[
                                                            styles.description,
                                                        ]}>
                                                        {`${selectedOutlet.scName ? selectedOutlet.scName : 'Service Charge'} (${(selectedOutlet.scRate * 100).toFixed(0)}%)`}
                                                    </Text>
                                                    :
                                                    <></>
                                            }
                                            <Text style={styles.descriptionReceipt}>
                                                Delivery Fee
                                            </Text>
                                            <Text style={styles.totalReceipt}>Total</Text>
                                        </View>
                                        <View style={{ alignSelf: 'flex-end', width: '25%' }}>
                                            {/* <Text style={styles.price}>RM {selectedUserOrder.subtotal}</Text>
                        <Text style={styles.price}> {orderId.discount}</Text>
                        <Text style={styles.price}>
                            {' '}
                            {orderTax
                                ? (((parseFloat(orderId.subtotal) - parseFloat(orderId.discount)))
                                    * parseFloat(orderTax.rate)).toFixed(2)
                                : 0}
                        </Text>
                        <Text style={styles.totalPrice}>
                            RM {orderId.total}
                        </Text> */}
                                            <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                                                <Text style={styles.priceReceipt}>RM </Text>
                                                {/*<Text style={styles.priceReceipt}>{selectedUserOrder.totalPrice.toFixed(2)}</Text>*/}
                                                <Text style={styles.priceReceipt}>{(selectedUserOrder && selectedUserOrder.totalPrice) ? selectedUserOrder.totalPrice.toFixed(2) : (0).toFixed(2)}</Text>
                                            </View>
                                            <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                                                <Text style={styles.priceReceipt}>RM </Text>
                                                {/*<Text style={styles.priceReceipt}>{(selectedUserOrder.discount).toFixed(2)}</Text>*/}
                                                <Text style={styles.priceReceipt}>{(selectedUserOrder && selectedUserOrder.discount) ? selectedUserOrder.discount.toFixed(2) : (0).toFixed(2)}</Text>
                                            </View>
                                            <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                                                <Text style={styles.priceReceipt}>RM </Text>
                                                {/*<Text style={styles.priceReceipt}>{selectedUserOrder.tax.toFixed(2)}</Text>*/}
                                                <Text style={styles.priceReceipt}>{(selectedUserOrder && selectedUserOrder.tax) ? selectedUserOrder.tax.toFixed(2) : (0).toFixed(2)}</Text>
                                            </View>
                                            <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                                                <Text style={styles.priceReceipt}>RM </Text>
                                                {/*<Text style={styles.priceReceipt}>{selectedUserOrder.deliveryFee.toFixed(2)}</Text>*/}
                                                <Text style={styles.priceReceipt}>{(selectedUserOrder && selectedUserOrder.deliveryFee) ? selectedUserOrder.deliveryFee.toFixed(2) : (0).toFixed(2)}</Text>
                                            </View>
                                            <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
                                                <Text style={styles.totalPriceReceipt}>RM </Text>
                                                <Text style={styles.totalPriceReceipt}>
                                                    {/* {(Math.ceil((selectedUserOrder.totalPrice + selectedUserOrder.tax + selectedUserOrder.deliveryFee - selectedUserOrder.discount.toFixed(2)) * 20 - 0.05) / 20).toFixed(2)} */}
                                                    {/*(selectedUserOrder.finalPrice).toFixed(2)*/}
                                                    {(selectedUserOrder && selectedUserOrder.finalPrice) ? selectedUserOrder.finalPrice.toFixed(2) : (0).toFixed(2)}
                                                </Text>
                                            </View>
                                        </View>
                                    </View>
                                </View>
                                <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'center', padding: 20 }}>
                                    <TouchableOpacity style={{
                                        width: "45%",
                                        height: 40,
                                        backgroundColor: Colors.whiteColor,
                                        borderRadius: 10,
                                        justifyContent: "center",
                                        alignItems: "center",
                                        borderWidth: 1,
                                        borderColor: Colors.descriptionColor,
                                        flexDirection: 'row',
                                        zIndex: 1000,
                                    }}
                                        onPress={() => {
                                            setReceiptModal(false)
                                        }}
                                    >
                                        <Text style={{ fontFamily: 'NunitoSans-Regular', color: Colors.primaryColor }}>CANCEL</Text>

                                    </TouchableOpacity>
                                    <TouchableOpacity style={{
                                        width: "45%",
                                        height: 40,
                                        backgroundColor: Colors.primaryColor,
                                        borderRadius: 10,
                                        justifyContent: "center",
                                        alignItems: "center",
                                        borderWidth: 1,
                                        borderColor: Colors.descriptionColor,
                                        flexDirection: 'row',
                                        zIndex: 1000,
                                        marginLeft: 10,
                                    }}

                                        onPress={() => {

                                        }}
                                    >
                                        <Text style={{ fontFamily: 'NunitoSans-Regular', color: Colors.whiteColor }}>DOWNLOAD</Text>

                                    </TouchableOpacity>
                                </View>
                            </View>
                        </ScrollView>
                    </View>
                </Modal>

                <Modal
                    style={{
                        // flex: 1
                    }}
                    visible={actionModalVisibility}
                    transparent={true}
                    animationType={'slide'}
                >
                    <View style={styles.modalContainer}>
                        <View style={[styles.modalView, {
                        }]}>
                            <View style={{ justifyContent: 'flex-end', alignItems: 'flex-end', padding: 6 }}>
                                <TouchableOpacity style={{
                                    width: "47%",
                                    height: 40,
                                    backgroundColor: '#EB5757',
                                    borderRadius: 10,
                                    justifyContent: "center",
                                    alignItems: "center",
                                    borderWidth: 1,
                                    borderColor: Colors.descriptionColor,
                                    flexDirection: 'row',
                                    zIndex: 1000,
                                    marginLeft: 10,
                                }}
                                    disabled={isLoading}
                                    onPress={() => {
                                        //cancelAndRefund()
                                    }}
                                >
                                    {
                                        isLoading
                                            ?
                                            <ActivityIndicator style={{
                                            }} color={Colors.whiteColor} size={'small'} />
                                            :
                                            <Text style={{ color: Colors.whiteColor, fontFamily: "NunitoSans-Regular" }}>REFUND</Text>
                                    }

                                </TouchableOpacity>
                            </View>

                            <View style={{
                                flexDirection: 'column',
                                width: '100%',
                                justifyContent: 'center',
                                alignItems: 'center',
                                marginTop: 15,
                                borderBottomWidth: 1,
                                borderBottomColor: Colors.fieldtTxtColor,
                                height: '60%',
                            }}>

                                <TouchableOpacity style={{
                                    width: "65%",
                                    height: 45,
                                    backgroundColor: Colors.primaryColor,
                                    borderRadius: 10,
                                    justifyContent: "center",
                                    alignItems: "center",
                                    borderWidth: 1,
                                    borderColor: Colors.descriptionColor,
                                    flexDirection: 'row',
                                    zIndex: 1000,

                                }}
                                    disabled={isLoading}
                                    onPress={() => {
                                        waitForSender()
                                    }}
                                >
                                    {
                                        isLoading
                                            ?
                                            <ActivityIndicator style={{
                                            }} color={Colors.whiteColor} size={'small'} />
                                            :
                                            <Text style={{ color: Colors.whiteColor, fontFamily: "NunitoSans-Regular" }}>Wait For Sender</Text>
                                    }


                                </TouchableOpacity>

                                <View style={{
                                    flexDirection: 'row',
                                    alignItems: 'center',
                                    width: '100%',
                                    justifyContent: 'space-between',
                                    marginTop: 20,
                                }}>
                                    <View style={{
                                        width: '50%',
                                    }}>
                                        <Picker
                                            placeholder={{}}
                                            style={Styles.rnPickerSelectStyle}
                                            items={SENDER_DROPDOWN_LIST}
                                            onValueChange={value => {
                                                setSelectedSender(value);
                                            }}
                                        />
                                    </View>

                                    <TouchableOpacity style={{
                                        width: "45%",
                                        height: 45,
                                        backgroundColor: Colors.primaryColor,
                                        borderRadius: 10,
                                        justifyContent: "center",
                                        alignItems: "center",
                                        borderWidth: 1,
                                        borderColor: Colors.descriptionColor,
                                        flexDirection: 'row',
                                        zIndex: 1000,
                                        // marginTop: 10,
                                    }}
                                        disabled={isLoading}
                                        onPress={() => {
                                            topup();
                                        }}
                                    >
                                        {
                                            isLoading
                                                ?
                                                <ActivityIndicator style={{
                                                }} color={Colors.whiteColor} size={'small'} />
                                                :
                                                selectedUserOrder ? <Text style={{ color: Colors.whiteColor, fontFamily: "NunitoSans-Regular" }}>{`Top-Up (RM${(Math.max(deliveryQuotation.totalFee - selectedUserOrder.deliveryFee, 0).toFixed(2))})`}</Text> : <></>
                                        }
                                    </TouchableOpacity>
                                </View>
                            </View>

                            <View style={{
                                flexDirection: 'row',
                                alignItems: 'center',
                                justifyContent: 'center',
                                margin: 15,
                            }}>

                                <TouchableOpacity style={{
                                    width: "47%",
                                    height: 40,
                                    backgroundColor: Colors.whiteColor,
                                    borderRadius: 10,
                                    justifyContent: "center",
                                    alignItems: "center",
                                    borderWidth: 1,
                                    borderColor: Colors.descriptionColor,
                                    flexDirection: 'row',
                                    zIndex: 1000,

                                }}
                                    disabled={isLoading} celAnd
                                    onPress={() => {
                                        setActionModalVisibility(false);
                                    }}
                                >
                                    <Text style={{ color: Colors.primaryColor, fontFamily: "NunitoSans-Regular" }}>CANCEL</Text>

                                </TouchableOpacity>

                                <TouchableOpacity style={{
                                    width: "47%",
                                    height: 40,
                                    backgroundColor: '#EB5757',
                                    borderRadius: 10,
                                    justifyContent: "center",
                                    alignItems: "center",
                                    borderWidth: 1,
                                    borderColor: Colors.descriptionColor,
                                    flexDirection: 'row',
                                    zIndex: 1000,
                                    marginLeft: 10,
                                }}
                                    disabled={isLoading}
                                    onPress={() => {
                                        //cancelAndRefund()
                                    }}
                                >
                                    {
                                        isLoading
                                            ?
                                            <ActivityIndicator style={{
                                            }} color={Colors.whiteColor} size={'small'} />
                                            :
                                            <Text style={{ color: Colors.whiteColor, fontFamily: "NunitoSans-Regular" }}>REFUND</Text>
                                    }

                                </TouchableOpacity>

                            </View>
                        </View>
                    </View>

                </Modal>


                <Modal
                    style={{
                    }}
                    visible={questionnaireVisibility}
                    transparent={true}
                    animationType={'slide'}
                >
                    <View style={styles.modalContainer}>
                        <View style={[styles.modalView1]}>
                            <FlatList
                                style={{}}
                                data={renderQuestionnaire}
                                //extraData={renderQuestionnaire}
                                renderItem={renderQuestionnaire}
                                keyExtractor={(item, index) => String(index)}
                            />

                        </View>
                    </View>
                </Modal>

                <Modal
                    style={{
                        // flex: 1
                    }}
                    visible={completeModalVisibility}
                    transparent={true}
                    animationType={'slide'}
                >
                    <View style={styles.modalContainer}>
                        <View style={[styles.modalView1, {}]}>
                            <View style={{ flexDirection: 'row', justifyContent: 'flex-end', alignItems: 'flex-end', padding: 10 }}>
                                <TouchableOpacity
                                    style={styles.closeButton}
                                    onPress={() => {
                                        setCompleteModalVisibility(false);
                                    }}>
                                    <AntDesign name="closecircle" size={25} color={Colors.fieldtTxtColor} />
                                </TouchableOpacity>
                            </View>
                            <View style={{ flexDirection: 'column', alignItems: 'flex-start', justifyContent: 'flex-start' }}>
                                <Text style={{
                                    fontSize: 20,
                                    fontFamily: "NunitoSans-Bold"
                                }}>
                                    Feedback
                                </Text>
                                <Text style={{
                                    fontSize: 13,
                                    fontFamily: "NunitoSans-Regular",
                                    marginTop: 10
                                }}>
                                    How was your experience in {selectedUserOrder ? selectedUserOrder.outletName : ''}?
                                </Text>
                            </View>

                            <View style={{ flexDirection: 'row', marginTop: 10 }}>
                                {/* <TouchableOpacity
                    onPress={() => {
                       
                    }}
                    >
                    <AntDesign name="star" size={35} color={Colors.primaryColor}/>
                    </TouchableOpacity>

                    <TouchableOpacity
                    onPress={() => {
                        
                    }}
                    >
                    <AntDesign name="star" size={35} color={Colors.primaryColor} />
                    </TouchableOpacity>

                    <TouchableOpacity
                    onPress={() => {
                        
                    }}
                    >
                    <AntDesign name="star" size={35} color={Colors.primaryColor} />
                    </TouchableOpacity>

                    <TouchableOpacity
                    onPress={() => {
                        
                    }}
                    >
                    <AntDesign name="star" size={35} color={Colors.primaryColor} />
                    </TouchableOpacity>

                    <TouchableOpacity
                    onPress={() => {
                       
                    }}
                    >
                    <AntDesign name="star" size={35} color={Colors.primaryColor} />
                    </TouchableOpacity> */}
                                <StarRatingView />
                            </View>

                            <View style={{ alignItems: 'flex-start', justifyContent: 'flex-start', marginTop: 20 }}>
                                <Text style={{
                                    fontSize: 13,
                                    fontFamily: "NunitoSans-Regular"
                                }}>
                                    Give some feedback in words.
                                </Text>
                            </View>

                            <View style={{ marginTop: 5 }}>
                                <TextInput
                                    //editable={}
                                    multiline={true}
                                    clearButtonMode="while-editing"
                                    style={styles.textInput}
                                    placeholder="Leave Your Comment..."
                                    onChangeText={(text) => { setReviewComment(text) }}
                                    defaultValue={reviewComment}
                                />
                            </View>
                            {/* <View style={{ marginTop: 10, alignItems: 'center' }}>
                                <Text style={{
                                    fontSize: 12.5,
                                    fontFamily: "NunitoSans-Regular",
                                }}>
                                    ( {reviewTotal} ) Ratings
                                </Text>
                            </View> */}


                            <View style={{ flexDirection: 'row', justifyContent: 'center', alignItems: 'center', marginTop: 25 }}>
                                <TouchableOpacity style={{
                                    width: "47%",
                                    height: 40,
                                    backgroundColor: Colors.primaryColor,
                                    borderRadius: 10,
                                    justifyContent: "center",
                                    alignItems: "center",
                                    borderWidth: 1,
                                    borderColor: Colors.descriptionColor,
                                    flexDirection: 'row',
                                    zIndex: 1000,
                                }}
                                    disabled={isLoading}
                                    onPress={() => {
                                        submitReview()
                                        //setQuestionnaireVisibility(true)
                                    }}
                                >
                                    {
                                        isLoading
                                            ?
                                            <ActivityIndicator style={{
                                            }} color={Colors.whiteColor} size={'small'} />
                                            :
                                            <Text style={{ color: Colors.whiteColor, fontFamily: "NunitoSans-Regular" }}>SUBMIT</Text>
                                    }

                                </TouchableOpacity>
                            </View>

                        </View>
                    </View>
                </Modal>

                {expandDetails &&
                    <>
                        {
                            (
                                !orderDetails ||
                                (
                                    orderDetails &&
                                    orderDetails.orderStatus !== USER_ORDER_STATUS.ORDER_CANCELLED_BY_MERCHANT &&
                                    orderDetails.orderStatus !== USER_ORDER_STATUS.ORDER_CANCELLED_BY_USER
                                )
                            )
                                ?
                                <View style={{
                                    alignSelf: "center",
                                    // marginTop: 80,
                                    // height: Dimensions.get('screen').height * 0.75
                                }}>
                                    {/* <Image style={styles.headerLogo} resizeMode="contain" source={selectedUserOrder.orderType === ORDER_TYPE.DELIVERY ? ORDER_STATUS_IMAGES_DELIVERY[parsedOrderStatus] : ORDER_STATUS_IMAGES[parsedOrderStatus]} /> */}

                                    <View style={[detailsTopSpacing, {
                                        alignItems: "center",
                                        justifyContent: 'center',
                                        //top: '48%',
                                    }]}>
                                        <Text style={[detailsTextScale, {
                                            //fontSize: 25,
                                            // fontWeight: "700",
                                            color: Colors.blackColor,
                                            fontFamily: 'NunitoSans-Bold',
                                            width: '100%',
                                            textAlign: 'center',
                                            // marginTop: 5,
                                            // zIndex: 10,
                                            // backgroundColor: 'red',
                                            width: '70%',
                                        }]}>{selectedUserOrder.orderType === ORDER_TYPE.DELIVERY ? ORDER_STATUS_TEXT_DELIVERY[parsedOrderStatus] : ORDER_STATUS_TEXT[parsedOrderStatus]}</Text>
                                        <Text style={[detailsTextScale2, {
                                            //fontSize: 16,
                                            marginTop: 30,
                                            color: "#b5b5b5",
                                            marginBottom: 50,
                                            fontFamily: 'NunitoSans-Regular'
                                        }]}>
                                            {/* {parsedOrderStatus !== ORDER_STATUS_PARSED.DELIVERED ? `Estimate time ${(((selectedUserOrder.totalPrepareTime ? selectedUserOrder.totalPrepareTime : 60) / 60) + 30).toFixed(0)}mins` : 'Delivered'} */}
                                        </Text>
                                    </View>

                                    {/* {selectedUserOrder.orderType === ORDER_TYPE.DELIVERY &&
                                        // <View style={{
                                        //     // backgroundColor: 'blue',
                                        // }}>
                                        <>
                                            <Image style={{
                                                // width: Styles.width * 0.9,
                                                width: '100%',
                                                // backgroundColor: 'red',

                                                // height: Dimensions.get('screen').width * 2.5,

                                                position: 'absolute',
                                                //bottom: '-2%',
                                                alignSelf: 'center',
                                            }}
                                                resizeMode="contain"
                                                source={ORDER_STATUS_IMAGES_BOTTOM[parsedOrderStatus]}
                                            />

                                            <View style={{
                                                flexDirection: "row",
                                                alignItems: "center",
                                                position: "absolute",
                                                height: Dimensions.get('screen').width * 2.7,
                                                //bottom: '-105%',
                                                //backgroundColor: 'red',
                                                //position: 'absolute',
                                                alignSelf: 'center',
                                                width: '107%',
                                            }}>
                                                <Text style={{
                                                    fontSize: 12,
                                                    marginTop: 20,
                                                    color: Colors.primaryColor,
                                                    fontFamily: 'NunitoSans-Bold',
                                                    width: '20%',
                                                    textAlign: 'center',
                                                }}>Placed</Text>

                                                <Text style={{
                                                    fontSize: 12,
                                                    marginTop: 20,
                                                    color: Colors.primaryColor,
                                                    fontFamily: 'NunitoSans-Bold',
                                                    width: '20%',
                                                    textAlign: 'center',
                                                }}>Preparing</Text>

                                                <Text style={{
                                                    fontSize: 12,
                                                    marginTop: 20,
                                                    color: Colors.primaryColor,
                                                    fontFamily: 'NunitoSans-Bold',
                                                    width: '20%',
                                                    textAlign: 'center',
                                                }}>Picking-up</Text>

                                                <Text style={{
                                                    fontSize: 12,
                                                    marginTop: 20,
                                                    color: Colors.primaryColor,
                                                    fontFamily: 'NunitoSans-Bold',
                                                    width: '20%',
                                                    textAlign: 'center',
                                                }}>Delivering</Text>

                                                <Text style={{
                                                    fontSize: 12,
                                                    marginTop: 20,
                                                    color: Colors.primaryColor,
                                                    fontFamily: 'NunitoSans-Bold',
                                                    width: '20%',
                                                    textAlign: 'center',
                                                }}>Arrived</Text>
                                            </View>
                                        </>
                                        // </View>
                                    }

                                    {(selectedUserOrder.orderType === ORDER_TYPE.PICKUP || selectedUserOrder.orderType === ORDER_TYPE.DINEIN) &&
                                        <>

                                            <Image style={[styles.headerLogo1, {
                                                width: Dimensions.get('screen').width,
                                                // marginLeft: 20
                                                // backgroundColor: 'red'
                                            }]} resizeMode="contain" source={ORDER_STATUS_IMAGES_BOTTOM_TAKEAWAY[parsedOrderStatus]} />

                                            <View style={{
                                                flexDirection: "row",
                                                alignItems: "center",
                                                bottom: '-115%',
                                                // backgroundColor: 'red',

                                                justifyContent: 'center',
                                                // width: '100%',
                                                width: Dimensions.get('screen').width,
                                            }}>
                                                <Text style={{
                                                    fontSize: 14,
                                                    marginTop: 10,
                                                    color: Colors.primaryColor,
                                                    fontFamily: 'NunitoSans-Bold',
                                                    width: '33.3%',
                                                    textAlign: 'center',
                                                }}>Order Placed</Text>

                                                <Text style={{
                                                    fontSize: 14,
                                                    marginTop: 10,
                                                    color: Colors.primaryColor,
                                                    fontFamily: 'NunitoSans-Bold',
                                                    width: '33.3%',
                                                    textAlign: 'center',
                                                }}>Preparing</Text>

                                                <Text style={{
                                                    fontSize: 14,
                                                    marginTop: 10,
                                                    color: Colors.primaryColor,
                                                    fontFamily: 'NunitoSans-Bold',
                                                    width: '33.3%',
                                                    textAlign: 'center',
                                                }}>Done</Text>
                                            </View>
                                        </>
                                    } */}
                                </View>
                                :
                                <></>
                        }

                        {
                            orderDetails
                                ?
                                <View style={[{
                                    width: '100%',
                                }, (
                                    orderDetails &&
                                    orderDetails.orderStatus !== USER_ORDER_STATUS.ORDER_CANCELLED_BY_MERCHANT &&
                                    orderDetails.orderStatus !== USER_ORDER_STATUS.ORDER_CANCELLED_BY_USER
                                ) ? {

                                } : {
                                    marginTop: '10%',
                                }]}>
                                    <Text style={{
                                        fontSize: 16,
                                        // marginTop: 10,
                                        color: Colors.secondaryColor,
                                        fontFamily: 'NunitoSans-SemiBold',
                                        textAlign: 'center',
                                    }}>{
                                            `Remark: ${orderDetails.message}`
                                        }</Text>
                                </View>
                                :
                                <></>
                        }

                        <View style={{ height: 120 }}></View>
                    </>
                }
            </ScrollView>
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#ffffff',
        padding: 10,
        paddingTop: 5,
    },
    titleDetail: {
        marginTop: 5,
        justifyContent: 'center',
        alignContent: 'center',
        alignItems: 'center',
    },
    orderTitle: {
        marginTop: 10,
        borderBottomWidth: StyleSheet.hairlineWidth,
        borderBottomColor: Colors.fieldtTxtColor,
    },
    totalContainer: {
        flexDirection: 'row',
        borderTopWidth: StyleSheet.hairlineWidth,
        borderTopColor: Colors.fieldtTxtColor,
        marginTop: 10,
    },
    description: {
        paddingVertical: 5,
        fontSize: 10,
        fontFamily: "NunitoSans-Bold",
    },
    descriptionReceipt: {
        paddingVertical: 5,
        fontSize: 12,
        fontFamily: "NunitoSans-Bold",
    },
    PromotionTitle: {
        paddingVertical: 5,
        fontSize: 13,
        fontFamily: "NunitoSans-Bold",
        color: Colors.fieldtTxtColor
    },
    PromotionTitleReceipt: {
        paddingVertical: 5,
        fontSize: 10,
        fontFamily: "NunitoSans-Bold",
        color: Colors.fieldtTxtColor
    },
    price: {
        paddingVertical: 5,
        fontSize: 10,
        alignSelf: 'flex-end',
        fontFamily: "NunitoSans-Bold",
    },
    priceReceipt: {
        paddingVertical: 5,
        fontSize: 10,
        alignSelf: 'flex-end',
        fontFamily: "NunitoSans-Bold",
    },
    promotionDescription: {
        paddingVertical: 5,
        fontSize: 13,
        fontFamily: "NunitoSans-Bold",
        color: Colors.fieldtTxtColor,
    },
    promotionDescriptionReceipt: {
        paddingVertical: 2,
        fontSize: 10,
        fontFamily: "NunitoSans-Bold",
        color: Colors.fieldtTxtColor,
    },
    totalPrice: {
        paddingVertical: 5,
        fontSize: 15,
        fontWeight: '700',
        marginTop: 5,
        fontFamily: "NunitoSans-Bold",
    },
    totalPriceReceipt: {
        paddingVertical: 5,
        fontSize: 15,
        fontWeight: '700',
        marginTop: 5,
        fontFamily: "NunitoSans-Bold",
    },
    total: {
        paddingVertical: 5,
        fontSize: 15,
        fontWeight: '700',
        marginTop: 5,
        fontFamily: "NunitoSans-Bold",
    },
    totalReceipt: {
        paddingVertical: 5,
        fontSize: 15,
        fontWeight: '700',
        marginTop: 5,
        fontFamily: "NunitoSans-Bold",
    },

    outletCover: {
        width: "100%",
        alignSelf: 'center',
        height: undefined,
        aspectRatio: 2,
        borderRadius: 5
    },
    infoTab: {
        backgroundColor: Colors.fieldtBgColor,
        padding: 16,
        justifyContent: 'center',
        alignContent: 'center',
        alignItems: 'center'
    },
    workingHourTab: {
        padding: 16,
        flexDirection: 'row'
    },
    outletAddress: {
        textAlign: 'center',
        color: Colors.mainTxtColor
    },
    outletName: {
        fontWeight: 'bold',
        fontSize: 20,
        marginBottom: 10
    },
    logo: {
        width: 100,
        height: 100
    },
    headerLogo: {
        width: Styles.width * 0.8,
        //height: '100%'
        // backgroundColor: 'red',
        position: 'absolute',
        top: '-95%',
        alignSelf: 'center',
    },
    headerLogo1: {
        // width: Styles.width * 0.9,
        width: '100%',
        // backgroundColor: 'red',
        position: 'absolute',
        bottom: '-2%',
        alignSelf: 'center',
    },
    actionTab: {
        flexDirection: 'row',
        marginTop: 20
    },
    actionView: {
        width: Styles.width / 4,
        height: Styles.width / 4,
        justifyContent: 'center',
        alignItems: 'center',
        alignContent: 'center'
    },
    actionBtn: {
        borderRadius: 50,
        width: 70,
        height: 70,
        borderColor: Colors.secondaryColor,
        borderWidth: StyleSheet.hairlineWidth,
        justifyContent: 'center',
        alignItems: 'center'
    },
    actionText: {
        fontSize: 12,
        marginTop: 10
    },
    modalContainer: {
        flex: 1,
        backgroundColor: Colors.modalBgColor,
        alignItems: 'center',
        justifyContent: 'center'
    },
    modalView: {
        height: Styles.width * 0.7,
        width: Styles.width * 0.8,
        backgroundColor: Colors.whiteColor,
        borderRadius: 12,
        padding: Styles.width * 0.03,
        //alignItems: 'center',
        //justifyContent: 'center'
    },
    modalContainerReceipt: {
        flex: 1,
        backgroundColor: Colors.modalBgColor,
        alignItems: 'center',
        justifyContent: 'center'
    },
    modalViewReceipt: {
        //height: Styles.height * 0.9,
        width: Styles.width * 0.85,
        backgroundColor: Colors.whiteColor,
        borderRadius: 12,
        //padding: Styles.width * 0.03,
        alignItems: 'center',
        justifyContent: 'center'
    },
    modalView1: {
        //height: Styles.width * 1,
        width: Styles.width * 0.85,
        backgroundColor: Colors.whiteColor,
        borderRadius: 12,
        padding: Styles.width * 0.03,
        //alignItems: 'center',
        //justifyContent: 'center'
    },
    closeButton: {
        position: 'absolute',
        right: Styles.width * 0.02,
        top: Styles.width * 0.02,

        elevation: 1000,
        zIndex: 1000,
    },
    textInput: {
        height: 150,
        paddingHorizontal: 5,
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 5,
        marginTop: 10,
        textAlignVertical: "top",
        fontSize: 13
    },
    starStyle: {
        width: 35,
        height: 35,
    }
});

export default OrderReceiptScreen;
