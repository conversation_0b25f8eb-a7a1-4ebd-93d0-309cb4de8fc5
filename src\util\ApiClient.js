import { Alert } from 'react-native';
import * as Token from '../util/Token';
import User from '../util/User';
import { apiUrl } from '../constant/env';
import AsyncStorage from '@react-native-async-storage/async-storage'
import axios from 'axios';
import http from 'http';
import https from 'https';

const client = axios.create({
    baseURL: apiUrl,
    timeout: 100000,
    httpAgent: new http.Agent({ keepAlive: true }),
    httpsAgent: new https.Agent({ keepAlive: true }),
});

client.interceptors.request.use(async options => {
    // let token = Token.getToken();
    // options.headers['Test'] = `Test`;

    let token = await AsyncStorage.getItem('accessToken');
    if (!token) {
        token = global.accessToken;
    }

    if (token) {
        // options.headers['Authorization'] = token ? `Bearer ${token}` : `Firebase ${Token.getFirebaseToken()}`;
        options.headers['Authorization'] = `Bearer ${token}`;
    }

    return options;
});

const logoutUser = async function () {
    await AsyncStorage.clear();
    // Token.clear();
    // User.setlogin(false);
    // User.getRefreshMainScreen();
};

client.interceptors.response.use(
    response => {
        return response;
    },
    async function (error) {
        console.error(error.response);

        const originalRequest = error.config;
        if (error.response && error.response.status === 401) {
            if (!originalRequest._retry) {
                originalRequest._retry = true;
                const refreshToken = Token.getRefreshToken();
                if (refreshToken) {
                    try {
                        const refreshRes = await client.get(
                            `/token?ctoken=REFRESH:${refreshToken}`,
                        );
                        if (refreshRes.data) {
                            Token.setToken(refreshRes.data.token);
                            Token.setRefreshToken(refreshRes.data.refreshToken);
                            return client(originalRequest);
                        }
                    } catch (err) {
                        logoutUser();
                    }
                } else {
                    logoutUser();
                }
            } else {
                logoutUser();
            }
        }

        return Promise.reject(error);
    },
);


////////////////////////////////////
const GET = async function (url, query = {}) {
    try {
        const queryString = new URLSearchParams(query).toString();
        const fullUrl = queryString ? `${url}?${queryString}` : url;
        const { data } = await client.get(fullUrl);
        if (data.error) {
            // Alert.alert("Error", data.message, [
            //     { text: "OK", onPress: () => { } }
            // ],
            //     { cancelable: false })
            // console.log("ERROR", fullUrl, data)
        }
        else {
            return data;
        }
    }
    catch (err) {
        // Alert.alert("Error", url + err.message, [
        //     { text: "OK", onPress: () => { } }
        // ],
        //     { cancelable: false })
        // console.log("ERROR", url, err)
    }
}

const POST = async function (url, params, config = {}) {
    try {
        // const result = await client.post(url, params || {}, {
        //     headers: {
        //         // 'Content-?Type': 'application/x-www-form-urlencoded; charset=UTF-8',
        //         Accept: 'application/json',
        //         'Content-Type': 'application/json',
        //     }
        // });
        // console.log('post result');
        // console.log(result);
        // let data = {};

        const { data } = await client.post(url, params, config);
        if (data.error) {
            // Alert.alert("Error", data.message, [
            //     { text: "OK", onPress: () => { } }
            // ],
            //     { cancelable: false })
            // console.log("ERROR", url, data)

            // window.confirm(data.error);
        }
        else {
            return data;
        }
    }
    catch (err) {
        // Alert.alert("Error", url + err.message, [
        //     { text: "OK", onPress: () => { } }
        // ],
        //     { cancelable: false })
        // console.log("ERROR", url, err)

        // window.confirm(err);
    }
}

const PATCH = async function (url, params) {
    try {
        const { data } = await client.patch(url, params);
        if (data.error) {
            // Alert.alert("Error", data.message, [
            //     { text: "OK", onPress: () => { } }
            // ],
            //     { cancelable: false })
            // console.log("ERROR", url, data)
        }
        else {
            return data;
        }
    }
    catch (err) {
        // Alert.alert("Error", url + err, [
        //     { text: "OK", onPress: () => { } }
        // ],
        //     { cancelable: false })
        // console.log("ERROR", url, err)
    }
}

const PUT = async function (url, params) {
    try {
        const { data } = await client.put(url, params);
        if (data.error) {
            // Alert.alert("Error", data.message, [
            //     { text: "OK", onPress: () => { } }
            // ],
            //     { cancelable: false })
            // console.log("ERROR", url, data)
        }
        else {
            return data;
        }
    }
    catch (err) {
        // Alert.alert("Error", url + err, [
        //     { text: "OK", onPress: () => { } }
        // ],
        //     { cancelable: false })
        // console.log("ERROR", url, err)
    }
}

const OPTIONS = async function (url, params, config = {}) {
    try {
        // const result = await client.post(url, params || {}, {
        //     headers: {
        //         // 'Content-?Type': 'application/x-www-form-urlencoded; charset=UTF-8',
        //         Accept: 'application/json',
        //         'Content-Type': 'application/json',
        //     }
        // });
        // console.log('post result');
        // console.log(result);
        // let data = {};

        const { data } = await client.options(url, params, config);
        if (data.error) {
            // Alert.alert("Error", data.message, [
            //     { text: "OK", onPress: () => { } }
            // ],
            //     { cancelable: false })
            // console.log("ERROR", url, data)
        }
        else {
            return data;
        }
    }
    catch (err) {
        // Alert.alert("Error", url + err.message, [
        //     { text: "OK", onPress: () => { } }
        // ],
        //     { cancelable: false })
        // console.log("ERROR", url, err)
    }
}

const ApiClient = {
    GET,
    POST,
    PATCH,
    PUT,
    OPTIONS,
};

export default ApiClient
