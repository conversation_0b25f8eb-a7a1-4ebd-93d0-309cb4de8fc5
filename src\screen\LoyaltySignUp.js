import React, { Component, useReducer, useState, useEffect } from 'react';
import {
  StyleSheet,
  ScrollView,
  Image,
  View,
  Text,
  TouchableOpacity,
  TextInput,
  FlatList,
  RefreshControl,
  Alert,
  Modal,
  Dimensions,
  Platform,
  useWindowDimensions
} from 'react-native';
// import firebase from 'firebase';
import { getFirestore, collection, query, where, getDocs, limit } from "firebase/firestore";
import { signInAnonymously } from "firebase/auth";
import Colors from '../constant/Colors';
// import Autocomplete from 'react-google-autocomplete'
import AntDesign from 'react-native-vector-icons/AntDesign';
import DatePicker from "react-datepicker";
import { ReactComponent as GCalendar } from '../svg/GCalendar.svg'
import "react-datepicker/dist/react-datepicker.css";
import "../constant/datePicker.css";
import * as Cart from '../util/Cart';
import * as User from '../util/User';
import Styles from '../constant/Styles';
// import ActionSheet from 'react-native-actionsheet';
import ApiClient from '../util/ApiClient';
import API from '../constant/API';
import Ionicons from 'react-native-vector-icons/Ionicons';
import Icon from 'react-native-vector-icons/Ionicons';
import Icons from 'react-native-vector-icons/Feather';
// import NumericInput from 'react-native-numeric-input';
// import { color } from 'react-native-reanimated';
// import QRCode from 'react-native-qrcode-svg';
import Close from 'react-native-vector-icons/AntDesign';
import { CommonStore } from '../store/commonStore';
import { UserStore } from '../store/userStore';
import moment from 'moment';
import AsyncImage from '../components/asyncImage';
// import LinearGradient from 'react-native-linear-gradient';
import LinearGradient from 'react-native-web-linear-gradient';
import { googleCloudApiKey, prefix } from "../constant/env";
import { useFocusEffect, useLinkTo } from "@react-navigation/native";
import { DataStore } from '../store/dataStore';
import Hashids from 'hashids';
import { ORDER_REGISTER_QR_SALT, ORDER_TYPE } from '../constant/common';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Collections } from '../constant/firebase';
import IconAnt from 'react-native-vector-icons/AntDesign';
import { isMobile, setRouteFrom, mondayFirst } from "../util/commonFuncs";
import imgLogo from '../asset/image/logo.png';

import { ReactComponent as Arrow } from '../svg/arrow.svg';
import { TempStore } from '../store/tempStore';

const hashids = new Hashids(ORDER_REGISTER_QR_SALT);

const LoyaltySignUp = (props) => {
  const { navigation, route } = props;
  const { height: windowHeight, width: windowWidth } = useWindowDimensions();

  const linkTo = useLinkTo();

  const registerUserOrder = CommonStore.useState(s => s.registerUserOrder);

  const isLoading = CommonStore.useState(s => s.isLoading);

  navigation.setOptions({
    // headerLeft: () => (
    //   <TouchableOpacity
    //     style={{}}
    //     onPress={() => {
    //       props.navigation.goBack();
    //       // link&&linkTo(``);
    //     }}>
    //     <View
    //       style={{
    //         marginLeft: 10,
    //         display: 'flex',
    //         flexDirection: 'row',
    //         alignItems: 'center',
    //         justifyContent: 'flex-start',
    //       }}>
    //       <Ionicons
    //         name="chevron-back"
    //         size={26}
    //         color={Colors.fieldtTxtColor}
    //         style={{}}
    //       />

    //       <Text
    //         style={{
    //           color: Colors.fieldtTxtColor,
    //           fontSize: 16,
    //           textAlign: 'center',
    //           fontFamily: 'NunitoSans-Regular',
    //           lineHeight: 22,
    //           marginTop: -1,
    //         }}>
    //         Back
    //       </Text>
    //     </View>
    //   </TouchableOpacity>
    // ),

    // headerRight: () => (
    //   <TouchableOpacity
    //     onPress={() => {
    //       props.navigation.navigate('Profile');
    //     }}
    //     style={{}}>
    //     <View style={{ marginRight: 15 }}>
    //       <Ionicons name="menu" size={30} color={Colors.primaryColor} />
    //     </View>
    //   </TouchableOpacity>
    // ),

    headerTitle: () => (
      <View
        style={{
          justifyContent: 'center',
          alignItems: 'center',
          bottom: -1,
        }}>
        <Text
          style={{
            fontSize: 20,
            lineHeight: 25,
            textAlign: 'center',
            fontFamily: 'NunitoSans-Bold',
            color: Colors.mainTxtColor,
          }}>
          {/* Loyalty Member Sign Up */}
          Member Registration
        </Text>
      </View>
    ),
  });

  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [name, setName] = useState('');
  const [phone, setPhone] = useState('');
  const [birthday, setBirthday] = useState(moment(Date.now()));
  const [address, setAddress] = useState('');
  const [lat, setLat] = useState(0);
  const [lng, setLng] = useState(0);
  const [switchMerchant, setSwitchMerchant] = useState(false);

  const [verifySMSModal, setVerifySMSModal] = useState(false);
  const [verificationCode, setVerificationCode] = useState('');
  const [isVerified, setIsVerified] = useState(false);

  const [showDOBPicker, setShowDOBPicker] = useState(false); //For DOB
  const [rev_DOBdate, setRev_DOBdate] = useState(moment());

  const [outletId, setOutletId] = useState('');
  const [merchantId, setMerchantId] = useState('');
  const [merchantName, setMerchantName] = useState('');

  const [sentCode, setSentCode] = useState(false);
  const [existingCustomer, setExistingCustomer] = useState(false); //For testing UI purpose
  useEffect(() => {
    if (linkTo) {
      DataStore.update(s => {
        s.linkToFunc = linkTo;
      });

      global.linkToFunc = linkTo;
    }

    console.log('route');
    console.log(route);
    console.log('window.location.href');
    console.log(window.location.href);

    CommonStore.update(s => {
      s.orderType = ORDER_TYPE.PICKUP;
    });

    if (route.params === undefined ||
      // route.params.outletId === undefined ||
      route.params.orderId === undefined ||
      // route.params.tableCode === undefined ||
      // route.params.tablePax === undefined ||
      // route.params.waiterId === undefined ||
      // route.params.outletId.length !== 36 ||
      // route.params.tableId.length !== 36 ||
      route.params.qrDateTimeEncrypted === undefined
    ) {
      // still valid, can proceed to register user

      // linkTo && linkTo(`${prefix}/scan`);     

      console.log('general');
    }
    else {
      // check qrDateTimeEncrypted, is it valid

      var qrDateTimeEncrypted = route.params.qrDateTimeEncrypted;
      var qrDateTime = null;
      if (qrDateTimeEncrypted) {
        // var qrDateTimeEncryptedReal = qrDateTimeEncrypted.replaceAll('mykoodoo', '/');
        // var qrDateTimeBytes = CryptoJS.AES.decrypt(qrDateTimeEncryptedReal, TABLE_QR_SALT);
        // qrDateTime = parseInt(qrDateTimeBytes.toString(CryptoJS.enc.Utf8));

        qrDateTime = parseInt(hashids.decodeHex(qrDateTimeEncrypted))
      }

      if (qrDateTime && moment().diff(qrDateTime, 'hour') <= 24) {
        // within 60 minutes still can use

        // means all got, can login this user to check all info valid or not

        try {
          // firebase.auth().signInAnonymously()
          signInAnonymously(global.auth)
            .then((result) => {
              // TempStore.update(s => {
              //   s.firebaseAuth = true;
              // });

              CommonStore.update(s => {
                s.orderType = ORDER_TYPE.PICKUP;
              });

              const firebaseUid = result.user.uid;

              ApiClient.GET(API.getTokenKWeb + firebaseUid).then(async (result) => {
                console.log('getTokenKWeb');
                console.log(result);

                if (result && result.token) {
                  await AsyncStorage.setItem('accessToken', result.token);
                  await AsyncStorage.setItem('refreshToken', result.refreshToken);

                  global.accessToken = result.token;

                  UserStore.update(s => {
                    s.firebaseUid = result.userId;
                    s.userId = result.userId;
                    s.role = result.role;
                    s.refreshToken = result.refreshToken;
                    s.token = result.token;
                    s.name = '';
                    s.email = '';
                    s.number = '';
                  });

                  // CommonStore.update(s => {
                  //   s.selectedOutletTableQRUrl = window.location.href;
                  // });

                  var orderId = hashids.decodeHex(route.params.orderId).toString().insert(8, '-').insert(13, '-').insert(18, '-').insert(23, '-');

                  // const userOrderSnapshot = await firebase.firestore().collection(Collections.UserOrder)
                  //   .where('uniqueId', '==', orderId)
                  //   .limit(1)
                  //   .get();

                  const userOrderSnapshot = await getDocs(
                    query(
                      collection(global.db, Collections.UserOrder),
                      where('uniqueId', '==', orderId),
                      limit(1),
                    )
                  );

                  var userOrder = null;

                  if (!userOrderSnapshot.empty) {
                    userOrder = userOrderSnapshot.docs[0].data();
                  }

                  if (userOrder) {
                    if (userOrder.isCashbackClaimed) {
                      linkTo && linkTo(`${prefix}/scan`);
                    }

                    // const outletSnapshot = await firebase.firestore().collection(Collections.Outlet)
                    //   .where('uniqueId', '==', userOrder.outletId)
                    //   .limit(1)
                    //   .get();

                    const outletSnapshot = await getDocs(
                      query(
                        collection(global.db, Collections.Outlet),
                        where('uniqueId', '==', userOrder.outletId),
                        limit(1),
                      )
                    );

                    var outlet = {};
                    if (!outletSnapshot.empty) {
                      outlet = outletSnapshot.docs[0].data();
                    }

                    if (outlet) {
                      // valid, can proceed to register user

                      setOutletId(outlet.uniqueId);
                      setMerchantId(outlet.merchantId);
                      setMerchantName(userOrder.merchantName);

                      CommonStore.update(s => {
                        s.registerUserOrder = userOrder;
                      });

                      // await AsyncStorage.setItem('latestOutletId', outlet.uniqueId);

                      // await updateUserCart(
                      //   {
                      //     ...route.params,
                      //     outletId: outlet.uniqueId,
                      //     tableId: tableId,
                      //     tablePax: outletTable.seated,
                      //     tableCode: outletTable.code,
                      //   }
                      //   , outlet, firebaseUid);

                      // linkTo && linkTo('/web/outlet');
                    }
                    else {
                      linkTo && linkTo(`${prefix}/scan`);
                    }
                  }
                  else {
                    linkTo && linkTo(`${prefix}/scan`);
                  }
                }
                else {
                  CommonStore.update(s => {
                    s.alertObj = {
                      title: 'Error',
                      message: 'Unauthorized access',
                    };

                    // s.isAuthenticating = false;
                  });

                  linkTo && linkTo(`${prefix}/scan`);
                }
              });
            });
        }
        catch (ex) {
          console.error(ex);
        }
      }
      else {
        linkTo && linkTo(`${prefix}/scan`);
      }
    }
  }, [linkTo, route]);

  useEffect(() => {
    if (linkTo) {
      DataStore.update(s => {
        s.linkToFunc = linkTo;
      });

      global.linkToFunc = linkTo;
    }
  }, [linkTo]);

  useEffect(() => {
    CommonStore.update(s => {
      s.routeName = route.name;
    });
  }, [route]);

  const renderSearch = (item) => {
    return (
      <View style={{ flexDirection: "column" }}>
        <Text style={{ fontWeight: "700", fontSize: 14, marginBottom: 5 }}>
          {item.structured_formatting.main_text}
        </Text>

        <Text style={{ fontSize: 12, color: "grey" }}>{item.description}</Text>
      </View>
    );
  };

  const registerUser = async () => {
    if (!isVerified) {
      CommonStore.update((s) => {
        s.alertObj = {
          title: "Error",
          message: "Please verify your phone number first.",
        };
      });
      return;
    }

    var userPhone = phone.replaceAll('-', '');

    // if (userPhone.startsWith('6')) {
    //   userPhone = userPhone.slice(1);
    // }

    userPhone = userPhone.replace(/[^0-9]/g, '');

    if (!userPhone.startsWith('6')) {
      userPhone = '6' + userPhone;
    }

    const body = {
      userPhone: userPhone,
      outletId: outletId,
      merchantId: merchantId,
      merchantName: merchantName,
      outletName: registerUserOrder.outletName || '',
      merchantLogo: registerUserOrder.merchantLogo || '',
      outletCover: registerUserOrder.outletCover || '',
      userName: name,

      dob: moment(birthday).valueOf(),

      address: address,
      lat: lat,
      lng: lng,

      userOrderId: (registerUserOrder.uniqueId && !registerUserOrder.isCashbackClaimed) ? registerUserOrder.uniqueId : null,
    };

    CommonStore.update(s => {
      s.isLoading = true;
    });

    // const userSnapshot = await firebase.firestore()
    //   .collection(Collections.User)
    //   // .where('uniqueName', '==', uniqueName)
    //   .where('number', '==', userPhone)
    //   .limit(1)
    //   .get();

    const userSnapshot = await getDocs(
      query(
        collection(global.db, Collections.User),
        where('number', '==', userPhone),
        limit(1),
      )
    );

    var findUser = null;
    if (!userSnapshot.empty) {
      findUser = userSnapshot.docs[0].data();
    }

    if (!findUser) {
      ApiClient.POST(API.signUpUserFromUserWeb, body).then((result) => {
        if (result && result.status === "success") {
          CommonStore.update(s => {
            s.registerUserOrder = result.userOrder || {};
          });

          CommonStore.update((s) => {
            s.alertObj = {
              title: "Success",
              message: "Account creation success! Please check your phone for the password.",
            };

            // s.isAuthenticating = false;
          });
        }
        else {
          CommonStore.update((s) => {
            s.alertObj = {
              title: "Error",
              message: "Failed to create the account.",
            };
          });
        }

        CommonStore.update(s => {
          s.isLoading = false;
        });
      });
    }
    else {
      CommonStore.update((s) => {
        s.isLoading = false;

        s.alertObj = {
          title: "Info",
          message: "Phone number is used, please choose another one.",
        };
      });
    }
  };

  // send a verification code to user phone number
  const sendVerifyOTP = async () => {
    var userPhone = phone.replaceAll('-', '');

    if (!userPhone) {
      setVerifySMSModal(false);
      CommonStore.update((s) => {
        s.alertObj = {
          title: "Error",
          message: "Please key in your contact.",
        };
      });
      return;
    }

    if (userPhone && /\D/.test(userPhone)) {
      setVerifySMSModal(false);
      CommonStore.update((s) => {
        s.alertObj = {
          title: "Error",
          message: "Only digits allowed for contact.",
        };
      });
      return;
    }

    userPhone = userPhone.replace(/[^0-9]/g, '');

    if (userPhone && !(userPhone.length === 10 || userPhone.length === 11)) {
      if (
        (userPhone.startsWith('011') && userPhone.length === 11)
        ||
        (userPhone.startsWith('6011') && userPhone.length === 12)
      ) {
        // still valid, do nothing
      }
      else {
        setVerifySMSModal(false);
        CommonStore.update((s) => {
          s.alertObj = {
            title: "Error",
            message: "Invalid contact format.\neg: 60123456789.",
          };
        });
        return;
      }
    }

    if (!userPhone.startsWith('6')) {
      userPhone = '6' + userPhone;
    }

    const data = {
      phoneNumber: userPhone,
    };

    const response = await ApiClient.POST(API.sendOTPVerificationToSMSWeb, data);

    if (response) {
      alert(`Success: Code has been sent to \n ${data.phoneNumber} !`);
      CommonStore.update((s) => {
        s.alertObj = {
          title: "Success",
          message: `Code has been sent to \n ${data.phoneNumber} !`,
        };
      });
    } else {
      setVerifySMSModal(false);
      CommonStore.update((s) => {
        s.alertObj = {
          title: "Failed",
          message: `Code failed to send to \n ${data.phoneNumber} !`,
        };
      });
    }
  }

  // check the verification code user inputted
  const verifyRegisterOTP = async (state) => {
    var userPhone = phone.replaceAll('-', '');

    userPhone = userPhone.replace(/[^0-9]/g, '');

    if (!userPhone.startsWith('6')) {
      userPhone = '6' + userPhone;
    }

    // const OTPSnapshot = await firebase.firestore().collection(Collections.OTPCache)
    //   .where('phoneNumber', '==', userPhone)
    //   .limit(1)
    //   .get();

    const OTPSnapshot = await getDocs(
      query(
        collection(global.db, Collections.OTPCache),
        where('phoneNumber', '==', userPhone),
        limit(1),
      )
    );

    var OTP = null;
    if (!OTPSnapshot.empty) {
      OTP = OTPSnapshot.docs[0].data();
    } else {
      setVerifySMSModal(false);
      CommonStore.update((s) => {
        s.alertObj = {
          title: "Invalid code",
          message: `Please try again !`,
        };
      });
      return
    }

    if (OTP.code == verificationCode) {
      setIsVerified(true);
      setVerifySMSModal(false);
      CommonStore.update((s) => {
        s.alertObj = {
          title: "Verification Success",
          message: `Please proceed to register !`,
        };
      });
    }
  }

  return (
    <View style={{ height: windowHeight * 0.9, alignItems: 'center', justifyContent: 'center', backgroundColor: 'light-gray', }}>
      {/* <View style={{ flex: 1, flexDirection: 'row', marginTop: 10, width: windowWidth * 0.8 }}>
        <View style={{ flex: 0.2, alignItems: 'flex-start', justifyContent: 'center' }}>
          <Text>
            First Name:
          </Text>
        </View>
        <View style={{ flex: 0.8, alignItems: 'flex-end', justifyContent: 'center' }}>
          <TextInput
            value={firstName}
            onChangeText={(text) => { setFirstName(text) }}
            style={[styles.textInput, { width: windowWidth * 0.7 }]}
            underlineColorAndroid={Colors.fieldtBgColor}
            clearButtonMode="while-editing"
            placeholder='First Name'
          />
        </View>
      </View> */}
      {/* <View style={{ flex: 1, flexDirection: 'row', marginTop: 10, width: windowWidth * 0.8 }}>
        <View style={{ flex: 0.2, alignItems: 'flex-start', justifyContent: 'center', }}>
          <Text>
            Last Name:
          </Text>
        </View>
        <View style={{ flex: 0.8, alignItems: 'flex-end', justifyContent: 'center' }}>
          <TextInput
            value={lastName}
            onChangeText={(text) => { setLastName(text) }}
            style={[styles.textInput, { width: windowWidth * 0.7 }]}
            underlineColorAndroid={Colors.fieldtBgColor}
            clearButtonMode="while-editing"
            placeholder='Last Name'
          />
        </View>
      </View> */}
      {/* <View style={{ flex: 1, flexDirection: 'row', marginTop: 10, width: windowWidth * 0.8 }}>
        <View style={{ flex: 0.2, alignItems: 'flex-start', justifyContent: 'center' }}>
          <Text>
            Name:
          </Text>
        </View>
        <View style={{ flex: 0.8, alignItems: 'flex-end', justifyContent: 'center' }}>
          <TextInput
            value={name}
            onChangeText={(text) => { setName(text) }}
            style={[styles.textInput, { width: windowWidth * 0.7 }]}
            underlineColorAndroid={Colors.fieldtBgColor}
            clearButtonMode="while-editing"
            placeholder='Name'
          />
        </View>
      </View> */}
      <View style={{
        width: isMobile() ? windowWidth : windowWidth,
        height: 160,
        backgroundColor: Colors.darkBgColor,
      }}>
        <Image style={{
          width: 100,
          height: 47,
          alignSelf: 'center',
          //marginBottom: 25.
        }} resizeMode="contain" source={imgLogo} />
        <Text style={{
          fontFamily: 'NunitoSans-Bold',
          color: Colors.whiteColor,
          fontSize: 30,
          textAlign: 'center',
          marginTop: 30,
          // width: '100%',
        }}>
          Outlet Name's Loyalty
        </Text>
      </View>
      {existingCustomer == true ?
        <View style={{
          justifyContent: 'center',
          alignContent: 'center',
          borderRadius: 10,
          borderColor: '#E5E5E5',

          shadowOffset: {
            width: 0,
            height: 2,
          },
          shadowOpacity: 0.22,
          shadowRadius: 3.22,
          elevation: 1,
          padding: 20,
          marginTop: '3%',

          width: windowWidth * 0.5,
        }}>
          <View style={{ marginTop: '1%', alignItems: 'center', justifyContent: 'center' }}>
            <Text style={{
              fontFamily: 'NunitoSans-Bold',
              color: 'Black',
              fontSize: 20,
              textAlign: 'center',
            }}>
              You've already signed up for Outlet Name's Loyalty Program before
            </Text>
          </View>
          <View style={{ marginTop: '5%', alignItems: 'center', justifyContent: 'center', }}>
            <Text style={{
              fontFamily: 'NunitoSans-Regular',
              color: 'Black',
              fontSize: 16,
              textAlign: 'center',
              width: windowWidth * 0.35,
            }}>
              Click the button below and we'll send you a unique link via SMS where you can check your history, how much credit, and which rewards, you have available
            </Text>
          </View>

          <View style={{ flexDirection: 'row', marginTop: '5%', width: windowWidth * 0.5, justifyContent: 'center' }}>
            <View style={{ alignItems: 'flex-start', justifyContent: 'center' }}>
              <Text style={{
                fontFamily: 'NunitoSans-Bold',
                color: 'Black',
                fontSize: 16,
                textAlign: 'center',
              }}>
                Phone:
              </Text>
            </View>
            <View style={{ alignItems: isMobile() ? 'flex-start' : 'flex-end', justifyContent: 'center' }}>
              <TextInput
                value={phone}
                onChangeText={(text) => { setPhone(text) }}
                style={[styles.textInput,
                {
                  width: isMobile() ? windowWidth * 0.55 : windowWidth * 0.325,
                  height: isMobile() ? 35 : windowHeight * 0.04,
                  borderRadius: 10,
                  borderColor: '#E5E5E5',

                  shadowOffset: {
                    width: 0,
                    height: 2,
                  },
                  shadowOpacity: 0.22,
                  shadowRadius: 3.22,
                  elevation: 1,
                  marginLeft: 50,
                }]}
                underlineColorAndroid={Colors.fieldtBgColor}
                clearButtonMode="while-editing"
                placeholder='Phone Number'
              />
            </View>
          </View>
          <View style={{ flex: 1, marginTop: '2%', alignContent: 'center' }}>
            <TouchableOpacity
              disabled={isLoading}
              onPress={() => {

              }}
              style={{
                backgroundColor:
                  Colors.primaryColor,
                width: isMobile() ? windowWidth * 0.17 : 200,
                height: isMobile() ? 35 : 40,
                // height: 60,
                alignSelf: "center",
                alignItems: 'center',
                borderRadius: 10,
                justifyContent: "center",
              }}
            >
              <Text
                style={{
                  alignSelf: 'center',
                  color: Colors.whiteColor,
                  fontFamily: 'NunitoSans-Bold',
                  fontSize: 14
                }}
              >
                SEND ME A LINK
              </Text>
            </TouchableOpacity>
          </View>
        </View>
        :
        <View style={{
          justifyContent: 'center',
          alignContent: 'center',
          borderRadius: 10,
          borderColor: '#E5E5E5',

          shadowOffset: {
            width: 0,
            height: 2,
          },
          shadowOpacity: 0.22,
          shadowRadius: 3.22,
          elevation: 1,
          padding: 20,
          marginTop: '3%',

          width: windowWidth * 0.5,
        }}>
          {/* <View style={{ marginTop: '1%', alignItems: 'center', justifyContent: 'center' }}>
            <Text style={{
              fontFamily: 'NunitoSans-Bold',
              color: 'Black',
              fontSize: 20,
              textAlign: 'center',
            }}>
              Receive instant vouchers and earn loyalty rewards when you spend
            </Text>
          </View> */}
          <View style={{
            // justifyContent: 'center',
            // alignContent: 'center',
            flexDirection: 'row',
            //width: isMobile() ? windowWidth : windowWidth * 1,
            marginTop: '3%',
            //width: windowWidth * 0.8,
          }}>
            <View style={{ marginRight: '1%', width: 30 }}>
              <Arrow
                width={20}
                height={20}
                color={Colors.primaryColor}
              />
            </View>
            <Text style={{
              fontFamily: 'NunitoSans-Regular',
              color: 'black',
              fontSize: 16,
              //textAlign: 'center',
              marginTop: 0,
              marginLeft: 5,
              // width: '100%',
            }}>
              Enter phone number to claim voucher
            </Text>
          </View>
          {/* <View style={{
            // justifyContent: 'center',
            // alignItems: 'center',
            flexDirection: 'row',
            //width: isMobile() ? windowWidth : windowWidth * 1,
            marginTop: '1%',
            //width: windowWidth * 0.8,
            //backgroundColor: 'red'
          }}>
            <View style={{ marginRight: '1%', width: 30 }}>
              <Arrow
                width={20}
                height={20}
                color={Colors.primaryColor}
              />
            </View>
            <Text style={{
              fontFamily: 'NunitoSans-Regular',
              color: 'black',
              fontSize: 16,
              //textAlign: 'center',
              marginTop: 0,
              marginLeft: 5,
              // width: '100%',
            }}>
              Sign up & start earning rewards
            </Text>
          </View>
          <View style={{
            // justifyContent: 'center',
            // alignItems: 'center',
            flexDirection: 'row',
            //width: isMobile() ? windowWidth : windowWidth * 1,
            marginTop: '1%',
            //width: windowWidth * 0.8,
          }}>
            <View style={{ marginRight: '1%', width: 30 }}>
              <Arrow
                width={20}
                height={20}
                color={Colors.primaryColor}
              />
            </View>
            <Text style={{
              fontFamily: 'NunitoSans-Regular',
              color: 'black',
              fontSize: 16,
              //textAlign: 'center',
              marginTop: 0,
              marginLeft: 5,
              // width: '100%',
            }}>
              Enjoy your rewards & claim on future visits
            </Text>
          </View> */}

          <View style={{ flexDirection: 'row', marginTop: '2%', width: windowWidth * 0.5, justifyContent: 'center' }}>
            <View style={{ alignItems: 'flex-start', justifyContent: 'center' }}>
              <Text style={{
                fontFamily: 'NunitoSans-Bold',
                color: 'Black',
                fontSize: 16,
                textAlign: 'center',
              }}>
                Phone:
              </Text>
            </View>
            <View style={{ alignItems: isMobile() ? 'flex-start' : 'flex-end', justifyContent: 'center' }}>
              <TextInput
                value={phone}
                onChangeText={(text) => { setPhone(text) }}
                style={[styles.textInput,
                {
                  width: isMobile() ? windowWidth * 0.55 : windowWidth * 0.325,
                  height: isMobile() ? 35 : windowHeight * 0.04,
                  borderRadius: 10,
                  borderColor: '#E5E5E5',

                  shadowOffset: {
                    width: 0,
                    height: 2,
                  },
                  shadowOpacity: 0.22,
                  shadowRadius: 3.22,
                  elevation: 1,
                  marginLeft: 50,
                }]}
                underlineColorAndroid={Colors.fieldtBgColor}
                clearButtonMode="while-editing"
                placeholder='Phone Number'
              />
            </View>
            <View style={{ alignItems: 'flex-end', justifyContent: 'center' }}>
              <TouchableOpacity
                onPress={() => {
                  setVerifySMSModal(true);
                }}
                style={{
                  backgroundColor:
                    Colors.primaryColor,
                  alignSelf: "center",
                  alignItems: 'center',
                  width: isMobile() ? windowWidth * 0.12 : 110,
                  height: isMobile() ? 35 : 40,
                  borderRadius: 10,
                  justifyContent: "center",

                  marginLeft: 50,
                }}
              >
                <Text
                  style={{
                    alignSelf: 'center',
                    color: Colors.whiteColor,
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: 14
                  }}>
                  VERIFY
                </Text>
              </TouchableOpacity>
            </View>
          </View>
          {/* <View style={{ flex: 1, flexDirection: 'row', marginTop: 10, width: windowWidth * 0.8 }}>
            <View style={{ flex: 0.2, alignItems: 'flex-start', justifyContent: 'center', }}>
              <Text>
                Birthday:
              </Text>
            </View>
            <View style={{ flex: 0.8, alignItems: 'flex-end', justifyContent: 'center' }}>
              <TouchableOpacity
                style={[styles.textInput, { width: windowWidth * 0.7, justifyContent: 'center' }]}
                onPress={() => {
                  setShowDOBPicker(true);
                }}
              >
                <View style={{ flexDirection: 'row' }}>
                  <GCalendar
                    width={switchMerchant ? 10 : 20}
                    height={switchMerchant ? 10 : 20}
                    style={{ marginRight: 5 }}
                  />
                  <Text
                    style={{
                      fontFamily: "NunitoSans-Regular",
                      fontSize: switchMerchant ? 10 : 14,
                    }}
                  >
                    {rev_DOBdate
                      ? moment(rev_DOBdate).format("DD/MM/YYYY")
                      : "N/A"}
                  </Text>
                </View>
              </TouchableOpacity>
            </View>
          </View> */}
          {/* <View style={{ flex: 1, flexDirection: 'row', marginTop: 10, width: windowWidth * 0.8 }}>
            <View style={{ flex: 0.2, alignItems: 'flex-start', justifyContent: 'center', }}>
              <Text>
                Address:
              </Text>
            </View>
            <View style={{ flex: 0.8, alignItems: 'flex-end', justifyContent: 'center' }}>
              <Autocomplete
                placeholder="📍 Type your address here"
                minLength={2} // minimum length of text to search
                autoFocus={false}
                returnKeyType={"search"} // Can be left out for default return key https://facebook.github.io/react-native/docs/textinput.html#returnkeytype
                listViewDisplayed="false" // true/false/undefined
                fetchDetails={true}
                renderDescription={(row) => renderSearch(row)} // custom description render
                onPlaceSelected={(data) => {
                  // 'details' is provided when fetchDetails = true
                  console.log("data", data);
                  // console.log("details", details);
                  // console.log("data.description", data.description);
                  // console.log("details.geometry.location", details.geometry.location);
                  setAddress(data.formatted_address);
                  setLat(data.geometry.location.lat);
                  setLng(data.geometry.location.lng);
                }}
                defaultValue={() => ""}
                // query={{
                //   // available options: https://developers.google.com/places/web-service/autocomplete
                //   //key: 'AIzaSyB_nKKIGIcEJ6ffsVBdYeIstW8KekjVXa8',
                //   // key: 'AIzaSyCsM9boPkQ7uaJ54RWUzPHGzjZfggkXf8g',
                //   key: 'AIzaSyAt1AOIT_1E5Ivec7PVWmylmDd8jSVm-j8',
                //   language: 'en', // language of the results
                //   //types: 'address', // default: 'geocode'
                //   components: 'country:my',
                // }}

                apiKey={googleCloudApiKey}

                // This api can use
                // apiKey={"AIzaSyABX4LTqTLQGg_b3jFOH8Z6_H5CDqn8tbc"}
                //This api cannot use
                // apiKey= {'AIzaSyAt1AOIT_1E5Ivec7PVWmylmDd8jSVm-j8'}

                options={{
                  types: ["address"],
                  componentRestrictions: { country: "my" },
                }}
                style={{
                  fontFamily: 'NunitoSans-Regular',
                  height: 50,
                  backgroundColor: Colors.fieldtBgColor,
                  borderRadius: 10,
                  borderColor: '#E5E5E5',
                  width: windowWidth * 0.7,
                }}

                // currentLocation={false}
                // currentLocationLabel="Current location"
                // nearbyPlacesAPI='GooglePlacesSearch'
                // GoogleReverseGeocodingQuery={{
                // }}
                // GooglePlacesSearchQuery={{
                //   rankby: 'distance'
                // }}

                // filterReverseGeocodingByTypes={['locality', 'administrative_area_level_3']}

                debounce={200}
              />
            </View>
          </View> */}
          <View style={{ flex: 1, marginTop: '2%', alignContent: 'center' }}>
            <TouchableOpacity
              disabled={isLoading}
              onPress={() => {
                registerUser();
              }}
              style={{
                backgroundColor:
                  Colors.primaryColor,
                width: isMobile() ? windowWidth * 0.17 : 110,
                height: isMobile() ? 35 : 40,
                // height: 60,
                alignSelf: "center",
                alignItems: 'center',
                borderRadius: 10,
                justifyContent: "center",
              }}
            >
              <Text
                style={{
                  alignSelf: 'center',
                  color: Colors.whiteColor,
                  fontFamily: 'NunitoSans-Bold',
                  fontSize: 14
                }}
              >
                {isLoading ? 'LOADING...' : 'REGISTER'}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      }
      <View style={{ flex: 1 }}></View>
      <Modal
        style={
          {
            // flex: 1
          }
        }
        visible={showDOBPicker}
        transparent={true}
        animationType={"slide"}
        supportedOrientations={["portrait", "landscape"]}
      >
        <View
          style={{
            flex: 1,
            backgroundColor: Colors.modalBgColor,
            alignItems: "center",
            justifyContent: "center",
            // top: keyboardHeight > 0 ? -keyboardHeight * 0.45 : 0,
          }}
        >
          <View
            style={{
              height: windowWidth * 0.2,
              width: windowWidth * 0.4,
              backgroundColor: Colors.whiteColor,
              borderRadius: 12,
              padding: windowWidth * 0.02,
              // borderWidth:1
            }}
          >
            <View style={{ flexDirection: "row" }}>
              <View style={{ flex: 1.8 }}>
                <Text
                  style={{
                    fontFamily: "NunitoSans-Bold",
                    fontSize: 20,
                    textAlign: "right",
                  }}
                >
                  Date of Birth
                </Text>
              </View>
              <View style={{ flex: 1.2 }}>
                <TouchableOpacity
                  style={{
                    elevation: 1000,
                    zIndex: 1000,
                    alignSelf: "flex-end",
                  }}
                  onPress={() => {
                    setShowDOBPicker(false);
                  }}
                >
                  <AntDesign
                    name="closecircle"
                    size={25}
                    color={Colors.fieldtTxtColor}
                  />
                </TouchableOpacity>
              </View>
            </View>
            <View
              style={{
                flexDirection: "row",
                marginTop: 60,
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              <Text
                style={{
                  fontFamily: "NunitoSans-Bold",
                  fontSize: 18,
                  textAlign: "right",
                  marginRight: 10,
                }}
              >
                Date:
              </Text>
              <View
                style={{
                  borderWidth: 0.5,
                  borderRadius: 5,
                }}
              >
                <DatePicker
                  // isVisible={showDOBPicker}
                  // mode={'date'}
                  selected={rev_DOBdate.toDate()}
                  onChange={(date) => {
                    setRev_DOBdate(moment(date));
                    setBirthday(moment(date));

                    // setShowDOBPicker(false);
                  }}
                // onCancel={() => {
                //   setShowDOBPicker(false);
                // }}
                />
              </View>
            </View>
          </View>
        </View>
      </Modal>

      <Modal
        visible={verifySMSModal}
        transparent={true}
        supportedOrientations={['portrait', 'landscape']}
        animationType="fade"
        onRequestClose={() => { setVerifySMSModal(false) }}
      >
        <View style={{
          backgroundColor: 'rgba(0,0,0,0.5)',
          height: windowHeight,
          justifyContent: 'center',
        }}>
          <View style={{
            alignSelf: 'center',
            width: windowWidth * 0.5,
            height: 300,
            backgroundColor: 'white',

            borderRadius: 10,
            borderColor: '#E5E5E5',

            shadowOffset: {
              width: 0,
              height: 2,
            },
            shadowOpacity: 0.22,
            shadowRadius: 3.22,
            elevation: 1,
            zIndex: -1,

            marginTop: 40,
          }}>
            <TouchableOpacity
              onPress={() => { setVerifySMSModal(false) }}
            >
              <View style={{
                marginTop: 10,
                marginRight: 10,
                alignSelf: 'flex-end',
                height: 20,
              }}>
                <AntDesign name="closecircle" size={25} color={Colors.fieldtTxtColor} />
              </View>
            </TouchableOpacity>
            <Text style={{
              fontFamily: 'NunitoSans-Bold',
              color: 'black',
              fontSize: 20,
              textAlign: 'center',
              marginTop: 0,
              // width: '100%',
            }}>
              SMS Verification
            </Text>
            <View style={{
              width: '80%',
              alignSelf: 'center',
              marginTop: 20,
            }}>
              {sentCode == true ?
                <Text
                  style={{
                    alignSelf: 'center',
                    //color: Colors.whiteColor,
                    fontFamily: 'NunitoSans-Regular',
                    fontSize: 16
                  }}
                >
                  Verification code has been sent to <b>+6{phone}</b>, please check your device if you have received the code
                </Text>
                :
                <Text
                  style={{
                    alignSelf: 'center',
                    //color: Colors.whiteColor,
                    fontFamily: 'NunitoSans-Regular',
                    fontSize: 16
                  }}
                >
                  Click <b>SEND CODE</b> to send verification code to your device
                </Text>
              }
            </View>
            <View
              style={{
                flexDirection: 'row',
                alignSelf: 'center',
              }}>
              <View style={{
                //justifyContent: 'center',
                alignItems: 'center',
                flexDirection: 'row',
                marginTop: '10%',
                //marginLeft: '-1%',
                //width: isMobile() ? windowWidth : windowWidth * 1,
                //backgroundColor: 'red'
              }}>
                <Text style={{
                  fontFamily: 'NunitoSans-Regular',
                  color: 'black',
                  fontSize: 16,
                  //textAlign: 'center',
                  marginTop: 0,
                  marginLeft: 5,
                  // width: '100%',
                }}>
                  Code:
                </Text>
                <TextInput
                  underlineColorAndroid={Colors.fieldtBgColor}
                  clearButtonMode="while-editing"
                  style={{
                    height: 40,
                    width: '60%',
                    maxWidth: 150,
                    paddingHorizontal: 20,
                    backgroundColor: Colors.fieldtBgColor,
                    paddingLeft: 15,
                    marginLeft: 10,
                    fontSize: 16,
                    fontFamily: 'NunitoSans-Regular',
                    textAlignVertical: 'center',
                    alignItems: 'center',
                    alignContent: 'center',

                    borderRadius: 10,
                    borderColor: '#E5E5E5',
                    borderWidth: 1,
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 1,
                  }}
                  placeholder="123456"
                  placeholderTextColor={Colors.descriptionColor}
                  keyboardType="decimal-pad"
                  onChangeText={(text) => {
                    setVerificationCode(text);
                  }}
                  value={verificationCode}
                  clearTextOnFocus={true}
                />
                {sentCode == false ?
                  <TouchableOpacity
                    style={{
                      backgroundColor:
                        Colors.primaryColor,
                      width: isMobile() ? windowWidth * 0.17 : 110,
                      height: isMobile() ? 35 : 40,
                      // height: 60,
                      alignSelf: "center",
                      alignItems: 'center',
                      borderRadius: 10,
                      justifyContent: "center",
                      marginLeft: 20,
                    }}
                    onPress={() => {
                      setSentCode(true);
                      sendVerifyOTP();
                    }}>
                    <Text
                      style={{
                        color: '#ffffff',
                        fontSize: 16,
                        fontFamily: 'NunitoSans-Regular',
                      }}>
                      SEND CODE
                    </Text>
                  </TouchableOpacity>
                  :
                  <></>
                }

              </View>
            </View>
            <View
              style={{
                flexDirection: 'row',
                alignSelf: 'center',
                marginTop: '5%',
              }}>
              <TouchableOpacity
                style={{
                  backgroundColor:
                    Colors.primaryColor,
                  alignSelf: "center",
                  alignItems: 'center',
                  width: isMobile() ? windowWidth * 0.12 : 110,
                  height: isMobile() ? 35 : 40,
                  borderRadius: 10,
                  justifyContent: "center",
                }}
                onPress={() => verifyRegisterOTP()}>
                <Text
                  style={{
                    color: '#ffffff',
                    fontSize: 16,
                    fontFamily: 'NunitoSans-Regular',
                  }}>
                  VERIFY
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

    </View>

  )
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.highlightColor,
    flexDirection: 'row',
    fontFamily: 'NunitoSans-Regular',
  },
  list: {
    backgroundColor: Colors.whiteColor,
    // width: windowWidth * 0.85,
    // height: windowHeight,
    marginTop: 40,
    marginHorizontal: 30,
    //alignSelf: 'center',
    //justifyContent: 'center',
    borderRadius: 5,
    shadowOpacity: 0,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
  },
  listItem: {
    fontFamily: 'NunitoSans-Regular',
    marginLeft: 20,
    color: Colors.descriptionColor,
    fontSize: 16,
  },
  textInput: {
    fontFamily: 'NunitoSans-Regular',
    height: 50,
    backgroundColor: Colors.fieldtBgColor,
    //borderRadius: 10,
    paddingLeft: 20,
  },
  textInputLocation: {
    fontFamily: 'NunitoSans-Regular',
    width: 300,
    height: 100,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 10,
    marginRight: 10,
  },
  textSize: {
    fontSize: 19,
    fontFamily: 'NunitoSans-SemiBold',
  },
  merchantDisplayView: {
    flexDirection: 'row',
    flex: 1,
    marginLeft: '17%',
  },
  shiftText: {
    marginLeft: '15%',
    color: Colors.primaryColor,
    fontFamily: 'NunitoSans-SemiBold',
    fontSize: 25,
  },
  confirmBox: {
    width: '30%',
    height: '30%',
    borderRadius: 30,
    backgroundColor: Colors.whiteColor,
  },
  closeButton: {
    position: 'absolute',
    right: 10,
    top: 10,
    elevation: 1000,
    zIndex: 1000,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: Colors.modalBgColor,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalView: {
    height: 200,
    width: 500,
    backgroundColor: Colors.whiteColor,
    borderRadius: 12,
    padding: 25,
    //alignItems: 'center',
    //justifyContent: 'center'
  },
});

export default LoyaltySignUp;