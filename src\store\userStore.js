import { Store } from 'pullstate';

export const UserStore = new Store({
    avatar: '',
    dob: null,
    email: '',
    gender: '',
    name: '',
    number: '',
    outletId: '',
    race: '',
    state: '',
    uniqueName: '',
    updatedAt: null,
    emailSecond: '',
    // merchantId: '',
    role: '',
    refreshToken: '',
    firebaseUid: '',
    levelName: '',

    userAddresses: [],
    selectedUserAddress: null,

    userPaymentPointers: [],
    selectedUserPaymentPointer: null,

    voucherIdValidList: [],
    selectedVoucherId: null,

    voucherIdRedemptionList: [],

    ///////////////////////////

    userGroups: ['EVERYONE'],

    userPointsTransactions: [],
    userPointsBalance: 0,

    token: '',
    userId: '',

    userInfoName: '',
    userInfoPhone: '',

    timestamp: 0,

    userIdAnonymous: 'none',
    anonymousDt: 'none',

    //////////////////////////////////////////////////////////////////////////////

    anonymousPointsTransactions: [],
    anonymousPointsBalance: [],

    selectedCustomerPointsTransactionsEmail: [],
    selectedCustomerPointsBalanceEmail: 0,
    selectedCustomerPointsTransactionsPhone: [],
    selectedCustomerPointsBalancePhone: 0,

    userGlobalPointsTransactions: [],
    userGlobalPointsBalance: 0,
    selectedCustomerGlobalPointsTransactionsEmail: [],
    selectedCustomerGlobalPointsBalanceEmail: 0,
    selectedCustomerGlobalPointsTransactionsPhone: [],
    selectedCustomerGlobalPointsBalancePhone: 0,

    userOutletDict: [],
    visitedOutletIdList: [],
    visitedOutletNameList: [],

    gReview: false,

    //////////////////////////////////////////////////////////////////////////////

    epStateTo: '',
    epNameTo: '',
    epPhoneTo: '',
    epAddr1To: '',
    epCityTo: '',
    epCodeTo: '',

    crmUserId: '',

    epTinTo: '',
    epEmailTo: '',
    epIdTo: '',
    epIdTypeTo: '',

    //////////////////////////////////////////////////////////////////////////////
});   