import React, { Component } from 'react';

import {
  StyleSheet,
  ScrollView,
  Image,
  View,
  Text,
  TouchableOpacity,
} from 'react-native';
import Colors from '../constant/Colors';
import ApiClient from '../util/ApiClient';
import API from '../constant/API';
import Octicons from 'react-native-vector-icons/Octicons';
import Entypo from 'react-native-vector-icons/Entypo';
import AntDesign from 'react-native-vector-icons/AntDesign';
import Ionicons from 'react-native-vector-icons/Ionicons';
import Styles from '../constant/Styles';
import * as Cart from '../util/Cart';
import * as User from '../util/User';
import Icon from 'react-native-vector-icons/MaterialIcons';
// import { FlatList, TextInput } from 'react-native-gesture-handler';
import moment from 'moment';
import Icons from 'react-native-vector-icons/EvilIcons';
// import DateTimePickerModal from 'react-native-modal-datetime-picker';
// import { GooglePlacesAutocomplete } from 'react-native-google-places-autocomplete';
import Autocomplete from 'react-google-autocomplete';
import { googleCloudApiKey } from '../constant/env';

import loadable from '@loadable/component';
// const AutocompleteLoadable = loadable.lib(() => import("react-google-autocomplete"));

/**
 * WorkAddress
 * function
 * *Select an address and lat lng from GooglePlacesAutocomplete, label as Work address
 */
class WorkAddress extends Component {
  constructor({ navigation, props, route }) {
    super(props);

    navigation.setOptions({
      headerLeft: () => (
        <TouchableOpacity style={{
        }} onPress={() => { navigation.navigate('Address - KooDoo Web Order'); }}>
          <View style={{
            marginLeft: 10,
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'flex-start',
          }}>
            <Ionicons
              name="chevron-back"
              size={26}
              color={Colors.fieldtTxtColor}
              style={{
              }}
            />

            <Text
              style={{
                color: Colors.fieldtTxtColor,
                fontSize: 16,
                textAlign: 'center',
                fontFamily: 'NunitoSans-Regular',
                lineHeight: 22,
                marginTop: -1,
              }}>
              Back
            </Text>
          </View>
        </TouchableOpacity>
      ),
      headerRight: () => (
        <TouchableOpacity onPress={() => {
          // props.navigation.navigate('Profile') 
        }}
          style={{
          }}>
          <View style={{ marginRight: 15 }}>
            <Ionicons name="menu" size={30} color={Colors.primaryColor} />
          </View>
        </TouchableOpacity>
      ),
      headerTitle: () => (
        <View style={{
          justifyContent: 'center',
          alignItems: 'center',
          bottom: -1,
        }}>
          <Text
            style={{
              fontSize: 20,
              lineHeight: 25,
              textAlign: 'center',
              fontFamily: 'NunitoSans-Bold',
              color: Colors.mainTxtColor,
            }}>
            Work Address
          </Text>
        </View>
      ),
    });
    this.state = {
      search: [{}, {}, {}]
    };
  }

  componentDidMount() { }

  // function here
  renderItem = ({ item }) => (
    <TouchableOpacity style={styles.container}
      onPress={() => { this.props.navigation.navigate('AddAddress - KooDoo Web Order', { test: 2 }) }}>
      <View>
        <Text style={{ fontWeight: "700", fontSize: 14, marginBottom: 5 }}>adddddddrrresssss name</Text>
      </View>
      <View>
        <Text>adddddddrrresssss</Text>
      </View>
    </TouchableOpacity>
  );
  renderSearch(item) {
    return (
      <View style={{ flexDirection: 'column', }}>

        <Text style={{ fontWeight: "700", fontSize: 14, marginBottom: 5 }}>{item.structured_formatting.main_text}</Text>


        <Text style={{ fontSize: 12, color: "grey" }}>{item.description}</Text>

      </View>

    )
  }
  // function end

  render() {
    return (
      <View style={{ flex: 1, }}>
        <ScrollView keyboardShouldPersistTaps='always' style={{ backgroundColor: Colors.whiteColor }}>
          {/* <GooglePlacesAutocomplete
            placeholder="📍  Type your address here"
            minLength={2} // minimum length of text to search
            autoFocus={false}
            returnKeyType={'search'} // Can be left out for default return key https://facebook.github.io/react-native/docs/textinput.html#returnkeytype
            listViewDisplayed='false'    // true/false/undefined
            fetchDetails={true}
            renderDescription={row => this.renderSearch(row)} // custom description render
            onPress={(data, details = null) => { // 'details' is provided when fetchDetails = true
              console.log("data", data);
              console.log("details", details);
              console.log("data.description", data.description);
              console.log("data.structured_formatting.main_text", data.structured_formatting.main_text);
              this.props.navigation.navigate('AddAddress', { test: 2, address: data.description, name: data.structured_formatting.main_text, location: details.geometry.location })
            }}

            getDefaultValue={() => ''}

            query={{
              // available options: https://developers.google.com/places/web-service/autocomplete
              //key: 'AIzaSyB_nKKIGIcEJ6ffsVBdYeIstW8KekjVXa8',
              // key: 'AIzaSyCsM9boPkQ7uaJ54RWUzPHGzjZfggkXf8g',
              key: 'AIzaSyAt1AOIT_1E5Ivec7PVWmylmDd8jSVm-j8',
              language: 'en', // language of the results
              types: 'address', // default: 'geocode'
              components: 'country:my',
            }}

            styles={{
              textInputContainer: {
                width: '90%',
                alignSelf: 'center',
                marginTop: 20,
              },
              textInput: {
                backgroundColor: Colors.fieldtBgColor,
                fontFamily: 'NunitoSans-Regular',
                fontSize: 14,
                height: 50,
              },
              description: {
                // fontWeight: 'bold',              
                fontFamily: 'NunitoSans-Bold',
                fontSize: 16,
              },
              predefinedPlacesDescription: {
                color: '#1faadb',
                fontFamily: 'NunitoSans-Regular',
                fontSize: 14,
              },
              position: "absolute",
            }}
            enablePoweredByContainer={false}

            currentLocation={false}
            currentLocationLabel="Current location"
            nearbyPlacesAPI='GooglePlacesSearch'
            GoogleReverseGeocodingQuery={{
            }}
            GooglePlacesSearchQuery={{
              rankby: 'distance'
            }}

            filterReverseGeocodingByTypes={['locality', 'administrative_area_level_3']}

            debounce={200}

          /> */}

          {/* <AutocompleteLoadable fallback={<></>}>
            {({ default: Autocomplete }) =>
              <Autocomplete
                placeholder="📍  Type your address here"
                minLength={2} // minimum length of text to search
                autoFocus={false}
                returnKeyType={'search'} // Can be left out for default return key https://facebook.github.io/react-native/docs/textinput.html#returnkeytype
                listViewDisplayed='false'    // true/false/undefined
                fetchDetails={true}
                renderDescription={row => this.renderSearch(row)} // custom description render
                onPlaceSelected={(data) => { // 'details' is provided when fetchDetails = true
                  console.log("data", data);
                  // console.log("place ", place)
                  // console.log("details", details);
                  // console.log("data.description", data.description);
                  // console.log("data.structured_formatting.main_text", data.structured_formatting.main_text);
                  // this.props.navigation.navigate('AddAddress - KooDoo Web Order', { test: 2, address: data.description, name: data.structured_formatting.main_text, location: details.geometry.location })
                  this.props.navigation.navigate('AddAddress - KooDoo Web Order', {
                    test: 2, address: data.formatted_address,
                    name: data.address_components[0].short_name + ", " + data.address_components[1].short_name, location: data.geometry.location
                  })
                }}

                defaultValue={() => ' '}

                // query={{
                //   // available options: https://developers.google.com/places/web-service/autocomplete
                //   //key: 'AIzaSyB_nKKIGIcEJ6ffsVBdYeIstW8KekjVXa8',
                //   // key: 'AIzaSyCsM9boPkQ7uaJ54RWUzPHGzjZfggkXf8g',
                //   key: 'AIzaSyAt1AOIT_1E5Ivec7PVWmylmDd8jSVm-j8',
                //   language: 'en', // language of the results
                //   types: 'address', // default: 'geocode'
                //   components: 'country:my',
                // }}

                apiKey={googleCloudApiKey}
                // apiKey= {'AIzaSyAt1AOIT_1E5Ivec7PVWmylmDd8jSVm-j8'}
                options={{
                  types: ["address"],
                  componentRestrictions: { country: "my" },
                }}
                styles={{
                  textInputContainer: {
                    width: '90%',
                    alignSelf: 'center',
                    marginTop: 20,
                  },
                  textInput: {
                    backgroundColor: Colors.fieldtBgColor,
                    fontFamily: 'NunitoSans-Regular',
                    fontSize: 14,
                    height: 50,
                  },
                  description: {
                    // fontWeight: 'bold',              
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: 16,
                  },
                  predefinedPlacesDescription: {
                    color: '#1faadb',
                    fontFamily: 'NunitoSans-Regular',
                    fontSize: 14,
                  },
                  position: "absolute",
                }}
                enablePoweredByContainer={false}

                currentLocation={false}
                currentLocationLabel="Current location"
                nearbyPlacesAPI='GooglePlacesSearch'
                GoogleReverseGeocodingQuery={{
                }}
                GooglePlacesSearchQuery={{
                  rankby: 'distance'
                }}

                filterReverseGeocodingByTypes={['locality', 'administrative_area_level_3']}

                debounce={200}
              />
            }
          </AutocompleteLoadable> */}

          <Autocomplete
            placeholder="📍  Type your address here"
            minLength={2} // minimum length of text to search
            autoFocus={false}
            returnKeyType={'search'} // Can be left out for default return key https://facebook.github.io/react-native/docs/textinput.html#returnkeytype
            listViewDisplayed='false'    // true/false/undefined
            fetchDetails={true}
            renderDescription={row => this.renderSearch(row)} // custom description render
            onPlaceSelected={(data) => { // 'details' is provided when fetchDetails = true
              console.log("data", data);
              // console.log("place ", place)
              // console.log("details", details);
              // console.log("data.description", data.description);
              // console.log("data.structured_formatting.main_text", data.structured_formatting.main_text);
              // this.props.navigation.navigate('AddAddress - KooDoo Web Order', { test: 2, address: data.description, name: data.structured_formatting.main_text, location: details.geometry.location })
              this.props.navigation.navigate('AddAddress - KooDoo Web Order', {
                test: 2, address: data.formatted_address,
                name: data.address_components[0].short_name + ", " + data.address_components[1].short_name, location: data.geometry.location
              })
            }}

            defaultValue={() => ' '}

            // query={{
            //   // available options: https://developers.google.com/places/web-service/autocomplete
            //   //key: 'AIzaSyB_nKKIGIcEJ6ffsVBdYeIstW8KekjVXa8',
            //   // key: 'AIzaSyCsM9boPkQ7uaJ54RWUzPHGzjZfggkXf8g',
            //   key: 'AIzaSyAt1AOIT_1E5Ivec7PVWmylmDd8jSVm-j8',
            //   language: 'en', // language of the results
            //   types: 'address', // default: 'geocode'
            //   components: 'country:my',
            // }}

            apiKey={googleCloudApiKey}
            // apiKey= {'AIzaSyAt1AOIT_1E5Ivec7PVWmylmDd8jSVm-j8'}
            options={{
              types: ["address"],
              componentRestrictions: { country: "my" },
            }}
            styles={{
              textInputContainer: {
                width: '90%',
                alignSelf: 'center',
                marginTop: 20,
              },
              textInput: {
                backgroundColor: Colors.fieldtBgColor,
                fontFamily: 'NunitoSans-Regular',
                fontSize: 14,
                height: 50,
              },
              description: {
                // fontWeight: 'bold',              
                fontFamily: 'NunitoSans-Bold',
                fontSize: 16,
              },
              predefinedPlacesDescription: {
                color: '#1faadb',
                fontFamily: 'NunitoSans-Regular',
                fontSize: 14,
              },
              position: "absolute",
            }}
            enablePoweredByContainer={false}

            currentLocation={false}
            currentLocationLabel="Current location"
            nearbyPlacesAPI='GooglePlacesSearch'
            GoogleReverseGeocodingQuery={{
            }}
            GooglePlacesSearchQuery={{
              rankby: 'distance'
            }}

            filterReverseGeocodingByTypes={['locality', 'administrative_area_level_3']}

            debounce={200}
          />
        </ScrollView>

      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#ffffff',
    height: 70,
    alignSelf: "center", justifyContent: "center",
    width: "90%",
    borderBottomWidth: StyleSheet.hairlineWidth,
  },
  searchBar: {
    backgroundColor: Colors.fieldtBgColor,
    flexDirection: 'row',
    padding: 12,
    borderRadius: 10,
    alignContent: 'center'
  },
  searchBarText: {
    color: Colors.fieldtTxtColor,
    width: "80%"
  },
  BarText: {
    color: Colors.primaryColor,
    marginLeft: 10,
    fontSize: 15,
    fontWeight: 'bold',
    textAlign: 'center',
  },

  text: {
    fontSize: 15,
    fontWeight: '700',
  },
  text1: {
    fontSize: 13,
    fontWeight: '700',
    marginTop: '2%',
  },
  textInput: {
    height: 50,
    width: "90%",
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    alignSelf: "center",
    marginTop: 20
  },
  text2: {
    fontSize: 24,
    fontWeight: '700',
    marginTop: '8%',
  },
  title: {
    color: Colors.blackColor,
    fontSize: 20,
    fontWeight: 'bold',
  },
  viewContainer: {
    marginTop: 10,
    justifyContent: 'space-between',
    flexDirection: 'row',
    flex: 10,
  },
  total: {
    fontSize: 14,
    color: Colors.blackColor,
    fontWeight: '700',
  },
  price: {
    fontSize: 14,
    color: Colors.primaryColor,
    fontWeight: '700',
  },
  totalBorder: {
    paddingTop: 5,
    justifyContent: 'space-between',
    flex: 1,
    flexDirection: 'row',
    borderTopWidth: StyleSheet.hairlineWidth,
    borderColor: Colors.blackColor,
    borderRadius: 1,
  },
  pic: {
    width: 90,
    height: 90,
    borderRadius: 10,
    marginBottom: 5,
    alignItems: 'center',
    justifyContent: 'center',
  },
  Bar1: {
    flexDirection: 'row',
    padding: 12,
    borderRadius: 10,
    alignContent: 'center',
    marginBottom: '2%',
  },
});
export default WorkAddress;
