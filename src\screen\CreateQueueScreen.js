import React, { Component, useState, useEffect } from 'react';
import {
    StyleSheet,
    ScrollView,
    Image,
    View,
    Text,
    TextInput,
    Keyboard,
    Alert,
    TouchableWithoutFeedback,
    TouchableOpacity,
    Dimensions,
    Modal,
    FlatList,
    useWindowDimensions,
} from 'react-native';
import Colors from '../constant/Colors';
import Styles from '../constant/Styles';
import * as User from '../util/User';
import API from '../constant/API';
import ApiClient from '../util/ApiClient';
import Ionicons from 'react-native-vector-icons/Ionicons';
import Back from 'react-native-vector-icons/EvilIcons';
import { CommonStore } from '../store/commonStore';
import { UserStore } from '../store/userStore';
// import firebase from "firebase";
import { getFirestore, collection, query, where, getDocs, limit } from "firebase/firestore";
import { signInAnonymously } from "firebase/auth";
import { Collections } from '../constant/firebase';
import moment from 'moment';
import { WEEK } from '../constant/common';
import AntDesign from 'react-native-vector-icons/AntDesign';
import { mondayFirst } from '../util/commonFuncs';
import { prefix } from "../constant/env";
import { useFocusEffect, useLinkTo } from "@react-navigation/native";
import { DataStore } from '../store/dataStore';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { TempStore } from '../store/tempStore';

/**
 * CreateQueueScreen
 * function
 * *create a queue at an outlet
 *
 * route.params
 * *outletData: array of data of the outlet
 */
const CreateQueueScreen = (props) => { //port changes til may 31
    const { navigation, route } = props;

    const linkTo = useLinkTo();

    const { height: windowHeight, width: windowWidth } = useWindowDimensions();

    const [selectTable, setSelectTable] = useState('');
    const [openSelectTable, setOpenSelectTable] = useState(false);
    const [remark, setRemark] = useState('');
    const [tableModalVisibility, setTableModalVisibility] = useState(false);
    const [selectedOutletTableId, setSelectedOutletTableId] = useState('');
    const [seatingModal, setSeatingModal] = useState(false);
    const [selectTableModal, setSelectTableModal] = useState(false);

    const [filteredOutletTablesForRendered, setFilteredOutletTablesForRendered] =
        useState([]);

    const selectedOutlet = CommonStore.useState((s) => s.selectedOutlet);

    const firebaseUid = UserStore.useState((s) => s.firebaseUid);
    const name = UserStore.useState((s) => s.name);
    const phone = UserStore.useState((s) => s.number);
    const email = UserStore.useState((s) => s.email);
    const outletsOpeningDict = CommonStore.useState((s) => s.outletsOpeningDict);
    const selectedOutletTable = CommonStore.useState((s) => s.selectedOutletTable,);
    const outletSections = CommonStore.useState((s) => s.selectedOutletSections);
    const selectedOutletSection = CommonStore.useState((s) => s.selectedOutletSection,);
    const outletTables = CommonStore.useState((s) => s.selectedOutletTables);

    // const [queue_Name, setQueue_Name] = useState(name ? name : "");
    // const [queue_Email, setQueue_Email] = useState(email? email : "");
    // const [queue_Phone, setQueue_Phone] = useState(phone? phone : "");

    const [queue_Name, setQueue_Name] = useState("");
    const [queue_Email, setQueue_Email] = useState("");
    const [queue_Phone, setQueue_Phone] = useState("");

    //const [detailsExist, setDetailsExist] = useState(false);

    const [rev_date, setRev_date] = useState(
        moment().format('DD/MM/YYYY'),
    );
    const [rev_time, setRev_time] = useState(moment().format('h.mmA'));

    navigation.setOptions({
        headerLeft: () => (
            <TouchableOpacity
                style={{}}
                onPress={() => {
                    props.navigation.goBack();
                }}>
                <View
                    style={{
                        marginLeft: 10,
                        display: 'flex',
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'flex-start',
                    }}>
                    <Ionicons
                        name="chevron-back"
                        size={26}
                        color={Colors.fieldtTxtColor}
                        style={{}}
                    />

                    <Text
                        style={{
                            color: Colors.fieldtTxtColor,
                            fontSize: 16,
                            textAlign: 'center',
                            fontFamily: 'NunitoSans-Regular',
                            lineHeight: 22,
                            marginTop: -1,
                        }}>
                        Back
                    </Text>
                </View>
            </TouchableOpacity>
        ),
        headerRight: () => (
            <TouchableOpacity
                onPress={() => {
                    props.navigation.navigate('Profile');
                }}
                style={{}}>
                <View style={{ marginRight: 15 }}>
                    <Ionicons name="menu" size={30} color={Colors.primaryColor} />
                </View>
            </TouchableOpacity>
        ),
        headerTitle: () => (
            <View
                style={{
                    justifyContent: 'center',
                    alignItems: 'center',
                    bottom: -1,
                }}>
                <Text
                    style={{
                        fontSize: 20,
                        lineHeight: 25,
                        textAlign: 'center',
                        fontFamily: 'NunitoSans-Bold',
                        color: Colors.mainTxtColor,
                    }}>
                    Queue
                </Text>
            </View>
        ),
        headerTintColor: '#000000',
    });

    const [pax, setPax] = useState(0);
    const [outletData, setOutletData] = useState([]);
    const [loading, setLoading] = useState(false);
    const [currentQueue, setCurrentQueue] = useState(0);

    const [queue_pax, setQueue_pax] = useState(0);

    const [queueStatus, setQueueStatus] = useState();

    const [temp, setTemp] = useState("")

    const setState = () => { };

    // componentDidMount() {
    //   getcurrentQueue()
    // }

    useEffect(() => {
        if (
            outletSections &&
            outletSections.length > 0 &&
            selectedOutletSection.uniqueId === undefined
        ) {
            CommonStore.update((s) => {
                s.selectedOutletSection = outletSections[0];
            });
        }
    }, [outletSections.length]);

    useEffect(() => {
        if (name) {
            setQueue_Name(name);
        }
        if (email) {
            setQueue_Email(email);
        }
        if (phone) {
            setQueue_Phone(phone);
        }
    })

    useEffect(() => {
        if (selectedOutletSection.uniqueId) {
            var filteredOutletTablesTemp = outletTables.filter(
                (table) => table.outletSectionId === selectedOutletSection.uniqueId,
            );

            filteredOutletTablesTemp.sort((a, b) => {
                if (a.joinedTables && b.joinedTables) {
                    return b.joinedTables.length - a.joinedTables.length;
                } else if (a.joinedTables && !b.joinedTables) {
                    return -1;
                } else if (!a.joinedTables && b.joinedTables) {
                    return 1;
                } else if (!a.joinedTables && !b.joinedTables) {
                    return 0;
                }
            });

            const MAX_COL_NUM = 6;

            var filteredOutletTablesTempFirstPass = [];

            if (filteredOutletTablesTemp && filteredOutletTablesTemp.length > 0) {
                filteredOutletTablesTempFirstPass = [filteredOutletTablesTemp[0]];

                if (filteredOutletTablesTemp && filteredOutletTablesTemp.length > 1) {
                    var colRemaining = MAX_COL_NUM;
                    if (filteredOutletTablesTemp[0].joinedTables) {
                        colRemaining -= filteredOutletTablesTemp[0].joinedTables.length;
                    } else {
                        colRemaining -= 1;
                    }

                    for (var i = 1; i < filteredOutletTablesTemp.length; i++) {
                        const currCol = i % MAX_COL_NUM;

                        const currTable = filteredOutletTablesTemp[i];

                        console.log(currTable);

                        if (colRemaining <= 0) {
                            // start with new row

                            colRemaining = MAX_COL_NUM;

                            filteredOutletTablesTempFirstPass.push(currTable);
                        } else if (colRemaining > 0) {
                            if (!currTable.joinedTables) {
                                // squeeze unjoined table

                                filteredOutletTablesTempFirstPass.push(currTable);
                            } else if (
                                currTable.joinedTables &&
                                currTable.joinedTables.length <= colRemaining
                            ) {
                                // squeeze remaining tables

                                filteredOutletTablesTempFirstPass.push(currTable);
                            } else {
                                for (var j = 0; j < colRemaining; j++) {
                                    filteredOutletTablesTempFirstPass.push({
                                        emptyLayout: true,
                                    });
                                }

                                colRemaining = MAX_COL_NUM;

                                filteredOutletTablesTempFirstPass.push(currTable);
                            }
                        }

                        if (currTable.joinedTables) {
                            colRemaining -= currTable.joinedTables.length;
                        } else {
                            colRemaining -= 1;
                        }
                    }
                }
            }

            // setFilteredOutletTables(filteredOutletTablesTemp);
            setFilteredOutletTablesForRendered(filteredOutletTablesTempFirstPass);
        }

        if (selectedOutletTable && selectedOutletTable.uniqueId) {
            CommonStore.update((s) => {
                s.selectedOutletTable =
                    outletTables.find(
                        (table) => table.uniqueId === selectedOutletTable.uniqueId,
                    ) || {};
            });
        }
    }, [
        selectedOutletSection,
        outletTables,
        // userOrdersTableDict,
    ]);

    useEffect(() => {
        if (linkTo) {
            DataStore.update((s) => {
                s.linkToFunc = linkTo;
            });

            global.linkToFunc = linkTo;
        }

        // console.log('passed selectedOutlet', selectedOutlet)
        // console.log('route', route)
        // console.log('route.params', route.params)
        //console.log('route.params.outletData', route.params.outletData)

        if (route.params === undefined) {
            //console.log("OH DAMN")
            // linkTo && linkTo(`${prefix}/error`);

            if (selectedOutlet && selectedOutlet.subdomain) {
                linkTo && linkTo(`${prefix}/outlet/${selectedOutlet.subdomain}`);
            }
            else {
                linkTo && linkTo(`${prefix}/error`);
            }
        } else {
            var subdomain = '';

            if (selectedOutlet == null) {
                subdomain = route.params.subdomain;
            }
            else {
                subdomain = selectedOutlet.subdomain;
            }

            if (subdomain) {
                // means all got, can login this user to check all info valid or not

                // firebase
                //     .auth()
                //     .signInAnonymously()

                try {
                    signInAnonymously(global.auth)
                        .then((result) => {
                            // TempStore.update(s => {
                            //     s.firebaseAuth = true;
                            // });

                            const firebaseUid = result.user.uid;

                            ApiClient.GET(API.getTokenKWeb + firebaseUid).then(
                                async (result) => {
                                    console.log("getTokenKWeb");
                                    console.log(result);

                                    if (result && result.token) {
                                        await AsyncStorage.setItem("accessToken", result.token);
                                        await AsyncStorage.setItem(
                                            "refreshToken",
                                            result.refreshToken
                                        );

                                        global.accessToken = result.token;

                                        UserStore.update((s) => {
                                            s.firebaseUid = result.userId;
                                            s.userId = result.userId;
                                            s.role = result.role;
                                            s.refreshToken = result.refreshToken;
                                            s.token = result.token;
                                            s.name = '';
                                            s.email = '';
                                            s.number = '';
                                        });

                                        var outletSnapshot = null;

                                        if (subdomain) {
                                            if (subdomain === "192") {
                                                // outletSnapshot = await firebase
                                                //     .firestore()
                                                //     .collection(Collections.Outlet)
                                                //     .where(
                                                //         "uniqueId",
                                                //         "==",
                                                //         "b422c1d9-d30b-4de7-ad49-2e601d950919"
                                                //     )
                                                //     .limit(1)
                                                //     .get();

                                                outletSnapshot = await getDocs(
                                                    query(
                                                        collection(global.db, Collections.Outlet),
                                                        where(
                                                            "uniqueId",
                                                            "==",
                                                            "b422c1d9-d30b-4de7-ad49-2e601d950919"
                                                        ),
                                                        limit(1),
                                                    )
                                                );
                                            } else {
                                                // outletSnapshot = await firebase
                                                //     .firestore()
                                                //     .collection(Collections.Outlet)
                                                //     .where("subdomain", "==", subdomain)
                                                //     .limit(1)
                                                //     .get();

                                                outletSnapshot = await getDocs(
                                                    query(
                                                        collection(global.db, Collections.Outlet),
                                                        where("subdomain", "==", subdomain),
                                                        limit(1),
                                                    )
                                                );
                                            }
                                        } else {
                                            console.log("web scan 3");
                                            linkTo && linkTo(`${prefix}/error`);
                                        }

                                        var outlet = {};
                                        if (!outletSnapshot.empty) {
                                            outlet = outletSnapshot.docs[0].data();
                                        }

                                        if (
                                            outlet &&
                                            (outlet.subdomain === subdomain || subdomain === "192")
                                        ) {
                                            // show pax modal before proceed

                                            await AsyncStorage.setItem(
                                                "latestOutletId",
                                                outlet.uniqueId
                                            );
                                            await AsyncStorage.setItem("latestSubdomain", subdomain);

                                            document.title = outlet.name;
                                            document.getElementsByTagName("META")[2].content =
                                                outlet.address;

                                            // CommonStore.update(
                                            //     (s) => {
                                            //         s.selectedOutlet = outlet;
                                            //     }
                                            // );
                                        } else {
                                            console.log("web scan 3");
                                            linkTo && linkTo(`${prefix}/error`);
                                        }
                                    } else {
                                        CommonStore.update((s) => {
                                            s.alertObj = {
                                                title: "Error",
                                                message: "Unauthorized access",
                                            };
                                        });

                                        console.log("web scan 4");
                                        linkTo && linkTo(`${prefix}/error`);
                                    }
                                }
                            );
                        });
                }
                catch (ex) {
                    console.error(ex);
                }

            } else {
                //console.log("OH SHIT")
                linkTo && linkTo(`${prefix}/error`);
            }
        }
    }, [linkTo, route]);

    // function here
    const getcurrentQueue = () => {
        ApiClient.GET(API.getAllUserQueue + User.getUserId()).then((result) => {
            setState({ currentQueue: result.length });
        });
    };

    const createQueue = async () => {
        console.log('selectedOutlet', selectedOutlet);

        if (!selectedOutlet) {
            return;
        }

        if (queue_pax == 0 || !queue_pax) {
            alert(
                'Error: Please key in all the infomation of queue');
            return;
        } else if (currentQueue > 0) {
            alert(
                'Error: You are already in a queue');
            return;
        } else {
            /* else if (queueStatus == false) {
              Alert.alert("Error", "Queue is not available for now", [
                { text: "OK", onPress: () => { } }
              ],
                { cancelable: false });
              return;
            }   */
            setLoading(true);

            // ApiClient.POST(API.queue, body).then((result) => {
            //   console.log(result)
            //   User.getRefreshCurrentAction();
            //   setState({ loading: false })

            //   if (!result.error) { props.navigation.navigate("ConfirmQueue", { queueResult: result, outletData: outletData }); }
            // });

            var merchant = {};
            var outlet = selectedOutlet;

            // const merchantSnapshot = await firebase.firestore()
            //     .collection(Collections.Merchant)
            //     .where('uniqueId', '==', outlet.merchantId)
            //     .limit(1)
            //     .get();

            const merchantSnapshot = await getDocs(
                query(
                    collection(global.db, Collections.Merchant),
                    where('uniqueId', '==', outlet.merchantId),
                    limit(1),
                )
            );

            var existingQueue = {};

            // const queueCheck = await firebase.firestore()
            //     .collection(Collections.UserQueue)
            //     .where('userPhone', '==', queue_Phone)
            //     .limit(1)
            //     .get();

            const queueCheck = await getDocs(
                query(
                    collection(global.db, Collections.UserQueue),
                    where('userPhone', '==', queue_Phone),
                    limit(1),
                )
            );

            if (!merchantSnapshot.empty) {
                merchant = merchantSnapshot.docs[0].data();
            }

            var body = {
                outletId: selectedOutlet.uniqueId,
                pax: queue_pax,
                userId: firebaseUid,

                merchantId: merchant.uniqueId,
                outletCover: outlet.cover,
                merchantLogo: merchant.logo,
                outletName: outlet.name,
                merchantName: merchant.name,

                userName: queue_Name,
                userPhone: queue_Phone,
                userEmail: queue_Email,

                userIdAnonymous: global.userIdAnonymous,
            };

            console.log('body', body);
            var isValidQueue = false;

            const newRev = moment(`${rev_date} ${rev_time}`, 'DD/MM/YYYY hh.mmA');

            try {
                const currDay = mondayFirst(moment(newRev).day());

                var outletOpeningToday = null;

                var startTimeStr = null;
                var endTimeStr = null;

                const outletOpening = outletsOpeningDict[selectedOutlet.uniqueId];
                if (outletOpening) {
                    outletOpeningToday = outletOpening[WEEK[currDay]];
                }

                if (outletOpeningToday) {
                    startTimeStr = outletOpeningToday.split('-')[0];
                    endTimeStr = outletOpeningToday.split('-')[1];

                    const startDateTime = moment(
                        `${rev_date} ${startTimeStr}`,
                        'DD/MM/YYYY HHmm',
                    );
                    const endDateTime = moment(
                        `${rev_date} ${endTimeStr}`,
                        'DD/MM/YYYY HHmm',
                    );

                    console.log('isSameOrAfter', moment(newRev).isSameOrAfter(startDateTime));
                    console.log('newRev', moment(newRev));
                    console.log('startDateTime', moment(startDateTime));
                    console.log('isBefore', moment(newRev).isBefore(endDateTime));

                    isValidQueue =
                        moment(newRev).isSameOrAfter(startDateTime) &&
                        moment(newRev).isBefore(endDateTime);
                }

                console.log('outletOpeningToday', outletOpeningToday);

            } catch (ex) {
                console.error(ex);
            }

            if (queueCheck.empty) {
                //create queue
                if (isValidQueue && selectedOutlet.queueStatus === true) {
                    ApiClient.POST(API.queue, body).then((result) => {
                        setLoading(false);

                        console.log("result", result);

                        if (result && result.uniqueId) {
                            if (window.confirm('Your queue has been added') == true) {
                                navigation.navigate("Queue Confirmation - KooDoo Web Order", {
                                    queueResult: result,
                                    outletData: selectedOutlet,
                                });
                            }
                        }
                    });
                } else {
                    alert('Notice: Queue is not available for now.');

                    setLoading(false);
                }
            }
            else {
                if (window.confirm('You are already in queue') == true) {

                    existingQueue = queueCheck.docs[0].data();
                    setLoading(false);

                    navigation.navigate("Queue Confirmation - KooDoo Web Order", {
                        queueResult: existingQueue,
                        outletData: selectedOutlet,
                    });

                }
            }
        }
    };

    const dummyData2 = [
        {
            uniqueId: '1',
            joinedTables: null,
            capacity: 4,
            code: 'alalaa',
            seated: 0,
        },
        {
            uniqueId: '2',
            joinedTables: null,
            capacity: 6,
            code: 'HOHO',
            seated: 0,
        },
        {
            uniqueId: '3',
            joinedTables: null,
            capacity: 24,
            code: 'alalaa',
            seated: 0,
        },
        {
            uniqueId: '4',
            joinedTables: null,
            capacity: 6,
            code: 'HOHO',
            seated: 2,
        },
        {
            uniqueId: '5',
            joinedTables: null,
            capacity: 4,
            code: 'alalaa',
            seated: 0,
        },
        {
            uniqueId: '6',
            joinedTables: null,
            capacity: 6,
            code: 'HOHO',
            seated: 2,
        },
        {
            uniqueId: '7',
            joinedTables: null,
            capacity: 4,
            code: 'alalaa',
            seated: 0,
        },
        {
            uniqueId: '8',
            joinedTables: null,
            capacity: 6,
            code: 'HOHO',
            seated: 2,
        },
        {
            uniqueId: '9',
            joinedTables: null,
            capacity: 4,
            code: 'alalaa',
            seated: 0,
        },
        {
            uniqueId: '10',
            joinedTables: null,
            capacity: 6,
            code: 'HOHO',
            seated: 2,
        },
        {
            uniqueId: '11',
            joinedTables: null,
            capacity: 4,
            code: 'alalaa',
            seated: 0,
        },
        {
            uniqueId: '12',
            joinedTables: null,
            capacity: 6,
            code: 'HOHO',
            seated: 2,
        },
    ];

    const renderTableLayout = ({ item, index }) => {
        if (item.code != null && item.capacity != null && item.seated == 0) {
            // not seated tables
            return (
                <TouchableOpacity
                    style={[
                        styles.emptyTableDisplay,
                        {
                            alignItems: 'center',
                            justifyContent: 'center',
                            zIndex: 10,
                            borderRadius: 8,
                            //backgroundColor: 'blue',
                            ...(item.joinedTables && {
                                width:
                                    (item.joinedTables.length == 2
                                        ? Dimensions.get('screen').height *
                                        0.1 *
                                        item.joinedTables.length
                                        : item.joinedTables.length == 3
                                            ? Dimensions.get('screen').height *
                                            0.1 *
                                            item.joinedTables.length
                                            : item.joinedTables.length == 4
                                                ? Dimensions.get('screen').height *
                                                0.1 *
                                                item.joinedTables.length
                                                : Dimensions.get('screen').height * 0.1) +
                                    (item.joinedTables.length - 1) * 20,
                            }),
                        },
                    ]}
                    onPress={() => {
                        CommonStore.update((s) => {
                            s.selectedOutletTable = item;
                        });
                        setSelectedOutletTableId(item.code);
                        setTableModalVisibility(false);
                        setSeatingModal(true);
                    }}>
                    <View
                        style={[
                            styles.emptyTableDisplay,
                            {
                                shadowOpacity: 0,
                                shadowColor: '#000',
                                shadowOffset: {
                                    width: 0,
                                    height: 2,
                                },

                                backgroundColor: Colors.primaryColor,
                                shadowOpacity: 0.22,
                                shadowRadius: 3.22,
                                elevation: 3,
                                borderRadius: 8,
                                ...(item.joinedTables && {
                                    width:
                                        (item.joinedTables.length == 2
                                            ? Dimensions.get('screen').height *
                                            0.1 *
                                            item.joinedTables.length
                                            : item.joinedTables.length == 3
                                                ? Dimensions.get('screen').height *
                                                0.1 *
                                                item.joinedTables.length
                                                : item.joinedTables.length == 4
                                                    ? Dimensions.get('screen').height *
                                                    0.1 *
                                                    item.joinedTables.length
                                                    : Dimensions.get('screen').height * 0.1) +
                                        (item.joinedTables.length - 1) * 20,
                                }),
                            },
                        ]}>
                        <View
                            style={{
                                flex: 1,
                                justifyContent: 'space-between',
                            }}>
                            <View
                                style={{
                                    flexDirection: 'row',
                                    justifyContent: 'space-between',
                                }}>
                                <Text>-</Text>

                                <View
                                    style={{
                                        //flex:0.2,
                                        alignItems: 'flex-end',
                                    }}>
                                    <Text
                                        style={[styles.tableCode, { fontSize: 11 }]}
                                        numberOfLines={1}
                                        ellipsizeMode="tail">
                                        {item.code}
                                    </Text>
                                </View>
                            </View>

                            <View style={{ alignItems: 'center' }}>
                                <Text style={[styles.tableCode, { fontSize: 13 }]}>
                                    Seats: {item.seated}/{item.capacity}
                                </Text>
                            </View>
                            <View style={{ alignItems: 'flex-end' }}>
                                <Text>-</Text>
                            </View>
                        </View>
                    </View>
                </TouchableOpacity>
            );
        } else if (item.seated > 0) {
            return (
                <TouchableOpacity
                    style={[
                        styles.emptyTableDisplay,
                        {
                            backgroundColor: Colors.secondaryColor,
                            shadowOpacity: 0,
                            shadowColor: '#000',
                            shadowOffset: {
                                width: 0,
                                height: 2,
                            },
                            shadowOpacity: 0.22,
                            shadowRadius: 3.22,
                            elevation: 3,
                            borderRadius: 8,

                            ...(item.joinedTables && {
                                // width: Dimensions.get('screen').width * 0.135 * (item.joinedTables.length),
                                width:
                                    (item.joinedTables.length == 2
                                        ? Dimensions.get('screen').height *
                                        0.1 *
                                        item.joinedTables.length
                                        : item.joinedTables.length == 3
                                            ? Dimensions.get('screen').height *
                                            0.1 *
                                            item.joinedTables.length
                                            : item.joinedTables.length == 4
                                                ? Dimensions.get('screen').height *
                                                0.1 *
                                                item.joinedTables.length
                                                : Dimensions.get('screen').height * 0.1) +
                                    (item.joinedTables.length - 1) * 20,
                            }),
                        },
                    ]}
                    onPress={() => {
                        alert('Failed: Table seated.');
                    }}>
                    <View
                        style={{
                            flex: 1,
                            justifyContent: 'space-between',
                        }}>
                        <View
                            style={{
                                flexDirection: 'row',
                                justifyContent: 'space-between',
                            }}>
                            <Text>-</Text>

                            <View
                                style={{
                                    //flex:0.2,
                                    alignItems: 'flex-end',
                                }}>
                                <Text
                                    style={[
                                        styles.tableCode,
                                        {
                                            color: Colors.whiteColor,
                                            fontSize: 11,
                                        },
                                    ]}
                                    numberOfLines={1}
                                    ellipsizeMode="tail">
                                    {item.code}
                                </Text>
                            </View>
                        </View>

                        <View style={{ alignItems: 'center' }}>
                            <Text
                                style={[
                                    {
                                        fontSize: 13,
                                        color: Colors.whiteColor,
                                        fontFamily: 'NunitoSans-Bold',
                                    },
                                ]}>
                                Seats: {item.seated}/{item.capacity}
                            </Text>
                        </View>
                        <View style={{ alignItems: 'flex-end' }}>
                            <Text>-</Text>
                        </View>
                    </View>
                </TouchableOpacity>
            );
        }
    };

    const dummyData1 = [
        {
            uniqueId: '1',
            sectionName: 'asd',
        },
        {
            uniqueId: '2',
            sectionName: 'lalala',
        },
        {
            uniqueId: '3',
            sectionName: 'asd',
        },
        {
            uniqueId: '4',
            sectionName: 'lalala',
        },
        {
            uniqueId: '5',
            sectionName: 'asd',
        },
        {
            uniqueId: '6',
            sectionName: 'lalala',
        },
    ];

    const renderSection = ({ item }) => {
        return (
            <View style={{ flexDirection: 'row' }}>
                <TouchableOpacity
                    onPress={() => {
                        CommonStore.update((s) => {
                            s.selectedOutletSection = item;
                        });
                    }}>
                    <View
                        style={[
                            styles.sectionAreaButton,
                            item.uniqueId == selectedOutletSection.uniqueId
                                ? { backgroundColor: Colors.primaryColor }
                                : null,
                        ]}>
                        <Text
                            style={[
                                styles.sectionAreaButtonTxt,
                                item.uniqueId == selectedOutletSection.uniqueId
                                    ? { color: Colors.whiteColor }
                                    : null,
                                { fontSize: 10 },
                            ]}>
                            {item.sectionName}
                        </Text>
                    </View>
                </TouchableOpacity>
            </View>
        );
    };

    const renderTableList = () => {
        return (
            <View
                style={{
                    paddingHorizontal: Dimensions.get('screen').width * 0.02,
                    paddingVertical: Dimensions.get('screen').height * 0.01,
                }}>
                <TouchableOpacity
                    onPress={() => {
                        setSelectTableModal(false);
                        setSelectedOutletTableId('tableID');
                        setSelectTable('something');
                        setTableModalVisibility(true);
                    }}>
                    <Text
                        style={{
                            fontSize: 16,
                        }}>
                        item here
                    </Text>
                </TouchableOpacity>
            </View>
        );
    };

    // function

    return (
        <ScrollView style={styles.container} keyboardShouldPersistTaps="handled">
            {/* modal */}
            <Modal visible={selectTableModal} transparent={true}>
                <View
                    style={[
                        styles.modalContainerTable,
                        {
                            // zIndex: 1000,
                        },
                    ]}>
                    <View style={[styles.modalViewTable, {}]}>
                        <FlatList
                            data={renderTableList}
                            renderItem={renderTableList}
                            keyExtractor={(item, index) => String(index)}
                            style={{
                                maxHeight: Dimensions.get('screen').height * 0.7,
                                minHeight: Dimensions.get('screen').height * 0.1,
                            }}
                        />
                        <TouchableOpacity
                            onPress={() => {
                                setSelectTableModal(false);
                                setSelectedOutletTableId('tableID');
                                setSelectTable('something');
                                setTableModalVisibility(true);
                            }}>
                            <Text
                                style={{
                                    fontSize: 16,
                                }}>
                                dummy data
                            </Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </Modal>
            <Modal
                style={{
                    flex: 1,
                }}
                visible={tableModalVisibility}
                transparent={true}
                animationType={'slide'}>
                <View
                    style={{
                        flex: 1,
                        backgroundColor: Colors.modalBgColor,
                        alignItems: 'center',
                        justifyContent: 'center',
                    }}>
                    <View
                        style={{
                            backgroundColor: Colors.whiteColor,
                            padding: Dimensions.get('screen').height * 0.03,
                            height: '90%',
                            width: '90%',
                            borderRadius: 12,
                        }}>
                        <TouchableOpacity
                            style={[styles.closeButton]}
                            onPress={() => {
                                setTableModalVisibility(false);
                            }}>
                            <AntDesign
                                name="closecircle"
                                size={25}
                                color={Colors.fieldtTxtColor}
                            />
                        </TouchableOpacity>
                        <View
                            style={{
                                height: 40,
                            }}>
                            {/* <FlatList
                contentContainerStyle={{
                  height: '100%',
                  alignItems: 'center',
                  width: '85%',
                }}
                data={outletSections}
                renderItem={renderSection}
                keyExtractor={(item, index) => String(index)}
                horizontal={true}
                showsHorizontalScrollIndicator={false}
                style={[
                  {
                    width: '93%',
                    // width: 50,
                    height: 100,
                  },
                ]}
                contentContainerStyle={{
                  // width: '100%',

                  // marginRight: 100,

                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  // paddingTop: 30,
                  // paddingLeft: 5,
                  // paddingRight: 70
                  paddingRight: 20,
                }}
              /> */}
                            <FlatList
                                contentContainerStyle={{
                                    height: '100%',
                                    alignItems: 'center',
                                    width: '85%',
                                    paddingRight: 20,
                                    display: 'flex',
                                    justifyContent: 'center',
                                }}
                                data={outletSections}
                                renderItem={renderSection}
                                keyExtractor={(item, index) => String(index)}
                                horizontal={true}
                                showsHorizontalScrollIndicator={false}
                                style={[
                                    {
                                        width: '93%',
                                        // width: 50,
                                        height: 100,
                                    },
                                ]}
                            />
                        </View>
                        <View
                            style={[
                                {
                                    // paddingLeft: Dimensions.get('screen').width * 0.04,
                                    paddingTop: Dimensions.get('screen').height * 0.01,
                                    paddingBottom: Dimensions.get('screen').height * 0.01,
                                    //marginLeft: Dimensions.get('screen').width * 0.04,
                                    width: Dimensions.get('screen').width * 0.8,
                                    height: Dimensions.get('screen').height * 0.62,
                                    display: 'flex',
                                    justifyContent: 'center',
                                    flexDirection: 'row',
                                    // backgroundColor: 'red',
                                },
                            ]}>
                            <FlatList
                                data={filteredOutletTablesForRendered}
                                showsVerticalScrollIndicator={false}
                                renderItem={renderTableLayout}
                                keyExtractor={(item, index) => String(index)}
                                // numColumns={6}
                                contentContainerStyle={{
                                    // backgroundColor: 'red',
                                    paddingBottom: Dimensions.get('screen').height * 0.05,
                                    // justifyContent: 'center',
                                    // alignItems: 'center',
                                    flexDirection: 'row',
                                    flexWrap: 'wrap',
                                    // justifyContent: 'center',
                                }}
                                keyboardShouldPersistTaps="handled"
                                maxToRenderPerBatch={1}
                            />
                        </View>
                        <View
                            style={{
                                flex: 1,
                                alignItems: 'center',
                            }}>
                            <View
                                style={{
                                    justifyContent: 'space-around',
                                    height: '100%',
                                }}>
                                <View
                                    style={{
                                        flexDirection: 'row',
                                        alignItems: 'center',
                                    }}>
                                    {/* rectangle */}
                                    <View
                                        style={{
                                            backgroundColor: Colors.primaryColor,
                                            color: Colors.whiteColor,
                                            width: Dimensions.get('screen').width * 0.04,
                                            height: Dimensions.get('screen').height * 0.02,
                                        }}></View>
                                    <Text
                                        style={{
                                            fontSize: 16,
                                            fontFamily: 'NunitoSans-Bold',
                                            paddingLeft: Dimensions.get('screen').width * 0.01,
                                        }}>
                                        Available
                                    </Text>
                                </View>
                                <View
                                    style={{
                                        flexDirection: 'row',
                                        alignItems: 'center',
                                    }}>
                                    {/* rectangle */}
                                    <View
                                        style={{
                                            backgroundColor: Colors.secondaryColor,
                                            color: Colors.whiteColor,
                                            width: Dimensions.get('screen').width * 0.04,
                                            height: Dimensions.get('screen').height * 0.02,
                                        }}></View>
                                    <Text
                                        style={{
                                            fontSize: 16,
                                            fontFamily: 'NunitoSans-Bold',
                                            paddingLeft: Dimensions.get('screen').width * 0.01,
                                        }}>
                                        Not Available
                                    </Text>
                                </View>
                            </View>
                        </View>
                    </View>
                </View>
            </Modal>
            <Modal
                supportedOrientations={['landscape', 'portrait']}
                style={{ flex: 1 }}
                visible={seatingModal}
                transparent={true}>
                <View
                    style={{
                        flex: 1,
                        backgroundColor: Colors.modalBgColor,
                        alignItems: 'center',
                        justifyContent: 'center',
                    }}>
                    <View
                        style={[
                            {
                                width: Dimensions.get('screen').height * 0.3,
                                height: Dimensions.get('screen').width * 0.8,
                                backgroundColor: Colors.whiteColor,
                                borderRadius: 12,
                                padding: Dimensions.get('screen').height * 0.04,
                                alignItems: 'center',
                                justifyContent: 'space-between',
                                paddingTop: Dimensions.get('screen').width * 0.06,
                            },
                        ]}>
                        <TouchableOpacity
                            style={[
                                styles.closeButton,
                                {
                                    position: 'absolute',
                                    top: Dimensions.get('screen').height * 0.02,
                                    right: Dimensions.get('screen').height * 0.02,
                                },
                            ]}
                            onPress={() => {
                                setSeatingModal(false);
                            }}>
                            <AntDesign
                                name="closecircle"
                                size={25}
                                color={Colors.fieldtTxtColor}
                            />
                        </TouchableOpacity>
                        <View
                            style={
                                (styles.modalTitle,
                                {
                                    alignItems: 'center',
                                })
                            }>
                            <Text
                                style={[
                                    styles.modalTitleText,
                                    {
                                        fontSize: 16,
                                        fontFamily: 'NunitoSans-SemiBold',
                                    },
                                ]}>
                                Table
                            </Text>
                            <View
                                style={[
                                    styles.tableSlotDisplay,
                                    {
                                        // width: selectedOutletTable.code.length <= 3 ? Dimensions.get('screen').width * 0.1 : selectedOutletTable.code.length <= 6 ? Dimensions.get('screen').width * 0.22 : selectedOutletTable.code.length <= 10 ? Dimensions.get('screen').width * 0.25 : Dimensions.get('screen').width * 0.1 ,
                                        ...(selectedOutletTable.code &&
                                            selectedOutletTable.code.length && {
                                            width: 27 * selectedOutletTable.code.length,
                                        }),
                                        minWidth: 140,
                                        maxWidth: 200,
                                        height: Dimensions.get('screen').height * 0.1,
                                        marginHorizontal: 5,
                                        paddingHorizontal: 0,
                                    },
                                ]}>
                                <Text
                                    style={[
                                        styles.modalTitleText,
                                        {
                                            fontSize: 14,
                                            fontFamily: 'NunitoSans-SemiBold',
                                        },
                                    ]}>
                                    {selectedOutletTable.code}
                                </Text>
                            </View>
                        </View>
                        <View
                            style={[
                                styles.modalBody,
                                {
                                    width: '100%',
                                    alignItems: 'center',
                                    right: Dimensions.get('screen').height * 0.016,
                                },
                            ]}>
                            <View
                                style={{
                                    flexDirection: 'row',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    width: '100%',
                                }}>
                                <View style={{ flex: 1 }} />
                                <View
                                    style={[
                                        {
                                            justifyContent: 'space-between',
                                            flexDirection: 'row',
                                            flex: 1.0,
                                            borderWidth: StyleSheet.hairlineWidth,
                                            borderColor: Colors.fieldtTxtColor,
                                            width: Dimensions.get('screen').height * 0.08,
                                            // borderWidth: 4
                                            left: Dimensions.get('screen').height * 0.016,
                                        },
                                    ]}>
                                    <TouchableOpacity
                                        style={{
                                            backgroundColor: Colors.primaryColor,
                                            width: Dimensions.get('screen').height * 0.02,
                                            height: Dimensions.get('screen').height * 0.025,
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                        }}
                                        onPress={() => {
                                            //queue_pax >= 1 ? setQueue_pax(queue_pax - 1) : null;
                                        }}>
                                        <Text
                                            style={[
                                                {
                                                    fontSize: 14,
                                                    fontFamily: 'NunitoSans-SemiBold',
                                                    color: Colors.whiteColor,
                                                    bottom: Dimensions.get('screen').width * 0.002,
                                                },
                                            ]}>
                                            -
                                        </Text>
                                    </TouchableOpacity>
                                    <View
                                        style={[
                                            {
                                                alignItems: 'center',
                                                justifyContent: 'center',
                                                width: '30%',
                                                right: Dimensions.get('screen').height * 0.026,
                                            },
                                        ]}>
                                        <Text
                                            style={[
                                                {
                                                    fontFamily: 'NunitoSans-Bold',
                                                    color: Colors.primaryColor,
                                                    fontSize: 14,
                                                },
                                            ]}>
                                            {queue_pax}
                                        </Text>
                                    </View>
                                    <TouchableOpacity
                                        style={[
                                            {
                                                backgroundColor: Colors.primaryColor,
                                                width: Dimensions.get('screen').height * 0.02,
                                                height: Dimensions.get('screen').height * 0.025,
                                                alignItems: 'center',
                                                justifyContent: 'center',
                                                position: 'absolute',
                                                right: 0,
                                            },
                                        ]}
                                        onPress={() => {
                                            // queue_pax <
                                            //     selectedOutletTable.capacity - selectedOutletTable.seated
                                            //     ? setQueue_pax(queue_pax + 1)
                                            //     : null;
                                        }}>
                                        <Text
                                            style={[
                                                {
                                                    fontFamily: 'NunitoSans-Bold',
                                                    fontSize: 14,
                                                    color: Colors.whiteColor,
                                                    bottom: Dimensions.get('screen').width * 0.002,
                                                },
                                            ]}>
                                            +
                                        </Text>
                                    </TouchableOpacity>
                                </View>
                                <View style={{ flex: 1, alignItems: 'center' }}>
                                    <Text
                                        style={[
                                            {
                                                fontFamily: 'NunitoSans-SemiBold',
                                                fontSize: 10,
                                                left: Dimensions.get('screen').height * 0.02,
                                            },
                                        ]}>
                                        Capacity{' '}
                                        {selectedOutletTable.capacity - selectedOutletTable.seated}
                                    </Text>
                                </View>
                            </View>
                        </View>

                        <View
                            style={{
                                alignItems: 'center',
                                flexDirection: 'row',
                                justifyContent: 'center',
                                width: '100%',
                            }}>
                            <TouchableOpacity
                                style={[
                                    {
                                        justifyContent: 'center',
                                        flexDirection: 'row',
                                        borderWidth: 1,
                                        borderColor: Colors.primaryColor,
                                        backgroundColor: '#4E9F7D',
                                        borderRadius: 5,
                                        maxWidth: Dimensions.get('screen').height * 0.2,
                                        paddingHorizontal: 10,
                                        maxHeight: Dimensions.get('screen').width * 0.15,
                                        alignItems: 'center',
                                        shadowOffset: {
                                            width: 0,
                                            height: 2,
                                        },
                                        shadowOpacity: 0.22,
                                        shadowRadius: 3.22,
                                        elevation: 1,
                                        zIndex: -1,
                                    },
                                ]}
                                onPress={() => {
                                    setSelectTable('existed');
                                    setTableModalVisibility(false);
                                    setSeatingModal(false);
                                }}>
                                <Text
                                    style={[
                                        {
                                            color: Colors.whiteColor,
                                            //marginLeft: 5,
                                            fontSize: 16,
                                            fontFamily: 'NunitoSans-Bold',
                                        },
                                    ]}>
                                    SELECT TABLE
                                </Text>
                            </TouchableOpacity>
                        </View>
                    </View>
                </View>
            </Modal>
            <View style={{ flex: 1, width: windowWidth * 0.5, alignSelf: 'center' }}>
                <Text
                    style={[
                        styles.description,
                        {
                            fontSize: 17,
                        },
                    ]}>
                    Name
                </Text>
                <TextInput
                    style={{
                        height: 45,
                        paddingHorizontal: 25,
                        backgroundColor: Colors.fieldtBgColor,
                        borderRadius: 10,
                        marginTop: 10,
                        fontFamily: 'NunitoSans-Regular',
                        fontSize: 14,
                        lineHeight: 20,
                        //paddingTop: Dimensions.get('screen').height * 0.01,
                    }}
                    onFocus={() => {
                        setTemp(queue_Name);
                        setQueue_Name("");
                    }}
                    ///////////////////////////////////////////////
                    //When textinput is not selected
                    onBlur={() => {
                        if (queue_Name == "") {
                            setQueue_Name(temp);
                        }
                    }}
                    onChangeText={(text) => {
                        setQueue_Name(text);
                    }}
                    defaultValue={queue_Name}
                    placeholder='Enter Name'>

                </TextInput>
            </View>
            <View style={{ flex: 1, width: windowWidth * 0.5, alignSelf: 'center' }}>
                <Text
                    style={[
                        styles.description,
                        {
                            fontSize: 17,
                        },
                    ]}>
                    Email
                </Text>
                <TextInput
                    style={{
                        height: 45,
                        paddingHorizontal: 25,
                        backgroundColor: Colors.fieldtBgColor,
                        borderRadius: 10,
                        marginTop: 10,
                        fontFamily: 'NunitoSans-Regular',
                        fontSize: 14,
                        lineHeight: 20,
                        //paddingTop: Dimensions.get('screen').height * 0.01,
                    }}
                    onFocus={() => {
                        setTemp(queue_Email);
                        setQueue_Email("");
                    }}
                    ///////////////////////////////////////////////
                    //When textinput is not selected
                    onBlur={() => {
                        if (queue_Email == "") {
                            setQueue_Email(temp);
                        }
                    }}
                    onChangeText={(text) => {
                        setQueue_Email(text);
                    }}
                    defaultValue={queue_Email}
                    placeholder='Enter Email Address'>

                </TextInput>
            </View>
            <View style={{ flex: 1, width: windowWidth * 0.5, alignSelf: 'center' }}>
                <Text
                    style={[
                        styles.description,
                        {
                            fontSize: 17,
                        },
                    ]}>
                    Phone No.
                </Text>
                <TextInput
                    style={{
                        height: 45,
                        paddingHorizontal: 25,
                        backgroundColor: Colors.fieldtBgColor,
                        borderRadius: 10,
                        marginTop: 10,
                        fontFamily: 'NunitoSans-Regular',
                        fontSize: 14,
                        lineHeight: 20,
                        //paddingTop: Dimensions.get('screen').height * 0.01,
                    }}
                    onFocus={() => {
                        setTemp(queue_Phone);
                        setQueue_Phone("");
                    }}
                    ///////////////////////////////////////////////
                    //When textinput is not selected
                    onBlur={() => {
                        if (queue_Phone == "") {
                            setQueue_Phone(temp);
                        }
                    }}
                    onChangeText={(text) => {
                        setQueue_Phone(text);
                    }}
                    defaultValue={queue_Phone}
                    placeholder='Enter Phone No.'>

                </TextInput>
            </View>
            <View style={{ flex: 1, width: windowWidth * 0.5, alignSelf: 'center' }}>
                <Text
                    style={[
                        styles.description,
                        {
                            fontSize: 17,
                        },
                    ]}>
                    Number of Person (pax)
                </Text>
                <TextInput
                    style={{
                        height: 45,
                        paddingHorizontal: 25,
                        backgroundColor: Colors.fieldtBgColor,
                        borderRadius: 10,
                        marginTop: 10,
                        fontFamily: 'NunitoSans-Regular',
                        fontSize: 14,
                        lineHeight: 20,
                        //paddingTop: Dimensions.get('screen').height * 0.01,
                    }}
                    onFocus={() => {
                        setTemp(queue_pax);
                        setQueue_pax("");
                    }}
                    ///////////////////////////////////////////////
                    //When textinput is not selected
                    onBlur={() => {
                        if (queue_pax == "") {
                            setQueue_pax(parseInt(temp));
                        }
                    }}
                    onChangeText={(text) => {
                        setQueue_pax(text);
                    }}
                    defaultValue={queue_pax}
                    placeholder='Enter No. of Pax'>

                </TextInput>
            </View>
            <TouchableOpacity
                disabled={loading}
                onPress={() => {
                    createQueue();
                }}>
                <View
                    style={[
                        {
                            flexDirection: 'row',
                            justifyContent: 'center',
                            alignItems: 'center',
                            borderRadius: 10,
                            left: 0,
                            backgroundColor: '#4E9F7D',
                            borderWidth: 1,
                            borderColor: Colors.primaryColor,
                            shadowOffset: {
                                width: 0,
                                height: 2,
                            },
                            shadowOpacity: 0.22,
                            shadowRadius: 3.22,
                            marginTop: Dimensions.get('screen').height * 0.05,
                            width: windowWidth * 0.5,
                            alignSelf: 'center',
                        },
                    ]}>
                    <Text
                        style={{
                            color: 'white',
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: 17,
                            paddingVertical: 10,
                        }}>
                        {!loading ? 'CONFIRM' : 'LOADING...'}
                    </Text>
                </View>
            </TouchableOpacity>
        </ScrollView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#ffffff',
        padding: 16,
    },
    outletCover: {
        width: '100%',
        alignSelf: 'center',
        height: undefined,
        aspectRatio: 2,
        borderRadius: 5,
    },
    infoTab: {
        backgroundColor: Colors.fieldtBgColor,
        padding: 16,
        justifyContent: 'center',
        alignContent: 'center',
        alignItems: 'center',
    },
    workingHourTab: {
        padding: 16,
        flexDirection: 'row',
    },
    outletAddress: {
        textAlign: 'center',
        color: Colors.mainTxtColor,
    },
    outletName: {
        fontWeight: 'bold',
        fontSize: 20,
        marginBottom: 10,
    },
    logo: {
        width: 100,
        height: 100,
    },
    actionTab: {
        flexDirection: 'row',
        marginTop: 20,
    },
    actionView: {
        width: Styles.width / 4,
        height: Styles.width / 4,
        justifyContent: 'center',
        alignItems: 'center',
        alignContent: 'center',
    },
    actionBtn: {
        borderRadius: 50,
        width: 70,
        height: 70,
        borderColor: Colors.secondaryColor,
        borderWidth: StyleSheet.hairlineWidth,
        justifyContent: 'center',
        alignItems: 'center',
    },
    actionText: {
        fontSize: 12,
        marginTop: 10,
    },
    textInput: {
        height: 45,
        paddingHorizontal: 25,
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 10,
        marginTop: 10,
        fontFamily: 'NunitoSans-Regular',
        fontSize: 14,
        lineHeight: 20,
    },
    modalContainerTable: {
        flex: 1,
        backgroundColor: Colors.modalBgColor,
        alignItems: 'center',
        justifyContent: 'center',
    },
    modalViewTable: {
        width: Dimensions.get('screen').width * 0.65,
        backgroundColor: Colors.whiteColor,
        borderRadius: Dimensions.get('screen').width * 0.03,
        padding: 20,
        alignItems: 'center',
        justifyContent: 'center',
    },
    closeButton: {
        position: 'absolute',
        right: Dimensions.get('screen').width * 0.02,
        top: Dimensions.get('screen').width * 0.02,
        elevation: 1000,
        zIndex: 1000,
    },
    description: {
        paddingVertical: 5,
        fontSize: 19,
        marginTop: 20,
        fontFamily: 'NunitoSans-Bold',
    },
    sectionAreaButton: {
        marginLeft: 10,
        // width: Dimensions.get('screen').width * 0.085,
        backgroundColor: Colors.whiteColor,
        height: Dimensions.get('screen').height * 0.04,
        borderRadius: 8,
        justifyContent: 'flex-start',
        alignItems: 'center',
        flexDirection: 'row',

        paddingLeft: 15,

        paddingRight: 35,

        elevation: 0,
        shadowOpacity: 0,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 3,
    },
    sectionAreaButtonTxt: {
        fontFamily: 'NunitoSans-SemiBold',
        fontSize: 14,
        color: Colors.primaryColor,
        textAlign: 'center',
    },
    tableSlotDisplay: {
        width: Dimensions.get('screen').height * 0.12,
        height: Dimensions.get('screen').height * 0.12,
        margin: 12,
        borderRadius: 8,
        padding: Dimensions.get('screen').width * 0.01,
        borderWidth: 1,
        borderStyle: 'dashed',
        borderColor: Colors.fieldtTxtColor,
        alignItems: 'center',
        justifyContent: 'center',
    },
    emptyTableDisplay: {
        backgroundColor: Colors.whiteColor,
        width: Dimensions.get('screen').height * 0.12,
        height: Dimensions.get('screen').height * 0.12,
        margin: 10,
        //borderRadius: Dimensions.get('screen').width * 0.02,
        padding: Dimensions.get('screen').width * 0.01,
    },
    tableCode: {
        fontFamily: 'NunitoSans-Bold',
        fontSize: 18,
    },
});
export default CreateQueueScreen;
