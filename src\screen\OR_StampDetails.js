import React, { Component, useReducer, useState, useEffect, useRef } from 'react';
import {
  StyleSheet,
  ScrollView,
  Image,
  View,
  Text,
  TouchableOpacity,
  TextInput,
  FlatList,
  RefreshControl,
  Alert,
  Modal,
  Dimensions,
  Platform,
  useWindowDimensions,
  ActivityIndicator,
} from 'react-native';
import Colors from '../constant/Colors';
import * as Cart from '../util/Cart';
import * as User from '../util/User';
import Styles from '../constant/Styles';
// import ActionSheet from 'react-native-actionsheet';
import ApiClient from '../util/ApiClient';
import API from '../constant/API';
import Ionicons from 'react-native-vector-icons/Ionicons';
import Icon from 'react-native-vector-icons/Ionicons';
import Icons from 'react-native-vector-icons/Feather';
// import NumericInput from 'react-native-numeric-input';
// import { color } from 'react-native-reanimated';
// import QRCode from 'react-native-qrcode-svg';
import Close from 'react-native-vector-icons/AntDesign';
import { CommonStore } from '../store/commonStore';
import { UserStore } from '../store/userStore';
import moment from 'moment';
import Entypo from 'react-native-vector-icons/Entypo';
import AsyncImage from '../components/asyncImage';
// import LinearGradient from 'react-native-linear-gradient';
import LinearGradient from 'react-native-web-linear-gradient';
import { prefix } from "../constant/env";
import { useFocusEffect, useLinkTo } from "@react-navigation/native";

import FontAwesome from "react-native-vector-icons/FontAwesome";
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";

import { ReactComponent as CircleCheck } from "../asset/svg/Circle-check.svg";
import { ReactComponent as GCalendar } from '../svg/GCalendar.svg';

import { isMobile, } from "../util/commonFuncs";
import { TempStore } from '../store/tempStore';
import { LOYALTY_PROMOTION_TYPE, LOYALTY_GET_TYPE } from "../constant/loyalty";

import AsyncStorage from '@react-native-async-storage/async-storage';
import { DataStore } from '../store/dataStore';
import AntDesign from 'react-native-vector-icons/AntDesign';
import { PROMOTION_TYPE_VARIATION } from '../constant/promotions';

const OR_StampDetails = (props) => {
  const { navigation, route } = props;

  const { width: windowWidth, height: windowHeight } = useWindowDimensions();
  const linkTo = useLinkTo();

  const [cartItem, setCartItem] = useState([]);
  const [refreshing, setRefreshing] = useState(false);
  const [visible, setVisible] = useState(false);
  const [visible1, setVisible1] = useState(false);
  const [isUniqueVoucherIDVisible, setIsUniqueVoucherIDVisible] = useState(false);
  const [value, setValue] = useState('');
  const [voucher, setVoucher] = useState([]);
  const [qrCodeModalVisibility, setQrCodeModalVisibility] = useState(false);
  const [selectedItem, setSelectedItem] = useState({});
  const [expandDetails, setExpandDetails] = useState(false);

  const [productModal, setProductModal] = useState(false);
  //const [pageFrom, setPageFrom] = useState(pageFromParam);

  const [tempIsInOutlet, setTempIsInOutlet] = useState(true);


  const setState = () => { };

  const redeemableStackedLoyaltyStamps = CommonStore.useState(s => s.redeemableStackedLoyaltyStamps);

  const pendingRedeemStackedLoyaltyStamp = CommonStore.useState(s => s.pendingRedeemStackedLoyaltyStamp);

  const selectedOutlet = CommonStore.useState(s => s.selectedOutlet);
  const availableTaggableVouchers = CommonStore.useState(
    (s) => s.availableTaggableVouchers,
  );
  const selectedStamp = CommonStore.useState(s => s.selectedStamp);

  const selectedOutletItems = CommonStore.useState(s => s.selectedOutletItems);

  const selectedOutletItemsSkuDict = CommonStore.useState(s => s.selectedOutletItemsSkuDict);


  //const allOutletsItemsSkuDict = CommonStore.useState(s => s.allOutletsItemsSkuDict);

  /* useEffect(() => {
      if (availableTaggableVouchers !== '' && availableTaggableVouchers.length > 0 ) {
        var availablePromotionDetails = {};

        for (var i = 0; i < availableTaggableVouchers.length; i++) {
        var PromoDetails = {
        campaignName = availableTaggableVouchers[i].campaignName,
        campaignDescription = availableTaggableVouchers[i].campaignDescription
        };

        availablePromotionDetails[availableTaggableVouchers[i].uniqueId] = PromoDetails;
        }

      }
      }); */



  ///////////////////////// Test Functionalities /////////////////////////

  const selectedOutletItemCategoriesDict = CommonStore.useState(s => s.selectedOutletItemCategoriesDict);
  const selectedOutletItemsCategoriesDict = CommonStore.useState(s => s.selectedOutletItemsCategoriesDict);
  const selectedOutletItemCategory = CommonStore.useState(s => s.selectedOutletItemCategory);
  const selectedOutletItemCategories = CommonStore.useState(s => s.selectedOutletItemCategories);

  ///////////////////////// Test Functionalities /////////////////////////

  const [promotionItem, setPromotionItem] = useState({});

  ////////////////////////////////////////////////////////////////////////

  const userPointsBalance = UserStore.useState(s => s.userPointsBalance);

  const userName = UserStore.useState((s) => s.name);
  const userNumber = UserStore.useState((s) => s.number);
  const email = UserStore.useState((s) => s.email);
  const [birthday, setBirthday] = useState(moment(Date.now()));
  const [address, setAddress] = useState('');
  const [lat, setLat] = useState(0);
  const [lng, setLng] = useState(0);

  const isLoading = CommonStore.useState((s) => s.isLoading);
  const linkToFunc = DataStore.useState(s => s.linkToFunc);

  const dummylist = [
    { name: 'test1', price: 10, quantity: 3 }, { name: 'test2', price: 10, quantity: 2 }, { name: 'test3', price: 100, quantity: 10 },
  ]

  // 2024-05-02 - loyalty stamp changes

  const [lsEligibleProducts, setLsEligibleProducts] = useState([]);

  /////////////////////////////////////////////////

  // const tempDes = 'asuifhaidgfhiuadhfuihaduifhiadgfyuagdiuffhiaudhiuahdfhauodhfoadyhfouahdofyhaodhfhioadhfiadf9qaejfiahdfihadihaodhfoadhfoadhfoaef0uadoifhaodhfofahdfouadifhaidhfoaeeidffhaoiddhfoaehfhae9ufjaedifvhaeodghaoisuf93qgvuehgvoaudgfivuoaeusfdoiaehdgfociahjsiodhgiawhsfiovaegsidhxiwadhvuaiuewsjdoujeadghfviaeskhfoaisfgvahoasuifhaidgfhiuadhfuihaduifhiadgfyuagdiuffhiaudhiuahdfhauodhfoadyhfouahdofyhaodhfhioadhfiadf9qaejfiahdfihadihaodhfoadhfoadhfoaef0uadoifhaodhfofahdfouadifhaidhfoaeeidffhaoiddhfoaehfhae9ufjaedifvhaeodghaoisuf93qgvuehgvoaudgfivuoaeusfdoiaehdgfociahjsiodhgiawhsfiovaegsidhxiwadhvuaiuewsjdoujeadghfviaeskhfoaisfgvahoasuifhaidgfhiuadhfuihaduifhiadgfyuagdiuffhiaudhiuahdfhauodhfoadyhfouahdofyhaodhfhioadhfiadf9qaejfiahdfihadihaodhfoadhfoadhfoaef0uadoifhaodhfofahdfouadifhaidhfoaeeidffhaoiddhfoaehfhae9ufjaedifvhaeodghaoisuf93qgvuehgvoaudgfivuoaeusfdoiaehdgfociahjsiodhgiawhsfiovaegsidhxiwadhvuaiuewsjdoujeadghfviaeskhfoaisfgvahoasuifhaidgfhiuadhfuihaduifhiadgfyuagdiuffhiaudhiuahdfhauodhfoadyhfouahdofyhaodhfhioadhfiadf9qaejfiahdfihadihaodhfoadhfoadhfoaef0uadoifhaodhfofahdfouadifhaidhfoaeeidffhaoiddhfoaehfhae9ufjaedifvhaeodghaoisuf93qgvuehgvoaudgfivuoaeusfdoiaehdgfociahjsiodhgiawhsfiovaegsidhxiwadhvuaiuewsjdoujeadghfviaeskhfoaisfgvahoasuifhaidgfhiuadhfuihaduifhiadgfyuagdiuffhiaudhiuahdfhauodhfoadyhfouahdofyhaodhfhioadhfiadf9qaejfiahdfihadihaodhfoadhfoadhfoaef0uadoifhaodhfofahdfouadifhaidhfoaeeidffhaoiddhfoaehfhae9ufjaedifvhaeodghaoisuf93qgvuehgvoaudgfivuoaeusfdoiaehdgfociahjsiodhgiawhsfiovaegsidhxiwadhvuaiuewsjdoujeadghfviaeskhfoaisfgvahoasuifhaidgfhiuadhfuihaduifhiadgfyuagdiuffhiaudhiuahdfhauodhfoadyhfouahdofyhaodhfhioadhfiadf9qaejfiahdfihadihaodhfoadhfoadhfoaef0uadoifhaodhfofahdfouadifhaidhfoaeeidffhaoiddhfoaehfhae9ufjaedifvhaeodghaoisuf93qgvuehgvoaudgfivuoaeusfdoiaehdgfociahjsiodhgiawhsfiovaegsidhxiwadhvuaiuewsjdoujeadghfviaeskhfoaisfgvaho idpxufyhchoaespfuhcoeuadkhfjpawsfh waifhnalesdxfuchahousjfghcjwpasikfgbneasiofkgvhdazxsdftgyuiakdemifuvhfatgeyudsjfnvfgyujhbvcxdfrtyujnbvftgyujhbvcftyhjbvftgyujnbvfghnbvfg'

  // const temp_voucherData = [
  //   { 
  //     point: 2000,
  //     expirationDate: 'product1',
  //     promoDateEnd: '2023-10-27'
  //   },
  // ]

  // const temp_userData = [
  //   { 
  //     point: 2000,
  //     expirationDate: 'product1',
  //     promoDateEnd: '2023-10-27'
  //   },
  // ]

  /* useEffect(() => {
      if (selectedTaggableVoucher && selectedTaggableVoucher.uniqueId) {
          if (promotionItem.uniqueId === undefined) {
              retrievePromotionItem();
          }
      }
  }, [selectedTaggableVoucher, promotionItem]);

  const retrievePromotionItem = async () => {
    const outletSnapshot = await firestore().collection(Collections.OutletItem)
        .where('uniqueId', '==', selectedTaggableVoucher.outletId)
        .limit(1)
        .get();

    if (!outletSnapshot.empty) {
        const outlet = outletSnapshot.docs[0].data();

        setPromotionItem(outlet);
    }
  }; */

  navigation.setOptions({
    headerLeft: () => (
      <TouchableOpacity
        style={{}}
        onPress={() => {
          props.navigation.goBack();
        }}>
        <View
          style={{
            marginLeft: 10,
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'flex-start',
          }}>
          <Ionicons
            name="chevron-back"
            size={26}
            color={Colors.fieldtTxtColor}
            style={{}}
          />

          <Text
            style={{
              color: Colors.fieldtTxtColor,
              fontSize: 16,
              textAlign: 'center',
              fontFamily: 'NunitoSans-Regular',
              lineHeight: 22,
              marginTop: -1,
            }}>
            Back
          </Text>
        </View>
      </TouchableOpacity>
    ),
    headerRight: () => (
      <>
        {/* <TouchableOpacity
        onPress={() => {
          props.navigation.navigate('Profile');
        }}
        style={{}}>
        <View style={{ marginRight: 15 }}>
          <Ionicons name="menu" size={30} color={Colors.primaryColor} />
        </View>
      </TouchableOpacity> */}
      </>
    ),
    headerTitle: () => (
      <View
        style={{
          justifyContent: 'center',
          alignItems: 'center',
          bottom: -1,
        }}>
        <Text
          style={{
            fontSize: 20,
            lineHeight: 25,
            textAlign: 'center',
            fontFamily: 'NunitoSans-Bold',
            color: Colors.mainTxtColor,
          }}>
          Reward Details
        </Text>
      </View>
    ),
  });

  useEffect(() => {
    global.currPageStack = [
      ...global.currPageStack,
      'OR_StampDetails',
    ];
  }, []);

  var itemNameFontSize = 15;

  if (windowWidth <= 360) {
    itemNameFontSize = 13;
    //console.log(windowWidth)
  }

  const itemNameTextScale = {
    fontSize: itemNameFontSize,
  };

  const renderPromotion = ({ item, index }) => {
    return (
      <View>
        {
          item.criteriaList && item.criteriaList.map(outletItemSku => {
            var outletItem = {};

            let foundItem = selectedOutletItems.find(findItem => findItem.sku === outletItemSku.variationItemsSku);

            if (foundItem) {
              outletItem = foundItem;
            }

            return (

              <View style={{ flexDirection: 'column' }}>
                <View
                  style={{
                    flexDirection: 'row',
                    padding: 15,
                    alignItems: 'center',
                    width: '70%',
                  }}>

                  <View>
                    <View style={[{
                      backgroundColor: Colors.secondaryColor,
                      // width: 60,
                      // height: 60,
                      width: windowWidth * 0.22,
                      height: windowWidth * 0.22,
                      borderRadius: 10,
                    }]}>
                      {outletItem.image
                        ?
                        // <Image source={{ uri: item.image }} style={{
                        //   width: windowWidth * 0.22,
                        //   height: windowWidth * 0.22,
                        //   borderRadius: 10
                        // }} />
                        <AsyncImage source={{ uri: outletItem.image }} item={outletItem} style={{
                          width: windowWidth * 0.22,
                          height: windowWidth * 0.22,
                          borderRadius: 10
                        }} />
                        :
                        <Ionicons name="fast-food-outline" size={50} />
                      }
                    </View>
                  </View>
                  <View style={{ flexDirection: 'column' }}>
                    <Text
                      style={{
                        marginLeft: 5,
                        fontSize: 16,
                        fontFamily: 'NunitoSans-Regular',
                      }}
                      numberOfLines={3}
                    >
                      {outletItem.name}
                    </Text>

                    <Text
                      style={[itemNameTextScale, {
                        textTransform:
                          'uppercase',
                        fontFamily: "NunitoSans-Bold",
                      }]} numberOfLines={3}>{outletItem.name}
                    </Text>

                    <View style={{ flexDirection: 'row', marginTop: 10 }}>
                      <Text
                        style={{
                          marginLeft: 5,
                          fontSize: 13,
                          fontFamily: 'NunitoSans-SemiBold',
                        }}>
                        Before: RM7.99
                      </Text>
                      <Text
                        style={{
                          marginLeft: 10,
                          fontSize: 13,
                          fontFamily: 'NunitoSans-SemiBold',
                        }}>
                        After: RM1.99
                      </Text>
                    </View>
                  </View>

                </View>
              </View>
            );
          })}
      </View>
    )
  };

  const handleStampProductClick = async (item) => {
    if (pendingRedeemStackedLoyaltyStamp && pendingRedeemStackedLoyaltyStamp.redeemableItem) {
      CommonStore.update((s) => {
        s.toRedeemStackedLoyaltyStamp = {
          ...pendingRedeemStackedLoyaltyStamp,
          redeemableItem: {
            ...pendingRedeemStackedLoyaltyStamp.redeemableItem,

            price: item.price,

            itemSku: item.sku,

            outletItemSku: item.sku,
            oisBu: pendingRedeemStackedLoyaltyStamp.redeemableItem.outletItemSku,

            itemName: item.name,
            inBu: pendingRedeemStackedLoyaltyStamp.redeemableItem.itemName,

            variation: PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS,
            vBu: pendingRedeemStackedLoyaltyStamp.redeemableItem.variation,

            // getType: 'PERCENTAGE',
          }
        };
      });

      const subdomain = await AsyncStorage.getItem("latestSubdomain");

      if (!subdomain) {
        linkTo && linkTo(`${prefix}/outlet/cart`)
      } else {
        linkTo && linkTo(`${prefix}/outlet/${subdomain}/cart`)
      }

      setProductModal(false);
    }
  };

  const renderProduct = ({ item, index }) => {
    return (
      <TouchableOpacity
        style={{
          flexDirection: "row",
          paddingVertical: 5,
          justifyContent: "flex-start",
          alignItems: "center",
          width: windowWidth * 0.85,
          paddingHorizontal: 5,
        }}
        onPress={() => {
          handleStampProductClick(item);
        }}
      >
        <View
          style={[
            {
              marginRight: 15,
              backgroundColor: Colors.secondaryColor,
              borderRadius: 5,
              alignSelf: "flex-start",
              alignItems: 'center',
              justifyContent: 'center',
              width: windowWidth * 0.1,
            },
          ]}
        >
          {item.image ? (
            <AsyncImage
              source={{ uri: item.image }}
              item={item}
              style={{
                width: 60,
                height: 60,
                borderRadius: 5,
              }}
            />
          ) : (
            <View
              style={{
                width: windowWidth * 0.1,
                height: windowWidth * 0.1,
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <Ionicons
                name="fast-food-outline"
                // size={45}
                size={isMobile() ? windowWidth * 0.06 : 40}
              />
            </View>
          )}
        </View>

        <View style={{ width: '45%', paddingRight: 5, }}>
          <Text
            style={{
              textTransform: "uppercase",
              fontFamily: "NunitoSans-Bold",
            }}
            numberOfLines={3}
          >
            {item.name}
          </Text>
        </View>

        <View
          style={{
            flexDirection: "column",
            justifyContent: "flex-start",
            // alignItems: "center",
            // justifyContent: "center",
            paddingLeft: 5,
            width: '35%',
          }}
        >
          <View style={{ justifyContent: 'space-between', flexDirection: 'row', width: '95%' }}>
            <Text
              style={[
                {
                  color: Colors.primaryColor,
                  fontFamily: "NunitoSans-Bold",
                  fontSize: 16,
                },
              ]}
            >
              {'RM '}
            </Text>

            <Text
              style={[
                {
                  color: Colors.primaryColor,
                  fontFamily: "NunitoSans-Bold",
                  fontSize: 16,
                },
              ]}
              numberOfLines={1}
            >
              {`${item.price.toFixed(2)}`}
            </Text>
          </View>

          {/* <View style={{ justifyContent: 'center', alignItems: 'center' }}>
            <Text
              style={[
                {
                  color: Colors.descriptionColor,
                  fontFamily: "NunitoSans-Regular",
                },
              ]}
              numberOfLines={1}
            >
              {`(x${item.quantity})`}
            </Text>
          </View> */}
        </View>
      </TouchableOpacity>
    )
  };

  // const redeemVoucher = async () => {
  //   if (selectedTaggableVoucher.voucherType !== LOYALTY_PROMOTION_TYPE.FREE_ITEM) {

  //     if (selectedTaggableVoucher.criteriaList.length > 0) {
  //       const firstCriteria = selectedTaggableVoucher.criteriaList[0];

  //       if (firstCriteria.variation === 'SPECIFIC_PRODUCTS') {
  //         // check variationItemsSku which product is required
  //         //variationItemsSku is using outletitem.sku - outletitem = selectedoutletitems 

  //         console.log('first criteria list', firstCriteria.variationItemsSku[0])

  //         CommonStore.update((s) => {
  //           s.selectedTaggableVoucher = selectedTaggableVoucher
  //         })
  //         linkTo && linkTo(`${prefix}/outlet/menu`)
  //       }
  //       else { // mean is PRODUCT_OF_CATEGORY
  //         //variationItemsSku is OutletItemCategory.name
  //         console.log('first criteria list', firstCriteria.variationItemsSku[0])

  //         CommonStore.update((s) => {
  //           s.selectedTaggableVoucher = selectedTaggableVoucher
  //         })

  //         linkTo && linkTo(`${prefix}/outlet/menu`)
  //       }

  //     }
  //     else {
  //       alert('Info, Something went wrong please contact support')
  //     }
  //   }
  // }

  // const claimVoucher = async () => {
  //   var userPhone = userNumber.replaceAll('-', '').replaceAll(' ', '').replaceAll('+', '');

  //   if (!userPhone.startsWith('6')) {
  //     userPhone = '6' + userPhone;
  //   }

  //   // if (userPhone && name) {

  //   // }
  //   // else {
  //   //     alert('Please fill in the name and phone number before proceed.');

  //   //     return;
  //   // }

  //   const body = {
  //     userPhone: userPhone,
  //     outletId: selectedTaggableVoucher.outletId,
  //     merchantId: selectedTaggableVoucher.merchantId,
  //     merchantName: selectedTaggableVoucher.merchantName,
  //     outletName: selectedTaggableVoucher.outletName || selectedOutlet.name,
  //     merchantLogo: selectedTaggableVoucher.merchantLogo || '',
  //     outletCover: selectedTaggableVoucher.outletCover || '',
  //     userName: userName,

  //     dob: moment(birthday).valueOf(),
  //     email: email || '',

  //     address: address,
  //     lat: lat,
  //     lng: lng,

  //     taggableVoucherId: selectedTaggableVoucher.uniqueId,

  //     voucherPoints: -selectedTaggableVoucher.voucherPointsRequired,

  //     // userOrderId: (registerUserOrder.uniqueId && !registerUserOrder.isCashbackClaimed) ? registerUserOrder.uniqueId : null,
  //   };

  //   //////////////////////////////////////

  //   // check validity

  //   if (selectedTaggableVoucher && selectedTaggableVoucher.voucherQuantity > 0) {

  //   }
  //   else {
  //     alert('This voucher has been fully redeemed.');

  //     return;
  //   }

  //   CommonStore.update(s => {
  //     s.isLoading = true;
  //   });

  //   ApiClient.POST(API.claimTaggableVoucherKweb, body).then(async (result) => {
  //     if (result && result.status === "success") {
  //       // const voucherSnapshot = await firebase.firestore().collection(Collections.TaggableVoucher)
  //       //   .where('uniqueId', '==', claimTaggableVoucher.uniqueId)
  //       //   .limit(1)
  //       //   .get();

  //       // 2022-05-08 - No need first
  //       // const voucherSnapshot = await getDocs(
  //       //     query(
  //       //         collection(global.db, Collections.TaggableVoucher),
  //       //         where('uniqueId', '==', selectedClaimVoucher.uniqueId),
  //       //         limit(1),
  //       //     )
  //       // );

  //       // var voucher = null;

  //       // if (!voucherSnapshot.empty) {
  //       //     voucher = voucherSnapshot.docs[0].data();
  //       // }

  //       // if (voucher) {
  //       //     TempStore.update(s => {
  //       //         s.selectedClaimVoucher = voucher;
  //       //     });
  //       // }

  //       TempStore.update(s => {
  //         s.selectedClaimVoucher = null;
  //       });

  //       alert(`${result.message}`);

  //       linkTo && linkTo(`${prefix}/outlet/outlet-rewards`)

  //     }
  //     else {
  //       TempStore.update(s => {
  //         s.selectedClaimVoucher = null;
  //       });

  //       CommonStore.update((s) => {
  //         s.alertObj = {
  //           title: "Error",
  //           message: "Unable to redeem the voucher for now.",
  //         };
  //       });
  //     }

  //     CommonStore.update(s => {
  //       s.isLoading = false;
  //     });
  //   });
  // };

  const [isExpanded, setIsExpanded] = useState(false);

  const toggleReadMore = () => {
    setIsExpanded((prevState) => !prevState);
  };

  const maxLines = 8;

  const containerStyle = {
    maxHeight: isExpanded ? null : maxLines * 18,
    overflow: 'hidden',
  };

  // const[itemImage, setItemImage] = useState('')
  // useEffect(()=> {
  //   selectedOutletItems.find(findItem => findItem.sku === selectedStamp.lsItems[0].outletItemSku).image
  // },[selectedStamp])

  const userLoyaltyStamps = CommonStore.useState((s) => s.userLoyaltyStamps);
  const [currentRedeemItem, setCurrentRedeemItem] = useState({});
  const [remainingStamps, setRemainingStamps] = useState(0);

  useEffect(() => {
    if (userLoyaltyStamps.length < 1)
      linkTo && linkTo(`${prefix}/outlet/outlet-rewards`)
    else {
      let selectedStampUserRecord = userLoyaltyStamps.find(filterStamp => filterStamp.loyaltyStampId === selectedStamp.uniqueId)
      let selectStampItems = selectedStamp.lsItems.slice().sort((a, b) => a.noOfStamp - b.noOfStamp)
      let remainingStamps = selectedStampUserRecord.stampCount;

      console.log('selectedStamp', selectedStamp);
      console.log('selectedStampUserRecord', selectedStampUserRecord);
      console.log('selectStampItems', selectStampItems);


      // for (var i = 0; i < selectedStampUserRecord.getHistory.length; i++) {
      //   remainingStamps -= selectedStampUserRecord.getHistory[i].noOfStamp;
      // }

      let noOfStampToDeduct = 0;
      // for (var i = 0; i < selectedStampUserRecord.getHistory.length; i++) {
      //   // remainingStamps -= selectedStampUserRecord.getHistory[i].noOfStamp;

      //   if (selectedStampUserRecord.getHistory[i].noOfStamp > noOfStampToDeduct) {
      //     noOfStampToDeduct = selectedStampUserRecord.getHistory[i].noOfStamp;
      //   }
      // }

      remainingStamps -= noOfStampToDeduct;

      setRemainingStamps(remainingStamps);

      for (var i = 0; i < selectStampItems.length; i++) {
        if (selectedStampUserRecord.getIdHistory.includes(selectStampItems[i].lsItemId))
          continue;

        setCurrentRedeemItem(selectStampItems[i]);
        break;
      }

      console.log('currentRedeemItem', currentRedeemItem);
    }
  }, [selectedStamp])

  return (
    <>
      <Modal
        style={{ flex: 1 }}
        visible={productModal}
        transparent={true}
        animationType="none"
      >
        <View
          style={{
            backgroundColor: "rgba(0,0,0,0.5)",
            justifyContent: "center",
            alignItems: "center",
            width: windowWidth,
            height: windowHeight,
          }}
        >
          <View style={{}}>

            <TouchableOpacity
              style={[styles.closeButton, {}]}
              onPress={() => {
                setProductModal(false);
              }}>
              <AntDesign name="closecircle" size={25} color={Colors.fieldtTxtColor} />
            </TouchableOpacity>
            <View
              style={[
                {
                  backgroundColor: 'white',
                  width: isMobile()
                    ? windowWidth * 0.9
                    : windowWidth * 0.9,
                  height: isMobile()
                    ? windowHeight * 0.8
                    : windowHeight * 0.8,
                  borderRadius: 5,
                },
              ]}
            >
              <View style={{
                marginTop: 25,
                justifyContent: "center",
                alignItems: 'center',
              }}>
                <Text
                  style={{
                    fontSize: 20,
                    fontFamily: "NunitoSans-Bold",
                  }}
                >
                  Choose Product
                </Text>
              </View>

              <View
                style={{
                  marginTop: 15,
                  justifyContent: "center",
                  alignItems: "center",
                  width: windowWidth * 0.9,
                  alignContent: "center",
                  height: "90%",
                }}
              >
                {!isLoading ? (
                  <FlatList
                    data={lsEligibleProducts}
                    renderItem={renderProduct}
                    keyExtractor={(item, index) => String(index)}
                    contentContainerStyle={{
                      paddingBottom: 20,
                    }}
                  />
                ) : (
                  <ActivityIndicator
                    color={Colors.primaryColor}
                    size={"large"}
                  />
                )}
              </View>
            </View>
          </View>
        </View>
      </Modal>
      {/* {selectedTaggableVoucher && */}
      <ScrollView
        style={{
          height: windowHeight * 0.75
        }}>
        <View style={{ height: '75%' }}>
          <View style={[styles.container1, { height: windowHeight }]}>
            <View style={styles.container}>

              {selectedStamp ?
                !isLoading ? (
                  <>
                    <View
                      style={{
                        backgroundColor: '#ffffff',
                        borderRadius: 20,
                        // width: '100%',
                        // height: '80%',
                        // flexDirection: 'row',
                      }}>
                      <View
                        style={{
                          //borderRadius: 8,
                          alignItems: 'center',
                          backgroundColor: '#ffffff',
                          //borderWidth: 1,
                          // width: '100%',
                          // height: '100%',
                          justifyContent: 'center',
                          // borderRightWidth: 1,
                          borderColor: Colors.fieldtTxtColor,
                        }}>
                        {currentRedeemItem.itemImage ?
                          <>
                            <AsyncImage
                              source={{ uri: currentRedeemItem.itemImage }}
                              item={selectedStamp}
                              style={{
                                width: windowWidth,
                                height: windowWidth * 0.8,
                                marginBottom: 20,
                              }}
                            />
                          </>
                          :
                          <>
                            <View style={{
                              backgroundColor: Colors.secondaryColor,
                              width: windowWidth,
                              height: windowWidth * 0.8,
                              marginBottom: 20,
                              alignSelf: 'center',
                              justifyContent: 'center',
                              alignItems: 'center',
                            }}>
                              <Ionicons name="fast-food-outline" size={200} />
                            </View>
                          </>
                        }
                      </View>
                    </View>

                    <View style={{
                      marginHorizontal: 30,
                      marginBottom: 15,
                    }}>
                      <Text style={{
                        fontSize: 25,
                        fontFamily: "NunitoSans-Bold",
                        marginBottom: 10,
                      }}>
                        {selectedStamp.name}
                      </Text>
                      <View style={{
                        flexDirection: 'row',
                      }}>
                        <Text style={{
                          fontSize: 15,
                          fontFamily: "NunitoSans-Regular",
                          color: Colors.descriptionColor,
                        }}>
                          {`Expires by ${moment(selectedStamp.endDate).format('dddd, Do MMM YYYY')}, ${moment(selectedStamp.endTime).format('hh:mm A')}`}
                        </Text>
                      </View>
                    </View>

                    <View style={{
                      marginHorizontal: 30,
                      marginBottom: 15,
                    }}>
                      <View style={containerStyle}>
                        <Text
                          style={{
                            fontSize: 15,
                            fontFamily: "NunitoSans-Regular",
                          }}>
                          {selectedStamp.terms}
                        </Text>
                      </View>
                      {selectedStamp.terms.length > maxLines * 40 ? (
                        <TouchableOpacity style={{
                          marginTop: 10,
                        }}
                          onPress={toggleReadMore}>
                          <Text style={{
                            color: Colors.primaryColor,
                            alignSelf: 'center',
                            justifyContent: 'center',

                            fontSize: 15,
                            fontFamily: "NunitoSans-Bold",
                          }}>
                            {isExpanded ? 'Read Less' : 'Read More'}
                          </Text>
                        </TouchableOpacity>
                      ) : <></>}
                    </View>

                    <View style={{
                      marginHorizontal: 30,
                      marginBottom: 15,
                    }}>
                      <Text style={{
                        fontSize: 15,
                        fontFamily: "NunitoSans-Bold",
                        marginBottom: 20,
                      }}>
                        Requirements
                      </Text>
                      <View style={{
                        flexDirection: 'row',
                        justifyContent: 'center',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        marginBottom: 20,
                      }}>
                        <View style={{
                          flexDirection: 'row',
                          alignItems: 'center',
                          width: '90%',
                        }}>
                          <FontAwesome
                            name="star"
                            size={31}
                            color={Colors.primaryColor}
                            style={{
                              marginLeft: 3,
                            }}
                          />
                          <Text style={{
                            fontSize: 15,
                            fontFamily: "NunitoSans-Regular",

                            paddingLeft: 50,
                          }}>
                            {currentRedeemItem.getType === LOYALTY_GET_TYPE.AMOUNT ?
                              `${currentRedeemItem.noOfStamp} stamps to redeem ${currentRedeemItem.quantity} ${[currentRedeemItem.itemName].concat(
                                currentRedeemItem.itemNameList ? currentRedeemItem.itemNameList : []
                              ).join(' / ')}${(currentRedeemItem.price > 0 ? ` for RM ${currentRedeemItem.price.toFixed(2)}` : ` for free`)}`
                              :
                              `${currentRedeemItem.noOfStamp} stamps to redeem ${currentRedeemItem.quantity} ${[currentRedeemItem.itemName].concat(
                                currentRedeemItem.itemNameList ? currentRedeemItem.itemNameList : []
                              ).join(' / ')}${(currentRedeemItem.price > 0 ? ` for ${currentRedeemItem.price.toFixed(1)}%` : ` for free`)}`}
                          </Text>
                        </View>
                        <View style={{ width: '10%' }}>
                          <CircleCheck
                            color={remainingStamps >= currentRedeemItem.noOfStamp ? Colors.primaryColor : Colors.tabGrey}
                            style={{
                              // marginRight: 30,
                              width: 30,
                              height: 30,

                              paddingLeft: 10,
                            }}
                          />
                        </View>
                      </View>
                    </View>

                    {/* <View style={{ justifyContent: 'center', alignItems: 'center', }}>
                      <TouchableOpacity
                        onPress={() => {
                          setExpandDetails(!expandDetails);
                        }}>
                        <Entypo
                          name={expandDetails ? 'chevron-up' : 'chevron-down'}
                          size={30}
                          color={Colors.primaryColor}
                        />
                        <Text
                          style={{
                            marginTop: 0,
                            fontSize: 14,
                            fontFamily: 'NunitoSans-Bold',
                            color: Colors.primaryColor,
                          }}>
                          MORE
                        </Text>
                      </TouchableOpacity>
                    </View> */}
                  </>
                ) : (
                  <>
                    <View style={{ justifyContent: 'center', alignSelf: 'center', marginTop: '70%' }}>
                      <ActivityIndicator color={Colors.primaryColor} size={60} />
                    </View>
                  </>
                ) : (
                  <>
                    <View style={{ justifyContent: 'center', alignSelf: 'center', marginTop: '70%' }}>
                      <Text style={{
                        fontSize: 15,
                        fontFamily: "NunitoSans-Regular",
                        color: Colors.descriptionColor,
                      }}>
                        You have not selected any rewards.
                      </Text>
                    </View>
                  </>
                )}
            </View>
          </View>
        </View>
      </ScrollView>
      {selectedStamp ? !isLoading ? (
        <View>
          {!availableTaggableVouchers.includes(selectedStamp) ? (
            tempIsInOutlet ? (
              <TouchableOpacity
                disabled={remainingStamps >= currentRedeemItem.noOfStamp ? false : true}
                onPress={async () => {
                  // setIsUniqueVoucherIDVisible(true)

                  const matchedStampData = redeemableStackedLoyaltyStamps.find(findStamp => findStamp.loyaltyStampId === selectedStamp.uniqueId);

                  if (matchedStampData) {
                    // if (matchedStampData.redeemableItem &&
                    //   matchedStampData.redeemableItem.variation === PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS) {
                    //   CommonStore.update((s) => {
                    //     s.toRedeemStackedLoyaltyStamp = matchedStampData;
                    //   });

                    //   const subdomain = await AsyncStorage.getItem("latestSubdomain");

                    //   if (!subdomain) {
                    //     linkTo && linkTo(`${prefix}/outlet/cart`)
                    //   } else {
                    //     linkTo && linkTo(`${prefix}/outlet/${subdomain}/cart`)
                    //   }
                    // }
                    // else {
                    //   // 2024-05-02 - for redeemeable category's items

                    //   CommonStore.update((s) => {
                    //     s.pendingRedeemStackedLoyaltyStamp = matchedStampData;
                    //   });

                    //   const foundCategory = selectedOutletItemCategories.find(category => category.name === matchedStampData.redeemableItem.itemName);

                    //   if (foundCategory) {
                    //     let lsEligibleProductsTemp = selectedOutletItems.filter(product => product.categoryId === foundCategory.uniqueId);

                    //     lsEligibleProductsTemp = lsEligibleProductsTemp.map(product => {
                    //       let priceNew = product.price;

                    //       if (matchedStampData.redeemableItem === undefined &&
                    //         (matchedStampData.redeemableItem.getType === LOYALTY_GET_TYPE.AMOUNT || matchedStampData.redeemableItem.getType === undefined)
                    //       ) {
                    //         priceNew = matchedStampData.redeemableItem.price;
                    //       }
                    //       else if (matchedStampData.redeemableItem.getType === LOYALTY_GET_TYPE.PERCENTAGE) {
                    //         priceNew = product.price * (matchedStampData.redeemableItem.price / 100);
                    //       }

                    //       priceNew = parseFloat(priceNew.toFixed(2));

                    //       return ({
                    //         ...product,
                    //         price: priceNew,
                    //         quantity: matchedStampData.redeemableItem.quantity,
                    //       });
                    //     });

                    //     setLsEligibleProducts(lsEligibleProductsTemp);

                    //     setProductModal(true);
                    //   }
                    // }

                    // 2024-05-03 - now either product or category items, will be able to choose first

                    CommonStore.update((s) => {
                      s.pendingRedeemStackedLoyaltyStamp = matchedStampData;
                    });

                    if (matchedStampData.redeemableItem &&
                      matchedStampData.redeemableItem.variation === PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS) {
                      // if item(s) based

                      const outletItemSkuListCombined = [matchedStampData.redeemableItem.outletItemSku].concat(matchedStampData.redeemableItem.outletItemSkuList ? matchedStampData.redeemableItem.outletItemSkuList : []);

                      if (outletItemSkuListCombined && outletItemSkuListCombined.length > 0) {
                        let lsEligibleProductsTemp = selectedOutletItems.filter(product => outletItemSkuListCombined.includes(product.sku));

                        lsEligibleProductsTemp = lsEligibleProductsTemp.map(product => {
                          let priceNew = product.price;

                          if (matchedStampData.redeemableItem === undefined &&
                            (matchedStampData.redeemableItem.getType === LOYALTY_GET_TYPE.AMOUNT || matchedStampData.redeemableItem.getType === undefined)
                          ) {
                            priceNew = matchedStampData.redeemableItem.price;
                          }
                          else if (matchedStampData.redeemableItem.getType === LOYALTY_GET_TYPE.PERCENTAGE) {

                            priceNew = product.price * (matchedStampData.redeemableItem.price / 100);

                            // if (product.name === 'Braised Wing') {
                            //   console.log('stop');  
                            // }
                          }

                          priceNew = parseFloat(priceNew.toFixed(2));

                          return ({
                            ...product,
                            price: priceNew,
                            quantity: matchedStampData.redeemableItem.quantity,
                          });
                        });

                        setLsEligibleProducts(lsEligibleProductsTemp);

                        setProductModal(true);
                      }
                    }
                    else {
                      // if category(s) based

                      const itemNameListCombined = [matchedStampData.redeemableItem.itemName].concat(matchedStampData.redeemableItem.itemNameList ? matchedStampData.redeemableItem.itemNameList : []);

                      const foundCategoryList = selectedOutletItemCategories.filter(category => itemNameListCombined.includes(category.name));
                      const foundCategoryIdList = foundCategoryList.map(category => category.uniqueId);

                      if (foundCategoryIdList && foundCategoryIdList.length > 0) {
                        let lsEligibleProductsTemp = selectedOutletItems.filter(product => foundCategoryIdList.includes(product.categoryId));

                        lsEligibleProductsTemp = lsEligibleProductsTemp.map(product => {
                          let priceNew = product.price;

                          if (matchedStampData.redeemableItem === undefined &&
                            (matchedStampData.redeemableItem.getType === LOYALTY_GET_TYPE.AMOUNT || matchedStampData.redeemableItem.getType === undefined)
                          ) {
                            priceNew = matchedStampData.redeemableItem.price;
                          }
                          else if (matchedStampData.redeemableItem.getType === LOYALTY_GET_TYPE.PERCENTAGE) {
                            priceNew = product.price * (matchedStampData.redeemableItem.price / 100);

                            if (product.name === 'Braised Wing') {
                              console.log('stop');
                            }
                          }

                          priceNew = parseFloat(priceNew.toFixed(2));

                          return ({
                            ...product,
                            price: priceNew,
                            quantity: matchedStampData.redeemableItem.quantity,
                          });
                        });

                        setLsEligibleProducts(lsEligibleProductsTemp);

                        setProductModal(true);
                      }
                    }
                  }

                  // alert('Please proceed with the payment method of Pay Now to redeem your voucher');                  

                  // if (selectedTaggableVoucher.voucherType !== LOYALTY_PROMOTION_TYPE.FREE_ITEM) {
                  //   redeemVoucher();
                  // } else {

                  //   CommonStore.update((s) => {
                  //     s.selectedTaggableVoucher = selectedTaggableVoucher;
                  //   });

                  //   // alert('Please proceed with the payment method of Pay Now to redeem your voucher');

                  //   const subdomain = await AsyncStorage.getItem("latestSubdomain");

                  //   if (!subdomain) {
                  //     global.linkToFunc && global.linkToFunc(`${prefix}/outlet/cart`)
                  //   } else {
                  //     global.linkToFunc && global.linkToFunc(`${prefix}/outlet/${subdomain}/cart`)
                  //   }

                  //   CommonStore.update((s) => {
                  //     s.isFromCartPage = false;
                  //   });
                  // }
                }}>
                <View style={{
                  paddingVertical: 12,
                  backgroundColor: remainingStamps >= currentRedeemItem.noOfStamp ? Colors.primaryColor : Colors.tabGrey,
                  padding: 20,
                  borderRadius: 10,
                  alignItems: 'center',
                  marginVertical: 20,
                  width: windowWidth * 0.8,
                  alignSelf: 'center'
                }}>
                  <Text style={{
                    fontSize: isUniqueVoucherIDVisible ? 13 : 15,
                    fontFamily: "NunitoSans-Bold",
                    color: Colors.whiteColor,
                  }}>
                    {isUniqueVoucherIDVisible ?
                      ("REDEEM") : (
                        remainingStamps >= currentRedeemItem.noOfStamp ?
                          ("REDEEM") : (
                            remainingStamps + " / " + currentRedeemItem.noOfStamp + " STAMPS")
                      )}
                  </Text>
                </View>
              </TouchableOpacity>
            ) : (
              <View style={{
                paddingVertical: 12,
                padding: 20,
                alignItems: 'center',
                marginVertical: 20,
                alignSelf: 'center'
              }}>
                <Text
                  style={{
                    fontSize: 15,
                    fontFamily: "NunitoSans-Bold",
                    color: Colors.blackColor,
                  }}
                >
                  Redeemable Through Outlet Menu.
                </Text>
              </View>
            )
          ) :
            <TouchableOpacity
              // disabled={userPointsBalance >= selectedTaggableVoucher.voucherPointsRequired ? false : true}
              onPress={async () => {
                TempStore.update(s => {
                  s.selectedClaimStamp = selectedStamp;
                });
                // claimVoucher();
              }}>
              <View style={{
                paddingVertical: 12,
                backgroundColor: Colors.primaryColor,
                // backgroundColor: userPointsBalance >= selectedTaggableVoucher.voucherPointsRequired ? Colors.primaryColor : Colors.descriptionColor,
                padding: 20,
                borderRadius: 10,
                alignItems: 'center',
                marginVertical: 20,
                width: windowWidth * 0.8,
                alignSelf: 'center'
              }}>
                <Text style={{
                  fontSize: 15,
                  fontFamily: "NunitoSans-Bold",
                  color: Colors.whiteColor,
                }}>
                  {/* {userPointsBalance >= selectedTaggableVoucher.voucherPointsRequired ? 'CLAIM' : 'This reward is locked'} */}
                  CLAIM
                </Text>
              </View>
            </TouchableOpacity>
          }
        </View>
      ) : <></> : <></>}
      {/* } */}
    </>
  );
};

const styles = StyleSheet.create({
  container1: {
    backgroundColor: '#ffffff',
    // backgroundColor: 'red',
  },
  container: {
    backgroundColor: '#ffffff',
    //padding: 16,
  },
  searchBar: {
    marginHorizontal: 16,
    backgroundColor: Colors.fieldtBgColor,
    flexDirection: 'row',
    padding: 12,
    borderRadius: 10,
    alignContent: 'center',
  },
  searchBarText: {
    color: Colors.fieldtTxtColor,
    marginLeft: 10,
  },
  card: {
    flex: 1,
    minWidth: Styles.width - 64,
    minHeight: 100,
    // backgroundColor: Colors.primaryColor,
    backgroundColor: '#416D5C',
    flexDirection: 'row',
    marginTop: 20,
    alignContent: 'center',
    alignItems: 'center',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  card1: {
    flex: 1,
    minWidth: Styles.width - 64,
    minHeight: 100,
    backgroundColor: Colors.fieldtBgColor,
    flexDirection: 'row',
    marginBottom: 20,
    alignContent: 'center',
    alignItems: 'center',
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
    marginBottom: 10,
  },
  cornerleft: {
    backgroundColor: 'white',
    height: 30,
    width: 30,
    alignSelf: 'flex-start',
    marginTop: '22%',
    borderTopRightRadius: 50,
  },
  cornerright: {
    backgroundColor: 'white',
    height: 30,
    width: 30,
    alignSelf: 'flex-start',
    borderBottomRightRadius: 50,

    // borderStyle: 'dashed',
    // borderWidth: 1,
    // borderColor: 'black',
  },

  cornerleft1: {
    backgroundColor: 'white',
    height: 30,
    width: 30,
    alignSelf: 'flex-start',
    marginTop: '22%',
    borderTopLeftRadius: 50,
  },
  cornerright1: {
    backgroundColor: 'white',
    height: 30,
    width: 30,
    alignSelf: 'flex-start',
    borderBottomLeftRadius: 50,
  },
  text: {
    fontSize: 13,
    color: Colors.fieldtTxtColor,
    fontFamily: 'NunitoSans-Bold',
  },
  title: {
    color: Colors.whiteColor,
    // fontSize: Platform.OS == 'ios' ? 30 : 20,
    fontSize: Platform.OS == 'ios' ? 30 : 22,
    fontFamily: 'NunitoSans-SemiBold',
    marginLeft: '1%',
    // marginTop: 10,
    marginBottom: 5,
    textAlign: 'right',
  },
  title1: {
    color: Colors.blackColor,
    fontSize: 16,
    fontFamily: 'NunitoSans-Bold',
  },
  title2: {
    color: Colors.primaryColor,
    fontSize: Platform.OS == 'ios' ? 13 : 15,
    fontFamily: 'NunitoSans-SemiBold',
    // fontWeight: 'bold',
    marginBottom: 8,
  },
  title3: {
    color: Colors.descriptionColor,
    fontSize: Platform.OS == 'ios' ? 13 : 14,
    fontFamily: 'NunitoSans-SemiBold',
    // fontWeight: 'bold',
  },
  viewContainer: {
    marginTop: 10,
    justifyContent: 'space-between',
    flexDirection: 'row',
    flex: 10,
  },
  total: {
    fontSize: 14,
    color: Colors.blackColor,
    fontFamily: 'NunitoSans-Bold',
  },
  price: {
    fontSize: 14,
    color: Colors.primaryColor,
    fontFamily: 'NunitoSans-Regular',
  },
  totalBorder: {
    paddingTop: 5,
    justifyContent: 'space-between',
    flex: 1,
    flexDirection: 'row',
    borderTopWidth: StyleSheet.hairlineWidth,
    borderColor: Colors.blackColor,
    borderRadius: 1,
  },
  pic: {
    backgroundColor: Colors.secondaryColor,
    width: 90,
    height: 90,
    borderRadius: 10,
    alignSelf: 'center',
    justifyContent: 'center',
    alignItems: 'center',
  },
  searchBar1: {
    backgroundColor: Colors.fieldtBgColor,
    flexDirection: 'row',
    padding: 12,
    borderRadius: 10,
    alignContent: 'center',
    marginBottom: '2%',
  },

  centerTextHolder: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  picOutlet: {
    backgroundColor: Colors.secondaryColor,
    width: 45,
    height: 45,
    borderRadius: 10,
    alignSelf: 'center',
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButton: {
    position: 'absolute',
    right: isMobile()
      ? Dimensions.get("window").width * 0.06
      : Dimensions.get("window").width * 0.01,
    top: isMobile()
      ? Dimensions.get("window").width * 0.06
      : Dimensions.get("window").width * 0.01,
    elevation: 1000,
    zIndex: 1000,
  },
});

export default OR_StampDetails;
