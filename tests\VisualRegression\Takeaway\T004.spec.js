const { test, expect } = require('@playwright/test');

test('Operation > Menu > Buy Vouchers', async ({ page }) => {
  // Set the viewports for different devices
  const viewports = [
    { name: 'iPhone 12', width: 1170, height: 2532 },
    { name: 'Pixel 5', width: 1080, height: 2340 },
  ];

    for (const viewport of viewports) {
      await page.goto('http://localhost:5400/web/outlet/hominsanttdi/takeaway');

    await page.waitForTimeout(5000);

    await page.waitForTimeout(500);
    await page.getByTestId('startOrdering').click();

    await page.waitForTimeout(500);
    await page.getByTestId('buyVouchers').click();

    await page.waitForTimeout(5000);

      // Capture the screenshot
      const screenshot = await page.screenshot({ path: `./tests/VisualRegression/Takeaway/Screenshots/${viewport.name}_T004.png`, });

      // Compare the screenshot with the baseline
      await expect(screenshot).toMatchSnapshot({ path: `./tests/VisualRegression/Takeaway/Screenshots/iPhone 12_T004.png`, maxDiffPixelRatio: 0.5 });
    }
  //1st Loop is to get 1st device's screenshot to compare during the 2nd Loop
});

  // Check  playwright.config.js has these
  //projects: [
  //   {
  //     name: 'Pixel5',
  //     use: { ...devices['Pixel 5'] },
  //   },
  //   {
  //     name: 'iPhone12',
  //     use: { ...devices['iPhone 12'] },
  //   },
  // ]




