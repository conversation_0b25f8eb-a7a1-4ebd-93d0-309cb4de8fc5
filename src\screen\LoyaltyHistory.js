import React, { Component, useReducer, useState, useEffect } from 'react';
import {
  StyleSheet,
  ScrollView,
  Image,
  View,
  Text,
  TouchableOpacity,
  TextInput,
  FlatList,
  RefreshControl,
  Alert,
  Modal,
  Dimensions,
  Platform,
  useWindowDimensions,
  InteractionManager,
} from 'react-native';
// import firebase from 'firebase';
import { getFirestore, collection, query, where, getDocs, limit } from "firebase/firestore";
import { signInAnonymously } from "firebase/auth";
import Colors from '../constant/Colors';
// import Autocomplete from 'react-google-autocomplete'
import AntDesign from 'react-native-vector-icons/AntDesign';
import DatePicker from "react-datepicker";
import { ReactComponent as GCalendar } from '../svg/GCalendar.svg'
import "react-datepicker/dist/react-datepicker.css";
import "../constant/datePicker.css";
import * as Cart from '../util/Cart';
import * as User from '../util/User';
import Styles from '../constant/Styles';
// import ActionSheet from 'react-native-actionsheet';
import ApiClient from '../util/ApiClient';
import API from '../constant/API';
import Ionicons from 'react-native-vector-icons/Ionicons';
import Icon from 'react-native-vector-icons/Ionicons';
import Icons from 'react-native-vector-icons/Feather';
// import NumericInput from 'react-native-numeric-input';
// import { color } from 'react-native-reanimated';
// import QRCode from 'react-native-qrcode-svg';
import Close from 'react-native-vector-icons/AntDesign';
import { CommonStore } from '../store/commonStore';
import { UserStore } from '../store/userStore';
import moment from 'moment';
import AsyncImage from '../components/asyncImage';
// import LinearGradient from 'react-native-linear-gradient';
import LinearGradient from 'react-native-web-linear-gradient';
import { googleCloudApiKey, prefix } from "../constant/env";
import { useFocusEffect, useLinkTo } from "@react-navigation/native";
import { DataStore } from '../store/dataStore';
import Hashids from 'hashids';
import { ORDER_REGISTER_QR_SALT } from '../constant/common';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Collections } from '../constant/firebase';
import IconAnt from 'react-native-vector-icons/AntDesign';
import { isMobile, setRouteFrom, mondayFirst, listenToGlobalPointsChanges } from "../util/commonFuncs";
import imgLogo from '../asset/image/logo.png';

import { ReactComponent as Arrow } from '../svg/arrow.svg';
import { Switch } from 'react-native-web';
import { LOYALTY_CAMPAIGN_CREDIT_TRANSACTION_TYPE_PARSED } from '../constant/loyalty';

import MultiSelect from "react-multiple-select-dropdown-lite";
import "../constant/styles.css";
import { TableStore } from '../store/tableStore';
import { PaymentStore } from '../store/paymentStore';
import { idbGet } from '../util/db';
import { TempStore } from '../store/tempStore';

const hashids = new Hashids(ORDER_REGISTER_QR_SALT);

const LoyaltyHistory = (props) => {
  const { navigation, route } = props;
  const { height: windowHeight, width: windowWidth } = useWindowDimensions();

  const linkTo = useLinkTo();

  const [blockSMS, setBlockSMS] = useState(false);
  const [blockEmail, setBlockEmail] = useState(false);

  const loyaltyHistoryCrmUser = CommonStore.useState(s => s.loyaltyHistoryCrmUser);
  const loyaltyHistoryLCCTransactions = CommonStore.useState(s => s.loyaltyHistoryLCCTransactions);
  const loyaltyHistoryLCCBalance = CommonStore.useState(s => s.loyaltyHistoryLCCBalance);
  const email = UserStore.useState((s) => s.email);
  const name = UserStore.useState((s) => s.name);
  const userPhone = UserStore.useState(s => s.number);
  const outletData = CommonStore.useState(s => s.selectedOutlet);
  const userGlobalPointsTransactions = UserStore.useState(s => s.userGlobalPointsTransactions)
  const userGlobalPointsBalance = UserStore.useState(s => s.userGlobalPointsBalance);

  const selectedOutlet = CommonStore.useState((s) => s.selectedOutlet);

  const availableTaggableVouchers = CommonStore.useState((s) => s.availableTaggableVouchers,);
  const availableUserTaggableVouchers = CommonStore.useState((s) => s.availableUserTaggableVouchers,);
  const selectedCustomerGlobalPointsTransactionsEmail = UserStore.useState((s) => s.selectedCustomerGlobalPointsTransactionsEmail);
  const selectedCustomerGlobalPointsBalanceEmail = UserStore.useState((s) => s.selectedCustomerGlobalPointsBalanceEmail);
  const selectedCustomerGlobalPointsTransactionsPhone = UserStore.useState((s) => s.selectedCustomerGlobalPointsTransactionsPhone);
  const selectedCustomerGlobalPointsBalancePhone = UserStore.useState((s) => s.selectedCustomerGlobalPointsBalancePhone);
  const userOutletDict = UserStore.useState((s) => s.userOutletDict);
  const visitedOutletIdList = UserStore.useState((s) => s.visitedOutletIdList);
  const visitedOutletNameList = UserStore.useState((s) => s.visitedOutletNameList);

  const selectOutletOption = visitedOutletNameList.map((items, index) => ({ label: items, value: visitedOutletIdList[index] }));
  const [outletDropdown, setOutletDropdown] = useState([]);
  const [selectOutletToShowPoint, setSelectOutletToShowPoint] = useState([]);

  const isLoading = CommonStore.useState(s => s.isLoading);

  navigation.setOptions({
    headerTitle: () => (
      <View
        style={{
          justifyContent: 'center',
          alignItems: 'center',
          bottom: -1,
        }}>
        <Text
          style={{
            fontSize: 20,
            lineHeight: 25,
            textAlign: 'center',
            fontFamily: 'NunitoSans-Bold',
            color: Colors.mainTxtColor,
          }}>
          Rewards
        </Text>
      </View>
    ),
  });

  // const [firstName, setFirstName] = useState('');
  // const [lastName, setLastName] = useState('');
  // const [name, setName] = useState('');
  // const [phone, setPhone] = useState('');
  // const [birthday, setBirthday] = useState(moment(Date.now()));
  // const [address, setAddress] = useState('');
  // const [lat, setLat] = useState(0);
  // const [lng, setLng] = useState(0);
  const [switchMerchant, setSwitchMerchant] = useState(false);

  const [verifySMSModal, setVerifySMSModal] = useState(false);
  const [verificationCode, setVerificationCode] = useState('');
  const [isVerified, setIsVerified] = useState(false);

  const [showDOBPicker, setShowDOBPicker] = useState(false); //For DOB
  const [rev_DOBdate, setRev_DOBdate] = useState(moment(Date.now()));

  const [outletId, setOutletId] = useState('');
  const [outletName, setOutletName] = useState('');
  const [outletCover, setOutletCover] = useState('');
  const [merchantId, setMerchantId] = useState('');
  const [merchantName, setMerchantName] = useState('');

  const [sentCode, setSentCode] = useState(false);
  const [seeHistory, setSeeHistory] = useState(false); //For testing UI purpose

  useEffect(() => {
    if (linkTo) {
      DataStore.update(s => {
        s.linkToFunc = linkTo;
      });

      global.linkToFunc = linkTo;
    }

    console.log('route');
    console.log(route);
    console.log('window.location.href');
    console.log(window.location.href);

    if (route.params === undefined ||
      // route.params.outletId === undefined ||
      // route.params.orderId === undefined ||
      // route.params.tableCode === undefined ||
      // route.params.tablePax === undefined ||
      // route.params.waiterId === undefined ||
      // route.params.outletId.length !== 36 ||
      // route.params.tableId.length !== 36 ||
      // route.params.qrDateTimeEncrypted === undefined
      route.params.userIdHuman == undefined
    ) {
      // still valid, can proceed to register user

      linkTo && linkTo(`${prefix}/error`);

      console.log('general');
    }
    else {
      try {
        // firebase.auth().signInAnonymously()
        signInAnonymously(global.auth)
          .then((result) => {
            // TempStore.update(s => {
            //   s.firebaseAuth = true;
            // });

            const firebaseUid = result.user.uid;

            ApiClient.GET(API.getTokenKWeb + firebaseUid).then(async (result) => {
              console.log('getTokenKWeb');
              console.log(result);

              if (result && result.token) {
                await AsyncStorage.setItem('accessToken', result.token);
                await AsyncStorage.setItem('refreshToken', result.refreshToken);

                global.accessToken = result.token;

                UserStore.update(s => {
                  s.firebaseUid = result.userId;
                  s.userId = result.userId;
                  s.role = result.role;
                  s.refreshToken = result.refreshToken;
                  s.token = result.token;
                  s.name = '';
                  s.email = '';
                  s.number = '';
                });

                // CommonStore.update(s => {
                //   s.selectedOutletTableQRUrl = window.location.href;
                // });

                // const crmUserSnapshot = await firebase.firestore().collection(Collections.CRMUser)
                //   .where('userIdHuman', '==', route.params.userIdHuman)
                //   .limit(1)
                //   .get();

                const crmUserSnapshot = await getDocs(
                  query(
                    collection(global.db, Collections.CRMUser),
                    where('userIdHuman', '==', route.params.userIdHuman),
                    limit(1),
                  )
                );

                var crmUser = null;

                if (!crmUserSnapshot.empty) {
                  crmUser = crmUserSnapshot.docs[0].data();
                }

                if (crmUser) {
                  await AsyncStorage.setItem('loyaltyHistoryCrmUserIdHuman', route.params.userIdHuman);

                  setBlockSMS(crmUser.blockSMS || false);
                  setBlockEmail(crmUser.blockEmail || false);

                  // const outletSnapshot = await firebase.firestore().collection(Collections.Outlet)
                  //   .where('uniqueId', '==', crmUser.outletId)
                  //   .limit(1)
                  //   .get();

                  const outletSnapshot = await getDocs(
                    query(
                      collection(global.db, Collections.Outlet),
                      where('uniqueId', '==', crmUser.outletId),
                      limit(1),
                    )
                  );

                  var outlet = {};
                  if (!outletSnapshot.empty) {
                    outlet = outletSnapshot.docs[0].data();
                  }

                  if (outlet) {
                    // valid, can proceed to register user

                    setOutletId(outlet.uniqueId);
                    setOutletName(outlet.name);
                    setOutletCover(outlet.cover);
                    setMerchantId(outlet.merchantId);
                    setMerchantName(crmUser.merchantName);

                    CommonStore.update(s => {
                      s.loyaltyHistoryCrmUser = crmUser;
                    });

                    //////////////////////////////////////////

                    // const loyaltyCampaignCreditTransactionSnapshot = await firebase.firestore()
                    //   .collection(Collections.LoyaltyCampaignCreditTransaction)
                    //   .where('email', '==', crmUser.email)
                    //   .where('outletId', '==', outlet.uniqueId)
                    //   .where('deletedAt', '==', null)
                    //   .get();

                    const loyaltyCampaignCreditTransactionSnapshot = await getDocs(
                      query(
                        collection(global.db, Collections.LoyaltyCampaignCreditTransaction),
                        where('email', '==', crmUser.email),
                        where('outletId', '==', outlet.uniqueId),
                        where('deletedAt', '==', null),
                      )
                    );

                    var loyaltyHistoryLCCTransactionsTemp = [];
                    var loyaltyHistoryLCCBalanceTemp = 0;

                    if (!loyaltyCampaignCreditTransactionSnapshot.empty) {
                      for (var i = 0; i < loyaltyCampaignCreditTransactionSnapshot.size; i++) {
                        const record = loyaltyCampaignCreditTransactionSnapshot.docs[i].data();

                        loyaltyHistoryLCCTransactionsTemp.push(record);
                        loyaltyHistoryLCCBalanceTemp += record.amount;
                      }
                    }

                    loyaltyHistoryLCCTransactionsTemp.sort((a, b) => b.createdAt - a.createdAt);

                    CommonStore.update(s => {
                      s.loyaltyHistoryLCCTransactions = loyaltyHistoryLCCTransactionsTemp;
                      s.loyaltyHistoryLCCBalance = loyaltyHistoryLCCBalanceTemp;

                      s.selectedOutlet = outlet;
                    });

                    //////////////////////////////////////////

                    // CommonStore.update(s => {
                    //   s.registerUserOrder = userOrder;
                    // });

                    // await AsyncStorage.setItem('latestOutletId', outlet.uniqueId);

                    // await updateUserCart(
                    //   {
                    //     ...route.params,
                    //     outletId: outlet.uniqueId,
                    //     tableId: tableId,
                    //     tablePax: outletTable.seated,
                    //     tableCode: outletTable.code,
                    //   }
                    //   , outlet, firebaseUid);

                    // linkTo && linkTo('/web/outlet');
                  }
                  else {
                    linkTo && linkTo(`${prefix}/error`);
                  }
                }
                else {
                  linkTo && linkTo(`${prefix}/error`);
                }
              }
              else {
                CommonStore.update(s => {
                  s.alertObj = {
                    title: 'Error',
                    message: 'Unauthorized access',
                  };

                  // s.isAuthenticating = false;
                });

                linkTo && linkTo(`${prefix}/error`);
              }
            });
          });
      }
      catch (ex) {
        console.error(ex);
      }
    }
  }, [linkTo, route]);

  useEffect(() => {
    window.addEventListener(
      "popstate",
      async (e) => {
        // e.preventDefault();
        // console.log('unload!');

        // linkTo(`${prefix}/outlet/menu`);

        const loyaltyHistoryCrmUserIdHuman = await AsyncStorage.getItem("loyaltyHistoryCrmUserIdHuman");

        if (!loyaltyHistoryCrmUserIdHuman) {
          linkTo && linkTo(`${prefix}/error`);
        } else {
          linkTo && linkTo(`${prefix}/rewards/${loyaltyHistoryCrmUserIdHuman}`);
        }
      },
      false
    );

    window.addEventListener(
      "load",
      (e) => {
        // e.preventDefault();
        // console.log('unload!');

        // linkTo('/web/outlet/menu');

        setTimeout(() => {
          readStates();
        }, 1000);

        setTimeout(() => {
          readCommonStates();
        }, 1000);
      },
      false
    );

    // window.onpopstate = e => {
    //   e.preventDefault();
    //   console.log('unload!');
    // }
  }, []);

  useEffect(() => {
    CommonStore.update(s => {
      s.availableTaggableVouchers = availableUserTaggableVouchers;
    });
  }, [availableUserTaggableVouchers]);

  useEffect(() => {
    setOutletDropdown(userOutletDict.map((item) => ({
      label: item.visitedOutletNameList,
      value: item.visitedOutletIdList,
    })))
  }, [userOutletDict])

  useEffect(() => {
    if (outletDropdown.length > 0) {
      setSelectOutletToShowPoint([outletDropdown[0]]);
    }
  }, [outletDropdown])

  useEffect(() => {
    if (linkTo) {
      DataStore.update((s) => {
        s.linkToFunc = linkTo;
      });

      global.linkToFunc = linkTo;
    }
  }, [linkTo]);

  useEffect(() => {
    if (selectedOutlet === null) {
      readStates();
    }

    readCommonStates();
  }, [selectedOutlet]);

  useEffect(() => {
    if (selectOutletToShowPoint) {

      typeof global.subscriberListenToGlobalPointsChangess === 'function' && global.subscriberListenToGlobalPointsChanges();
      global.subscriberListenToGlobalPointsChanges = () => { };

      let subscriber = listenToGlobalPointsChanges(email, userPhone, selectOutletToShowPoint);

      global.subscriberListenToGlobalPointsChanges = subscriber;

      return () => {
        typeof subscriber === 'function' && subscriber();
      };
    }
  }, [email, userPhone, selectOutletToShowPoint, outletDropdown]);

  useEffect(() => {
    InteractionManager.runAfterInteractions(() => {
      var userGlobalPointsTransactions = [];
      var userGlobalPointsBalance = 0;
      console.log('length finder', selectedCustomerGlobalPointsTransactionsEmail.length)


      for (var i = 0; i < selectedCustomerGlobalPointsTransactionsEmail.length; i++) {
        if (!userGlobalPointsTransactions.find(item => item.uniqueId === selectedCustomerGlobalPointsTransactionsEmail[i].uniqueId)) {
          // means not exist

          userGlobalPointsTransactions.push(selectedCustomerGlobalPointsTransactionsEmail[i]);
          userGlobalPointsBalance += selectedCustomerGlobalPointsTransactionsEmail[i].amount;
        }
      }

      for (var i = 0; i < selectedCustomerGlobalPointsTransactionsPhone.length; i++) {
        if (!userGlobalPointsTransactions.find(item => item.uniqueId === selectedCustomerGlobalPointsTransactionsPhone[i].uniqueId)) {
          // means not exist

          userGlobalPointsTransactions.push(selectedCustomerGlobalPointsTransactionsPhone[i]);
          userGlobalPointsBalance += selectedCustomerGlobalPointsTransactionsPhone[i].amount;
        }
      }

      userGlobalPointsTransactions.sort((a, b) => b.createdAt - a.createdAt);

      UserStore.update((s) => {
        s.userGlobalPointsTransactions = userGlobalPointsTransactions;
        s.userGlobalPointsBalance = parseFloat(userGlobalPointsBalance.toFixed(2));
      });
    });
  }, [
    selectedCustomerGlobalPointsTransactionsEmail,
    selectedCustomerGlobalPointsBalanceEmail,
    selectedCustomerGlobalPointsTransactionsPhone,
    selectedCustomerGlobalPointsBalancePhone,
  ]);

  const readStates = async () => {
    console.log('global.selectedoutlet = readStates (1) (==test==)');

    if (selectedOutlet === null) {
      console.log('global.selectedoutlet = readStates (2) (==test==)');

      // const commonStoreDataRaw = await AsyncStorage.getItem("@commonStore");
      const commonStoreDataRaw = await idbGet('@commonStore');
      if (commonStoreDataRaw !== undefined) {
        console.log('global.selectedoutlet = readStates (3) (==test==)');

        const commonStoreData = JSON.parse(commonStoreDataRaw);

        const latestOutletId = await AsyncStorage.getItem("latestOutletId");

        console.log('latestOutletId');
        console.log(latestOutletId);
        console.log('commonStoreData.selectedOutlet');
        console.log(commonStoreData.selectedOutlet);

        if (
          commonStoreData.selectedOutlet &&
          latestOutletId === commonStoreData.selectedOutlet.uniqueId
        ) {
          // check if it's the same outlet user scanned

          console.log('global.selectedoutlet = readStates (4) (==test==)');

          // if (isPlacingReservation) {
          // } else {
          //   // if (
          //   //   commonStoreData.orderType === ORDER_TYPE.DINEIN 
          //   //   // 2022-10-08 - Try to disable this
          //   //   // &&
          //   //   // commonStoreData.userCart.uniqueId === undefined
          //   // ) {
          //   //   // logout the user

          //   //   linkTo && linkTo(`${prefix}/scan`);
          //   // }
          // }

          console.log('global.selectedoutlet = commonStoreData.selectedOutlet (3) (==test==)');

          global.selectedOutlet = commonStoreData.selectedOutlet;

          CommonStore.replace(commonStoreData);

          // const userStoreDataRaw = await AsyncStorage.getItem("@userStore");
          const userStoreDataRaw = await idbGet('@userStore');
          if (userStoreDataRaw !== undefined) {
            const userStoreData = JSON.parse(userStoreDataRaw);

            UserStore.replace(userStoreData);
          }

          // const dataStoreDataRaw = await AsyncStorage.getItem('@dataStore');
          const dataStoreDataRaw = await idbGet('@dataStore');
          if (dataStoreDataRaw !== undefined) {
            const dataStoreData = JSON.parse(dataStoreDataRaw);

            DataStore.replace(dataStoreData);
            // DataStore.replace({
            //   ...dataStoreData,
            //   ...dataStoreData.linkToFunc !== undefined && {
            //     linkToFunc: dataStoreData.linkToFunc,
            //   },
            // });
          }

          // const tableStoreDataRaw = await AsyncStorage.getItem("@tableStore");
          const tableStoreDataRaw = await idbGet('@tableStore');
          if (tableStoreDataRaw !== undefined) {
            const tableStoreData = JSON.parse(tableStoreDataRaw);

            TableStore.replace(tableStoreData);
          }

          // const paymentStoreDataRaw = await AsyncStorage.getItem("@paymentStore");
          const paymentStoreDataRaw = await idbGet('@paymentStore');
          if (paymentStoreDataRaw !== undefined) {
            const paymentStoreData = JSON.parse(paymentStoreDataRaw);

            PaymentStore.replace(paymentStoreData);
          }

          // const dataStoreDataRaw = await AsyncStorage.getItem('@dataStore');
          // if (dataStoreDataRaw !== undefined) {
          //   const dataStoreData = JSON.parse(dataStoreDataRaw);

          //   DataStore.replace(dataStoreData);
          // }
        }
      }
    }
  };

  const readCommonStates = async () => {
    // if (!linkToFunc) {
    //   const dataStoreDataRaw = await AsyncStorage.getItem('@dataStore');
    //   if (dataStoreDataRaw !== undefined) {
    //     const dataStoreData = JSON.parse(dataStoreDataRaw);
    //     DataStore.replace(dataStoreData);
    //   }
    // }
  };

  useEffect(() => {
    CommonStore.update(s => {
      s.routeName = route.name;
    });
  }, [route]);

  const renderViewHistory = ({ item }) => {
    return (
      <View
        style={{
          flexDirection: 'row',
          paddingVertical: 10,
          alignItems: 'center',
          paddingHorizontal: 10,
        }}>
        {/* <Text style={{ fontSize: switchMerchant ? 10 : 16, width: '20%' }}>
          {loyaltyHistoryCrmUser ? loyaltyHistoryCrmUser.name : '-'}
        </Text>
        <Text style={{ fontSize: switchMerchant ? 10 : 16, width: '25%' }}>
          {loyaltyHistoryCrmUser ? loyaltyHistoryCrmUser.number : '-'}
        </Text> */}
        <View style={{ flexDirection: 'column', width: '42.5%' }}>
          <Text style={{ fontSize: switchMerchant ? 10 : 16 }}>
            {
              LOYALTY_CAMPAIGN_CREDIT_TRANSACTION_TYPE_PARSED[
              item.transactionType
              ]
            }
          </Text>
          <Text style={{ fontSize: switchMerchant ? 10 : 16 }}>
            {item.amount.toFixed(2)}
          </Text>
        </View>
        <View style={{ flexDirection: 'column', width: '42.5%' }}>
          <Text style={{ fontSize: switchMerchant ? 10 : 16 }}>
            {moment(item.createdAt).format('DD MMM YYYY')}
          </Text>
          <Text style={{ fontSize: switchMerchant ? 10 : 16 }}>
            {moment(item.createdAt).format('hh:mm A')}
          </Text>
        </View>
        <View style={{ width: '15%', alignItems: 'center', justifyContent: 'center' }}>
          <Text
            style={{
              fontSize: switchMerchant ? 10 : 16,
              fontFamily: 'NunitoSans-SemiBold',
              backgroundColor: Colors.primaryColor,
              color: Colors.whiteColor,
              padding: 8,
              borderRadius: 5,
            }}>
            Success
          </Text>
        </View>
      </View>
    );
  };

  const renderItem = ({ item, index }) => (
    <>
      <View style={{
        backgroundColor: 'white',
        flexDirection: 'row',
        marginTop: 10,
        alignContent: 'center',
        alignItems: 'center',
        borderRadius: 20,
      }}>
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          width: windowWidth * 0.9,
          margin: 10,
          justifyContent: 'space-between'
        }}>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              width: windowWidth * 0.9,
              margin: 10,
              justifyContent: 'space-between'
            }}>
            <View style={{ flexDirection: 'row' }}>
              <View style={{ justifyContent: 'center' }}>
                {item.image ? (
                  <>
                    <AsyncImage
                      source={{ uri: item.image }}
                      item={item}
                      style={{
                        width: windowHeight * 0.07,
                        height: windowHeight * 0.07,
                        borderRadius: 10,
                      }}
                    />
                  </>
                ) :
                  <View style={{ backgroundColor: Colors.secondaryColor, width: windowHeight * 0.07, height: windowHeight * 0.07, alignItems: 'center', alignSelf: 'center', justifyContent: 'center', borderRadius: 10, }}>
                    <Ionicons
                      name="fast-food-outline"
                      // size={45}
                      size={windowHeight * 0.04}
                    />
                  </View>
                }
              </View>

              <View>
                <Text
                  style={{
                    // marginTop: 15,
                    marginLeft: 10,
                    fontSize: 20,
                    fontFamily: 'NunitoSans-Bold',
                    //color: Colors.whiteColor,
                  }}
                  numberOfLines={1}>
                  {item.campaignName}
                </Text>

                <Text
                  style={{
                    // marginTop: 10,
                    marginLeft: 10,
                    fontSize: 12,
                    fontFamily: 'NunitoSans-SemiBold',
                    //color: Colors.whiteColor,
                  }}>
                  Expiry Date:
                </Text>

                <Text
                  style={{
                    //marginTop: 10,
                    marginLeft: 10,
                    fontSize: 12,
                    fontFamily: 'NunitoSans-Regular',
                    //color: Colors.whiteColor,
                  }}>
                  {moment(item.expirationDate).format('dddd, Do MMM YYYY')}
                </Text>
              </View>
            </View>
          </View>
        </View>
      </View>
    </>
  );
  const renderSearch = (item) => {
    return (
      <View style={{ flexDirection: "column" }}>
        <Text style={{ fontWeight: "700", fontSize: 14, marginBottom: 5 }}>
          {item.structured_formatting.main_text}
        </Text>

        <Text style={{ fontSize: 12, color: "grey" }}>{item.description}</Text>
      </View>
    );
  };

  //old ui
  // return (
  //   <View style={{ height: windowHeight * 0.9, alignItems: 'center', justifyContent: 'center', backgroundColor: 'light-gray', }}>
  //     <View style={{
  //       width: isMobile() ? windowWidth : windowWidth,
  //       height: 160,
  //       backgroundColor: Colors.darkBgColor,
  //     }}>
  //       <Image style={{
  //         width: 100,
  //         height: 47,
  //         alignSelf: 'center',
  //         //marginBottom: 25.
  //       }} resizeMode="contain" source={imgLogo} />
  //       <Text style={{
  //         fontFamily: 'NunitoSans-Bold',
  //         color: Colors.whiteColor,
  //         fontSize: 30,
  //         textAlign: 'center',
  //         marginTop: 30,
  //         // width: '100%',
  //       }}>
  //         {/* +60 12 345 6789 */}
  //         {loyaltyHistoryCrmUser.number ? loyaltyHistoryCrmUser.number : 'N/A'}
  //       </Text>
  //     </View>
  //     {seeHistory == true ?
  //       <View style={{
  //         justifyContent: 'center',
  //         alignContent: 'center',
  //         borderRadius: 10,
  //         borderColor: '#E5E5E5',

  //         shadowOffset: {
  //           width: 0,
  //           height: 2,
  //         },
  //         shadowOpacity: 0.22,
  //         shadowRadius: 3.22,
  //         elevation: 1,
  //         padding: 20,
  //         marginTop: '3%',

  //         width: isMobile() ? windowWidth * 0.75 : windowWidth * 0.4,
  //       }}>
  //         <View style={{ marginTop: '1%', alignItems: 'center', justifyContent: 'center', marginBottom: 30 }}>
  //           <Text style={{
  //             fontFamily: 'NunitoSans-Bold',
  //             color: Colors.descriptionColor,
  //             fontSize: 15,
  //             textAlign: 'center',
  //           }}>
  //             Balance
  //           </Text>
  //           <Text style={{
  //             fontFamily: 'NunitoSans-Bold',
  //             color: 'Black',
  //             fontSize: 20,
  //             textAlign: 'center',
  //           }}>
  //             RM {loyaltyHistoryLCCBalance.toFixed(2)}
  //           </Text>
  //         </View>

  //         {/* <View style={{
  //           flexDirection: 'row',
  //           //marginTop: '3%',
  //           justifyContent: 'space-between',
  //           backgroundColor: Colors.primaryColor,
  //           height: 20,
  //           //alignContent: 'center'
  //           paddingBottom: 50
  //         }}>
  //           <View style={{
  //             flexDirection: 'column',
  //             marginTop: '3%',
  //             //marginRight: '50%',
  //             //width: '5%',
  //             //backgroundColor: 'red'
  //             //justifyContent: 'center'
  //           }}>
  //             <View style={{ justifyContent: 'flex-start', }}>
  //               <Text style={{
  //                 fontFamily: 'NunitoSans-Regular',
  //                 color: 'white',
  //                 fontSize: 14,
  //                 textAlign: 'left',
  //                 marginTop: 0,
  //                 marginLeft: 20,
  //                 // width: '100%',

  //               }}>
  //                 Action
  //               </Text>
  //             </View>
  //           </View>
  //           <View style={{
  //             flexDirection: 'column',
  //             marginTop: '3%',
  //             //marginRight: '50%',
  //             //width: '33%',
  //             //backgroundColor: 'yellow'
  //             //justifyContent: 'space-evenly'
  //           }}>
  //             <View>
  //               <Text style={{
  //                 fontFamily: 'NunitoSans-Regular',
  //                 color: 'white',
  //                 fontSize: 14,
  //                 textAlign: 'center',
  //                 marginTop: 0,
  //                 //marginLeft: 5,
  //                 // width: '100%',
  //               }}>
  //                 Amount
  //               </Text>
  //             </View>
  //           </View>
  //           <View style={{
  //             flexDirection: 'column',
  //             marginTop: '3%',
  //             //marginRight: '50%',
  //             //width: '33%',
  //             //backgroundColor: 'blue'
  //             //justifyContent: 'space-evenly'
  //           }}>
  //             <View style={{ justifyContent: 'flex-end' }}>
  //               <Text style={{
  //                 fontFamily: 'NunitoSans-Regular',
  //                 color: 'white',
  //                 fontSize: 14,
  //                 textAlign: 'right',
  //                 marginTop: 0,
  //                 //marginLeft: 5,
  //                 // width: '100%',
  //                 marginRight: 20
  //               }}>
  //                 Date
  //               </Text>
  //             </View>
  //           </View>
  //         </View> */}

  //         <View style={{ height: 50 }} />
  //       </View>
  //       :
  //       <View style={{
  //         justifyContent: 'center',
  //         alignContent: 'center',
  //         borderRadius: 10,
  //         borderColor: '#E5E5E5',

  //         shadowOffset: {
  //           width: 0,
  //           height: 2,
  //         },
  //         shadowOpacity: 0.22,
  //         shadowRadius: 3.22,
  //         elevation: 1,
  //         padding: 20,
  //         marginTop: '3%',

  //         width: isMobile() ? windowWidth * 0.75 : windowWidth * 0.4,
  //       }}>
  //         <View style={{ marginTop: '1%', alignItems: 'center', justifyContent: 'center', }}>
  //           <Text style={{
  //             fontFamily: 'NunitoSans-Bold',
  //             color: Colors.descriptionColor,
  //             fontSize: 15,
  //             textAlign: 'center',
  //           }}>
  //             Balance
  //           </Text>
  //           <Text style={{
  //             fontFamily: 'NunitoSans-Bold',
  //             color: 'Black',
  //             fontSize: 20,
  //             textAlign: 'center',
  //           }}>
  //             RM {loyaltyHistoryLCCBalance.toFixed(2)}
  //           </Text>
  //         </View>
  //         <View style={{
  //           flexDirection: 'row',
  //           marginTop: '3%',
  //           justifyContent: 'space-evenly'
  //         }}>
  //           <View style={{
  //             flexDirection: 'column',
  //             marginTop: '3%',
  //             marginRight: '50%'
  //             //justifyContent: 'space-evenly'
  //           }}>
  //             <View style={{ justifyContent: 'flex-start' }}>
  //               <Text style={{
  //                 fontFamily: 'NunitoSans-Regular',
  //                 color: Colors.descriptionColor,
  //                 fontSize: 14,
  //                 textAlign: 'left',
  //                 marginTop: 0,
  //                 marginLeft: 5,
  //                 // width: '100%',
  //               }}>
  //                 Name
  //               </Text>
  //               <Text style={{
  //                 fontFamily: 'NunitoSans-Bold',
  //                 color: 'black',
  //                 fontSize: 16,
  //                 textAlign: 'left',
  //                 marginTop: 0,
  //                 marginLeft: 5,
  //                 // width: '100%',
  //               }}>
  //                 {loyaltyHistoryCrmUser.name ? loyaltyHistoryCrmUser.name : '-'}
  //               </Text>
  //             </View>
  //             <View style={{ justifyContent: 'flex-start', marginTop: '20%' }}>
  //               <Text style={{
  //                 fontFamily: 'NunitoSans-Regular',
  //                 color: Colors.descriptionColor,
  //                 fontSize: 14,
  //                 textAlign: 'left',
  //                 marginTop: 0,
  //                 marginLeft: 5,
  //                 // width: '100%',
  //               }}>
  //                 Birthday
  //               </Text>
  //               <Text style={{
  //                 fontFamily: 'NunitoSans-Bold',
  //                 color: 'black',
  //                 fontSize: 16,
  //                 textAlign: 'left',
  //                 marginTop: 0,
  //                 marginLeft: 5,
  //                 // width: '100%',
  //               }}>
  //                 {loyaltyHistoryCrmUser.dob ? moment(loyaltyHistoryCrmUser.dob).format('YYYY/MM/DD') : '-'}
  //               </Text>
  //             </View>
  //             {/* <View style={{ justifyContent: 'flex-start', marginTop: '20%' }}>
  //               <Text style={{
  //                 fontFamily: 'NunitoSans-Regular',
  //                 color: Colors.descriptionColor,
  //                 fontSize: 14,
  //                 textAlign: 'left',
  //                 marginTop: 0,
  //                 marginLeft: 5,
  //                 // width: '100%',
  //               }}>
  //                 Email
  //               </Text>
  //               <Text style={{
  //                 fontFamily: 'NunitoSans-Bold',
  //                 color: 'black',
  //                 fontSize: 16,
  //                 textAlign: 'left',
  //                 marginTop: 0,
  //                 marginLeft: 5,
  //                 // width: '100%',
  //               }}>
  //                 -
  //               </Text>
  //             </View> */}
  //           </View>
  //           <View style={{
  //             flexDirection: 'column',
  //             marginTop: '3%',
  //             //justifyContent: 'space-evenly'
  //           }}>
  //             <View style={{ justifyContent: 'flex-end' }}>
  //               <Text style={{
  //                 fontFamily: 'NunitoSans-Regular',
  //                 color: Colors.descriptionColor,
  //                 fontSize: 14,
  //                 textAlign: 'left',
  //                 marginTop: 0,
  //                 marginLeft: 5,
  //                 // width: '100%',
  //               }}>
  //                 User ID
  //               </Text>
  //               <Text style={{
  //                 fontFamily: 'NunitoSans-Bold',
  //                 color: 'black',
  //                 fontSize: 16,
  //                 textAlign: 'right',
  //                 marginTop: 0,
  //                 marginLeft: 5,
  //                 // width: '100%',
  //               }}>
  //                 {loyaltyHistoryCrmUser.userIdHuman ? loyaltyHistoryCrmUser.userIdHuman : '-'}
  //               </Text>
  //             </View>
  //             <View style={{ justifyContent: 'flex-end', marginTop: '20%' }}>
  //               <Text style={{
  //                 fontFamily: 'NunitoSans-Regular',
  //                 color: Colors.descriptionColor,
  //                 fontSize: 14,
  //                 textAlign: 'right',
  //                 marginTop: 0,
  //                 marginLeft: 5,
  //                 // width: '100%',
  //               }}>
  //                 Gender
  //               </Text>
  //               <Text style={{
  //                 fontFamily: 'NunitoSans-Bold',
  //                 color: 'black',
  //                 fontSize: 16,
  //                 textAlign: 'right',
  //                 marginTop: 0,
  //                 marginLeft: 5,
  //                 // width: '100%',
  //               }}>
  //                 {loyaltyHistoryCrmUser.gender ? loyaltyHistoryCrmUser.gender : '-'}
  //               </Text>
  //             </View>
  //           </View>
  //         </View>

  //         <View style={{ flex: 1, marginTop: '2%', alignContent: 'center' }}>
  //           {/* <TouchableOpacity
  //             disabled={isLoading}
  //             onPress={() => {
  //               setSeeHistory(true);
  //             }}
  //             style={{
  //               backgroundColor:
  //                 Colors.primaryColor,
  //               width: 150,
  //               height: 40,
  //               // height: 60,
  //               alignSelf: "center",
  //               alignItems: 'center',
  //               borderRadius: 10,
  //               justifyContent: "center",
  //             }}
  //           >
  //             <Text
  //               style={{
  //                 alignSelf: 'center',
  //                 color: Colors.whiteColor,
  //                 fontFamily: 'NunitoSans-Bold',
  //                 fontSize: 14
  //               }}
  //             >
  //               CHECK HISTORY
  //             </Text>
  //           </TouchableOpacity> */}

  //           <View
  //             style={{
  //               flexDirection: 'row',
  //               backgroundColor: '#F2F3F7',
  //               padding: 10,
  //               marginTop: 20,
  //               borderRadius: 5,
  //             }}>
  //             {/* <Text
  //             style={{
  //               fontSize: switchMerchant ? 10 : 16,
  //               fontFamily: 'NunitoSans-Bold',
  //               width: '20%',
  //             }}>
  //             Name
  //           </Text>
  //           <Text
  //             style={{
  //               fontSize: switchMerchant ? 10 : 16,
  //               fontFamily: 'NunitoSans-Bold',
  //               width: '25%',
  //             }}>
  //             Phone
  //           </Text> */}
  //             <Text
  //               style={{
  //                 fontSize: switchMerchant ? 10 : 16,
  //                 fontFamily: 'NunitoSans-Bold',
  //                 width: '42.5%',
  //               }}>
  //               Action
  //             </Text>
  //             <Text
  //               style={{
  //                 fontSize: switchMerchant ? 10 : 16,
  //                 fontFamily: 'NunitoSans-Bold',
  //                 width: '42.5%',
  //               }}>
  //               Date
  //             </Text>
  //             <Text
  //               style={{
  //                 fontSize: switchMerchant ? 10 : 16,
  //                 fontFamily: 'NunitoSans-Bold',
  //                 width: '15%',
  //                 textAlign: 'center',
  //               }}>
  //               Status
  //             </Text>
  //           </View>
  //           {/* List */}

  //           <FlatList
  //             data={loyaltyHistoryLCCTransactions}
  //             // extraData={selectedCustomerLCCTransactions}
  //             renderItem={renderViewHistory}
  //             keyExtractor={(item, index) => index}
  //             contentContainerStyle={
  //               {
  //                 // paddingLeft: 10,
  //                 // paddingTop: 20,
  //                 // width: windowWidth * 0.88,
  //                 // backgroundColor: 'red'
  //               }
  //             }
  //           />
  //         </View>
  //         <View style={{ marginTop: '5%', alignItems: 'center', justifyContent: 'center' }}>
  //           <Text style={{
  //             fontFamily: 'NunitoSans-Bold',
  //             color: 'black',
  //             fontSize: 18,
  //             textAlign: 'center',
  //           }}>
  //             Available Rewards
  //           </Text>
  //           <Text style={{
  //             fontFamily: 'NunitoSans-Regular',
  //             color: Colors.descriptionColor,
  //             fontSize: 14,
  //             textAlign: 'center',
  //             marginTop: '2%',
  //           }}>
  //             You currently don't have any rewards available
  //           </Text>
  //         </View>
  //         <View style={{
  //           flexDirection: 'row',
  //           marginTop: '3%',
  //           justifyContent: 'space-evenly'
  //         }}>
  //           <View style={{
  //             flexDirection: 'column',
  //             marginTop: '3%',
  //             marginRight: '37.5%'
  //             //justifyContent: 'space-evenly'
  //           }}>
  //             <View style={{ justifyContent: 'flex-start' }}>
  //               <Text style={{
  //                 fontFamily: 'NunitoSans-Bold',
  //                 color: 'black',
  //                 fontSize: 18,
  //                 textAlign: 'left',
  //               }}>
  //                 Receive SMS Rewards
  //               </Text>
  //             </View>
  //           </View>
  //           <View style={{
  //             flexDirection: 'column',
  //             marginTop: '3%',
  //             //justifyContent: 'space-evenly'
  //           }}>
  //             <View style={{ justifyContent: 'flex-end' }}>
  //               <Switch
  //                 width={35}
  //                 height={20}
  //                 handleDiameter={30}
  //                 uncheckedIcon={false}
  //                 checkedIcon={false}
  //                 boxShadow="0px 1px 5px rgba(0, 0, 0, 0.6)"
  //                 activeBoxShadow="0px 0px 1px 10px rgba(0, 0, 0, 0.2)"
  //                 value={!blockSMS}
  //                 onValueChange={async value => {
  //                   setBlockSMS(!value);

  //                   if (loyaltyHistoryCrmUser && loyaltyHistoryCrmUser.uniqueId) {
  //                     const body = {
  //                       crmUserId: loyaltyHistoryCrmUser.uniqueId,
  //                       blockSMS: !value,

  //                       blockSMSFromUserSide: !value,
  //                     };

  //                     ApiClient.POST(API.switchCRMUserBlockSMSFromUserWeb, body).then((result) => {
  //                       if (result && result.status === "success") {

  //                       }
  //                     });
  //                   }
  //                 }}
  //               // onChange={async () => {

  //               // }}
  //               />
  //             </View>
  //           </View>
  //         </View>

  //         <View style={{
  //           flexDirection: 'row',
  //           marginTop: '3%',
  //           justifyContent: 'space-evenly'
  //         }}>
  //           <View style={{
  //             flexDirection: 'column',
  //             marginTop: '3%',
  //             marginRight: '37.5%'
  //             //justifyContent: 'space-evenly'
  //           }}>
  //             <View style={{ justifyContent: 'flex-start' }}>
  //               <Text style={{
  //                 fontFamily: 'NunitoSans-Bold',
  //                 color: 'black',
  //                 fontSize: 18,
  //                 textAlign: 'left',
  //               }}>
  //                 Receive Email Rewards
  //               </Text>
  //             </View>
  //           </View>
  //           <View style={{
  //             flexDirection: 'column',
  //             marginTop: '3%',
  //             //justifyContent: 'space-evenly'
  //           }}>
  //             <View style={{ justifyContent: 'flex-end' }}>
  //               <Switch
  //                 width={35}
  //                 height={20}
  //                 handleDiameter={30}
  //                 uncheckedIcon={false}
  //                 checkedIcon={false}
  //                 boxShadow="0px 1px 5px rgba(0, 0, 0, 0.6)"
  //                 activeBoxShadow="0px 0px 1px 10px rgba(0, 0, 0, 0.2)"
  //                 value={!blockEmail}
  //                 onValueChange={async value => {
  //                   setBlockEmail(!value);

  //                   if (loyaltyHistoryCrmUser && loyaltyHistoryCrmUser.uniqueId) {
  //                     const body = {
  //                       crmUserId: loyaltyHistoryCrmUser.uniqueId,
  //                       blockEmail: !value,

  //                       blockEmailFromUserSide: !value,
  //                     };

  //                     ApiClient.POST(API.switchCRMUserBlockEmailFromUserWeb, body).then((result) => {
  //                       if (result && result.status === "success") {

  //                       }
  //                     });
  //                   }
  //                 }}
  //               // onChange={async () => {

  //               // }}
  //               />
  //             </View>
  //           </View>
  //         </View>
  //         <View style={{ height: 50 }} />
  //       </View>
  //     }
  //     <View style={{ flex: 1 }}></View>
  //   </View>

  // )

  return (
    <View style={{ height: windowHeight * 0.9, alignItems: 'center', justifyContent: 'center', backgroundColor: 'light-gray', }}>
      {/* <View style={{
            width: isMobile() ? windowWidth : windowWidth,
            height: 160,
            backgroundColor: Colors.darkBgColor,
        }}>
            <Image style={{
                width: 100,
                height: 47,
                alignSelf: 'center',
                //marginBottom: 25.
            }} resizeMode="contain" source={imgLogo} />
            <Text style={{
                fontFamily: 'NunitoSans-Bold',
                color: Colors.whiteColor,
                fontSize: 30,
                textAlign: 'center',
                marginTop: 30,
                // width: '100%',
            }}>
                {loyaltyHistoryCrmUser.number ? loyaltyHistoryCrmUser.number : 'N/A'}
            </Text>
        </View> */}

      <View style={{
        justifyContent: 'center',
        alignContent: 'center',
        borderRadius: 10,
        borderColor: '#E5E5E5',

        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 1,
        padding: 20,
        marginTop: '3%',

        height: windowHeight * 0.85,
        width: isMobile() ? windowWidth * 0.9 : windowWidth * 0.4,
      }}>
        <ScrollView showsVerticalScrollIndicator={false}>
          <View style={{ marginTop: '1%', alignItems: 'center', justifyContent: 'center', }}>
            <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'center', }}>
              <Text style={{
                fontFamily: 'NunitoSans-Bold',
                color: 'black',
                fontSize: 18,
                textAlign: 'center',
              }}>
                {`Available Points`}
              </Text>
              <View style={{ marginLeft: 5 }}>
                <MultiSelect
                  singleSelect={true}
                  placeholder={'Select'}
                  // defaultValue={selectOutletToShowPoint}
                  // options={[
                  //   { label: "All", value: "ALL" },
                  // ].concat(selectOutletOption)}
                  options={outletDropdown}
                  onChange={(item) => {
                    if (item) {
                      setSelectOutletToShowPoint(item);
                    }
                  }}
                />
              </View>
            </View>

            <Text style={{
              fontFamily: 'NunitoSans-Bold',
              color: 'Black',
              fontSize: 20,
              textAlign: 'center',
              marginTop: '2%'
            }}>
              {userGlobalPointsBalance ? userGlobalPointsBalance.toFixed(0) : 0}
            </Text>
          </View>
          <View style={{
            width: isMobile() ? '100%' : '80%',
            alignSelf: 'center',
            flexDirection: 'row',
            marginTop: '3%',
            justifyContent: 'space-between',
            zIndex: -1,
          }}>
            <View style={{
              flexDirection: 'column',
              marginTop: '3%',
              // marginRight: '50%'
              //justifyContent: 'space-evenly'
            }}>
              <View style={{ justifyContent: 'flex-start' }}>
                <Text style={{
                  fontFamily: 'NunitoSans-Regular',
                  color: Colors.descriptionColor,
                  fontSize: 14,
                  textAlign: 'left',
                  marginTop: 0,
                  marginLeft: 5,
                  // width: '100%',
                }}>
                  Name
                </Text>
                <Text style={{
                  fontFamily: 'NunitoSans-Bold',
                  color: 'black',
                  fontSize: 16,
                  textAlign: 'left',
                  marginTop: 0,
                  marginLeft: 5,
                  // width: '100%',
                }}>
                  {name ? name : '-'}
                </Text>
              </View>
            </View>
            <View style={{
              flexDirection: 'column',
              marginTop: '3%',
              //justifyContent: 'space-evenly'
            }}>
              <View style={{ justifyContent: 'flex-end' }}>
                <Text style={{
                  fontFamily: 'NunitoSans-Regular',
                  color: Colors.descriptionColor,
                  fontSize: 14,
                  textAlign: 'left',
                  marginTop: 0,
                  marginLeft: 5,
                  // width: '100%',
                }}>
                  Phone Number
                </Text>
                <Text style={{
                  fontFamily: 'NunitoSans-Bold',
                  color: 'black',
                  fontSize: 16,
                  textAlign: 'right',
                  marginTop: 0,
                  marginLeft: 5,
                  // width: '100%',
                }}>
                  {userPhone ? userPhone : '-'}
                </Text>
              </View>
            </View>
          </View>
          <View style={{ marginTop: '2%', alignContent: 'center', width: isMobile() ? '100%' : '80%', alignSelf: 'center', height: windowHeight * 0.27, paddingBottom: 10, }}>
            <View
              style={{
                flexDirection: 'row',
                padding: 10,
                marginTop: 20,
                borderRadius: 5,
              }}>
              <Text
                style={{
                  fontSize: isMobile() ? 10 : 16,
                  fontFamily: 'NunitoSans-Bold',
                  width: '42.5%',
                }}>
                Action
              </Text>
              <Text
                style={{
                  fontSize: isMobile() ? 10 : 16,
                  fontFamily: 'NunitoSans-Bold',
                  width: '42.5%',
                }}>
                Date
              </Text>
              <Text
                style={{
                  fontSize: isMobile() ? 10 : 16,
                  fontFamily: 'NunitoSans-Bold',
                  width: '15%',
                  textAlign: 'center',
                }}>
                Status
              </Text>
            </View>
            {/* List */}

            <FlatList
              showsVerticalScrollIndicator={false}
              data={userGlobalPointsTransactions}
              // extraData={selectedCustomerLCCTransactions}
              renderItem={renderViewHistory}
              keyExtractor={(item, index) => index}
              contentContainerStyle={{}}
            />
          </View>

          <View style={{ marginTop: '5%', alignItems: 'center', justifyContent: 'center', width: isMobile() ? '100%' : '80%', alignSelf: 'center', height: windowHeight * 0.2, }}>
            <Text style={{
              fontFamily: 'NunitoSans-Bold',
              color: 'black',
              fontSize: 18,
              textAlign: 'center',
            }}>
              Available Rewards
            </Text>
            {availableTaggableVouchers && availableTaggableVouchers.length > 0 ? (
              <>
                {availableTaggableVouchers && availableTaggableVouchers.length > 0 && (
                  <View style={{ height: '100%', width: '100%', paddingBottom: 10, marginTop: 10, }}>
                    <FlatList
                      showsVerticalScrollIndicator={false}
                      data={availableTaggableVouchers}
                      style={{}}
                      //extraData={availableTaggableVouchers}
                      renderItem={renderItem}
                      keyExtractor={(item, index) => String(index)}
                    />
                  </View>
                )}
              </>
            ) : (
              <View
                style={{
                  justifyContent: 'center',
                  alignItems: 'center',
                  // marginTop: 30,
                  alignSelf: 'center',
                }}>
                <Text style={{
                  fontFamily: 'NunitoSans-Regular',
                  color: Colors.descriptionColor,
                  fontSize: 14,
                  textAlign: 'center',
                  marginTop: '2%',
                }}>
                  This outlet currently don't have any rewards available
                </Text>
              </View>
            )}

          </View>
          <View style={{
            flexDirection: 'row',
            marginTop: '5%',
            justifyContent: 'space-between',
            alignItems: 'center',
            width: isMobile() ? '100%' : '80%',
            alignSelf: 'center',
          }}>
            <View style={{
              flexDirection: 'column',
              marginTop: '2%',
            }}>
              <View style={{ justifyContent: 'flex-start' }}>
                <Text style={{
                  fontFamily: 'NunitoSans-Bold',
                  color: 'black',
                  fontSize: 18,
                  textAlign: 'left',
                }}>
                  Receive SMS Rewards
                </Text>
              </View>
            </View>
            <View style={{
              flexDirection: 'column',
              marginTop: '2%',
              //justifyContent: 'space-evenly'
            }}>
              <View style={{ justifyContent: 'flex-end' }}>
                <Switch
                  width={35}
                  height={20}
                  handleDiameter={30}
                  uncheckedIcon={false}
                  checkedIcon={false}
                  boxShadow="0px 1px 5px rgba(0, 0, 0, 0.6)"
                  activeBoxShadow="0px 0px 1px 10px rgba(0, 0, 0, 0.2)"
                  value={!blockSMS}
                  onValueChange={async value => {
                    setBlockSMS(!value);

                    if (loyaltyHistoryCrmUser && loyaltyHistoryCrmUser.uniqueId) {
                      const body = {
                        crmUserId: loyaltyHistoryCrmUser.uniqueId,
                        blockSMS: !value,

                        blockSMSFromUserSide: !value,
                      };

                      ApiClient.POST(API.switchCRMUserBlockSMSFromUserWeb, body).then((result) => {
                        if (result && result.status === "success") {

                        }
                      });
                    }
                  }}
                // onChange={async () => {

                // }}
                />
              </View>
            </View>
          </View>

          <View style={{
            flexDirection: 'row',
            marginTop: '2%',
            justifyContent: 'space-between',
            alignItems: 'center',
            width: isMobile() ? '100%' : '80%',
            alignSelf: 'center',
          }}>
            {/* <View style={{
              flexDirection: 'column',
              marginTop: '2%',
            }}>
              <View style={{ justifyContent: 'flex-start' }}>
                <Text style={{
                  fontFamily: 'NunitoSans-Bold',
                  color: 'black',
                  fontSize: 18,
                  textAlign: 'left',
                }}>
                  Receive Email Rewards
                </Text>
              </View>
            </View> */}
            <View style={{
              flexDirection: 'column',
              marginTop: '2%',
              //justifyContent: 'space-evenly'
            }}>
              <View style={{ justifyContent: 'flex-end' }}>
                <Switch
                  width={35}
                  height={20}
                  handleDiameter={30}
                  uncheckedIcon={false}
                  checkedIcon={false}
                  boxShadow="0px 1px 5px rgba(0, 0, 0, 0.6)"
                  activeBoxShadow="0px 0px 1px 10px rgba(0, 0, 0, 0.2)"
                  value={!blockEmail}
                  onValueChange={async value => {
                    setBlockEmail(!value);

                    if (loyaltyHistoryCrmUser && loyaltyHistoryCrmUser.uniqueId) {
                      const body = {
                        crmUserId: loyaltyHistoryCrmUser.uniqueId,
                        blockEmail: !value,

                        blockEmailFromUserSide: !value,
                      };

                      ApiClient.POST(API.switchCRMUserBlockEmailFromUserWeb, body).then((result) => {
                        if (result && result.status === "success") {

                        }
                      });
                    }
                  }}
                // onChange={async () => {

                // }}
                />
              </View>
            </View>
          </View>
          <View style={{ height: 20 }} />
        </ScrollView>
      </View>
      {/* <View style={{ flex: 1 }}></View> */}
    </View>

  )
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.highlightColor,
    flexDirection: 'row',
    fontFamily: 'NunitoSans-Regular',
  },
  list: {
    backgroundColor: Colors.whiteColor,
    // width: windowWidth * 0.85,
    // height: windowHeight,
    marginTop: 40,
    marginHorizontal: 30,
    //alignSelf: 'center',
    //justifyContent: 'center',
    borderRadius: 5,
    shadowOpacity: 0,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
  },
  listItem: {
    fontFamily: 'NunitoSans-Regular',
    marginLeft: 20,
    color: Colors.descriptionColor,
    fontSize: 16,
  },
  textInput: {
    fontFamily: 'NunitoSans-Regular',
    height: 50,
    backgroundColor: Colors.fieldtBgColor,
    //borderRadius: 10,
    paddingLeft: 20,
  },
  textInputLocation: {
    fontFamily: 'NunitoSans-Regular',
    width: 300,
    height: 100,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 10,
    marginRight: 10,
  },
  textSize: {
    fontSize: 19,
    fontFamily: 'NunitoSans-SemiBold',
  },
  merchantDisplayView: {
    flexDirection: 'row',
    flex: 1,
    marginLeft: '17%',
  },
  shiftText: {
    marginLeft: '15%',
    color: Colors.primaryColor,
    fontFamily: 'NunitoSans-SemiBold',
    fontSize: 25,
  },
  confirmBox: {
    width: '30%',
    height: '30%',
    borderRadius: 30,
    backgroundColor: Colors.whiteColor,
  },
  closeButton: {
    position: 'absolute',
    right: 10,
    top: 10,
    elevation: 1000,
    zIndex: 1000,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: Colors.modalBgColor,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalView: {
    height: 200,
    width: 500,
    backgroundColor: Colors.whiteColor,
    borderRadius: 12,
    padding: 25,
    //alignItems: 'center',
    //justifyContent: 'center'
  },
});

export default LoyaltyHistory;