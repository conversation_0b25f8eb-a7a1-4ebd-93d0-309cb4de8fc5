const { test, expect } = require('@playwright/test');

test('Place order with up signup Test', async ({ page, isMobile }) => {
    // await page.setViewportSize({ width: 1600, height: 900 }); //modify to adjust window size
    await page.goto('http://localhost:5400/web/outlet/hominsanttdi/takeaway');

    await page.waitForTimeout(3000);
    if (isMobile) {
        await page.evaluate(() => { window.scrollBy(0, 100); });
    }
    else {
        await page.mouse.wheel(0, 100);
    }

    await page.waitForTimeout(500);

    await page.getByTestId('startOrdering').click();
    // await page.getByTestId('closeButton').click();

    await page.waitForTimeout(500);
    await page.getByTestId('categoryName-2').click();

    await page.waitForTimeout(500);
    if (isMobile) {
        await page.evaluate(() => { window.scrollBy(0, 100); });
    }
    else {
        await page.mouse.wheel(0, 100);
    }

    await page.waitForTimeout(500);
    await page.getByTestId('productName-0').click();

    const storeProductName = await page.getByTestId('productName-0');
    const storedName = await storeProductName.textContent();

    await page.waitForTimeout(500);
    const storeProductQuantity = await page.getByTestId('menuItemDetailQuantity');
    const storedQuantity = await storeProductQuantity.textContent();

    await page.getByTestId('addToCart').click();

    await page.waitForTimeout(500);
    await page.getByTestId('cartIcon').click();

    await page.waitForTimeout(500);

    const cartProductName = await page.getByTestId('cartProductName-0');
    const storedCartName = await cartProductName.textContent();

    const cartProductQuantity = await page.getByTestId('cartQuantity-0');
    const storedCartQuantity = await cartProductQuantity.textContent();

    if (storedName === storedCartName && storedQuantity === storedCartQuantity) {
        // await page.mouse.wheel(0, 700);

        await page.waitForTimeout(500);
        await page.getByTestId('cartPlaceOrder').click();

        await page.waitForTimeout(500);
        await page.getByTestId('payInCash').click();

        await page.waitForTimeout(500);
        await page.getByTestId('confirmButton').click();

        await page.waitForTimeout(500);
        await page.getByTestId('closeButton').click();

        await page.waitForTimeout(1000);
        await page.getByText('OK').click();
    }
});