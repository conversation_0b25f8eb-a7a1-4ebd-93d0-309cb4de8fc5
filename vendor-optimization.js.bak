const BrotliPlugin = require('brotli-webpack-plugin');

module.exports = {
  plugins: [
    new BrotliPlugin({
      asset: '[path].br[query]',
      test: /\.(js|css|html|svg|jpg|png)$/,
      threshold: 10240,
      minRatio: 0.8
    }),
  ],

  optimization: {
    splitChunks: {
      chunks: 'async',
      minSize: 20000,
      minRemainingSize: 0,
      minChunks: 1,
      maxAsyncRequests: 30,
      maxInitialRequests: 30,
      enforceSizeThreshold: 50000,
      cacheGroups: {
        // defaultVendors: {
        //   test: /[\\/]node_modules[\\/]/,
        //   priority: -10,
        //   reuseExistingChunk: true,
        // },
        // default: {
        //   minChunks: 2,
        //   priority: -20,
        //   reuseExistingChunk: true,
        // },
        commons: {
          test: /[\\/]node_modules[\\/]((?!(geofire-common|moment|nanoid|rc-checkbox|react-datepicker|react-google-autocomplete|react-horizontal-datepicker|react-native-vector-icons|react-native-web-linear-gradient|react-select)).*)[\\/]/,
          name: 'commons',
          chunks: 'all',
          enforce: true
        },
        geofireCommon: {
          test: /[\\/]node_modules[\\/](geofire-common)[\\/]/,
          name: 'geofireCommon',
          chunks: 'all',
          enforce: true
        },
        moment: {
          test: /[\\/]node_modules[\\/](moment)[\\/]/,
          name: 'moment',
          chunks: 'all',
          enforce: true
        },
        nanoid: {
          test: /[\\/]node_modules[\\/](nanoid)[\\/]/,
          name: 'nanoid',
          chunks: 'all',
          enforce: true
        },
        rcCheckbox: {
          test: /[\\/]node_modules[\\/](rc-checkbox)[\\/]/,
          name: 'rcCheckbox',
          chunks: 'all',
          enforce: true
        },
        datepicker: {
          test: /[\\/]node_modules[\\/](react-datepicker)[\\/]/,
          name: 'datepicker',
          chunks: 'all',
          enforce: true
        },
        googleAutocomplete: {
          test: /[\\/]node_modules[\\/](react-google-autocomplete)[\\/]/,
          name: 'googleAutocomplete',
          chunks: 'all',
          enforce: true
        },
        horizontalDatepicker: {
          test: /[\\/]node_modules[\\/](react-horizontal-datepicker)[\\/]/,
          name: 'horizontalDatepicker',
          chunks: 'all',
          enforce: true
        },
        vectorIcons: {
          test: /[\\/]node_modules[\\/](react-native-vector-icons)[\\/]/,
          name: 'vectorIcons',
          chunks: 'all',
          enforce: true
        },
        webLinearGradient: {
          test: /[\\/]node_modules[\\/](react-native-web-linear-gradient)[\\/]/,
          name: 'webLinearGradient',
          chunks: 'all',
          enforce: true
        },
        reactSelect: {
          test: /[\\/]node_modules[\\/](react-select)[\\/]/,
          name: 'reactSelect',
          chunks: 'all',
          enforce: true
        },
      },
    },
  },
}