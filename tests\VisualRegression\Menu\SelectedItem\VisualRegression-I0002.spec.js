const { test, expect } = require('@playwright/test');

test('Max Diff Pixels 250', async ({ page }) => {
    await page.goto('http://localhost:5400/web/outlet/hominsanttdi/menu/item?menuItem=%5Bobject%20Object%5D&outletData=%5Bobject%20Object%5D&refresh=function%20%28%29%20%7B%20%5Bnative%20code%5D%20%7D');

    await expect(page).toHaveScreenshot({ maxDiffPixels: 200 });
});

test('Max Diff Pixels 500', async ({ page }) => {
    await page.goto('http://localhost:5400/web/outlet/hominsanttdi/menu/item?menuItem=%5Bobject%20Object%5D&outletData=%5Bobject%20Object%5D&refresh=function%20%28%29%20%7B%20%5Bnative%20code%5D%20%7D');

    await expect(page).toHaveScreenshot({ maxDiffPixels: 200 });
});

test('Max Diff Pixels 1000', async ({ page }) => {
    await page.goto('http://localhost:5400/web/outlet/hominsanttdi/menu/item?menuItem=%5Bobject%20Object%5D&outletData=%5Bobject%20Object%5D&refresh=function%20%28%29%20%7B%20%5Bnative%20code%5D%20%7D');

    await expect(page).toHaveScreenshot({ maxDiffPixels: 200 });
});



