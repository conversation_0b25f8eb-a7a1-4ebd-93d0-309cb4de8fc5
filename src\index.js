import { AppRegistry } from "react-native";
import { createRoot } from 'react-dom/client';
import * as Sentry from "@sentry/browser";
import App from "./App";
import { TempStore } from "./store/tempStore";

Sentry.init({
  // glitchtip
  dsn : process.env.NODE_ENV === 'production' ? 'https://<EMAIL>/6288' : undefined,
  
  // sentry
  // dsn : process.env.NODE_ENV === 'production' ? 'https://<EMAIL>/4507229827891200' : undefined,
  
  // dsn : 'https://<EMAIL>/6288',
});

if (process.env.NODE_ENV === 'PROD') {
  console.log = () => { }
  console.error = () => { }
  console.debug = () => { }
}

AppRegistry.registerComponent("App", () => App);

const rootTag = document.getElementById("root");
const root = createRoot(rootTag);
root.render(<App />);

if ('serviceWorker' in navigator) {
  navigator.serviceWorker
    .register(`${process.env.PUBLIC_URL}/firebase-messaging-sw.js`)
    .then(function (registration) {
      console.log('Service Worker registered with scope:', registration.scope);
      global.newsw = registration;

      TempStore.update(s => {
        s.newsw = registration;
      });
    })
    .catch(function (error) {
      console.error('Service Worker registration failed:', error);
    });
}

// // Handle iOS bfcache restores and page visibility to refresh subscriptions/state
// window.addEventListener('pageshow', (e) => {
//   const evt = e; // PageTransitionEvent in supporting browsers
//   if (evt && evt.persisted) {
//     try {
//       // Re-run any lightweight revalidation you need; avoid full reloads
//       if (global && global.subscriberListenToSelectedOutletChangesCore) {
//         global.subscriberListenToSelectedOutletChangesCore();
//       }
//     } catch (err) {
//       console.warn('pageshow refresh failed:', err);
//     }
//   }
// });

// document.addEventListener('visibilitychange', () => {
//   if (document.visibilityState === 'visible') {
//     try {
//       if (global && global.subscriberListenToSelectedOutletChangesCore) {
//         global.subscriberListenToSelectedOutletChangesCore();
//       }
//     } catch (err) {
//       console.warn('visibility refresh failed:', err);
//     }
//   }
// });