import React, { useState, useEffect, useRef, useMemo } from "react";
import {
  StyleSheet,
  View,
  Text,
  Dimensions,
  useWindowDimensions,
  ScrollView,
  TouchableOpacity,
  Modal,
  TextInput
} from "react-native";
import Colors from "../constant/Colors";
import AsyncImage from "../components/asyncImage";
import Checkbox from 'rc-checkbox';
import Close from "react-native-vector-icons/AntDesign";
import FontAwesome from "react-native-vector-icons/FontAwesome";
import { isMobile } from "../util/commonFuncs";
import { ReactComponent as ArrowForward } from "../asset/svg/Arrow-forward-ios.svg";
import { ReactComponent as ArrowBack } from "../asset/svg/Arrow-back-ios.svg";
import { useLinkTo } from "@react-navigation/native"
import { prefix } from "../constant/env";
import { getDoc, doc } from "firebase/firestore";
import { Collections } from '../constant/firebase';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

const OutletPreviewScreen = (props) => {
  const { navigation, route } = props;
  const { previewId } = route.params || {};
  const linkTo = useLinkTo();
  const { width: windowWidth, height: windowHeight } = useWindowDimensions();
  const insets = useSafeAreaInsets();

  //////////////////////////////////////////////////

  // Scrolling functionality
  const scrollViewRef = useRef(null);
  const categoryPositions = useRef({});
  const isScrolling = useRef(false); // Flag to track scrolling status

  const scrollToCategory = (category) => {
    const position = categoryPositions.current[category];
    if (position !== undefined) {
      isScrolling.current = true; // Set to true when scrolling starts
      scrollViewRef.current.scrollTo({ y: position.y - 50, animated: true });
      
      // Reset scrolling status after animation
      setTimeout(() => {
        isScrolling.current = false;
      }, 500);
    }
  };

  const handleCategoryClick = (category) => {
    setSelectedCategory(category);
    scrollToCategory(category);
  };

  const handleScroll = (event) => {
    if (isScrolling.current) return; // Return if animation scrolling

    const scrollY = event.nativeEvent.contentOffset.y;
    const screenMiddle = scrollY + (windowHeight / 2);

    let closestCategory = null;
    let minDistance = Infinity;

    Object.entries(categoryPositions.current).forEach(([category, position]) => {
      const categoryMiddle = position.y + (position.height / 2);
      const distance = Math.abs(categoryMiddle - screenMiddle);
      
      if (distance < minDistance) {
        minDistance = distance;
        closestCategory = category;
      }
    });

    if (closestCategory !== selectedCategory) {
      setSelectedCategory(closestCategory);
    }
  };

  //////////////////////////////////////////////////
  // State management
  const [isLoading, setIsLoading] = useState(true);
  const [jsonObj, setJsonObj] = useState({});
  const [outletItemList, setOutletItemList] = useState({});
  const [selectedItem, setSelectedItem] = useState(null);
  const [selectedCategory, setSelectedCategory] = useState(null);

  // Fetch Json Data
  useEffect(() => {
    const loadData = async () => {
      try {
        const docRef = doc(global.db, Collections.OutletPreview, previewId);
        const docSnap = await getDoc(docRef);

        if (!docSnap.exists()) {
          global.errorMsg = 'Invalid link.';
          linkTo && linkTo(`${prefix}/scan`);
          return;
        }

        const metadata = docSnap.get('metadata');
        const parsedData = JSON.parse(metadata);

        if (!docSnap.exists() || parsedData === null || Object.keys(parsedData).length === 0) {
          global.errorMsg = 'Error Preview. Please try again later.';
          linkTo && linkTo(`${prefix}/scan`);
          return;
        }

        setJsonObj(parsedData);
        
        const outletItemList = parsedData.ItemCatalogSetup.reduce((acc, item) => {
          const category = item["Item Category"];
          if (!acc[category]) {
            acc[category] = [];
          }
          acc[category].push(item);
          return acc;
        }, {});
        
        setOutletItemList(outletItemList);
        setSelectedCategory(Object.keys(outletItemList)[0]);
        setIsLoading(false);
        
        navigation.setOptions({
          headerLeft: () => null,
          headerRight: () => null,
          headerTitle: () => (
            <View style={{alignItems: 'center'}}>
              <Text style={{ 
                fontSize: 20,
                lineHeight: 25,
                textAlign: "center",
                fontFamily: "NunitoSans-Bold",
                color: Colors.mainTxtColor,
              }}>
                {parsedData.BasicOutletInformation["Outlet Name"]}
              </Text>
              <Text style={{
                fontSize: 12,
                lineHeight: 12, 
                textAlign: "center",
                fontFamily: "NunitoSans-Regular",
                color: Colors.mainTxtColor,
              }}>
                Preview
              </Text>
            </View>
          )
        });
      } 
      catch (error) {
        console.error('Error loading data:', error);
        setIsLoading(false);
      }
    };

    loadData();
  }, []);

  //////////////////////////////////////////////////
  // Category navigation functions
  const switchCategory = (direction) => {
    const categories = Object.keys(outletItemList);
    const currentIndex = categories.indexOf(selectedCategory);
    const newIndex = direction === 'left' ? currentIndex - 1 : currentIndex + 1;

    if (direction === 'left' && newIndex >= 0) {
      setSelectedCategory(categories[newIndex]);
      scrollToCategory(categories[newIndex]);
    } else if (direction === 'right' && newIndex < categories.length) {
      setSelectedCategory(categories[newIndex]);
      scrollToCategory(categories[newIndex]);
    }
  }

  const switchCategoryL = () => switchCategory('left');
  const switchCategoryR = () => switchCategory('right');

  //////////////////////////////////////////////////

  // Stylesheet
  const styles = StyleSheet.create({
    container: {
      width: Dimensions.get("window").width,
      height: Dimensions.get("window").height,
      backgroundColor: "#ffffff",
      position: "relative",
    },
    outletCover: {
      width: "100%",
      height: Dimensions.get("window").height * 0.25,
      alignSelf: "center",
      resizeMode: "stretch",
    },
    infoTab: {
      backgroundColor: Colors.fieldtBgColor,
    },
    category: {
      paddingHorizontal: 25,
      justifyContent: "center",
      alignItems: "center",
    },
    categoryText: {
      fontFamily: "NunitoSans-Regular",
      fontSize: 16,
      color: Colors.mainTxtColor,
    },
    modalCloseButton: {
      position: "absolute",
      top: 17,
      left: 15,
      zIndex: 1000,
    },
    itemDetailImage: {
      width: '100%',
      height: Dimensions.get('window').height * 0.35,
      resizeMode: "cover",
      zIndex: -1,
    },
    itemDetailImagePlaceholder: {
      width: '100%',
      height: '100%',
      backgroundColor: Colors.fieldtBgColor
    },
    itemDetailsContainer: {
      width: windowWidth,
      height: windowHeight * 0.65,
      position: 'absolute', bottom: 0,
      paddingBottom: 80, paddingHorizontal: 20,
      backgroundColor: Colors.whiteColor,
      borderRadius: 25,
      borderWidth: 0.1,
      borderColor: Colors.fieldtBgColor,
    },
    specialRemarkInput: {
      height: windowHeight * 0.1,
      paddingHorizontal: 20,
      backgroundColor: Colors.fieldtBgColor,
      borderRadius: 12,
      fontSize: 14,
      fontFamily: 'NunitoSans-Regular',
      textAlignVertical: 'top',
      paddingVertical: 15,
      borderWidth: 0.5,
    },
    addBtn: {
      height: 45,
      display: 'flex',
      justifyContent: "center",
      alignItems: "center",
      borderColor: 'transparent',
    },
  });

  // Modal component for item details
  const ItemDetailsModal = ({ selectedItem }) => {
    const [modalVisible, setModalVisible] = useState(false);
    const [selectedVariant, setSelectedVariant] = useState(null);
    const [selectedAddOn, setSelectedAddOn] = useState({});
    const [quantity, setQuantity] = useState(1);

    const finalPrice = useMemo(() => {
      if (!selectedItem) return "0.00";

      let price = selectedItem["Item Price"];

      if (selectedVariant) {
        price += selectedVariant["additional Price"]; 
      }

      Object.values(selectedAddOn).forEach(addOn => {
        price += addOn["additional Price Each"] * addOn["quantity"] * quantity; 
      })

      price *= quantity;

      return price.toFixed(2);
    }, [selectedItem, selectedVariant, selectedAddOn, quantity])

    useEffect(() => {
      if (selectedItem) {
        setModalVisible(true);
      }
    }, [selectedItem]);

    const handleClose = () => {
      setModalVisible(false);

      setTimeout(() => {
        setSelectedItem(null);
        setSelectedVariant(null);
      }, 300);
    };

    const handleAddOnSelect = (addOn, amount) => {
      const currentQuantity = selectedAddOn[addOn["AddOn Name"]]?.quantity || 0;
      const newQuantity = currentQuantity + amount;
      
      if (amount > 0 && addOn["AddOn Max Quantity"] && newQuantity > addOn["AddOn Max Quantity"]) {
        return;
      }
      
      if (amount < 0 && newQuantity < (addOn["AddOn Min Quantity"] || 0)) {
        return;
      }

      setSelectedAddOn(prevAddOns => ({
        ...prevAddOns,
        [addOn["AddOn Name"]]: {
          ...addOn,
          quantity: newQuantity,
        },
      }));
    }

    if (!selectedItem) return null;

    return (
      <Modal
        visible={modalVisible}
        onRequestClose={handleClose}
      >
        <View style={{flex: 1, backgroundColor: Colors.modalBgColor}}>
          {/* Close Button */}
          <TouchableOpacity 
            onPress={() => {handleClose()}} 
            style={styles.modalCloseButton}
          >
            <Close name="closecircle" size={30} color={Colors.blackColor} />
          </TouchableOpacity>
          
          {/* Modal Image */}
          <AsyncImage
            source={{
              uri: selectedItem?.image
            }}
            style={styles.itemDetailImage}
            placeholderStyle={styles.itemDetailImagePlaceholder}
          />

          {/* Item Details */}
          <ScrollView style={styles.itemDetailsContainer}>
            <View style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              width: '100%',
              paddingTop: selectedItem && selectedItem.image ? 20 : 20,
            }}>
              {/* Item Name */}
              <View style={{ width: '70%' }}>
                <Text style={{
                  fontSize: 20,
                  textTransform: 'uppercase',
                  fontFamily: "NunitoSans-Bold",
                }}>
                  {selectedItem["Item Name"]}
                </Text>
              </View>
              
              {/* Item Price */}
              <View style={{ width: '30%', justifyContent: 'center', }}>
                <Text style={{
                    color: Colors.primaryColor,
                    fontFamily: "NunitoSans-Bold",
                    textAlign: 'right',
                    fontSize: 16,
                }}>
                  {selectedItem["Item Currency"]} {selectedItem["Item Price"].toFixed(2)}
                </Text>
              </View>
            </View>

            <Text style={{
              fontSize: 14,
              fontFamily: "NunitoSans-Regular",
              paddingTop: 5,
              color: '#86898f',
            }}>
              {selectedItem["Item Description"]}
            </Text>
            
            {/* Divider */}
            <View style={{ borderBottomWidth: 2, borderStyle: 'dotted', marginVertical: 25, opacity: 0.6, borderColor: 'grey' }} />

            {/* Variant / AddOns */}
            {/* Show text when variants or addons exist */}
            {
              (selectedItem["Variant"]?.length > 0 || selectedItem["AddOn"]?.length > 0) &&
              <Text style={{
                color: Colors.blackColor,
                marginBottom: 10,
                fontSize: 18,
                fontFamily: 'NunitoSans-SemiBold',
                marginBottom: 10
              }}>
                Let's make it better?
              </Text>
            }

            {/* Render variants if they exist */}
            {selectedItem["Variant"]?.length > 0 && (
              <View>
                <Text style={{
                  color: Colors.primaryColor,
                  fontSize: 16,
                  fontFamily: 'NunitoSans-SemiBold',
                  marginBottom: 10,
                }}>
                  {'Variants'}
                </Text>
                
                {selectedItem["Variant"].map((variant) => (
                  <TouchableOpacity style={{
                    height: "auto",
                    marginVertical: 10,
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                  }}>
                    <View style={{ flexDirection: 'row', flex: 1, flexWrap: 'wrap' }}>
                      <Text style={{
                        alignSelf: "center",
                        color: Colors.blackColor,
                        fontFamily: 'NunitoSans-Regular',
                        fontSize: 14,
                        maxWidth: '90%',
                      }}>
                        {`${variant["Variant Name"]}`}
                      </Text>
                    </View>

                    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                      <Text style={{
                        alignSelf: "center",
                        color: Colors.blackColor,
                        fontFamily: 'NunitoSans-Regular',
                        fontSize: 14,
                        paddingRight: 5,
                      }}>
                        {`+ RM ${variant["additional Price"].toFixed(2)}`}
                      </Text>

                      <Checkbox 
                        checked={selectedVariant === variant}
                        onChange={() => setSelectedVariant(variant)}
                      />
                    </View>
                  </TouchableOpacity>
                ))}

                <View style={{ borderBottomWidth: 2, borderStyle: 'dotted', marginVertical: 25, opacity: 0.6, borderColor: 'grey' }} />
              </View>
            )}

            {/* Render addons if they exist */}
            {selectedItem["AddOn"]?.length > 0 && (
              <View>
                <Text style={{
                  color: Colors.primaryColor,
                  fontSize: 16,
                  fontFamily: 'NunitoSans-SemiBold',
                  marginBottom: 10,
                }}>
                  {'Add Ons'}
                </Text>

                {selectedItem["AddOn"].map((addon) => (
                  <TouchableOpacity style={{
                    height: "auto",
                    marginVertical: 10,
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                  }}>
                    <View style={{ flexDirection: 'row', flex: 1, flexWrap: 'wrap', justifyContent: 'space-between'}}>
                      <Text style={{
                        alignSelf: "center",
                        color: Colors.blackColor,
                        fontFamily: 'NunitoSans-Regular',
                        fontSize: 14,
                        maxWidth: '90%',
                      }}>
                        {`${addon["AddOn Name"]}`}
                      </Text>

                      <View style={{
                        flexDirection: 'row',
                        alignItems: 'center',                             
                        justifyContent: 'space-between',
                        width: '55%',
                      }}>
                        <View style={{
                          display: 'flex',
                          flexDirection: "row",
                          borderWidth: 1,
                          borderRadius: 25,
                          borderColor: Colors.primaryColor,
                          alignItems: 'center',
                          width: '35%',
                          justifyContent: 'space-around'
                        }}>
                          <TouchableOpacity onPress={() => {handleAddOnSelect(addon, -1)}}>
                            <View style={[styles.addBtn, { height: 27, }]}>
                              <FontAwesome 
                                name="minus"
                                color={(selectedAddOn[addon["AddOn Name"]]?.quantity || 0) <= (addon["AddOn Min Quantity"] || 0) ? Colors.tabGrey : Colors.primaryColor}
                                size={10}
                              />
                            </View>
                          </TouchableOpacity>

                          <View style={[styles.addBtn, { height: 27, }]} >
                            <Text style={{
                              fontSize: 14,
                              fontFamily: "NunitoSans-Bold",
                              color: Colors.primaryColor,
                            }}>
                              {`${selectedAddOn[addon["AddOn Name"]]?.quantity || 0}`}
                            </Text>
                          </View>

                          <TouchableOpacity onPress={() => {handleAddOnSelect(addon, 1)}}>
                            <View style={[styles.addBtn, { height: 27}]}>
                              <FontAwesome 
                                name="plus"
                                color={(selectedAddOn[addon["AddOn Name"]]?.quantity || 0) >= (addon["AddOn Max Quantity"] || 0) ? Colors.tabGrey : Colors.primaryColor}
                                size={10}
                              />
                            </View>
                          </TouchableOpacity>
                        </View>

                        <View style={{
                          width: '65%',
                          flexDirection: 'row',
                          justifyContent: 'flex-end',
                        }}>
                          <Text style={{
                            fontFamily: 'NunitoSans-Regular',
                            fontSize: 14,
                            marginLeft: 10,
                          }}>
                            {`RM ${(addon["additional Price Each"] * quantity).toFixed(2)}`}
                          </Text>
                        </View>
                      </View>
                    </View>
                  </TouchableOpacity>
                ))}

                <View style={{ borderBottomWidth: 2, borderStyle: 'dotted', marginVertical: 25, opacity: 0.6, borderColor: 'grey' }} />
              </View>
            )}
            
            {/* Special Remarks */}
            <View style={{
              marginBottom: 15,
              marginTop: 5,
            }}>
              <Text style={{
                color: Colors.descriptionColor,
                fontSize: 17,
                fontFamily: 'NunitoSans-SemiBold',
                marginBottom: 15,
              }}>
                  Special Remarks:
              </Text>

              <TextInput
                underlineColorAndroid={Colors.fieldtBgColor}
                style={styles.specialRemarkInput}
                placeholder="eg: no onions"
                multiline={true}
              />
            </View>
          </ScrollView>

          <View style={{
            height: 60,
            width: windowWidth,
            position: "absolute",
            left: 0,
            right: 0,
            bottom: insets.bottom,
            flexDirection: 'row',
            paddingHorizontal: 20,
            paddingVertical: 7,
            backgroundColor: Colors.whiteColor,
            justifyContent: 'space-between',
          }}>
            <View style={{ flexDirection: "row", borderWidth: 1, borderRadius: 25, borderColor: Colors.primaryColor, alignItems: 'center', width: '25%', justifyContent: 'space-around' }}>
              <TouchableOpacity onPress={() => {setQuantity(quantity - 1)}}>
                <View style={[styles.addBtn]}>
                  <FontAwesome name="minus" color={Colors.primaryColor} size={12} />
                </View>
              </TouchableOpacity>
                
              <View style={styles.addBtn}>
                <Text style={{
                  fontSize: 18,
                  fontFamily: "NunitoSans-Bold",
                  color: Colors.primaryColor,
                  marginBottom: isMobile() ? 0 : 3,
                }}>
                  {quantity}
                </Text>
              </View>

              <TouchableOpacity onPress={() => {setQuantity(quantity + 1)}}>
                <View style={[styles.addBtn]}>
                  <FontAwesome name="plus" color={Colors.primaryColor} size={12} />
                </View>
              </TouchableOpacity>
            </View>

            <View style={{ width: '70%', backgroundColor: Colors.primaryColor, alignItems: 'center', borderRadius: 20, }}>
              <TouchableOpacity disabled>
                <View style={{paddingVertical: 12, flexDirection: 'row', alignItems: 'center'}}>
                  <Text
                    style={{
                        color: "#ffffff",
                        fontSize: 16,
                        // fontWeight: "bold",
                        fontFamily: 'NunitoSans-Regular',
                        borderRightWidth: 1,
                        borderColor: Colors.whiteColor,
                        paddingRight: 7,
                    }}
                  >
                    {"Add to Cart"}
                  </Text>

                  <Text style={{
                    color: "#ffffff",
                    fontSize: 16,
                    fontFamily: 'NunitoSans-Bold',
                    paddingLeft: 7,
                  }}>
                    {`RM ${finalPrice}`}
                  </Text>
                </View>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    );
  };

  // Main Content
  if (isLoading) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <Text style={{fontFamily: "NunitoSans-Regular"}}>Loading...</Text>
      </View>
    );
  }

  return (
    <View
      nativeID="rootOutletMenu"
      style={styles.container}
    >
      <ItemDetailsModal selectedItem={selectedItem} />

      <ScrollView 
        ref={scrollViewRef}
        style={{paddingBottom: 120}}
        onScroll={handleScroll}
        scrollEventThrottle={16}
      >
        {/* Outlet Cover Image */}
        <AsyncImage
          source={{
            uri: `${jsonObj.BasicOutletInformation["Outlet Image Url"]}`,
          }}
          hideLoading={true}
          style={styles.outletCover}
        />
      
        {/* Category List */}
        <View style={{ 
          width: '100%',
          position: 'sticky',
          top: 0,
          zIndex: 1,
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          backgroundColor: '#fff',
        }}>
          {/* Left Arrow */}
          <View style={{
            alignItems: "center",
            paddingVertical: 15,
            paddingLeft: 6,
            width: 20,
            height: 47,
            backgroundColor: Colors.highlightColor,
            opacity: 1,
            shadowColor: "#000",
            shadowOffset: {
              width: 1,
              height: 0.5,
            },
            shadowOpacity: 0.12,
            shadowRadius: 3.22,
          }}>
            <TouchableOpacity onPress={switchCategoryL}>
              <ArrowBack
                name="arrow-back-ios"
                size={40}
                color={Colors.darkBgColor}
                style={{ height: '120%', zIndex: 3, elevation: 1 }}
              />
            </TouchableOpacity>
          </View>

          <ScrollView
            horizontal={true}
            alwaysBounceHorizontal={true}
            showsHorizontalScrollIndicator={isMobile() ? false : true}
            style={styles.infoTab}
          >
            {
              outletItemList && Object.keys(outletItemList).map((category, index) => {
                return (
                  <TouchableOpacity
                    key={index}
                    onPress={() => handleCategoryClick(category)}
                  >
                    <View style={[styles.category]}>
                      <View style={{
                        borderBottomColor: selectedCategory === category ? Colors.primaryColor : null,
                        borderBottomWidth: selectedCategory === category ? 3 : 0,
                      }}>
                        <Text
                          style={{
                            textTransform: "capitalize",
                            paddingVertical: 12,
                            fontFamily: selectedCategory == category ? "NunitoSans-Bold" : "NunitoSans-Regular",
                            color: selectedCategory == category ? Colors.primaryColor : Colors.mainTxtColor,
                            fontSize: 16,
                          }}
                        >
                          {category}
                        </Text>
                      </View>
                    </View>
                  </TouchableOpacity>
                )
              })
            }
          </ScrollView>
          
          {/* Right Arrow */}
          <View
            style={{
              alignItems: "center",
              alignSelf: "flex-end",
              paddingVertical: 15,
              paddingRight: 1,
              width: 20,
              height: 47,
              backgroundColor: Colors.highlightColor,
              opacity: 1,
              shadowColor: "#000",
              shadowOffset: {
                width: 1,
                height: 0.5,
              },
              shadowOpacity: 0.12,
              shadowRadius: 3.22,
            }}
          >
            <TouchableOpacity onPress={switchCategoryR}>
              <ArrowForward
                name="arrow-forward-ios"
                size={40}
                color={Colors.darkBgColor}
                style={{ height: '120%', zIndex: 3, elevation: 1, }}
              />
            </TouchableOpacity>
          </View>
        </View>
      
        {
          outletItemList && Object.keys(outletItemList).map((category, index) => {
            return (
              <View 
                key={index} 
                onLayout={(event) => {
                  const { y, height } = event.nativeEvent.layout;
                  categoryPositions.current[category] = { y, height };
                }}
                style={{ display: 'flex', alignItems: 'center' }}
              >
                {/* Category Name */}
                <View style={{
                  width: '95%',
                  backgroundColor: Colors.primaryColor,
                  marginTop: 10,
                  marginBottoM: 5,
                }}>
                  <Text style={{
                    textTransform: "capitalize",
                    paddingVertical: 12,
                    fontFamily: "NunitoSans-Bold",
                    color: Colors.whiteColor,
                    fontSize: 16,
                    textAlign: 'center',
                  }}>
                    {category}
                  </Text>
                </View>

                {/* Item List */}
                {outletItemList[category].map((item, idx) => (
                  <TouchableOpacity 
                    key={idx}
                    style={{
                      width: '90%',
                      margin: 10,
                    }}
                    onPress={() => setSelectedItem(item)}
                  >
                    <View style={{
                      flexDirection: "row",
                      alignContent: "center",
                      alignItems: "center",
                      display: "flex",
                      justifyContent: "flex-start",
                    }}>
                      {/* Item Image */}
                      <View style={{
                        backgroundColor: Colors.secondaryColor,
                        width: isMobile()
                          ? windowWidth * 0.22
                          : windowWidth * 0.05,
                        height: isMobile()
                          ? windowWidth * 0.22
                          : windowWidth * 0.05,
                        borderRadius: 10,
                      }}>
                        <AsyncImage
                          source={{ uri: item.image }}
                          style={{
                            width: isMobile()
                              ? windowWidth * 0.22
                              : windowWidth * 0.05,
                            height: isMobile()
                              ? windowWidth * 0.22
                              : windowWidth * 0.05,
                            borderRadius: 10,
                          }}
                        />
                      </View>
                      
                      {/* Item Details */}
                      <View style={{
                        marginLeft: 15,
                        width: "50%",
                      }}>
                        <Text 
                          style={{
                            textTransform: "uppercase",
                            fontFamily: "NunitoSans-Bold",
                          }}
                          numberOfLines={3}
                        >
                          {item["Item Name"]}
                        </Text>

                        <View
                          style={{
                            flexDirection: "row",
                            alignItems: "center",
                          }}
                        >
                          <Text
                            style={{
                              color: Colors.primaryColor,
                              fontFamily: "NunitoSans-Bold",
                              paddingTop: 5,
                              fontSize: 16
                            }}
                          >
                            {`${item["Item Currency"]} ${item["Item Price"].toFixed(2)}`}
                          </Text>
                        </View>
                      </View>
                
                      {/* Add Button */}
                      <View
                        style={{
                          flexDirection: "row",
                          ...(!isMobile() && {
                            height: 26,
                            alignSelf: "center",
                          }),
                        }}
                      >
                        <View style={{
                          backgroundColor: Colors.primaryColor,
                          width: 68,
                          height: 26,
                          borderRadius: 10,
                          justifyContent: "center",
                          alignSelf: "center",
                        }}>
                          <TouchableOpacity onPress={() => {}}>
                            <Text
                              style={{
                                alignSelf: "center",
                                color: Colors.whiteColor,
                                fontSize: 13,
                                fontFamily: "NunitoSans-Bold",
                              }}
                            >
                              {"Add"}
                            </Text>
                          </TouchableOpacity>
                        </View>
                      </View>
                    </View>
                  </TouchableOpacity>
                ))}
              </View>
            );
          })
        }
      </ScrollView>
    </View>
  );
};

export default OutletPreviewScreen;
