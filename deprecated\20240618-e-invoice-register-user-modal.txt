{/* 20240618 e-invoice */}
      <Modal
        style={{ flex: 1 }}
        visible={showSignUpMember}
        transparent={true}
        animationType="none"
      >
        <View
          style={{
            backgroundColor: "rgba(0,0,0,0.5)",
            // flex: 1,
            justifyContent: "center",
            alignItems: "center",
            minHeight: windowHeight,
          }}
        >
          <View
            style={[
              styles.confirmBox,
              {
                // height: windowHeight * 0.35,
                height: windowHeight * 0.3,

                ...isMobile() && {
                  height: windowHeight * 0.8,
                },
              },
            ]}
          >
            <TouchableOpacity
              onPress={() => {
                TempStore.update((s) => {
                  s.showSignUpMember = false;
                })
              }}
            >
              <View
                style={{
                  alignSelf: "flex-end",
                  padding: 14,
                }}
              >
                <AntDesign name="closecircle" size={28} color={"#b0b0b0"} />
              </View>
            </TouchableOpacity>
            <View style={{ marginBottom: 10, paddingHorizontal: 10 }}>
              <Text
                style={{
                  textAlign: "center",
                  // fontWeight: '700',
                  fontSize: 18,
                  fontFamily: "NunitoSans-Bold",
                }}
              >
                {"Sign Up As Member"}
              </Text>
            </View>
            <ScrollView
              style={{
                // alignSelf: "center",
                marginTop: 10,
                // justifyContent: "center",
                // alignItems: "center",
                // width: 250,
                // height: 40,
                // alignContent: "center",
                // marginTop: 12,
              }}
            >
              <View style={{ marginTop: 10, width: isMobile() ? "80%" : "94%", alignSelf: "center", zIndex: 1, }}>
                {/* name */}
                {userName === '' || userPhone === '' ?
                  <>
                    <View>
                      <Text
                        style={[
                          styles.payment,
                          {
                            // paddingHorizontal: 24,
                            fontSize: windowWidth <= 360 ? 14 : 16
                          },
                        ]}
                      >
                        Name
                      </Text>
                      <TextInput
                        onChangeText={(text) => { setEpNameToTemp(text) }}
                        style={[styles.textInput, , { width: '93%' }]}
                        placeholder="Name (Ex: John)"
                        value={epNameToTemp}
                      />
                    </View>
                    {/* phone */}
                    <View>
                      <Text
                        style={[
                          styles.payment,
                          {
                            // paddingHorizontal: 24,
                            fontSize: windowWidth <= 360 ? 14 : 16
                          },
                        ]}
                      >
                        Phone
                      </Text>
                      <TextInput
                        onChangeText={(text) => { setEpPhoneToTemp(text) }}
                        style={[styles.textInput, , { width: '93%' }]}
                        placeholder="Phone (Ex: 60123456789)"
                        value={epPhoneToTemp}
                      />
                    </View>
                    <View style={{ flexDirection: 'row', alignItems: 'center', }}>
                      <Checkbox
                        checked={eInvoiceInfo}
                        onChange={(e) => {
                          setEInvoiceInfo(!eInvoiceInfo);
                        }}
                        style={{
                          marginRight: eInvoiceInfo ? 4 : 5,
                          marginTop: 1,
                        }}
                      />
                      <Text
                        style={[
                          styles.payment,
                          {
                            // paddingHorizontal: 24,
                            fontSize: windowWidth <= 360 ? 14 : 16,
                            marginBottom: 0
                          },
                        ]}
                      >
                        Register for e-invoice
                      </Text>
                    </View>
                  </>
                  :
                  <></>}

                {eInvoiceInfo || (userName !== '' && userPhone !== '') ?
                  <>
                    {/* email */}
                    < View >
                      <Text
                        style={[
                          styles.payment,
                          {
                            // paddingHorizontal: 24,
                            fontSize: windowWidth <= 360 ? 14 : 16
                          },
                        ]}
                      >
                        Email
                      </Text>
                      <TextInput
                        onChangeText={(text) => { setEpEmailToTemp(text) }}
                        style={[styles.textInput, , { width: '93%' }]}
                        placeholder="Email"
                        value={epEmailToTemp}
                      />
                    </View>

                    {/* id type & id */}
                    <View style={{ flexDirection: 'row', justifyContent: 'space-between', }}>
                      <View style={{ width: '30%' }}>
                        <Text style={[
                          styles.payment,
                          {
                            // paddingHorizontal: 24,
                            fontSize: windowWidth <= 360 ? 14 : 16,
                            marginBottom: 5,
                          },
                        ]}>
                          ID Type
                        </Text>
                        <DropDownPicker
                          style={{
                            backgroundColor: Colors.fieldtBgColor,
                            width: '93%',
                            height: 50,
                            borderRadius: 10,
                            borderWidth: 1,
                            borderColor: "#E5E5E5",
                            flexDirection: "row",

                          }}
                          dropDownContainerStyle={{
                            width: '93%',
                            backgroundColor: Colors.fieldtBgColor,
                            borderColor: "#E5E5E5",
                          }}
                          labelStyle={{
                            marginLeft: 5,
                            flexDirection: "row",
                          }}
                          textStyle={{
                            fontSize: windowWidth <= 360 ? 14 : 16,
                            fontFamily: 'NunitoSans-Regular',

                            marginLeft: 5,
                            paddingVertical: 15,
                            paddingLeft: 15,
                            flexDirection: "row",
                          }}
                          selectedItemContainerStyle={{
                            flexDirection: "row",
                          }}

                          showArrowIcon={true}
                          ArrowDownIconComponent={({ style }) => (
                            <Ionicons
                              size={25}
                              color={Colors.fieldtTxtColor}
                              style={{ paddingHorizontal: 5, marginTop: 7.5 }}
                              name="chevron-down-outline"
                            />
                          )}
                          ArrowUpIconComponent={({ style }) => (
                            <Ionicons
                              size={25}
                              color={Colors.fieldtTxtColor}
                              style={{ paddingHorizontal: 5, marginTop: 7.5 }}
                              name="chevron-up-outline"
                            />
                          )}

                          showTickIcon={true}
                          TickIconComponent={({ press }) => (
                            <Ionicons
                              style={{ paddingHorizontal: 5, marginTop: 7.5 }}
                              color={
                                press ? Colors.fieldtBgColor : Colors.primaryColor
                              }
                              name={'md-checkbox'}
                              size={25}
                            />
                          )}
                          placeholder={'Select an ID Type'}
                          placeholderStyle={{
                            color: Colors.fieldtTxtColor
                          }}
                          searchable
                          searchableStyle={{
                            paddingHorizontal: windowWidth * 0.0079,
                          }}
                          value={epIdTypeToTemp}
                          items={idtypeOption}
                          onSelectItem={(item) => {
                            setEpIdTypeToTemp(item.value);
                          }}
                          open={openIdType}
                          setOpen={setOpenIdType}
                        />
                      </View>

                      <View style={{ width: '70%' }}>
                        <Text style={[
                          styles.payment,
                          {

                            fontSize: windowWidth <= 360 ? 14 : 16
                          },
                        ]}>
                          ID
                        </Text>
                        <TextInput
                          onChangeText={(text) => { setEpIdToTemp(text) }}
                          style={[styles.textInput, { width: '90%' }]}
                          placeholder="ID"
                          value={epIdToTemp}
                        />
                      </View>
                    </View>

                    {/* tin */}
                    <View style={{ marginTop: 5, zIndex: -1, }}>
                      <Text
                        style={[
                          styles.payment,
                          {
                            // paddingHorizontal: 24,
                            fontSize: windowWidth <= 360 ? 14 : 16
                          },
                        ]}
                      >
                        TIN
                      </Text>
                      <TextInput
                        onChangeText={(text) => { setEpTinToTemp(text) }}
                        style={[styles.textInput, { width: '93%' }]}
                        placeholder="TIN"
                        value={epTinToTemp}
                      />
                    </View>

                    {/* address */}
                    <View style={{ marginTop: 5, zIndex: -1, }}>
                      <Text
                        style={[
                          styles.payment,
                          {
                            // paddingHorizontal: 24,
                            fontSize: windowWidth <= 360 ? 14 : 16
                          },
                        ]}
                      >
                        Address
                      </Text>
                      <TextInput
                        onChangeText={(text) => { setEpAddr1ToTemp(text) }}
                        style={[styles.textInput, { width: '93%' }]}
                        placeholder="Address"
                        value={epAddr1ToTemp}
                      />
                    </View>

                    {/* city & zip code */}
                    <View style={{ flexDirection: 'row', justifyContent: 'space-between', zIndex: -1, }}>
                      <View style={{ width: '50%' }}>
                        <Text style={[
                          styles.payment,
                          {
                            // paddingHorizontal: 24,
                            fontSize: windowWidth <= 360 ? 14 : 16
                          },
                        ]}>
                          City
                        </Text>
                        <TextInput
                          onChangeText={(text) => { setEpCityToTemp(text) }}
                          style={[styles.textInput, { width: '85%' }]}
                          placeholder="City"
                          value={epCityToTemp}
                        />
                      </View>

                      <View style={{ width: '50%' }}>
                        <Text style={[
                          styles.payment,
                          {
                            // paddingHorizontal: 24,
                            fontSize: windowWidth <= 360 ? 14 : 16
                          },
                        ]}>
                          Zip Code
                        </Text>
                        <TextInput
                          onChangeText={(text) => { setEpCodeToTemp(text) }}
                          style={[styles.textInput, , { width: '85%' }]}
                          placeholder="Zip Code"
                          value={epCodeToTemp}
                        />
                      </View>
                    </View>

                    {/* province */}
                    <View style={{}}>
                      <Text style={[
                        styles.payment,
                        {
                          // paddingHorizontal: 24,
                          fontSize: windowWidth <= 360 ? 14 : 16,
                          marginBottom: 5,
                        },
                      ]}>
                        Province
                      </Text>

                      <DropDownPicker
                        style={{
                          backgroundColor: Colors.fieldtBgColor,
                          width: '93%',
                          height: 50,
                          borderRadius: 10,
                          borderWidth: 1,
                          borderColor: "#E5E5E5",
                          flexDirection: "row",
                        }}
                        dropDownContainerStyle={{
                          width: '93%',
                          backgroundColor: Colors.fieldtBgColor,
                          borderColor: "#E5E5E5",
                        }}
                        labelStyle={{
                          marginLeft: 5,
                          flexDirection: "row",
                        }}
                        textStyle={{
                          fontSize: windowWidth <= 360 ? 14 : 16,
                          fontFamily: 'NunitoSans-Regular',

                          marginLeft: 5,
                          paddingVertical: 15,
                          paddingLeft: 15,
                          flexDirection: "row",
                        }}
                        selectedItemContainerStyle={{
                          flexDirection: "row",
                        }}

                        showArrowIcon={true}
                        ArrowDownIconComponent={({ style }) => (
                          <Ionicons
                            size={25}
                            color={Colors.fieldtTxtColor}
                            style={{ paddingHorizontal: 5, marginTop: 7.5 }}
                            name="chevron-down-outline"
                          />
                        )}
                        ArrowUpIconComponent={({ style }) => (
                          <Ionicons
                            size={25}
                            color={Colors.fieldtTxtColor}
                            style={{ paddingHorizontal: 5, marginTop: 7.5 }}
                            name="chevron-up-outline"
                          />
                        )}

                        showTickIcon={true}
                        TickIconComponent={({ press }) => (
                          <Ionicons
                            style={{ paddingHorizontal: 5, marginTop: 7.5 }}
                            color={
                              press ? Colors.fieldtBgColor : Colors.primaryColor
                            }
                            name={'md-checkbox'}
                            size={25}
                          />
                        )}
                        placeholder={'Select a Province'}
                        placeholderStyle={{
                          color: Colors.fieldtTxtColor,
                          // marginTop: 15,
                        }}
                        searchable
                        searchableStyle={{
                          paddingHorizontal: windowWidth * 0.0079,
                        }}
                        value={epStateToTemp}
                        items={provinceOption}
                        onSelectItem={(item) => {
                          setEpStateToTemp(item.value);
                        }}
                        open={openProvince}
                        setOpen={setOpenProvince}
                      />
                    </View>
                  </>
                  : null}
                <TouchableOpacity
                  // disabled={isLoading}
                  style={{
                    marginHorizontal: 48,
                    marginTop: 30,
                    marginBottom: 30,
                  }}
                  onPress={() => { registerUser(); }}
                >
                  <View
                    style={{
                      backgroundColor: Colors.primaryColor,
                      // padding: 28,
                      // paddingVertical: 12,
                      padding: 20,
                      paddingVertical: 12,
                      borderRadius: 10,
                      alignItems: "center",

                      // marginHorizontal: 48,
                      // marginTop: 30,
                      // marginBottom: 30,

                      shadowColor: "#000",
                      shadowOffset: {
                        width: 0,
                        height: 1,
                      },
                      shadowOpacity: 0.22,
                      shadowRadius: 2.22,
                      elevation: 3,

                      ...(!isMobile() && {
                        width: "100%",
                        alignSelf: "center",
                      }),
                    }}
                  >
                    <Text
                      style={{
                        color: "#ffffff",
                        fontSize: 16,
                        fontFamily: "NunitoSans-SemiBold",
                      }}
                    >
                      Sign Up
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
            </ScrollView>
          </View>
        </View >
      </Modal>