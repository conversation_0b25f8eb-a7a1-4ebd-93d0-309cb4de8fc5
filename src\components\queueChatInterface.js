import React, { useState, useEffect, useMemo } from 'react';
import { Text, StyleSheet, ScrollView, View, ActivityIndicator, Image, TouchableOpacity } from 'react-native';
import { doc, onSnapshot, updateDoc, arrayUnion } from 'firebase/firestore';
import { Collections } from '../constant/firebase';
import { CommonStore } from '../store/commonStore';
import { UserStore } from '../store/userStore';
import Colors from '../constant/Colors';
import { TextInput } from 'react-native-web';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { updateWebTokenAnonymous } from '../util/commonFuncs';

const QueueChatInterface = ({ queueId }) => {
  const [messages, setMessages] = useState([]);
  const [sendingMessage, setSendingMessage] = useState(false);
 
  // For Require Input
  const [input, setInput] = useState('');

  const selectedOutlet = CommonStore.useState((s) => s.selectedOutlet);
  const selectedMerchant = CommonStore.useState((s) => s.selectedMerchant);
  const userIdAnonymous = UserStore.useState(s => s.userIdAnonymous);

  const requireInput = useMemo(() => {
    if (!messages || messages.length === 0) return false;
    const lastMessage = messages[messages.length - 1];
    return lastMessage?.requireContent;
  }, [messages]);

  ////////////////////////////////////////////////////////////

  useEffect(() => {
    if (!queueId || queueId === '') return;

    // Set up realtime listener for the specific queue document
    const unsubscribe = onSnapshot(
      doc(global.db, Collections.UserQueue, queueId.toString()), 
      (snapshot) => {
        if (snapshot.exists()) {
          const messageData = {
            id: snapshot.id,
            ...snapshot.data()
          };
          setMessages(messageData.chat);
        }
      },
      (error) => {
        console.error("Error listening to queue chat:", error);
      }
    );

    // Cleanup listener on unmount
    return () => unsubscribe();
  }, [queueId]);

  ////////////////////////////////////////////////////////////

  const handleOptionPress = async (option) => {
    if (option === 'CANCEL') {
      if (!window.confirm('Are you sure you want to cancel your queue?')) {
        return;
      }
    }

    try {
      setSendingMessage(true);

      const queueRef = doc(global.db, Collections.UserQueue, queueId.toString());

      await updateDoc(queueRef, {
        chat: arrayUnion({
          message: option,
          from: userIdAnonymous,
          dt: Date.now()
        })
      });

      updateWebTokenAnonymous(1000);
    }
    catch (error) {
      console.error("Error sending message:", error);
      setSendingMessage(false);
    }
    finally {
      setSendingMessage(false);
    }
  }

  const handleInputSend = async (input) => {
    try {
      setSendingMessage(true);

      const queueRef = doc(global.db, Collections.UserQueue, queueId.toString());

      await updateDoc(queueRef, {
        chat: arrayUnion({
          message: input,
          from: userIdAnonymous,
          dt: Date.now()
        })
      });

      updateWebTokenAnonymous(1000);
    }
    catch (error) {
      CommonStore.update((s) => {
        s.alertObj = {
          title: 'Error',
          message: 'Invalid Input.',
        };
      });

      setSendingMessage(false);
    }
    finally {
      setSendingMessage(false);
    }
  }

  ////////////////////////////////////////////////////////////

  const messageBubble = (message) => {
    const isFromOutlet = selectedOutlet?.uniqueId ? message.from === selectedOutlet.uniqueId : false;
    const hasOption = message.options && message.options.length > 0;
    const isLastMessage = messages.indexOf(message) === messages.length - 1;

    const messageParseDict = {
      'CONFIRM': 'Confirm Queue',
      'CANCEL': 'Cancel Queue', 
      'REMARKS': 'Add Remark',
    }
    
    const parsedMessage = messageParseDict[message.message] || message.message;

    const merchantLogo = () => {
      return (
        <Image
          source={selectedMerchant && selectedMerchant.logo !== ""
            ? { uri: selectedMerchant.logo }
            : require('../asset/image/logo.png')}
          style={{
            width: 30,
            height: 30,
            alignSelf: 'flex-start',
            borderRadius: 15,
          }}
          defaultSource={require('../asset/image/logo.png')}
          onError={(e) => console.log("Error loading image:", e.nativeEvent.error)}
        />
      );
    }

    return (
      <View >
        {isFromOutlet ? 
          // Outlet Message
          <View style={[styles.messageContainer, styles.outletMessageContainer]}>
            {merchantLogo()}

            <View style={[styles.messageBubble, styles.outletMessageBubble]}>
              <Text style={styles.outletMessage}>{parsedMessage}</Text>
              
              {/* Render Options */}
              {(hasOption && isLastMessage && !sendingMessage) &&
                <View style={styles.optionContainer}>
                  {message.options.map(option => {
                    const parsedOption = messageParseDict[option] || option;

                    return (
                      <TouchableOpacity style={styles.optionButton} onPress={() => handleOptionPress(option)} disabled={sendingMessage}>
                        <Text style={styles.optionsStyle}>{parsedOption}</Text>
                      </TouchableOpacity>
                    );
                  })}
                </View>
              }
            </View>
          </View>
          : 
          // Customer Message
          <View style={[styles.messageContainer, styles.customerMessageContainer]}>
            <View style={[styles.messageBubble, styles.customerMessageBubble]}>
              <Text style={styles.customerMessage}>{parsedMessage}</Text>
            </View>
          </View>
        }
      </View>
    );
  }

  if (messages.length === 0 || !selectedOutlet) {
    return <ActivityIndicator size="large" color={Colors.primaryColor} />;
  }

  return (
    <>
      <ScrollView style={styles.container}>
          {messages.map(message => messageBubble(message))}
      </ScrollView>

      {requireInput && (
        <View style={{width: '100%', flexDirection: 'row', alignItems: 'center', justifyContent: 'flex-end'}}>
          <TextInput
            style={{
              flex: 1,
              borderWidth: 1,
              borderColor: Colors.primaryColor,
              borderRadius: 5,
              padding: 10,
              marginHorizontal: 10,
              marginVertical: 5,
              fontFamily: 'NunitoSans-Regular',
              backgroundColor: Colors.fieldtBgColor
            }}
            disabled={sendingMessage}
            placeholder="Type a message..."
            placeholderTextColor="#999"
            value={input}
            onChangeText={setInput}
          ></TextInput>

          <TouchableOpacity style={{backgroundColor: Colors.primaryColor, padding: 10, borderRadius: 20}} disabled={sendingMessage} onPress={() => handleInputSend(input)}>
            <Ionicons name="send-sharp" size={16} color={Colors.whiteColor} />
          </TouchableOpacity>
        </View>
      )}
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: '100%',
    paddingVertical: 20,
    flexDirection: 'column',
  },
  messageContainer: {
    width: '100%',
    paddingBottom: 10,
    gap: 10,
  },
  messageBubble: {
    textWrap: 'wrap',
    maxWidth: '75%',
    padding: 10,
    borderRadius: 15,
  },
  outletMessageContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
  },
  outletMessageBubble: {
    backgroundColor: Colors.highlightColor,
  },
  outletMessage: {
    fontFamily: 'NunitoSans-Regular',
  },
  customerMessageContainer: {
    justifyContent: 'flex-end',
    alignItems: 'flex-end',
  },
  customerMessage: {
    fontFamily: 'NunitoSans-Regular',
  },
  customerMessageContent: {
    color: Colors.fieldtTxtColor,
    fontSize: 14,
  },
  customerMessageBubble: {
    backgroundColor: Colors.primaryColor,
  },

  // Options Style
  optionContainer: {
    marginTop: 10,
    flexDirection: 'column',
    width: '100%',
    gap: 5,
  },
  optionButton: {
    width: '95%',
    alignSelf: 'center',
    padding: 10,
    borderRadius: 5,
    backgroundColor: Colors.whiteColor,
  },
  optionsStyle: {
    fontFamily: 'NunitoSans-Regular',
    textAlign: 'center',
  },
});

export default QueueChatInterface;