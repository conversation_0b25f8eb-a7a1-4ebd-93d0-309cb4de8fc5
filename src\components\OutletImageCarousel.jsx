import { useState, useEffect } from "react";
import { View, Animated, useWindowDimensions } from "react-native";
import { isMobile as useIsMobile } from "../util/commonFuncs";
import { CommonStore } from "../store/commonStore";
import AsyncImage from "../components/asyncImage";

const OutletImageCarousel = () => {
  const { height: windowHeight } = useWindowDimensions();
  const isMobile = useIsMobile();

  const selectedOutlet = CommonStore.useState(s => s.selectedOutlet);
  const availablePromotions = CommonStore.useState(s => s.availablePromotions);

  const selectedOutletCover = selectedOutlet?.cover || "";
  const [images, setImages] = useState([selectedOutletCover]);
  const [promoName, setPromoName] = useState([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [imageOpacity] = useState(new Animated.Value(1));

  // Load Images
  useEffect(() => {
    if (availablePromotions?.length) {
      const promotionImages = availablePromotions.map(p => p.image);
      const promotionNames = availablePromotions.map(p => p.campaignName);
      const allImages = [
        selectedOutletCover,
        ...promotionImages.filter(img => img !== selectedOutletCover),
      ];
      setImages(allImages);
      setPromoName(promotionNames);
    }
  }, [availablePromotions]);

  useEffect(() => {
    if (images.length <= 1) return;

    const interval = setInterval(() => {
      Animated.timing(imageOpacity, {
        toValue: 0,
        duration: 1000,
        useNativeDriver: true,
      }).start(() => {
        setCurrentIndex(prev => (prev + 1) % images.length);
        Animated.timing(imageOpacity, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }).start();
      });
    }, 5000);

    return () => clearInterval(interval);
  }, [images, imageOpacity]);

  const imageHeight = selectedOutlet?.portraitCoverOptions ? windowHeight * 0.6 : windowHeight * 0.25;

  ////////////////////////////////////////////////////////////

  return images.length > 1 ? (
    <View style={{ position: "relative" }}>
      <View
        style={{
          position: "absolute",
          bottom: 15,
          left: 15,
          zIndex: 1000,
          paddingHorizontal: 7,
          backgroundColor: "white",
          opacity: 0.8,
          borderRadius: 10,
        }}
      >
        <Animated.Text
          style={{
            opacity: imageOpacity,
            fontSize: 16,
            fontFamily: "NunitoSans-SemiBold",
          }}
        >
          {currentIndex === 0 ? "" : promoName[currentIndex - 1]}
        </Animated.Text>
      </View>
      <Animated.Image
        style={{
          height: imageHeight,
          opacity: imageOpacity,
          resizeMode: "cover",
        }}
        source={{ uri: images[currentIndex] }}
      />
    </View>
  ) : (
    <AsyncImage
      source={{ uri: selectedOutletCover }}
      hideLoading
      item={selectedOutlet}
      style={{
        width: "100%",
        alignSelf: "center",
        height: imageHeight,
        resizeMode: "stretch",
      }}
    />
  );
};

export default OutletImageCarousel;
