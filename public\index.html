<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8" />
  <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
  <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no, viewport-fit=cover" />
  <meta name="theme-color" content="#000000" />
  <meta name="description" content="KooDoo Web Order System" />

  <META HTTP-EQUIV="Pragma" CONTENT="no-cache" />
  <META HTTP-EQUIV="Cache-Control" CONTENT="no-cache" />
  <META HTTP-EQUIV="Expires" CONTENT="0" />

  <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
  <!--
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
  <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
  <!--
      Notice the use of %PUBLIC_URL% in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "%PUBLIC_URL%/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->
  <title>KooDoo Web Order</title>

  <style>
    /* These styles make the body full-height */
    html,
    body {
      height: 100%;
    }

    /* These styles disable body scrolling if you are using <ScrollView> */
    body {
      overflow: hidden;
    }

    /* These styles make the root element full-height */
    #root {
      display: flex;
      height: 100dvh;
    }
  </style>

  <!-- The core Firebase JS SDK is always required and must be listed first -->
  <!-- <script src="https://www.gstatic.com/firebasejs/8.8.0/firebase-app.js"></script> -->

  <!-- TODO: Add SDKs for Firebase products that you want to use
     https://firebase.google.com/docs/web/setup#available-libraries -->
  <!-- <script src="https://www.gstatic.com/firebasejs/8.8.0/firebase-analytics.js"></script> -->

  <script src="https://ajax.googleapis.com/ajax/libs/jquery/1.11.1/jquery.min.js"></script>
  <!-- <script src="https://sandbox.merchant.razer.com/MOLPay/API/seamless/latest/js/MOLPay_seamless.deco.js"></script> -->
  <!-- <script src="https://uat.merchant.razer.com/MOLPay/API/seamless/latest/js/MOLPay_seamless.deco.js"></script> -->
  <!-- <script src="https://www.onlinepayment.com.my/MOLPay/API/seamless/latest/js/MOLPay_seamless.deco.js"></script> -->
  <script src="https://pay.merchant.razer.com/RMS/API/seamless/latest/js/MOLPay_seamless.deco.js"></script>

  <!-- <script src="https://otpless.com/auth.js"></script> -->

  <script>
    window.initMolpay = async (paymentDetails, callback) => {
      try {
        if ($('#myPay').length) {
          $.when($('#myPay').remove()).then(() => {
            $.when(
              $('#myPayHolder').append(`<button
          id='myPay'
          style='display: none;'
          type='button'
          data-toggle='molpayseamless'
        >
          Pay by RazerPay
        </button>`)
            ).then(() => {
              // $('#myPay').remove();

              //   $('#myPayHolder').append(`<button
              //   id="myPay"
              //   style={{
              //     display: "none",
              //   }}
              //   type="button"
              //   data-toggle="molpayseamless"
              // >
              //   Pay by RazerPay
              // </button>`);        

              const result = $('#myPay').MOLPaySeamless(paymentDetails);

              console.log('molpay result');
              console.log(result);

              callback(result);
            });
          });
        }
        else {
          $.when(
            $('#myPayHolder').append(`<button
          id='myPay'
          style='display: none;'
          type='button'
          data-toggle='molpayseamless'
        >
          Pay by RazerPay
        </button>`)
          ).then(() => {
            // $('#myPay').remove();

            //   $('#myPayHolder').append(`<button
            //   id="myPay"
            //   style={{
            //     display: "none",
            //   }}
            //   type="button"
            //   data-toggle="molpayseamless"
            // >
            //   Pay by RazerPay
            // </button>`);        

            const result = $('#myPay').MOLPaySeamless(paymentDetails);

            console.log('molpay result');
            console.log(result);

            callback(result);
          });
        }

        // const result = $('#myPay').MOLPaySeamless(paymentDetails);

        // console.log('molpay result');
        // console.log(result);

        // callback(result);
      }
      catch (ex) {
        console.error('ex molpay');

        console.error(ex);
      }
    }

    window.startMolpay = async (paymentDetails, callback) => {
      try {
        const result = $('#myPay').click();

        console.log('molpay result');
        console.log(result);

        callback(result);
      }
      catch (ex) {
        console.error('ex molpay');

        console.error(ex);
      }
    }
  </script>
</head>

<!-- <script>
  // Your web app's Firebase configuration
  // For Firebase JS SDK v7.20.0 and later, measurementId is optional
  var firebaseConfig = {
    apiKey: "AIzaSyB_nKKIGIcEJ6ffsVBdYeIstW8KekjVXa8",
    authDomain: "saasdev-56bf0.firebaseapp.com",
    databaseURL: "https://saasdev-56bf0.firebaseio.com",
    projectId: "saasdev-56bf0",
    storageBucket: "saasdev-56bf0.appspot.com",
    messagingSenderId: "*************",
    appId: "1:*************:web:41aec1e67c556cb8e14f90",
    measurementId: "G-M26K58YJNZ"
  };
  // Initialize Firebase
  firebase.initializeApp(firebaseConfig);
  firebase.analytics();
</script> -->

<body>
  <!-- <button id='myPay' type="button" data-toggle="molpayseamless">Pay by Maybank2u</button> -->

  <noscript>You need to enable JavaScript to run this app.</noscript>
  <div id="root"></div>
  <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
</body>

</html>