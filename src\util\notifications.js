// import firestore from '@react-native-firebase/firestore';
// import storage from '@react-native-firebase/storage';
// import auth from '@react-native-firebase/auth';
import { Collections } from '../constant/firebase';
import { UserStore } from '../store/userStore';
import * as User from '../util/User';
import AsyncStorage from '@react-native-async-storage/async-storage';
// import { MerchantStore } from '../store/merchantStore';
// import { OutletStore } from '../store/outletStore';
import { OUTLET_SHIFT_STATUS, ROLE_TYPE, USER_ORDER_STATUS, USER_QUEUE_STATUS, USER_RESERVATION_STATUS, USER_RING_STATUS, NOTIFICATIONS_TYPE, NOTIFICATIONS_ID, NOTIFICATIONS_CHANNEL, ORDER_TYPE_PARSED } from '../constant/common';
import { CommonStore } from '../store/commonStore';
import API from '../constant/API';
import moment from 'moment';
// import messaging from '@react-native-firebase/messaging';
// import PushNotification from 'react-native-push-notification';
import { NotificationStore } from '../store/notificationStore';
import { getImageFromFirebaseStorage } from './commonFuncs';

export const parseMessages = async msg => {
    // try {
    //     const type = msg.data.type;

    //     if (type === NOTIFICATIONS_TYPE.USER_ORDER) {
    //         console.log('incoming user order!');

    //         PushNotification.localNotification({
    //             id: NOTIFICATIONS_ID.USER_ORDER,

    //             channelId: NOTIFICATIONS_CHANNEL.USER_ORDER,

    //             title: `A ${ORDER_TYPE_PARSED[msg.data.orderType].toLowerCase()} order placed!`,
    //             message: `Total: RM${msg.data.finalPrice}`,
    //             data: msg.data,
    //             userInfo: msg.data,
    //             // actions: [
    //             //     SURVEY_NOTIFICATION_ACTIONS.PROCEED,
    //             //     SURVEY_NOTIFICATION_ACTIONS.SNOOZE,
    //             // ],
    //             // date: dayjs(inputData.formData.currTime).add(1, 'minute').toDate(),
    //             // date: dayjs(nextTime).toDate(),
    //             // repeatType: 'time',
    //             // repeatTime: intervalTime,
    //         });
    //     }
    //     else if (type === NOTIFICATIONS_TYPE.USER_RING) {
    //         console.log('incoming user ring!');

    //         PushNotification.localNotification({
    //             id: NOTIFICATIONS_ID.USER_RING,

    //             channelId: NOTIFICATIONS_CHANNEL.USER_RING,

    //             title: `Table ${msg.data.tableCode} ring!`,
    //             message: `Name: ${msg.data.userName}`,
    //             data: msg.data,
    //             userInfo: msg.data,
    //         });
    //     }
    //     else if (type === NOTIFICATIONS_TYPE.PROMOTION_NOTIFICATION_MANUAL) {
    //         console.log('incoming promotion notification manual!');

    //         if (msg.data.image.startsWith('http')) {
    //             PushNotification.localNotification({
    //                 id: NOTIFICATIONS_ID.PROMOTION_NOTIFICATION_MANUAL,

    //                 channelId: NOTIFICATIONS_CHANNEL.PROMOTION_NOTIFICATION_MANUAL,

    //                 title: msg.data.title,
    //                 message: msg.data.description,
    //                 data: msg.data,
    //                 userInfo: msg.data,

    //                 bigPictureUrl: msg.data.image,
    //             });
    //         }
    //         else {
    //             getImageFromFirebaseStorage(msg.data.image, (parsedUrl) => {
    //                 PushNotification.localNotification({
    //                     id: NOTIFICATIONS_ID.PROMOTION_NOTIFICATION_MANUAL,

    //                     channelId: NOTIFICATIONS_CHANNEL.PROMOTION_NOTIFICATION_MANUAL,

    //                     title: msg.data.title,
    //                     message: msg.data.description,
    //                     data: msg.data,
    //                     userInfo: msg.data,

    //                     bigPictureUrl: parsedUrl,
    //                 });
    //             });

    //             // const urlTemp = retrieveImageFromFirebase(source);

    //             // setUrl(urlTemp);
    //         }
    //     }
    //     else if (type === NOTIFICATIONS_TYPE.PROMOTION_NOTIFICATION_AUTO) {
    //         console.log('incoming promotion notification auto!');

    //         if (msg.data.image.startsWith('http')) {
    //             PushNotification.localNotification({
    //                 id: NOTIFICATIONS_ID.PROMOTION_NOTIFICATION_AUTO,

    //                 channelId: NOTIFICATIONS_CHANNEL.PROMOTION_NOTIFICATION_AUTO,

    //                 title: msg.data.title,
    //                 message: msg.data.description,
    //                 data: msg.data,
    //                 userInfo: msg.data,

    //                 bigPictureUrl: msg.data.image,
    //             });
    //         }
    //         else {
    //             getImageFromFirebaseStorage(msg.data.image, (parsedUrl) => {
    //                 PushNotification.localNotification({
    //                     id: NOTIFICATIONS_ID.PROMOTION_NOTIFICATION_AUTO,

    //                     channelId: NOTIFICATIONS_CHANNEL.PROMOTION_NOTIFICATION_AUTO,

    //                     title: msg.data.title,
    //                     message: msg.data.description,
    //                     data: msg.data,
    //                     userInfo: msg.data,

    //                     bigPictureUrl: parsedUrl,
    //                 });
    //             });
    //         }
    //     }
    //     else if (type === NOTIFICATIONS_TYPE.USER_ORDER_COURIER_ACTION) {
    //         console.log('incoming user order courier action!');

    //         PushNotification.localNotification({
    //             id: NOTIFICATIONS_ID.USER_ORDER_COURIER_ACTION,

    //             channelId: NOTIFICATIONS_CHANNEL.USER_ORDER_COURIER_ACTION,

    //             title: msg.data.title,
    //             message: msg.data.description,
    //             data: msg.data,
    //             userInfo: msg.data,
    //         });
    //     }
    // }
    // catch (ex) {
    //     console.error(ex);
    // }
};

export const parseLocalNotifications = notifications => {
    // if (notification.action === SURVEY_NOTIFICATION_ACTIONS.PROCEED ||
    //     (notification.action !== SURVEY_NOTIFICATION_ACTIONS.SNOOZE && notification.action === undefined)) {

    // }

    // console.log(notifications);

    // if (notifications.userInteraction) {
    //     console.log('user tapped local notifications!');

    //     if (notifications.data.type === NOTIFICATIONS_TYPE.USER_ORDER) {
    //         // clicked user order notifications

    //         NotificationStore.update(s => {
    //             s.nUserOrder = notifications.data;
    //         });
    //     }
    //     else if (notifications.data.type === NOTIFICATIONS_TYPE.USER_RING) {
    //         // clicked user ring notifications

    //         NotificationStore.update(s => {
    //             s.nUserRing = notifications.data;
    //         });
    //     }
    //     else if (notifications.data.type === NOTIFICATIONS_TYPE.PROMOTION_NOTIFICATION_MANUAL) {
    //         // clicked user ring notifications

    //         NotificationStore.update(s => {
    //             s.nPromotionNotificationManual = notifications.data;
    //         });
    //     }
    //     else if (notifications.data.type === NOTIFICATIONS_TYPE.PROMOTION_NOTIFICATION_AUTO) {
    //         // clicked user ring notifications

    //         NotificationStore.update(s => {
    //             s.nPromotionNotificationAuto = notifications.data;
    //         });
    //     }
    //     else if (notifications.data.type === NOTIFICATIONS_TYPE.USER_ORDER_COURIER_ACTION) {
    //         // clicked user ring notifications

    //         NotificationStore.update(s => {
    //             s.nUserOrderCourierAction = notifications.data;
    //         });
    //     }
    // }
};
