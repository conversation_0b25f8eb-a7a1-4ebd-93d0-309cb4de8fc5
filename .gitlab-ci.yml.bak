stages:
  - build
  - deploy

build-dev:
  stage: build
  script:
    - npm ci
    - REACT_APP_ENV=DEV npm run build

# build-prod:
#   stage: build
#   script:
#     - npm ci
#     - REACT_APP_ENV=PROD npm run build

deploy-dev:
  stage: deploy
  script:
    - ssh-add <(echo "$SSH_PRIVATE_KEY")
    - scp -r ./build/* <EC2_USERNAME>@<EC2_PUBLIC_IP>:/var/www/html/web

# deploy-prod:
#   stage: deploy
#   script:
# <AUTHOR> <EMAIL>:/var/www/html/my-website