import React, { Component, useReducer, useState, useEffect, useRef } from 'react';
import {
  StyleSheet,
  ScrollView,
  Image,
  View,
  Text,
  TouchableOpacity,
  TextInput,
  FlatList,
  RefreshControl,
  Alert,
  Modal,
  Dimensions,
  Platform,
  useWindowDimensions
} from 'react-native';
// import firebase from 'firebase';
import { getFirestore, collection, query, where, getDocs, limit } from "firebase/firestore";
import { signInAnonymously } from "firebase/auth";
import Colors from '../constant/Colors';
// import Autocomplete from 'react-google-autocomplete'
import AntDesign from 'react-native-vector-icons/AntDesign';
import DatePicker from "react-datepicker";
import { ReactComponent as GCalendar } from '../svg/GCalendar.svg'
import "react-datepicker/dist/react-datepicker.css";
import "../constant/datePicker.css";
import * as Cart from '../util/Cart';
import * as User from '../util/User';
import Styles from '../constant/Styles';
// import ActionSheet from 'react-native-actionsheet';
import ApiClient from '../util/ApiClient';
import API from '../constant/API';
import Ionicons from 'react-native-vector-icons/Ionicons';
import Icon from 'react-native-vector-icons/Ionicons';
import Icons from 'react-native-vector-icons/Feather';
// import NumericInput from 'react-native-numeric-input';
// import { color } from 'react-native-reanimated';
// import QRCode from 'react-native-qrcode-svg';
import Close from 'react-native-vector-icons/AntDesign';
import { CommonStore } from '../store/commonStore';
import { UserStore } from '../store/userStore';
import moment from 'moment';
import AsyncImage from '../components/asyncImage';
// import LinearGradient from 'react-native-linear-gradient';
import LinearGradient from 'react-native-web-linear-gradient';
import { googleCloudApiKey, prefix } from "../constant/env";
import { useFocusEffect, useLinkTo } from "@react-navigation/native";
import { DataStore } from '../store/dataStore';
import Hashids from 'hashids';
import { ORDER_REGISTER_QR_SALT } from '../constant/common';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Collections } from '../constant/firebase';
import IconAnt from 'react-native-vector-icons/AntDesign';
import { isMobile, setRouteFrom, mondayFirst } from "../util/commonFuncs";
import imgLogo from '../asset/image/logo.png';

import { ReactComponent as Arrow } from '../svg/arrow.svg';
import { Switch } from 'react-native-web';
import { LOYALTY_CAMPAIGN_CREDIT_TRANSACTION_TYPE_PARSED } from '../constant/loyalty';
import { TempStore } from "../store/tempStore";

import FontAwesome from 'react-native-vector-icons/FontAwesome';
import { PaymentStore } from '../store/paymentStore';
import { TableStore } from '../store/tableStore';
import { idbGet } from '../util/db';

const hashids = new Hashids(ORDER_REGISTER_QR_SALT);

const OR_StampCards = (props) => {
  const { navigation, route } = props;
  const { height: windowHeight, width: windowWidth } = useWindowDimensions();

  const linkTo = useLinkTo();

  const [blockSMS, setBlockSMS] = useState(false);
  const [blockEmail, setBlockEmail] = useState(false);

  const loyaltyHistoryCrmUser = CommonStore.useState(s => s.loyaltyHistoryCrmUser);
  const loyaltyHistoryLCCTransactions = CommonStore.useState(s => s.loyaltyHistoryLCCTransactions);
  const loyaltyHistoryLCCBalance = CommonStore.useState(s => s.loyaltyHistoryLCCBalance);
  const email = UserStore.useState((s) => s.email);
  const userNumber = UserStore.useState((s) => s.number);
  const name = UserStore.useState((s) => s.name);
  const userPointsTransactions = UserStore.useState(s => s.userPointsTransactions)
  const userPointsBalance = UserStore.useState(s => s.userPointsBalance);

  const selectedOutlet = CommonStore.useState((s) => s.selectedOutlet);

  const availableTaggableVouchers = CommonStore.useState((s) => s.availableTaggableVouchers,);

  const availableUserTaggableVouchers = CommonStore.useState((s) => s.availableUserTaggableVouchers,);
  const [searchText, setSearchText] = useState('');

  const isLoading = CommonStore.useState(s => s.isLoading);
  const REWARD_SECTION = {
    REWARDS: 'REWARDS',
    AVAILABLE_REWARDS: 'AVAILABLE_REWARDS',
  }
  const [rewardSection, setRewardSection] = useState(REWARD_SECTION.REWARDS);
  const claimVoucherList = TempStore.useState((s) => s.claimVoucherList);
  const selectedMerchant = CommonStore.useState(s => s.selectedMerchant);

  const [outletId, setOutletId] = useState('');
  const [outletName, setOutletName] = useState('');
  const [outletCover, setOutletCover] = useState('');
  const [merchantId, setMerchantId] = useState('');
  const [merchantName, setMerchantName] = useState('');

  const temporarydata = [
    { point: 2000, name: 'product1', time: '13.00-15.00' },
    { point: 2000, name: 'product2', time: '13.00-15.00' },
    { point: 1000, name: 'product3', time: '15.00-17.00' },
    // { point: 1000, name: 'product4', time: '15.00-17.00' },
  ]

  const temp_availableTaggableVouchers = [
    { point: 2000, campaignName: 'product1', promoDateEnd: '2023-10-27', voucherPointsRequired: 10, requiredStamp: 1, campaignDescription: 'test\ntest\ntest\ntest', },
    { point: 2000, campaignName: 'product2', promoDateEnd: '2023-10-27', voucherPointsRequired: 10, requiredStamp: 2, campaignDescription: 'test\ntest\ntest\ntest', },
    { point: 1000, campaignName: 'product3', promoDateEnd: '2023-10-27', voucherPointsRequired: 10, requiredStamp: 3, campaignDescription: 'test\ntest\ntest\ntest', },
    { point: 1000, campaignName: 'product4', promoDateEnd: '2023-10-27', voucherPointsRequired: 10, requiredStamp: 4, campaignDescription: 'test\ntest\ntest\ntest', },
    { point: 1000, campaignName: 'product5', promoDateEnd: '2023-10-27', voucherPointsRequired: 10, requiredStamp: 5, campaignDescription: 'test\ntest\ntest\ntest', },
    { point: 1000, campaignName: 'product6', promoDateEnd: '2023-10-27', voucherPointsRequired: 10, requiredStamp: 6, campaignDescription: 'test\ntest\ntest\ntest', },
    { point: 1000, campaignName: 'product7', promoDateEnd: '2023-10-27', voucherPointsRequired: 10, requiredStamp: 7, campaignDescription: 'test\ntest\ntest\ntest', },
    { point: 1000, campaignName: 'product8', promoDateEnd: '2023-10-27', voucherPointsRequired: 10, requiredStamp: 8, campaignDescription: 'test\ntest\ntest\ntest', },
    { point: 1000, campaignName: 'product9', promoDateEnd: '2023-10-27', voucherPointsRequired: 10, requiredStamp: 9, campaignDescription: 'test\ntest\ntest\ntest', },
    { point: 1000, campaignName: 'product10', promoDateEnd: '2023-10-27', voucherPointsRequired: 10, requiredStamp: 10, campaignDescription: 'test\ntest\ntest\ntest', },
    { point: 1000, campaignName: 'product20', promoDateEnd: '2023-10-27', voucherPointsRequired: 10, requiredStamp: 20, campaignDescription: 'test\ntest\ntest\ntest', },
    { point: 1000, campaignName: 'product50', promoDateEnd: '2023-10-27', voucherPointsRequired: 10, requiredStamp: 50, campaignDescription: 'test\ntest\ntest\ntest', },
    { point: 1000, campaignName: 'product100', promoDateEnd: '2023-10-27', voucherPointsRequired: 10, requiredStamp: 100, campaignDescription: 'test\ntest\ntest\ntest', },
  ]

  const [userPhone, setUserPhone] = useState('0122891759')
  const [outletIdTest, setOutletIdTest] = useState('b422c1d9-d30b-4de7-ad49-2e601d950919')

  const [stampArr, setStampArr] = useState([]);
  const [userStampArr, setUserStampArr] = useState([]);

  const userLoyaltyStamps = CommonStore.useState((s) => s.userLoyaltyStamps);
  const selectedOutletLoyaltyStamps = CommonStore.useState((s) => s.selectedOutletLoyaltyStamps);

  const filteredUserLoyaltyStamps =
    selectedOutletLoyaltyStamps.filter(item1 => {
      return (
        userLoyaltyStamps.some(item2 => item2.loyaltyStampId === item1.uniqueId && item2.phone === userNumber)
      )
    })

  // useEffect(async () => {
  //     var userStampArrTemp = []
  //     const userLoyaltySnapshot = await getDocs(
  //         query(
  //             collection(global.db, Collections.UserLoyaltyStamp),
  //             //   where('phone', '==', userPhone),
  //               where('outletId', '==', outletIdTest),
  //             // limit(1)
  //         )
  //     );

  //     if (!userLoyaltySnapshot.empty) {
  //         for (var i = 0; i < userLoyaltySnapshot.length; i++) {
  //             userStampArrTemp.push(userLoyaltySnapshot.docs[i].data());
  //         }
  //     }
  //     setUserStampArr(userStampArrTemp)
  //     console.log('DATAAAD', userStampArr)
  // }, [])

  // useEffect(async () => {
  //     var stampArrTemp = []
  //     const loyaltySnapshot = await getDocs(
  //         query(
  //             collection(global.db, Collections.LoyaltyStamp),
  //             //   where('phone', '==', userPhone),
  //               where('outletId', '==', outletIdTest),
  //             // limit(1)
  //         )
  //     );

  //     if (!loyaltySnapshot.empty) {
  //         for (var i = 0; i < loyaltySnapshot.length; i++) {
  //             stampArrTemp.push(loyaltySnapshot.docs[i].data());
  //         }
  //     }
  //     setStampArr(stampArrTemp)
  //     console.log('DATAAA', stampArr)
  // }, [])

  // useEffect(() => {
  //     if (linkTo) {
  //         DataStore.update(s => {
  //             s.linkToFunc = linkTo;
  //         });
  //     }

  //     console.log('route');
  //     console.log(route);
  //     console.log('window.location.href');
  //     console.log(window.location.href);

  //     if (route.params === undefined ||
  //         // route.params.outletId === undefined ||
  //         // route.params.orderId === undefined ||
  //         // route.params.tableCode === undefined ||
  //         // route.params.tablePax === undefined ||
  //         // route.params.waiterId === undefined ||
  //         // route.params.outletId.length !== 36 ||
  //         // route.params.tableId.length !== 36 ||
  //         // route.params.qrDateTimeEncrypted === undefined
  //         route.params.userIdHuman == undefined
  //     ) {
  //         // still valid, can proceed to register user

  //         // linkTo && linkTo(`${prefix}/error`);

  //         console.log('general');
  //     }
  //     else {
  //         // firebase.auth().signInAnonymously()
  //         signInAnonymously(global.auth)
  //             .then((result) => {
  //                 const firebaseUid = result.user.uid;

  //                 ApiClient.GET(API.getTokenKWeb + firebaseUid).then(async (result) => {
  //                     console.log('getTokenKWeb');
  //                     console.log(result);

  //                     if (result && result.token) {
  //                         await AsyncStorage.setItem('accessToken', result.token);
  //                         await AsyncStorage.setItem('refreshToken', result.refreshToken);

  //                         global.accessToken = result.token;

  //                         UserStore.update(s => {
  //                             s.firebaseUid = result.userId;
  //                             s.userId = result.userId;
  //                             s.role = result.role;
  //                             s.refreshToken = result.refreshToken;
  //                             s.token = result.token;
  //                             s.name = '';
  //                             s.email = '';
  //                             s.number = '';
  //                         });

  //                         // CommonStore.update(s => {
  //                         //   s.selectedOutletTableQRUrl = window.location.href;
  //                         // });

  //                         // const crmUserSnapshot = await firebase.firestore().collection(Collections.CRMUser)
  //                         //   .where('userIdHuman', '==', route.params.userIdHuman)
  //                         //   .limit(1)
  //                         //   .get();

  //                         const crmUserSnapshot = await getDocs(
  //                             query(
  //                                 collection(global.db, Collections.CRMUser),
  //                                 where('userIdHuman', '==', route.params.userIdHuman),
  //                                 limit(1),
  //                             )
  //                         );

  //                         var crmUser = null;

  //                         if (!crmUserSnapshot.empty) {
  //                             crmUser = crmUserSnapshot.docs[0].data();
  //                         }

  //                         if (crmUser) {
  //                             await AsyncStorage.setItem('loyaltyHistoryCrmUserIdHuman', route.params.userIdHuman);

  //                             setBlockSMS(crmUser.blockSMS || false);
  //                             setBlockEmail(crmUser.blockEmail || false);

  //                             // const outletSnapshot = await firebase.firestore().collection(Collections.Outlet)
  //                             //   .where('uniqueId', '==', crmUser.outletId)
  //                             //   .limit(1)
  //                             //   .get();

  //                             const outletSnapshot = await getDocs(
  //                                 query(
  //                                     collection(global.db, Collections.Outlet),
  //                                     where('uniqueId', '==', crmUser.outletId),
  //                                     limit(1),
  //                                 )
  //                             );

  //                             var outlet = {};
  //                             if (!outletSnapshot.empty) {
  //                                 outlet = outletSnapshot.docs[0].data();
  //                             }

  //                             if (outlet) {
  //                                 // valid, can proceed to register user

  //                                 setOutletId(outlet.uniqueId);
  //                                 setOutletName(outlet.name);
  //                                 setOutletCover(outlet.cover);
  //                                 setMerchantId(outlet.merchantId);
  //                                 setMerchantName(crmUser.merchantName);

  //                                 CommonStore.update(s => {
  //                                     s.loyaltyHistoryCrmUser = crmUser;
  //                                 });

  //                                 //////////////////////////////////////////

  //                                 // const loyaltyCampaignCreditTransactionSnapshot = await firebase.firestore()
  //                                 //   .collection(Collections.LoyaltyCampaignCreditTransaction)
  //                                 //   .where('email', '==', crmUser.email)
  //                                 //   .where('outletId', '==', outlet.uniqueId)
  //                                 //   .where('deletedAt', '==', null)
  //                                 //   .get();

  //                                 // const userPointsTransactionsSnapshot = await getDocs(
  //                                 //     query(
  //                                 //         collection(global.db, Collections.UserPointsTransaction),
  //                                 //         where('phone', '==', crmUser.number),
  //                                 //         where('outletId', '==', outlet.uniqueId),
  //                                 //         where('deletedAt', '==', null),
  //                                 //     )
  //                                 // );

  //                                 // var userPointsTransactionsTemp = [];
  //                                 // var userPointsBalanceTemp = 0;

  //                                 // if (!userPointsTransactionsSnapshot.empty) {
  //                                 //     for (var i = 0; i < userPointsTransactionsSnapshot.size; i++) {
  //                                 //         const record = userPointsTransactionsSnapshot.docs[i].data();

  //                                 //         userPointsTransactionsTemp.push(record);

  //                                 //         userPointsBalanceTemp += record.amount;
  //                                 //     }
  //                                 // }

  //                                 // userPointsTransactions.sort((a, b) => b.createdAt - a.createdAt);

  //                                 // CommonStore.update(s => {
  //                                 //     s.selectedOutlet = outlet;
  //                                 // });

  //                                 // UserStore.update(s => {
  //                                 //     s.userPointsTransactions = userPointsTransactionsTemp;
  //                                 //     s.userPointsBalance = userPointsBalanceTemp;
  //                                 // });

  //                                 //////////////////////////////////////////

  //                                 // CommonStore.update(s => {
  //                                 //   s.registerUserOrder = userOrder;
  //                                 // });

  //                                 // await AsyncStorage.setItem('latestOutletId', outlet.uniqueId);

  //                                 // await updateUserCart(
  //                                 //   {
  //                                 //     ...route.params,
  //                                 //     outletId: outlet.uniqueId,
  //                                 //     tableId: tableId,
  //                                 //     tablePax: outletTable.seated,
  //                                 //     tableCode: outletTable.code,
  //                                 //   }
  //                                 //   , outlet, firebaseUid);

  //                                 // linkTo && linkTo('/web/outlet');
  //                             }
  //                             else {
  //                                 // linkTo && linkTo(`${prefix}/error`);
  //                             }
  //                         }
  //                         else {
  //                             // linkTo && linkTo(`${prefix}/error`);
  //                         }
  //                     }
  //                     else {
  //                         CommonStore.update(s => {
  //                             s.alertObj = {
  //                                 title: 'Error',
  //                                 message: 'Unauthorized access',
  //                             };

  //                             // s.isAuthenticating = false;
  //                         });

  //                         // linkTo && linkTo(`${prefix}/error`);
  //                     }
  //                 });
  //             });
  //     }
  // }, [linkTo, route]);

  useEffect(() => {
    var tempVoucherCheckList = [];

    // claimVoucherList.filter((item) => {
    //     if (item.voucherPointsRequired <= userPointsBalance) {
    //         tempVoucherCheckList.push(item);
    //     }
    // });

    if (tempVoucherCheckList.length == 0) {
      TempStore.update(s => {
        s.showClaimVoucher = false;
      });
    }
    else {
      // TempStore.update(s => {
      //     s.claimVoucherList = tempVoucherCheckList;
      // });

      TempStore.update(s => {
        s.showClaimVoucher = false;
      });

    }
  }, [claimVoucherList]);

  useEffect(() => {
    CommonStore.update(s => {
      s.availableTaggableVouchers = availableUserTaggableVouchers;
    });
  }, [availableUserTaggableVouchers]);

  useEffect(() => {
    if (linkTo) {
      DataStore.update((s) => {
        s.linkToFunc = linkTo;
      });

      global.linkToFunc = linkTo;
    }
  }, [linkTo]);

  // useEffect(() => {
  //     const subdomain = AsyncStorage.getItem("latestSubdomain");
  //     if ((
  //         selectedOutlet.subdomain === 'hominsanttdi' ||
  //         selectedOutlet.subdomain === 'hominsan-ss15' ||
  //         selectedOutlet.subdomain === 'kenneth-cafe' ||
  //         selectedOutlet.subdomain === 'cascada-magical-beans' ||
  //         selectedOutlet.subdomain === 'dream-coffee-pastry' ||
  //         selectedOutlet.subdomain === 'kopi-empat-petang' ||
  //         selectedOutlet.subdomain === 'wenji-cafe'
  //     )
  //     ) { // now only show for ttdi
  //         if (!email || !userNumber) {
  //             if (!subdomain) {
  //                 linkTo && linkTo(`${prefix}/outlet/menu`);
  //             }
  //             else {
  //                 linkTo && linkTo(`${prefix}/outlet/${subdomain}/menu`);
  //             }
  //         }
  //     }
  //     else {
  //         if (!subdomain) {
  //             linkTo && linkTo(`${prefix}/outlet/menu`);
  //         }
  //         else {
  //             linkTo && linkTo(`${prefix}/outlet/${subdomain}/menu`);
  //         }
  //     }
  // }, [selectedOutlet, email, userNumber])

  useEffect(() => {
    if (selectedOutlet === null) {
      readStates();
    }

    readCommonStates();
  }, [selectedOutlet]);

  const readStates = async () => {
    console.log('global.selectedoutlet = readStates (1) (==test==)');

    if (selectedOutlet === null) {
      console.log('global.selectedoutlet = readStates (2) (==test==)');

      // const commonStoreDataRaw = await AsyncStorage.getItem("@commonStore");
      const commonStoreDataRaw = await idbGet('@commonStore');
      if (commonStoreDataRaw !== undefined) {
        console.log('global.selectedoutlet = readStates (3) (==test==)');

        const commonStoreData = JSON.parse(commonStoreDataRaw);

        const latestOutletId = await AsyncStorage.getItem("latestOutletId");

        console.log('latestOutletId');
        console.log(latestOutletId);
        console.log('commonStoreData.selectedOutlet');
        console.log(commonStoreData.selectedOutlet);

        if (
          commonStoreData.selectedOutlet &&
          latestOutletId === commonStoreData.selectedOutlet.uniqueId
        ) {
          // check if it's the same outlet user scanned

          console.log('global.selectedoutlet = readStates (4) (==test==)');

          // if (isPlacingReservation) {
          // } else {
          //   // if (
          //   //   commonStoreData.orderType === ORDER_TYPE.DINEIN 
          //   //   // 2022-10-08 - Try to disable this
          //   //   // &&
          //   //   // commonStoreData.userCart.uniqueId === undefined
          //   // ) {
          //   //   // logout the user

          //   //   linkTo && linkTo(`${prefix}/scan`);
          //   // }
          // }

          console.log('global.selectedoutlet = commonStoreData.selectedOutlet (3) (==test==)');

          global.selectedOutlet = commonStoreData.selectedOutlet;

          CommonStore.replace(commonStoreData);

          // const userStoreDataRaw = await AsyncStorage.getItem("@userStore");
          const userStoreDataRaw = await idbGet('@userStore');
          if (userStoreDataRaw !== undefined) {
            const userStoreData = JSON.parse(userStoreDataRaw);

            UserStore.replace(userStoreData);
          }

          // const dataStoreDataRaw = await AsyncStorage.getItem('@dataStore');
          const dataStoreDataRaw = await idbGet('@dataStore');
          if (dataStoreDataRaw !== undefined) {
            const dataStoreData = JSON.parse(dataStoreDataRaw);

            DataStore.replace(dataStoreData);
            // DataStore.replace({
            //   ...dataStoreData,
            //   ...dataStoreData.linkToFunc !== undefined && {
            //     linkToFunc: dataStoreData.linkToFunc,
            //   },
            // });
          }

          // const tableStoreDataRaw = await AsyncStorage.getItem("@tableStore");
          const tableStoreDataRaw = await idbGet('@tableStore');
          if (tableStoreDataRaw !== undefined) {
            const tableStoreData = JSON.parse(tableStoreDataRaw);

            TableStore.replace(tableStoreData);
          }

          // const paymentStoreDataRaw = await AsyncStorage.getItem("@paymentStore");
          const paymentStoreDataRaw = await idbGet('@paymentStore');
          if (paymentStoreDataRaw !== undefined) {
            const paymentStoreData = JSON.parse(paymentStoreDataRaw);

            PaymentStore.replace(paymentStoreData);
          }

          // const dataStoreDataRaw = await AsyncStorage.getItem('@dataStore');
          // if (dataStoreDataRaw !== undefined) {
          //   const dataStoreData = JSON.parse(dataStoreDataRaw);

          //   DataStore.replace(dataStoreData);
          // }
        }
      }
    }
  };

  const readCommonStates = async () => {
    // if (!linkToFunc) {
    //   const dataStoreDataRaw = await AsyncStorage.getItem('@dataStore');
    //   if (dataStoreDataRaw !== undefined) {
    //     const dataStoreData = JSON.parse(dataStoreDataRaw);
    //     DataStore.replace(dataStoreData);
    //   }
    // }
  };

  const flatListRef = useRef(null);
  const [flatListHeight, setFlatListHeight] = useState(0);

  const handleFlatListLayout = (event) => {
    const { height } = event.nativeEvent.layout;
    if (height < 50) {
      setFlatListHeight(60);
    }
    else {
      setFlatListHeight(height);
    }
    // console.log('flatListHeight', flatListHeight)
  };

  const [numUserStampsAcquired, setNumUserStampsAcquired] = useState(0)
  const [totalNumberOfIcons, setTotalNumberOfIcons] = useState(0)

  const renderStamp = ({ item, index }) => {
    return (
      <>
        <View
          style={{
            // position: 'absolute',
            // top: 10 + Math.floor(index / 5) * 50, // Adjust the positioning for rows
            // left: 10 + (index % 5) * 65, // Adjust the positioning for columns
            marginLeft: 25,
            marginBottom: windowHeight * 0.015,
            borderWidth: 1,
            borderRadius: 100,
            width: 40,
            height: 40,
            backgroundColor: 'white',

            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <FontAwesome
            key={index}
            name="star"
            size={30}
            color={index < numUserStampsAcquired ? Colors.primaryColor : Colors.whiteColor}
          />
        </View>
      </>
    )
  }

  const renderRewards = ({ item, index }) => {
    //try to move everything before return out into a useeffect - 565
    //temp is used to find the stamps
    console.log("item", item);

    let temp = userLoyaltyStamps.find(filterStamp => filterStamp.loyaltyStampId === item.uniqueId)
    console.log('TEMMP', temp)
    var totalGetStamps = 0;

    for (var i = 0; i < temp.buyHistory.length; i++) {
      totalGetStamps = totalGetStamps + temp.buyHistory[i].noOfStamp
    }
    setNumUserStampsAcquired(totalGetStamps);
    setTotalNumberOfIcons(item.totalNumberOfStamp)

    const innerData = Array.from({ length: item.totalNumberOfStamp }, (_, index) => ({ key: String(index) }));
    // console.log('ITEMM', userLoyaltyStamps)
    // console.log('numUserStampsAcquired', numUserStampsAcquired)
    // console.log('totalNumberOfIcons', totalNumberOfIcons)
    return (
      <>
        {/* <TouchableOpacity onPress={() => {
                    // CommonStore.update(s => {
                    //     s.selectedTaggableVoucher = item;
                    // });
                    // linkTo && linkTo(`${prefix}/outlet/outlet-reward-details`)
                }}
                    style={{
                        backgroundColor: 'white',
                        // flex: 0.5,
                        width: 350,
                        // marginRight: 5,
                        borderRadius: 5,
                        paddingHorizontal: 10,
                        paddingBottom: 10,
                        margin: 10,
                    }}> */}
        <View style={{
          backgroundColor: 'white',
          // flex: 0.5,
          width: 350,
          // marginRight: 5,
          borderRadius: 5,
          paddingHorizontal: 10,
          paddingBottom: 10,
          // margin: 10,
        }}>
          <View style={{ alignSelf: 'center', }}>
            <View style={{
              position: 'absolute',
              marginVertical: 10,
              // backgroundColor: 'blue'
              zIndex: 100,
              // alignContent: 'center',
            }}>
              <FlatList
                ref={flatListRef}
                showsVerticalScrollIndicator={false}
                data={innerData}
                // style={{paddingBottom: 20,}}
                numColumns={5}
                renderItem={renderStamp}
                keyExtractor={(item) => item.key}
                onLayout={handleFlatListLayout}
              />
            </View>
            {item.merchantLogo ? (
              <>
                <View style={{ backgroundColor: Colors.secondaryColor, width: 350, height: flatListHeight + 30, alignItems: 'center', alignSelf: 'center', justifyContent: 'center', borderRadius: 5, zIndex: -1 }}>
                  <AsyncImage
                    source={{ uri: item.merchantLogo }}
                    item={item}
                    style={{
                      width: windowWidth * 0.3,
                      height: windowWidth * 0.3,
                      borderRadius: 5,
                      zIndex: -1,
                    }}
                  />
                </View>
              </>
            ) :
              <View style={{ backgroundColor: Colors.secondaryColor, width: 350, height: flatListHeight + 30, alignItems: 'center', alignSelf: 'center', justifyContent: 'center', borderRadius: 5, zIndex: -1 }}>
                <Ionicons
                  name="fast-food-outline"
                  // size={45}
                  size={windowWidth * 0.25}
                />
              </View>
            }
          </View>


          <View>
            <Text
              style={{
                marginTop: 10,
                marginLeft: 5,
                // marginBottom: 10,
                fontSize: 14,
                fontFamily: 'NunitoSans-Bold',
                //color: Colors.whiteColor,
                // borderBottomWidth: 1,
                // paddingBottom: 7,
                // height: windowHeight * 0.07,
              }}
            // numberOfLines={2}
            >
              {item.name}
            </Text>

            <Text
              style={{
                marginTop: 10,
                marginLeft: 5,
                fontSize: 14,
                fontFamily: 'NunitoSans-SemiBold',
                //color: Colors.whiteColor,
              }}>
              Expires by {moment(item.endDate).format('dddd, Do MMM YYYY')}, {moment(item.endTime).format('hh:mm A')}
            </Text>

            <TouchableOpacity
              onPress={() => {
                CommonStore.update(s => {
                  s.selectedStamp = item;
                  s.userStampAmountSelected = numUserStampsAcquired;
                });
                console.log('selectedStamp', item)
                linkTo && linkTo(`${prefix}/outlets/outlet-stamp-details`)
                // linkTo && linkTo(`${prefix}/outlet-stamp-details`)
              }}>
              <Text
                style={{
                  //marginTop: 10,
                  // marginLeft: 10,
                  fontSize: 16,
                  fontFamily: 'NunitoSans-Bold',
                  color: Colors.primaryColor,
                  marginLeft: 5,
                  marginTop: 10,
                }}>
                CHECK REWARDS
              </Text>
            </TouchableOpacity>
          </View>
        </View>
        {/* </TouchableOpacity > */}
      </>
    )
  };

  return (
    <>
      {selectedOutletLoyaltyStamps && selectedOutletLoyaltyStamps.length > 0 ? (
        <>
          {selectedOutletLoyaltyStamps && selectedOutletLoyaltyStamps.length > 0 && filteredUserLoyaltyStamps.length > 0 ? (
            <>
              <View style={{ height: windowHeight * 0.9, alignItems: 'center', justifyContent: 'center', backgroundColor: 'light-gray', }}>
                <View style={{
                  justifyContent: 'center',
                  alignContent: 'center',
                  alignItems: 'center',
                  borderRadius: 10,
                  borderColor: '#E5E5E5',

                  // shadowOffset: {
                  //     width: 0,
                  //     height: 2,
                  // },
                  // shadowOpacity: 0.22,
                  // shadowRadius: 3.22,
                  // elevation: 1,
                  padding: 20,
                  marginTop: '25%',
                  marginBottom: '10%',

                  height: windowHeight * 1,
                  // width: isMobile() ? windowWidth * 0.9 : windowWidth * 0.4,
                }}>
                  <ScrollView showsVerticalScrollIndicator={false}>
                    <View style={{ alignItems: 'center', justifyContent: 'center', width: isMobile() ? '100%' : '80%', alignSelf: 'center', }}>
                      <View style={{ height: '100%', width: '100%', marginTop: 10, marginBottom: 10, }}>
                        <FlatList
                          showsVerticalScrollIndicator={false}
                          data={filteredUserLoyaltyStamps}
                          style={{}}
                          renderItem={renderRewards}
                          keyExtractor={(item, index) => String(index)}
                          contentContainerStyle={{
                            width: 400,
                            backgroundColor: 'blue'
                          }}
                        />
                      </View>
                    </View>
                    <View style={{ height: 20 }} />
                  </ScrollView>
                </View>
                <View style={{ flex: 1 }}></View>
              </View>
            </>
          ) : (
            <View
              style={{
                width: windowWidth,
                height: windowHeight,
                justifyContent: 'center',
                alignItems: 'center',
                alignContent: 'center',
                // marginTop: '70%',
                alignSelf: 'center',
              }}>
              <Text style={{
                fontFamily: 'NunitoSans-Regular',
                color: Colors.descriptionColor,
                fontSize: 14,
                textAlign: 'center',
                // marginTop: '2%',
                // marginBottom: '20%'
              }}>
                You do not own any stamp card
              </Text>
            </View>
          )}
        </>
      ) : (
        <View
          style={{
            width: windowWidth,
            height: windowHeight,
            justifyContent: 'center',
            alignItems: 'center',
            alignContent: 'center',
            // marginTop: '70%',
            alignSelf: 'center',            
          }}>
          <Text style={{
            fontFamily: 'NunitoSans-Regular',
            color: Colors.descriptionColor,
            fontSize: 14,
            textAlign: 'center',
            // marginTop: '2%',
            // marginBottom: '20%'
          }}>
            This outlet currently don't have any stamp cards available.
          </Text>
        </View>
      )}
    </>
  )
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.highlightColor,
    flexDirection: 'row',
    fontFamily: 'NunitoSans-Regular',
  },
  list: {
    backgroundColor: Colors.whiteColor,
    // width: windowWidth * 0.85,
    // height: windowHeight,
    marginTop: 40,
    marginHorizontal: 30,
    //alignSelf: 'center',
    //justifyContent: 'center',
    borderRadius: 5,
    shadowOpacity: 0,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
  },
  listItem: {
    fontFamily: 'NunitoSans-Regular',
    marginLeft: 20,
    color: Colors.descriptionColor,
    fontSize: 16,
  },
  textInput: {
    fontFamily: 'NunitoSans-Regular',
    height: 50,
    backgroundColor: Colors.fieldtBgColor,
    //borderRadius: 10,
    paddingLeft: 20,
  },
  textInputLocation: {
    fontFamily: 'NunitoSans-Regular',
    width: 300,
    height: 100,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 10,
    marginRight: 10,
  },
  textSize: {
    fontSize: 19,
    fontFamily: 'NunitoSans-SemiBold',
  },
  merchantDisplayView: {
    flexDirection: 'row',
    flex: 1,
    marginLeft: '17%',
  },
  shiftText: {
    marginLeft: '15%',
    color: Colors.primaryColor,
    fontFamily: 'NunitoSans-SemiBold',
    fontSize: 25,
  },
  confirmBox: {
    width: '30%',
    height: '30%',
    borderRadius: 30,
    backgroundColor: Colors.whiteColor,
  },
  closeButton: {
    position: 'absolute',
    right: 10,
    top: 10,
    elevation: 1000,
    zIndex: 1000,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: Colors.modalBgColor,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalView: {
    height: 200,
    width: 500,
    backgroundColor: Colors.whiteColor,
    borderRadius: 12,
    padding: 25,
    //alignItems: 'center',
    //justifyContent: 'center'
  },
});

export default OR_StampCards;