.react-datepicker-wrapper,
.react-datepicker__input-container,
.react-datepicker-ignore-onclickoutside,
.react-datepicker__input-container input {
  border-width: 0 !important;

  border: none;
  font-family: "NunitoSans-Regular";
  border-radius: 10;
  border-width: 1px;
  height: 30px;
  padding-left: 0px;
  z-index: 1000;
}

.react-datepicker-wrapper input {
  border: black;
  border-width: 1px;
  /* min-width: none; */
  /* width: 29.5vw; */
  width: 100px;
  padding-left: 25px;
  background-color: #F7F7F7;
  z-index: 1000;
}

.react-datepicker__day--selected {
  background-color: #4E9F7D !important;
  /* border-top: 0px;
  cursor: pointer;
  text-align: center;
  font-weight: bold;
  padding: 5px 0;
  clear: left; */
}

.react-datepicker__header {
  background-color: #4E9F7D !important;
  /* text-align: center;
  border-bottom: 0px;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  padding-top: 8px;
  position: relative; */

}

.react-datepicker__year-select,
.react-datepicker__month-select {
    background-color:  #4E9F7D !important;
    border: none;
    color: #F7F7F7;    
    /* padding: 5px;             */
    border-radius: 4px;       
    font-size: 12px;           
    /* margin-bottom: 10px;        */
    max-height: 100px;
}

/* Add hover effect */
.react-datepicker__year-select:hover,
.react-datepicker__month-select:hover {
    background-color: #e6e6e6; /* Change background on hover */
    
}

/* Add focus effect */
.react-datepicker__year-select:focus,
.react-datepicker__month-select:focus {
    outline: none;              /* Remove the default outline */
}

/* Change the dropdown arrow */
.react-datepicker__year-select::after,
.react-datepicker__month-select::after {
    content: "▼";              /* Customize the dropdown arrow */

}

/* Example for custom dropdown height and width */
.react-datepicker__year-select,
.react-datepicker__month-select {
    width: 100px;              /* Set a custom width */
    height: 30px;              /* Set a custom height */
}
