import React, { Component, useState, useEffect } from "react";

import {
  StyleSheet,
  ScrollView,
  Image,
  View,
  Text,
  TouchableOpacity,
  useWindowDimensions,
  FlatList,
} from "react-native";
import Colors from "../constant/Colors";
import ApiClient from "../util/ApiClient";
import API from "../constant/API";
import Octicons from "react-native-vector-icons/Octicons";
import Icon2 from "react-native-vector-icons/Feather";
import AntDesign from "react-native-vector-icons/AntDesign";
import Styles from "../constant/Styles";
import * as Cart from "../util/Cart";
import * as User from "../util/User";
import { getNavFrom, getRouteFrom } from "../util/commonFuncs";
import Icon from "react-native-vector-icons/MaterialIcons";
import Ionicons from "react-native-vector-icons/Ionicons";
// import { FlatList, TextInput } from 'react-native-gesture-handler';
import moment from "moment";
import Icons from "react-native-vector-icons/EvilIcons";
// import DateTimePickerModal from 'react-native-modal-datetime-picker';
// import { GooglePlacesAutocomplete } from 'react-native-google-places-autocomplete';
import Autocomplete from "react-google-autocomplete";
import { CommonStore } from "../store/commonStore";
import { ADDRESS_TYPE } from "../constant/common";
import { UserStore } from "../store/userStore";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { googleCloudApiKey, prefix } from "../constant/env";
import { CommonActions, useLinkTo } from "@react-navigation/native";
import { ReactComponent as House } from '../svg/house.svg';
import { ReactComponent as WorkIcon } from '../svg/work.svg';
import { DataStore } from "../store/dataStore";

import loadable from '@loadable/component';
// const AutocompleteLoadable = loadable.lib(() => import("react-google-autocomplete"));

/**
 * AddressScreen
 * function
 * *Display list of added addresses
 * *add new addresses, label as Home, Work or Other
 *
 * route.params
 * *testing: indicate the navigation route to this page, 1 = Cart, else = Takeaway
 * navFrom: indicate the navigation route to this page
 */
const AddressScreen = (props) => {
  const { navigation, route } = props;

  const { width: windowWidth, height: windowHeight } = useWindowDimensions();

  const linkTo = useLinkTo();

  const linkToFunc = DataStore.useState(s => s.linkToFunc);

  var testingParam = null;
  var navFromParam = null;

  if (route.params) {
    testingParam = route.params.testing;
    navFromParam = route.params.navFrom;
  } else {
    testingParam = getRouteFrom();
    navFromParam = getNavFrom();
  }

  const [address, setAddress] = useState([]);
  const [Home, setHome] = useState([]);
  const [Work, setWork] = useState([]);
  const [Other, setOther] = useState([]);
  const [testing, setTesting] = useState(testingParam);
  const [navFrom, setNavFrom] = useState(navFromParam);
  const [outletData, setOutletData] = useState([]);

  const [test, setTest] = useState([]);

  const [homeAddresses, setHomeAddresses] = useState([]);
  const [workAddresses, setWorkAddresses] = useState([]);
  const [otherAddresses, setOtherAddresses] = useState([]);

  const userAddresses = UserStore.useState((s) => s.userAddresses);
  const selectedUserAddress = UserStore.useState((s) => s.selectedUserAddress);

  const setState = () => { };

  navigation.setOptions({
    headerLeft: () => (
      <TouchableOpacity
        style={{}}
        onPress={async () => {
          // props.navigation.goBack();
          const subdomain = await AsyncStorage.getItem("latestSubdomain");

          // console.log(subdomain)
          if (!subdomain) {
            global.linkToFunc && global.linkToFunc(`${prefix}/outlet/cart`);
          }
          else {
            global.linkToFunc && global.linkToFunc(`${prefix}/outlet/${subdomain}/cart`);
          }
        }}
      >
        <View
          style={{
            marginLeft: 10,
            display: "flex",
            flexDirection: "row",
            alignItems: "center",
            justifyContent: "flex-start",
          }}
        >
          <Ionicons
            name="chevron-back"
            size={26}
            color={Colors.fieldtTxtColor}
            style={{}}
          />

          <Text
            style={{
              color: Colors.fieldtTxtColor,
              fontSize: 16,
              textAlign: "center",
              fontFamily: "NunitoSans-Regular",
              lineHeight: 22,
              marginTop: -1,
            }}
          >
            Back
          </Text>
        </View>
      </TouchableOpacity>
    ),
    headerRight: () => (
      <TouchableOpacity
        onPress={() => {
          // props.navigation.navigate('Profile')
          // linkTo(`${prefix}/outlet/menu`);
        }}
        style={{}}
      >
        <View style={{ marginRight: 15 }}>
          <Ionicons name="menu" size={30} color={Colors.primaryColor} />
        </View>
      </TouchableOpacity>
    ),
    headerTitle: () => (
      <View
        style={{
          justifyContent: "center",
          alignItems: "center",
          bottom: -1,
        }}
      >
        <Text
          style={{
            fontSize: 20,
            lineHeight: 25,
            textAlign: "center",
            fontFamily: "NunitoSans-Bold",
            color: Colors.mainTxtColor,
          }}
        >
          My Address
        </Text>
      </View>
    ),
  });

  useEffect(() => {
    if (navFrom != "TAKEAWAY") {
      getOutlet();
    }

    // setInterval(() => {
    //   getAddress()
    // }, 5000);
    // getAddress()
  }, []);

  useEffect(() => {
    if (userAddresses.length > 0) {
      setHomeAddresses(
        userAddresses.filter((address) => address.type === ADDRESS_TYPE.HOME)
      );
      setWorkAddresses(
        userAddresses.filter((address) => address.type === ADDRESS_TYPE.WORK)
      );
      setOtherAddresses(
        userAddresses.filter((address) => address.type === ADDRESS_TYPE.OTHER)
      );
    }
  }, [userAddresses]);

  // function here
  // const getAddress = () => {
  //   ApiClient.GET(API.getUserAddress + User.getUserId()).then((result) => {
  //     //console.log("getUserAddress",result)
  //     setState({ address: result })
  //     result.forEach((element) => {
  //       const HomeAddress = result.filter(element => element.type === "Home")
  //       //console.log("HOME",HomeAddress)
  //       const WorkAddress = result.filter(element => element.type === "Work")
  //       //console.log("WORK",WorkAddress)
  //       const OtherAddress = result.filter(element => element.type !== "Work" && element.type !== "Home")
  //       //console.log("OTHER",OtherAddress)
  //       setState({ Home: HomeAddress, Work: WorkAddress, Other: OtherAddress })
  //     });

  //   });
  // }


  const getOutlet = () => {
    console.log("Cart.getOutletId()", Cart.getOutletId());
    ApiClient.GET(API.outlet2 + Cart.getOutletId()).then((result) => {
      setState({ outletData: result });
    });
  };

  const getQuotation = (item) => {
    var body = {
      serviceType: "MOTORCYCLE",
      specialRequests: [],
      stops: [
        {
          // Location information for pick-up point
          location: {
            lat: outletData.latlng.split(",")[0],
            lng: outletData.latlng.split(",")[1],
          },
          addresses: {
            ms_MY: {
              displayString: outletData.address,
              country: "MY_KUL",
            },
          },
        },
        {
          // Location information for drop-off point (#1)
          location: {
            lat: item.lat,
            lng: item.lng,
          },
          addresses: {
            ms_MY: {
              displayString: item.address,
              country: "MY_KUL",
            },
          },
        },
      ],
      // Pick-up point copntact details
      requesterContact: {
        name: "Chris Wong", //get outlet person in charge?
        phone: "0376886555", //or get waiter?
      },
      deliveries: [
        {
          // Contact information at the drop-off point (#1)
          toStop: 1,
          toContact: {
            name: User.getName(),
            phone: User.getUserData().number,
          },
          remarks: item.note,
        },
      ],
    };
    console.log("QUOTATION BODY", body);

    try {
      ApiClient.POST(API.lalamoveQuotation, body).then((result) => {
        console.log("quotation result", result);
        Cart.setDeliveryQuotation(result);
        //props.navigation.goBack()
        // if (navFrom == "TAKEAWAY") {
        //   props.navigation.navigate("Takeaway", { deliveryAddress: Other })
        // }
        // else {
        //   props.navigation.navigate("Cart", { test: test, outletData: outletData, })
        // }
      });
    } catch (err) {
      console.log("quotation", err);
    }
  };

  const renderItem = ({ item }) => (
    <>
      <TouchableOpacity
        onPress={async () => {
          UserStore.update((s) => {
            s.selectedUserAddress = item;
          });

          await AsyncStorage.setItem("defaultUserAddressId", item.uniqueId);

          Cart.setDeliveryAddress(item);

          // getQuotation(item)
          const subdomain = await AsyncStorage.getItem("latestSubdomain");

          if (navFrom == "TAKEAWAY") {
            // props.navigation.navigate("Takeaway", { deliveryAddress: item })

            if (!subdomain) {
              linkTo && linkTo(`${prefix}/outlet/takeaway`);
            } else {
              linkTo && linkTo(`${prefix}/outlet/${subdomain}/takeaway`);
            }
          } else {
            // props.navigation.navigate("Cart", { test: test, outletData: outletData, })
            if (!subdomain) {
              linkTo && linkTo(`${prefix}/outlet/cart`);
            } else {
              linkTo && linkTo(`${prefix}/outlet/${subdomain}/cart`);
            }
          }
        }}
        style={{
          // backgroundColor: 'red',
          marginLeft: 10,
        }}
      >
        <View
          style={[
            styles.container,
            {
              // backgroundColor: 'red',
            },
          ]}
        >
          <View>
            <Text
              style={{
                fontFamily: "NunitoSans-Bold",
                top: -1,
                fontSize: 16,
              }}
            >{`${item.type[0].toUpperCase() + item.type.slice(1).toLowerCase()
              }`}</Text>
          </View>
          <View>
            <Text
              style={{
                fontSize: 14,
                color: Colors.mainTxtColor,
                fontFamily: "NunitoSans-SemiBold",
                width: "90%",
              }}
            >
              {item.address}
            </Text>
          </View>
        </View>
      </TouchableOpacity>

      <View
        style={{
          height: 1.5,
          left: "5%",
          width: "90%",
          backgroundColor: "#C2C1C0",
          opacity: 0.2,
          marginBottom: 4,

          shadowColor: "#000",
          shadowOffset: {
            width: 0,
            height: 1,
          },
          shadowOpacity: 0.22,
          shadowRadius: 2.22,
          elevation: 3,
        }}
      ></View>
    </>
  );

  const renderSearch = (item) => {
    return (
      <View style={{ flexDirection: "column" }}>
        <Text style={{ fontWeight: "700", fontSize: 14, marginBottom: 5 }}>
          {item.structured_formatting.main_text}
        </Text>

        <Text style={{ fontSize: 12, color: "grey" }}>{item.description}</Text>
      </View>
    );
  };
  // function end

  return (
    <View style={{ flex: 1 }}>
      <ScrollView
        style={{ backgroundColor: Colors.whiteColor }}
        keyboardShouldPersistTaps="always"
      >
        {/* <GooglePlacesAutocomplete
          placeholder="📍  Type your address here"
          minLength={2} // minimum length of text to search
          autoFocus={false}
          returnKeyType={'search'} // Can be left out for default return key https://facebook.github.io/react-native/docs/textinput.html#returnkeytype
          listViewDisplayed='false'    // true/false/undefined
          fetchDetails={true}
          renderDescription={row => renderSearch(row)} // custom description render
          onPress={(data, details = null) => { // 'details' is provided when fetchDetails = true
            console.log("data", data);
            console.log("details", details);
            console.log("data.description", data.description);
            console.log("details.geometry.location", details.geometry.location);
            props.navigation.navigate('AddAddress', { test: 3, address: data.description, name: data.structured_formatting.main_text, location: details.geometry.location })
          }}

          getDefaultValue={() => ''}

          query={{
            // available options: https://developers.google.com/places/web-service/autocomplete
            //key: 'AIzaSyB_nKKIGIcEJ6ffsVBdYeIstW8KekjVXa8',
            // key: 'AIzaSyCsM9boPkQ7uaJ54RWUzPHGzjZfggkXf8g',
            key: 'AIzaSyAt1AOIT_1E5Ivec7PVWmylmDd8jSVm-j8',
            language: 'en', // language of the results
            //types: 'address', // default: 'geocode'
            components: 'country:my',
          }}

          styles={{
            textInputContainer: {
              width: '90%',
              alignSelf: 'center',
              marginTop: 20,
            },
            textInput: {
              backgroundColor: Colors.fieldtBgColor,
              fontFamily: 'NunitoSans-Regular',
              fontSize: 14,
              height: 50,
            },
            description: {
              // fontWeight: 'bold',              
              fontFamily: 'NunitoSans-Bold',
              fontSize: 16,
            },
            predefinedPlacesDescription: {
              color: '#1faadb',
              fontFamily: 'NunitoSans-Regular',
              fontSize: 14,
            },
            position: "absolute",
          }}

          currentLocation={false}
          currentLocationLabel="Current location"
          nearbyPlacesAPI='GooglePlacesSearch'
          GoogleReverseGeocodingQuery={{
          }}
          GooglePlacesSearchQuery={{
            rankby: 'distance'
          }}

          filterReverseGeocodingByTypes={['locality', 'administrative_area_level_3']}

          debounce={200}

        /> */}

        {/* <AutocompleteLoadable fallback={<></>}>
          {({ default: Autocomplete }) =>
            <Autocomplete
              placeholder="📍  Type your address here"
              minLength={2} // minimum length of text to search
              autoFocus={false}
              returnKeyType={"search"} // Can be left out for default return key https://facebook.github.io/react-native/docs/textinput.html#returnkeytype
              listViewDisplayed="false" // true/false/undefined
              fetchDetails={true}
              renderDescription={(row) => renderSearch(row)} // custom description render
              onPlaceSelected={(data) => {
                // 'details' is provided when fetchDetails = true
                console.log("data", data);
                // console.log("details", details);
                // console.log("data.description", data.description);
                // console.log("details.geometry.location", details.geometry.location);

                CommonStore.update(s => {
                  s.selectedAddress = data.formatted_address;
                  s.selectedAddressLocation = {
                    lat: data.geometry.location.lat(),
                    lng: data.geometry.location.lng(),
                  };
                });

                props.navigation.navigate("AddAddress - KooDoo Web Order", {
                  test: 3,
                  address: data.formatted_address,
                  name:
                    data.address_components[0].short_name +
                    ", " +
                    data.address_components[1].short_name,
                  location: {
                    lat: data.geometry.location.lat(),
                    lng: data.geometry.location.lng(),
                  },
                });
              }}
              defaultValue={() => ""}
              // query={{
              //   // available options: https://developers.google.com/places/web-service/autocomplete
              //   //key: 'AIzaSyB_nKKIGIcEJ6ffsVBdYeIstW8KekjVXa8',
              //   // key: 'AIzaSyCsM9boPkQ7uaJ54RWUzPHGzjZfggkXf8g',
              //   key: 'AIzaSyAt1AOIT_1E5Ivec7PVWmylmDd8jSVm-j8',
              //   language: 'en', // language of the results
              //   //types: 'address', // default: 'geocode'
              //   components: 'country:my',
              // }}

              apiKey={googleCloudApiKey}

              // This api can use
              // apiKey={"AIzaSyABX4LTqTLQGg_b3jFOH8Z6_H5CDqn8tbc"}
              //This api cannot use
              // apiKey= {'AIzaSyAt1AOIT_1E5Ivec7PVWmylmDd8jSVm-j8'}

              options={{
                types: ["address"],
                componentRestrictions: { country: "my" },
              }}
              styles={{
                textInputContainer: {
                  width: "90%",
                  alignSelf: "center",
                  marginTop: 20,
                },
                textInput: {
                  backgroundColor: Colors.fieldtBgColor,
                  fontFamily: "NunitoSans-Regular",
                  fontSize: 14,
                  height: 50,
                },
                description: {
                  // fontWeight: 'bold',
                  fontFamily: "NunitoSans-Bold",
                  fontSize: 16,
                },
                predefinedPlacesDescription: {
                  color: "#1faadb",
                  fontFamily: "NunitoSans-Regular",
                  fontSize: 14,
                },
                position: "absolute",
              }}
              // currentLocation={false}
              // currentLocationLabel="Current location"
              // nearbyPlacesAPI='GooglePlacesSearch'
              // GoogleReverseGeocodingQuery={{
              // }}
              // GooglePlacesSearchQuery={{
              //   rankby: 'distance'
              // }}

              // filterReverseGeocodingByTypes={['locality', 'administrative_area_level_3']}

              debounce={200}
            />
          }
        </AutocompleteLoadable> */}

        <Autocomplete
          placeholder="📍  Type your address here"
          minLength={2} // minimum length of text to search
          autoFocus={false}
          returnKeyType={"search"} // Can be left out for default return key https://facebook.github.io/react-native/docs/textinput.html#returnkeytype
          listViewDisplayed="false" // true/false/undefined
          fetchDetails={true}
          renderDescription={(row) => renderSearch(row)} // custom description render
          onPlaceSelected={(data) => {
            // 'details' is provided when fetchDetails = true
            console.log("data", data);
            // console.log("details", details);
            // console.log("data.description", data.description);
            // console.log("details.geometry.location", details.geometry.location);

            CommonStore.update(s => {
              s.selectedAddress = data.formatted_address;
              s.selectedAddressLocation = {
                lat: data.geometry.location.lat(),
                lng: data.geometry.location.lng(),
              };
            });

            props.navigation.navigate("AddAddress - KooDoo Web Order", {
              test: 3,
              address: data.formatted_address,
              name:
                data.address_components[0].short_name +
                ", " +
                data.address_components[1].short_name,
              location: {
                lat: data.geometry.location.lat(),
                lng: data.geometry.location.lng(),
              },
            });
          }}
          defaultValue={() => ""}
          // query={{
          //   // available options: https://developers.google.com/places/web-service/autocomplete
          //   //key: 'AIzaSyB_nKKIGIcEJ6ffsVBdYeIstW8KekjVXa8',
          //   // key: 'AIzaSyCsM9boPkQ7uaJ54RWUzPHGzjZfggkXf8g',
          //   key: 'AIzaSyAt1AOIT_1E5Ivec7PVWmylmDd8jSVm-j8',
          //   language: 'en', // language of the results
          //   //types: 'address', // default: 'geocode'
          //   components: 'country:my',
          // }}

          apiKey={googleCloudApiKey}

          // This api can use
          // apiKey={"AIzaSyABX4LTqTLQGg_b3jFOH8Z6_H5CDqn8tbc"}
          //This api cannot use
          // apiKey= {'AIzaSyAt1AOIT_1E5Ivec7PVWmylmDd8jSVm-j8'}

          options={{
            types: ["address"],
            componentRestrictions: { country: "my" },
          }}
          styles={{
            textInputContainer: {
              width: "90%",
              alignSelf: "center",
              marginTop: 20,
            },
            textInput: {
              backgroundColor: Colors.fieldtBgColor,
              fontFamily: "NunitoSans-Regular",
              fontSize: 14,
              height: 50,
            },
            description: {
              // fontWeight: 'bold',
              fontFamily: "NunitoSans-Bold",
              fontSize: 16,
            },
            predefinedPlacesDescription: {
              color: "#1faadb",
              fontFamily: "NunitoSans-Regular",
              fontSize: 14,
            },
            position: "absolute",
          }}
          // currentLocation={false}
          // currentLocationLabel="Current location"
          // nearbyPlacesAPI='GooglePlacesSearch'
          // GoogleReverseGeocodingQuery={{
          // }}
          // GooglePlacesSearchQuery={{
          //   rankby: 'distance'
          // }}

          // filterReverseGeocodingByTypes={['locality', 'administrative_area_level_3']}

          debounce={200}
        />

        <View
          style={{
            width: "100%",
            // height: 60,
            paddingVertical: 16,
            backgroundColor: "#ddebe5",
            justifyContent: "center",
            marginTop: 20,
            paddingHorizontal: 28,
          }}
        >
          <Text
            style={{
              color: Colors.primaryColor,
              // marginLeft: 30,
              fontSize: 17,
              fontFamily: "NunitoSans-SemiBold",
            }}
          >
            Saved Places
          </Text>
        </View>

        {homeAddresses.length == 0 ? (
          <TouchableOpacity
            style={{ justifyContent: "center" }}
            onPress={() => {
              CommonStore.update((s) => {
                s.selectedAddressType = ADDRESS_TYPE.HOME;
              });

              // props.navigation.navigate('HomeAddress - KooDoo Web Order');
              linkTo && linkTo(`${prefix}/outlet/homeaddress`);
            }}
          >
            <View style={styles.container}>
              <View
                style={{
                  flexDirection: "row",
                  alignItems: "center",
                }}
              >
                <View
                  style={{
                    backgroundColor: Colors.primaryColor,
                    width: 32,
                    height: 32,
                    borderRadius: 20,
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                  }}
                >
                  {/* <Icon name="house" size={24} color={Colors.whiteColor} /> */}
                  <House />
                </View>
                <Text
                  style={{
                    marginLeft: 15,
                    // fontWeight: "700"
                    fontFamily: "NunitoSans-Bold",
                    top: -1,
                    fontSize: 16,
                  }}
                >
                  Add Home
                </Text>
              </View>
            </View>
          </TouchableOpacity>
        ) : (
          <TouchableOpacity
            style={{ justifyContent: "center" }}
            onPress={async () => {
              //props.navigation.goBack()

              UserStore.update((s) => {
                s.selectedUserAddress = homeAddresses[0];
              });

              await AsyncStorage.setItem(
                "defaultUserAddressId",
                homeAddresses[0].uniqueId
              );

              const subdomain = await AsyncStorage.getItem("latestSubdomain");

              Cart.setDeliveryAddress(homeAddresses[0]);
              if (navFrom == "TAKEAWAY") {
                // props.navigation.navigate("Takeaway", { deliveryAddress: Home })
                if (!subdomain) {
                  linkTo && linkTo(`${prefix}/outlet/takeaway`);
                } else {
                  linkTo && linkTo(`${prefix}/outlet/${subdomain}/takeaway`);
                }
              } else {
                // props.navigation.navigate("Cart", { test: test, outletData: outletData, })
                if (!subdomain) {
                  linkTo && linkTo(`${prefix}/outlet/cart`);
                } else {
                  linkTo && linkTo(`${prefix}/outlet/${subdomain}/cart`);
                }
              }
            }}
          >
            <View style={styles.container}>
              <View
                style={{
                  flexDirection: "row",
                  alignItems: "center",
                }}
              >
                <View
                  style={{
                    backgroundColor: Colors.primaryColor,
                    width: 32,
                    height: 32,
                    borderRadius: 20,
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                  }}
                >
                  <Icon name="house" size={24} color={Colors.whiteColor} />
                </View>

                {homeAddresses.map((element) => {
                  return (
                    <View
                      style={{
                        marginLeft: 15,
                        width: "80%",
                      }}
                    >
                      <View>
                        <Text
                          style={{
                            // fontWeight: "700",
                            // fontSize: 16,
                            // marginBottom: 5,
                            fontFamily: "NunitoSans-Bold",
                            top: -1,
                            fontSize: 16,
                          }}
                        >{`${element.type[0].toUpperCase() +
                          element.type.slice(1).toLowerCase()
                          }`}</Text>
                      </View>
                      <View
                        style={
                          {
                            // marginBottom: 5
                          }
                        }
                      >
                        <Text
                          style={{
                            fontSize: 14,
                            color: Colors.mainTxtColor,
                            fontFamily: "NunitoSans-SemiBold",
                            width: "90%",
                          }}
                        >
                          {element.address}
                        </Text>
                      </View>
                    </View>
                  );
                })}
                {/* <TouchableOpacity onPress={() => { props.navigation.navigate('HomeAddress') }}> */}
                <TouchableOpacity
                  onPress={() => {
                    props.navigation.navigate(
                      "EditAddress - KooDoo Web Order",
                      { address: homeAddresses[0] }
                    );
                    // linkTo && linkTo(`${prefix}/outlet/editaddress`);
                  }}
                >
                  <Icon2 name="edit" size={24} color={Colors.primaryColor} />
                </TouchableOpacity>
              </View>
            </View>
          </TouchableOpacity>
        )}

        <View
          style={{
            height: 1.5,
            left: "5%",
            width: "90%",
            backgroundColor: "#C2C1C0",
            opacity: 0.2,
            marginBottom: 4,

            shadowColor: "#000",
            shadowOffset: {
              width: 0,
              height: 1,
            },
            shadowOpacity: 0.22,
            shadowRadius: 2.22,
            elevation: 3,
          }}
        ></View>

        {workAddresses.length == 0 ? (
          <TouchableOpacity
            style={{ justifyContent: "center" }}
            onPress={() => {
              CommonStore.update((s) => {
                s.selectedAddressType = ADDRESS_TYPE.WORK;
              });

              // props.navigation.navigate('WorkAddress - KooDoo Web Order');
              linkTo && linkTo(`${prefix}/outlet/workaddress`);
            }}
          >
            <View style={styles.container}>
              <View
                style={{
                  flexDirection: "row",
                  alignItems: "center",
                }}
              >
                <View
                  style={{
                    backgroundColor: Colors.primaryColor,
                    width: 32,
                    height: 32,
                    borderRadius: 20,
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                  }}
                >
                  <WorkIcon />
                  {/* <Icon name="work" size={20} color={Colors.whiteColor} /> */}
                </View>
                <Text
                  style={{
                    marginLeft: 15,
                    // fontWeight: "700"
                    fontFamily: "NunitoSans-Bold",
                    top: -1,
                    fontSize: 16,
                  }}
                >
                  Add Work
                </Text>
              </View>
            </View>
          </TouchableOpacity>
        ) : (
          <TouchableOpacity
            style={{ justifyContent: "center" }}
            onPress={async () => {
              UserStore.update((s) => {
                s.selectedUserAddress = workAddresses[0];
              });

              await AsyncStorage.setItem(
                "defaultUserAddressId",
                workAddresses[0].uniqueId
              );

              const subdomain = await AsyncStorage.getItem("latestSubdomain");

              Cart.setDeliveryAddress(workAddresses[0]);
              if (navFrom == "TAKEAWAY") {
                // props.navigation.navigate("Takeaway", { deliveryAddress: Work })
                if (!subdomain) {
                  linkTo && linkTo(`${prefix}/outlet/takeaway`);
                } else {
                  linkTo && linkTo(`${prefix}/outlet/${subdomain}/takeaway`);
                }
              } else {
                // props.navigation.navigate("Cart", { test: test, outletData: outletData, })
                if (!subdomain) {
                  linkTo && linkTo(`${prefix}/outlet/cart`);
                } else {
                  linkTo && linkTo(`${prefix}/outlet/${subdomain}/cart`);
                }
              }
            }}
          >
            <View style={styles.container}>
              <View
                style={{
                  flexDirection: "row",
                  alignItems: "center",
                }}
              >
                <View
                  style={{
                    backgroundColor: Colors.primaryColor,
                    width: 32,
                    height: 32,
                    borderRadius: 20,
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                  }}
                >
                  <Icon name="work" size={20} color={Colors.whiteColor} />
                </View>

                {workAddresses.map((element) => {
                  return (
                    <View
                      style={{
                        marginLeft: 15,
                        width: "80%",
                      }}
                    >
                      <View>
                        <Text
                          style={{
                            // fontWeight: "700",
                            // fontSize: 14,
                            // marginBottom: 5
                            fontFamily: "NunitoSans-Bold",
                            top: -1,
                            fontSize: 16,
                          }}
                        >{`${element.type[0].toUpperCase() +
                          element.type.slice(1).toLowerCase()
                          }`}</Text>
                      </View>
                      <View style={{ marginBottom: 10 }}>
                        <Text
                          style={{
                            fontSize: 14,
                            color: Colors.mainTxtColor,
                            fontFamily: "NunitoSans-SemiBold",
                            width: "90%",
                          }}
                        >
                          {element.address}
                        </Text>
                      </View>
                    </View>
                  );
                })}
                {/* <TouchableOpacity onPress={() => { props.navigation.navigate('WorkAddress') }}> */}
                <TouchableOpacity
                  style={
                    {
                      // marginLeft: 10,
                    }
                  }
                  onPress={() => {
                    // props.navigation.navigate('EditAddress - KooDoo Web Order', { address: workAddresses[0] })
                    linkTo && linkTo(`${prefix}/outlet/editaddress`);
                  }}
                >
                  <Icon2 name="edit" size={24} color={Colors.primaryColor} />
                </TouchableOpacity>
              </View>
            </View>
          </TouchableOpacity>
        )}

        <View
          style={{
            height: 1.5,
            left: "5%",
            width: "90%",
            backgroundColor: "#C2C1C0",
            opacity: 0.2,
            marginBottom: 4,

            shadowColor: "#000",
            shadowOffset: {
              width: 0,
              height: 1,
            },
            shadowOpacity: 0.22,
            shadowRadius: 2.22,
            elevation: 3,
          }}
        ></View>

        <FlatList
          style={{ marginBottom: 10 }}
          data={otherAddresses}
          extraData={otherAddresses}
          renderItem={renderItem}
          keyExtractor={(item, index) => String(index)}
        />

        <TouchableOpacity
          style={{ justifyContent: "center" }}
          onPress={() => {
            CommonStore.update((s) => {
              s.selectedAddressType = ADDRESS_TYPE.OTHER;
            });

            // props.navigation.navigate('NewAddress - KooDoo Web Order');
            linkTo && linkTo(`${prefix}/outlet/newaddress`);
          }}
        >
          <View style={styles.container}>
            <View
              style={{
                flexDirection: "row",
                alignItems: "center",
              }}
            >
              <View
                style={{
                  backgroundColor: Colors.primaryColor,
                  width: 32,
                  height: 32,
                  borderRadius: 20,
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                }}
              >
                <Ionicons
                  name="add-sharp"
                  size={30}
                  color={Colors.whiteColor}
                  style={{
                    right: -1,
                  }}
                />
              </View>
              <Text
                style={{
                  marginLeft: 15,
                  // fontWeight: "700"
                  fontFamily: "NunitoSans-Bold",
                  top: -1,
                  fontSize: 16,
                }}
              >
                Add New
              </Text>
            </View>
          </View>
        </TouchableOpacity>

        <View
          style={{
            width: "100%",
            // height: 60,
            paddingVertical: 16,
            backgroundColor: "#ddebe5",
            justifyContent: "center",
            marginTop: 20,
            paddingHorizontal: 28,
          }}
        >
          <Text
            style={{
              color: Colors.primaryColor,
              // marginLeft: 30,
              fontSize: 17,
              fontFamily: "NunitoSans-SemiBold",
            }}
          >
            Recently Used
          </Text>
        </View>

        <FlatList
          style={{ marginBottom: 10 }}
          data={address}
          extraData={address}
          renderItem={renderItem}
          keyExtractor={(item, index) => String(index)}
        />

        <View style={{ height: 120 }}></View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: "#ffffff",
    // height: 70,
    alignSelf: "center",
    justifyContent: "center",
    width: "90%",
    // borderBottomWidth: StyleSheet.hairlineWidth,
    // marginTop: 5,
    // marginBottom: 5,
    paddingVertical: 25,
  },
  searchBar: {
    backgroundColor: Colors.fieldtBgColor,
    flexDirection: "row",
    padding: 12,
    borderRadius: 10,
    alignContent: "center",
  },
  searchBarText: {
    color: Colors.fieldtTxtColor,
    width: "80%",
  },
  BarText: {
    color: Colors.primaryColor,
    marginLeft: 10,
    fontSize: 15,
    fontWeight: "bold",
    textAlign: "center",
  },

  text: {
    fontSize: 15,
    fontWeight: "700",
  },
  text1: {
    fontSize: 13,
    fontWeight: "700",
    marginTop: "2%",
  },
  textInput: {
    height: 50,
    width: "90%",
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    alignSelf: "center",
    marginTop: 20,
  },
  text2: {
    fontSize: 24,
    fontWeight: "700",
    marginTop: "8%",
  },
  title: {
    color: Colors.blackColor,
    fontSize: 20,
    fontWeight: "bold",
  },
  viewContainer: {
    marginTop: 10,
    justifyContent: "space-between",
    flexDirection: "row",
    flex: 10,
  },
  total: {
    fontSize: 14,
    color: Colors.blackColor,
    fontWeight: "700",
  },
  price: {
    fontSize: 14,
    color: Colors.primaryColor,
    fontWeight: "700",
  },
  totalBorder: {
    paddingTop: 5,
    justifyContent: "space-between",
    flex: 1,
    flexDirection: "row",
    borderTopWidth: StyleSheet.hairlineWidth,
    borderColor: Colors.blackColor,
    borderRadius: 1,
  },
  pic: {
    width: 90,
    height: 90,
    borderRadius: 10,
    marginBottom: 5,
    alignItems: "center",
    justifyContent: "center",
  },
  Bar1: {
    flexDirection: "row",
    padding: 12,
    borderRadius: 10,
    alignContent: "center",
    marginBottom: "2%",
  },
});
export default AddressScreen;
