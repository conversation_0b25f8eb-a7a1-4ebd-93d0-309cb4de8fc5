{"name": "koodoo-web", "version": "0.1.0", "private": true, "dependencies": {"@loadable/component": "5.15.3", "@react-native-async-storage/async-storage": "1.17.11", "@react-navigation/bottom-tabs": "5.11.15", "@react-navigation/native": "5.9.8", "@react-navigation/stack": "5.14.9", "@sentry/browser": "^7.109.0", "axios": "^1.7.5", "bignumber.js": "9.1.1", "crypto-js": "4.1.1", "file-saver": "^2.0.5", "firebase": "10.14.0", "geofire-common": "5.2.0 ", "hashids": "2.2.11", "html2canvas": "^1.4.1", "http": "0.0.1-security", "https": "1.0.0", "idb": "^7.1.0", "moment": "2.29.4", "nanoid": "4.0.1", "pullstate": "1.25.0", "qr-scanner": "^1.4.2", "rc-checkbox": "2.3.2", "react": "18.2.0", "react-datepicker": "4.10.0", "react-dom": "18.2.0", "react-fast-marquee": "^1.6.5", "react-google-autocomplete": "2.7.3", "react-horizontal-datepicker": "2.0.3", "react-multiple-select-dropdown-lite": "2.0.6", "react-native-awesome-alerts": "1.5.2", "react-native-draggable": "3.3.0", "react-native-dropdown-picker": "^5.4.6", "react-native-get-random-values": "1.8.0", "react-native-safe-area-context": "3.4.1", "react-native-screens": "3.20.0", "react-native-vector-icons": "8.1.0", "react-native-web": "0.18.12", "react-native-web-linear-gradient": "1.1.2", "react-scripts": "4.0.3", "react-secure-storage": "^1.3.2", "react-select": "5.7.0", "react-switch": "^7.0.0", "toastify-js": "^1.12.0", "uuid": "8.3.2", "w-json-stream": "^1.0.16", "web-vitals": "1.1.2"}, "scripts": {"start": "shx cp manifest.dev.json public/manifest.json && shx cp src/config/firebase-web.dev.json src/config/firebase-web.json && shx cp src/config/firebase-web.dev.json public/config/firebase-web.json && shx cp src/constant/dev.env.js src/constant/env.js && shx cp .dev.env .env && react-app-rewired start", "start-prod": "shx cp manifest.prod.json public/manifest.json && shx cp src/config/firebase-web.prod.json src/config/firebase-web.json && shx cp src/config/firebase-web.prod.json public/config/firebase-web.json && shx cp src/constant/prod.env.js src/constant/env.js && shx cp .prod.env .env && react-app-rewired start", "start-uat": "shx cp manifest.uat.json public/manifest.json && shx cp src/config/firebase-web.prod.json src/config/firebase-web.json && shx cp src/config/firebase-web.prod.json public/config/firebase-web.json && shx cp src/constant/uat.env.js src/constant/env.js && shx cp .uat.env .env && react-app-rewired start", "build": "shx cp manifest.dev.json public/manifest.json && shx cp src/config/firebase-web.dev.json src/config/firebase-web.json && shx cp src/config/firebase-web.dev.json public/config/firebase-web.json && shx cp src/constant/dev.env.js src/constant/env.js && shx cp .dev.env .env && npx env-cmd .env && react-app-rewired build", "build-prod": "shx cp manifest.prod.json public/manifest.json && shx cp src/config/firebase-web.prod.json src/config/firebase-web.json && shx cp src/config/firebase-web.prod.json public/config/firebase-web.json && shx cp src/constant/prod.env.js src/constant/env.js && shx cp .prod.env .env && npx env-cmd .env && react-app-rewired build", "build-prod-s3": "shx cp manifest.prod.json public/manifest.json && shx cp src/config/firebase-web.prod.json src/config/firebase-web.json && shx cp src/config/firebase-web.prod.json public/config/firebase-web.json && shx cp src/constant/prod.env.js src/constant/env.js && shx cp .prod-build.env .env && npx env-cmd .env && react-app-rewired build && aws s3 cp build s3://kd-koodoo-web/prod --recursive && rm -r debug/prod/static && cp -rf build/static debug/prod/static && rm -r build/static", "build-uat": "shx cp manifest.uat.json public/manifest.json && shx cp src/config/firebase-web.prod.json src/config/firebase-web.json && shx cp src/config/firebase-web.prod.json public/config/firebase-web.json && shx cp src/constant/uat.env.js src/constant/env.js && shx cp .uat.env .env && npx env-cmd .env && react-app-rewired build", "build-uat-s3": "shx cp manifest.uat.json public/manifest.json && shx cp src/config/firebase-web.prod.json src/config/firebase-web.json && shx cp src/config/firebase-web.prod.json public/config/firebase-web.json && shx cp src/constant/uat.env.js src/constant/env.js && shx cp .uat-build.env .env && npx env-cmd .env && react-app-rewired build && aws s3 cp build s3://kd-koodoo-web/uat --recursive && rm -r debug/uat/static && cp -rf build/static debug/uat/static && rm -r build/static", "start-w": "shx cp src/config/firebase-web.dev.json src/config/firebase-web.json && shx cp src/config/firebase-web.dev.json public/config/firebase-web.json && shx cp src/constant/dev.env.js src/constant/env.js && shx cp .dev.env .env && webpack-dev-server --config webpack.config.js", "start-prod-w": "shx cp src/config/firebase-web.prod.json src/config/firebase-web.json && shx cp src/config/firebase-web.prod.json public/config/firebase-web.json && shx cp src/constant/prod.env.js src/constant/env.js && shx cp .prod.env .env && webpack-dev-server --config webpack.config.js", "build-w": "shx cp src/config/firebase-web.dev.json src/config/firebase-web.json && shx cp src/config/firebase-web.dev.json public/config/firebase-web.json && shx cp src/constant/dev.env.js src/constant/env.js && shx cp .dev.env .env && npx env-cmd .env && webpack --config webpack.config.js", "build-prod-w": "shx cp src/config/firebase-web.prod.json src/config/firebase-web.json && shx cp src/config/firebase-web.prod.json public/config/firebase-web.json && shx cp src/constant/prod.env.js src/constant/env.js && shx cp .prod.env .env && npx env-cmd .env && webpack --config webpack.config.js", "test": "react-app-rewired test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": [">0.2%", "not dead", "not op_mini all"], "devDependencies": {"@babel/plugin-proposal-class-properties": "7.14.5", "@babel/plugin-proposal-private-methods": "7.18.6", "@babel/plugin-proposal-private-property-in-object": "7.21.0", "@playwright/test": "1.34.0", "@sentry/webpack-plugin": "^2.16.1", "babel-plugin-transform-remove-console": "^6.9.4", "customize-cra": "1.0.0", "react-app-rewired": "2.2.1", "shx": "0.3.4", "webpack-bundle-analyzer": "4.8.0"}}