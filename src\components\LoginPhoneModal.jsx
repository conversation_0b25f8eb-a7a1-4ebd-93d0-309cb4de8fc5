import React, { useState } from "react";
import {
  View,
  Text,
  Modal,
  Linking,
  TouchableOpacity,
  useWindowDimensions,
} from "react-native";
import { TextInput } from "react-native-web";
import { UserStore } from "../store/userStore";
import { CommonStore } from "../store/commonStore";
import { TempStore } from "../store/tempStore";
import { isMobile, signInWithPhoneForCRMUser } from "../util/commonFuncs";
import { CheckBox } from "react-native-web";
import { signInAnonymously } from "firebase/auth";
import ApiClient from "../util/ApiClient";
import API from "../constant/API";
import AsyncStorage from "@react-native-async-storage/async-storage";
import AntDesign from "react-native-vector-icons/AntDesign";
import Colors from "../constant/Colors";
import Toastify from 'toastify-js';

const LoginPhoneModal = React.memo(() => {
  const { width: windowWidth, height: windowHeight } = useWindowDimensions();

  const selectedOutlet = CommonStore.useState((s) => s.selectedOutlet);
  const email = UserStore.useState((s) => s.email);

  const tickboxConsent = TempStore.useState((s) => s.tickboxConsent);
  const showLoginPhoneModal = TempStore.useState((s) => s.showLoginPhoneModal);

  const [inputPhone, setInputPhone] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  //////////////////////////////////////////////////////////////////////

  const LOGOUT_COUNT_KEY = 'logout_count';
  const LOGOUT_DATE_KEY = 'logout_date';

  const handleLogout = async () => {
    try {
      setIsLoading(true);
      const todayDate = new Date().toISOString().split('T')[0];

      const storedDate = await AsyncStorage.getItem(LOGOUT_DATE_KEY);
      const storedCount = await AsyncStorage.getItem(LOGOUT_COUNT_KEY);

      if (storedDate !== todayDate) {
        await AsyncStorage.setItem(LOGOUT_COUNT_KEY, '0');
        await AsyncStorage.setItem(LOGOUT_DATE_KEY, todayDate);
        return true;
      }

      const logoutCount = parseInt(storedCount, 10) || 0;

      // if (logoutCount >= 2) {
      //   alert('You have reached the maximum logout attempts for today.\n(only allowed to logout twice per day.)');
      //   return false; // Prevent logout
      // }

      /////////////////////////////////////

      CommonStore.update(s => {
        s.currCrmUser = null;
      });

      UserStore.update(s => {
        s.name = '';
        s.email = '';
        s.number = '';
        s.firebaseUid = '';
        s.userId = '';
        s.gReview = false;
        s.levelName = '-';

        s.avatar = '';
        s.dob = '';
        s.gender = 'Male';
        s.outletId = '';
        s.race = '';
        s.state = '';
        s.uniqueName = '';
        s.updatedAt = Date.now();
        s.levelName = '';
        s.levelOrderIndex = -1;

        s.epStateTo = '';
        s.epNameTo = '';
        s.epPhoneTo = '';
        s.epAddr1To = '';
        s.epCityTo = '';
        s.epCodeTo = '';

        s.epTinTo = '';
        s.epEmailTo = '';
        s.epIdTo = '';
        s.epIdTypeTo = '';

        s.crmUserId = '';
        s.userGroups = ['EVERYONE'];
      });

      global.crmUser = null;

      global.userPhone = '';

      /////////////////////////////////////

      await AsyncStorage.removeItem('storedUserName');
      await AsyncStorage.removeItem('storedUserPhone');

      // Increment logout count
      try {
        const storedCount = await AsyncStorage.getItem(LOGOUT_COUNT_KEY);
        const logoutCount = parseInt(storedCount, 10) || 0;
        await AsyncStorage.setItem(LOGOUT_COUNT_KEY, (logoutCount + 1).toString());
      }
      catch (error) {
        console.error('Failed to increment logout count:', error);
      }

      // Anonymous Login
      await loginUserAccountByPhone();
    }
    catch (error) {
      console.error('Error during logout process:', error);
      alert('An error occurred during logout. Please try again later.');
    }
    finally {
      setIsLoading(false);
    }
  };

  const loginUserAccountByPhone = async () => {
    setIsLoading(true);

    try {
      if (!email) {
        if (!inputPhone) {
          window.confirm("Info\n\nPlease fill in the phone field.");
          return;
        }

        let parsedPhone = inputPhone.trim();
        if (parsedPhone && !parsedPhone.startsWith('6')) {
          parsedPhone = `6${parsedPhone}`;
        }

        const isValidMYNumber = /^601[0-9]{8,9}$/.test(parsedPhone);
        if (!isValidMYNumber) {
          window.confirm("Info\n\nInvalid Malaysian phone number.");
          return;
        }

        const result = await signInWithPhoneForCRMUser(selectedOutlet, {
          nameVerification: false,
          name: '',
          phone: parsedPhone,
        });

        if (result && result.name && result.phone) {
          await AsyncStorage.setItem('storedUserName', result.name);
          await AsyncStorage.setItem('storedUserPhone', parsedPhone);

          global.storedUserName = result.name;
          global.storedUserPhone = parsedPhone;

          window.confirm('Info\n\nSigned in successfully, page will be reloaded shortly.');
        } 
        else {
          window.confirm('Info\n\nPhone number not registered as user.');
        }

        TempStore.update(s => { s.showLoginPhoneModal = false; });
        CommonStore.update(s => { s.isLoading = false; });

        setTimeout(() => window.location.reload(), 2000);
        return;
      }
      else {
        const anonResult = await signInAnonymously(global.auth);
        const firebaseUid = anonResult.user.uid;

        const resp = await ApiClient.GET(API.getTokenKWeb + firebaseUid);
        if (resp?.token) {
          await AsyncStorage.setItem("accessToken", resp.token);
          await AsyncStorage.setItem("refreshToken", resp.refreshToken);

          global.accessToken = resp.token;

          UserStore.update(s => {
            s.firebaseUid = resp.userId;
            s.userId = resp.userId;
            s.role = resp.role;
            s.refreshToken = resp.refreshToken;
            s.token = resp.token;
            s.name = "";
            s.email = "";
            s.number = "";
          });
        } 
        else {
          alert("Unauthorized access.");
        }
      }
    }
    catch (error) {
      console.error('Error during login:', error);
      alert('An error occurred. Please try again later.');
    }
    finally {
      setIsLoading(false);
    }
  };

  //////////////////////////////////////////////////////////////////////

  if (!showLoginPhoneModal) {
    return null;
  };

  return (
    <Modal
      style={{ flex: 1 }}
      visible={showLoginPhoneModal}
      transparent={true}
      animationType="slide"
    >
      <View style={{
        backgroundColor: "rgba(0,0,0,0.5)",
        justifyContent: "center",
        alignItems: "center",
        minHeight: '100%',
      }}>
        <View style={{
          padding: 20,
          paddingVertical: 40,
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          width: '85%',
          maxWidth: 400,
          gap: 20,
          borderRadius: 12,
          backgroundColor: Colors.whiteColor,
        }}>
          {/* Close button */}
          <TouchableOpacity
            style={{
              position: "absolute",
              top: 0,
              right: 0,
            }}
            onPress={() => {
              TempStore.update(s => {
                s.showLoginPhoneModal = false;
              });
            }}
          >
            <View style={{
              alignSelf: "flex-end",
              padding: 14,
            }}>
              <AntDesign name="closecircle" size={28} color={"#b0b0b0"} />
            </View>
          </TouchableOpacity>

          <Text style={{
            textAlign: "center",
            fontSize: 18,
            fontFamily: "NunitoSans-Bold",
          }}>
            {!email
              ? "Login to enjoy rewards now!"
              : "Are your sure you want to logout?"}
          </Text>

          <View style={{
            alignSelf: "center",
            justifyContent: "center",
            alignItems: "center",
            alignContent: "center",
            gap: 10,
          }}>
            {!email && (
              <>
                <View style={{
                  flexDirection: "row",
                  justifyContent: "space-between",
                  marginVertical: 20,
                }}>
                  <View style={{ justifyContent: "center" }}>
                    <Text
                      style={{
                        fontFamily: "NunitoSans-Bold",
                        color: "Black",
                        fontSize: 16,
                        textAlign: "center",
                      }}
                    >{`Phone :`}</Text>
                  </View>

                  <TextInput
                    value={inputPhone}
                    onChangeText={(text) => {
                      setInputPhone(text);
                    }}
                    style={{
                      width: "100%",
                      height: isMobile() ? 35 : windowHeight * 0.04,
                      borderRadius: 10,
                      borderColor: "#E5E5E5",
                      shadowOffset: {
                        width: 0,
                        height: 2,
                      },
                      shadowOpacity: 0.22,
                      shadowRadius: 3.22,
                      elevation: 0,
                      marginLeft: isMobile() ? 20 : 30,
                      paddingHorizontal: 10,
                    }}
                    underlineColorAndroid={Colors.fieldtBgColor}
                    clearButtonMode="while-editing"
                    placeholder="60123456789"
                    placeholderTextColor={Colors.fieldtTxtColor}
                    // secureTextEntry={true}
                    keyboardType={'phone-pad'}
                  />
                </View>

                {
                  (selectedOutlet && selectedOutlet.tickTerms)
                  && (
                    <View style={{
                      maxWidth: '90%',
                      flexDirection: "row",
                      justifyContent: 'center',
                    }}>
                      <CheckBox
                        style={{
                          marginVertical: 2,
                        }}
                        value={
                          tickboxConsent
                        }
                        onValueChange={() => {
                          console.log('tickboxConsent: ', tickboxConsent);
                          if (tickboxConsent == true) {
                            TempStore.update(s => { s.tickboxConsent = false; })
                          } else {
                            TempStore.update(s => { s.tickboxConsent = true; })
                          }
                        }}
                      />

                      <Text style={{ paddingHorizontal: 10 }}>
                        {'By logging in, you agree to MyKoodoo '}
                        <Text style={{ color: 'blue', textDecorationLine: 'underline' }}
                          onPress={() => { Linking.openURL('https://mykoodoo.com/terms/') }}>{'Terms of Use'}</Text>
                        {' & '}
                        <Text style={{ color: 'blue', textDecorationLine: 'underline' }}
                          onPress={() => { Linking.openURL('https://mykoodoo.com/privacy-policy/') }}>{'Privacy Policy'}</Text>
                      </Text>
                    </View>
                  )}
              </>
            )}

            <TouchableOpacity
              disabled={isLoading}
              style={{
                backgroundColor: Colors.primaryColor,
                padding: 20,
                paddingVertical: 12,
                borderRadius: 10,
                alignItems: "center",
                shadowColor: "#000",
                shadowOffset: {
                  width: 0,
                  height: 1,
                },
                shadowOpacity: 0.22,
                shadowRadius: 2.22,
                elevation: 3,
                ...(!isMobile() && {
                  width: "100%",
                  alignSelf: "center",
                }),
              }}
              onPress={() => {
                try {
                  if (email) {
                    handleLogout();
                    TempStore.update((s) => {
                      s.showLoginPhoneModal = false;
                    });
                  }
                  else if (tickboxConsent !== true) {
                    Toastify({
                      text: 'Please agree to MyKoodoo Terms of Use and Privacy Policy before continuing.',
                      duration: 3000,
                      newWindow: true,
                      close: false,
                      gravity: "top", // top or bottom
                      position: "center", // left, center or right
                      stopOnFocus: true, // Prevents dismissing of toast on hover
                      style: {
                        background: "linear-gradient(to right, #4E9F7D, #75bd9f)",
                        color: 'white',

                        // marginLeft: '15px !important',
                        // marginRight: '15px !important',
                      },
                      onClick: function () { } // Callback after click
                    }).showToast();
                  }
                  else {
                    loginUserAccountByPhone();
                  }
                }
                catch (error) {
                  console.error('Error during login/logout process:', error);
                }
                finally {
                }
              }}
            >
              <Text style={{
                color: "#ffffff",
                fontSize: 16,
                fontFamily: "NunitoSans-SemiBold",
              }}>
                {isLoading ? "LOADING..." : email ? "LOGOUT" : "LOGIN"}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
});

export default LoginPhoneModal;