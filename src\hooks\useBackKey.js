import React from 'react';

import BackHandlerWrapper from './backHandlerWrapper';

/** This helper will call onBackPress when we press the hardware back button */
const useBackKey = (onBackPress) => {
  React.useEffect(() => {
    if (!onBackPress) return
    const onBack = () => {
      onBackPress()
      return true
    }
    BackHandlerWrapper.addEventListener('hardwareBackPress', onBack)
    return () => BackHandlerWrapper.removeEventListener('hardwareBackPress', onBack)
  }, [onBackPress])
}

export default useBackKey;