const path = require('path');
const webpack = require('webpack');
const HtmlWebpackPlugin = require('html-webpack-plugin');

const appDirectory = path.resolve(__dirname, '/');

// This is needed for webpack to compile JavaScript.
// Many OSS React Native packages are not compiled to ES5 before being
// published. If you depend on uncompiled packages they may cause webpack build
// errors. To fix this webpack can be configured to compile to the necessary
// `node_module`.
const babelLoaderConfiguration = {
  test: /\.js$/,
  // Add every directory that needs to be compiled by Babel during the build.
  include: [
    path.resolve(__dirname, 'src/index.js'),
    path.resolve(__dirname, 'src'),
    path.resolve(__dirname, 'node_modules/react-native-uncompiled'),
    path.resolve(__dirname, 'node_modules/react-native-vector-icons'),
    path.resolve(__dirname, 'node_modules/react-native-draggable'),
    path.resolve(__dirname, 'node_modules/react-native-awesome-alerts'),
  ],
  use: {
    loader: 'babel-loader',
    options: {
      cacheDirectory: true,
      // The 'metro-react-native-babel-preset' preset is recommended to match React Native's packager

      // presets: [
      //   // 'module:metro-react-native-babel-preset',
      //   // [
      //   //   "@babel/preset-env",
      //   //   {
      //   //     modules: false,
      //   //     corejs: 3,
      //   //     useBuiltIns: "usage",
      //   //     include: [
      //   //       "@babel/plugin-proposal-optional-chaining",
      //   //     ],
      //   //     targets: "last 2 Chrome versions, last 2 Firefox versions",
      //   //   },
      //   // ],
      // ],

      presets: [['module:metro-react-native-babel-preset', {
        useTransformReactJSXExperimental: true,
      }]],

      // presets: presets,

      // Re-write paths to import only the modules needed by the app
      plugins: [
        'react-native-web',
      ]
    }
  }
};

// This is needed for webpack to import static images in JavaScript files.
const imageLoaderConfiguration = {
  test: /\.(gif|jpe?g|png|svg)$/,
  use: {
    loader: 'url-loader',
    options: {
      name: '[name].[ext]',
      esModule: false,
    }
  }
};

const cssLoaderConfiguration = {
  test: /\.css$/i,
  use: ["style-loader", "css-loader"],
};

// const ttfLoaderConfiguration = {
//   test: /\.ttf$/,
//   use: ["style-loader", "ttf-loader"],
// };

module.exports = {
  target: 'node',
  node: {
    __dirname: true,
  },

  entry: [
    // load any web API polyfills
    // path.resolve(appDirectory, 'polyfills-web.js'),
    // your web-specific entry file
    path.resolve(__dirname, 'src/index.js')
  ],

  // configures where the build ends up
  output: {
    // filename: 'bundle.js',

    // path: path.resolve(__dirname, 'dist'),
    // // publicPath: path.join(__dirname, 'public-webpack'),
    // publicPath: 'dist',

    // path: path.resolve(__dirname, 'dist'),
    // // publicPath: path.join(__dirname, 'public-webpack'),
    // // publicPath: 'dist',
    // publicPath: process.env.PUBLIC_URL,

    publicPath: process.env.PUBLIC_PATH,
    filename: '[name].js',
    sourceMapFilename: '[id].[contenthash].lib.map',
    chunkFilename: '[id].[contenthash].lib.js',
    path: path.resolve(__dirname, 'dist/'),
  },

  // ...the rest of your config

  devtool: process.env.NODE_ENV === 'PROD' ? false : 'source-map',
  mode: process.env.NODE_ENV === 'PROD' ? 'production' : 'development',
  watch: process.env.NODE_ENV === 'PROD' ? false : true,

  devServer: {
    static: [
      {
        directory: path.join(__dirname, 'public-webpack'),
        publicPath: process.env.PUBLIC_URL,
      },
      // {
      //   directory: path.join(__dirname, 'dist'),
      //   publicPath: 'dist',
      // },
    ],
  },

  module: {
    rules: [
      babelLoaderConfiguration,
      imageLoaderConfiguration,
      cssLoaderConfiguration,

      {
        test: /\.(woff|woff2|eot|ttf|otf)$/i,
        type: 'asset/resource',
      },
    ],
  },

  plugins: [
    new webpack.DefinePlugin({
      // https://webpack.js.org/guides/public-path/#root
      'process.env.PUBLIC_PATH': JSON.stringify(process.env.PUBLIC_PATH),
    }),
  ],

  // plugins: [
  //   new HtmlWebpackPlugin({
  //     inject: true,
  //     // template: path.resolve(__dirname, 'public-webpack/index.html'),
  //     template: 'public-webpack/index.html',
  //     publicPath: 'public-webpack',
  //     filename: 'index.html',
  //     // favicon: path.resolve(__dirname, 'public-webpack/favicon.ico'),
  //     favicon: 'public-webpack/favicon.ico',
  //     // manifest: path.resolve(__dirname, 'public-webpack/manifest.json'),
  //     manifest: 'public-webpack/manifest.json',
  //     // logo192: path.resolve(__dirname, 'public-webpack/logo192.png'),
  //     logo192: 'public-webpack/logo192.png',
  //     // logo512: path.resolve(__dirname, 'public-webpack/logo512.png'),
  //     logo512: 'public-webpack/logo512.png',

  //     chunks: 'all',
  //   }),
  // ],

  resolve: {
    // This will only alias the exact import "react-native"
    alias: {
      'react-native$': 'react-native-web'
    },
    // If you're working on a multi-platform React Native app, web-specific
    // module implementations should be written in files using the extension
    // `.web.js`.
    extensions: ['.web.js', '.js']
  }
}