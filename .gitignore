# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# production
/build

# misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local

npm-debug.log*
yarn-debug.log*
yarn-error.log*

src/config/firebase-web.json
src/config/firebase-web.prod.json
src/constant/env.js
src/constant/prod.env.js
/test-results/
/playwright-report/
/playwright/.cache/

.env
.prod.env
.prod-build.env
.uat.env
.uat-build.env

/debug