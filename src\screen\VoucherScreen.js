import React, { Component, useReducer, useState, useEffect, useRef, useCallback } from 'react';
import {
    StyleSheet,
    ScrollView,
    Image,
    View,
    Text,
    TouchableOpacity,
    TextInput,
    FlatList,
    RefreshControl,
    Alert,
    Modal,
    Dimensions,
    Platform,
    ActivityIndicator,
    useWindowDimensions,
} from 'react-native';
import Colors from '../constant/Colors';
import Styles from '../constant/Styles';
import ApiClient from '../util/ApiClient';
import API from '../constant/API';
import Ionicons from "react-native-vector-icons/Ionicons";
import Icon from 'react-native-vector-icons/Ionicons';
import Icons from 'react-native-vector-icons/Feather';
//import QRCode from 'react-native-qrcode-svg';
import Close from "react-native-vector-icons/AntDesign";
import { CommonStore } from '../store/commonStore';
import { UserStore } from '../store/userStore';
import moment from 'moment';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
/////////////////////// Test for search bar ///////////////////////
import MaterialIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import { MERCHANT_VOUCHER_CODE_FORMAT } from '../constant/common';
import { Collections } from '../constant/firebase';
//import firestore from '@react-native-firebase/firestore';
import { useScrollToTop } from '@react-navigation/native';
import { removeOrdinalFromDate, getOrdinalFromDate, subText } from '../util/commonFuncs';
// import firebase from 'firebase';
import { getFirestore, collection, query, where, getDocs, limit } from "firebase/firestore";
import { signInAnonymously } from "firebase/auth";
import { prefix } from "../constant/env";
import { useFocusEffect, useLinkTo } from "@react-navigation/native";

/**
 * VoucherScreen
 * function
 * *display list of available vouchers
 * *redeem voucher
 */

const VoucherScreen = props => {
    const {
        navigation,
        route,
    } = props;
    const { width: windowWidth, height: windowHeight } = useWindowDimensions();
    const linkTo = useLinkTo();
    var pageFromParam = null;
    if (route && route.params) {
        pageFromParam = route.params.pageFrom;
    }
    const [voucher, setVoucher] = useState([]);
    const [qrCodeModalVisibility, setQrCodeModalVisibility] = useState(false);
    const [selectedItem, setSelectedItem] = useState({});
    const [pageFrom, setPageFrom] = useState(pageFromParam);

    const [merchantVoucherLoadingDict, setMerchantVoucherLoadingDict] = useState({});

    const setState = () => { };

    const selectedOutlet = CommonStore.useState(s => s.selectedOutlet);
    ;
    const isLoading = CommonStore.useState(s => s.isLoading);

    const availableTaggableVouchers = CommonStore.useState(s => s.availableTaggableVouchers);

    const selectedOutletTaggableVouchers = CommonStore.useState(
        (s) => s.selectedOutletTaggableVouchers,
    );

    const firebaseUid = UserStore.useState(s => s.firebaseUid);

    const [searchText, setSearchText] = useState('');
    const [searchList, setSearchList] = useState(false);
    const [list, setList] = useState(true);

    const ref = React.useRef(null);

    useScrollToTop(ref);

    ////////////////////////////////////////////////////////////

    // 2022-08-02 - Buy vouchers online

    // useEffect(() => {
    //     CommonStore.update(s => {
    //         s.availableTaggableVouchers = selectedOutletTaggableVouchers;
    //     });
    // }, [selectedOutletTaggableVouchers]);

    ////////////////////////////////////////////////////////////

    navigation.setOptions({
        headerLeft: () => (
            <TouchableOpacity
                style={{
                    opacity: 1,
                }}
                onPress={() => {
                    props.navigation.goBack();
                }}
            >
                <View
                    style={{
                        marginLeft: 10,
                        display: "flex",
                        flexDirection: "row",
                        alignItems: "center",
                        justifyContent: "flex-start",
                    }}
                >
                    <Ionicons
                        name="chevron-back"
                        size={26}
                        color={Colors.fieldtTxtColor}
                        style={{}}
                    />

                    <Text
                        style={{
                            color: Colors.fieldtTxtColor,
                            fontSize: 16,
                            textAlign: "center",
                            fontFamily: "NunitoSans-Regular",
                            lineHeight: 22,
                            marginTop: -1,
                        }}
                    >
                        Back
                    </Text>
                </View>
            </TouchableOpacity>
        ),
        headerRight: () => (
            <TouchableOpacity
                onPress={() => {
                    linkTo && linkTo(`${prefix}/outlet/voucher-history`);
                }}

                style={{}}>
                <View style={{ marginRight: 25 }}>
                    <Text
                        style={{
                            fontSize: 20,
                            lineHeight: 25,
                            textAlign: "center",
                            fontFamily: "NunitoSans-Bold",
                            color: Colors.mainTxtColor,
                        }}
                    >
                        History
                    </Text>
                </View>
            </TouchableOpacity>
        ),
        headerTitle: () => (
            <View
                style={{
                    justifyContent: "center",
                    alignItems: "center",
                    bottom: -1,
                }}
            >
                <Text
                    style={{
                        fontSize: 20,
                        lineHeight: 25,
                        textAlign: "center",
                        fontFamily: "NunitoSans-Bold",
                        color: Colors.mainTxtColor,
                    }}
                >
                    Voucher
                </Text>
            </View>
        ),
    });

    const redeemVoucher = async item => {
        if (item.quantity > 0) {
            CommonStore.update(s => {
                s.isLoading = true;
            });

            setMerchantVoucherLoadingDict({
                ...merchantVoucherLoadingDict,
                [item.uniqueId]: true,
            });

            const body = {
                userId: firebaseUid,
                voucherId: item.uniqueId,
            };

            ApiClient.POST(API.redeemMerchantVoucher, body).then(async (result) => {
                if (result && result.status === 'success') {
                    // get the latest one without delay

                    // const merchantVoucherSnapshot = await firebase.firestore().collection(Collections.MerchantVoucher)
                    //     .where('uniqueId', '==', item.uniqueId)
                    //     .limit(1)
                    //     .get();

                    const merchantVoucherSnapshot = await getDocs(
                        query(
                            collection(global.db, Collections.MerchantVoucher),
                            where('uniqueId', '==', item.uniqueId),
                            limit(1),
                        )
                    );

                    if (!merchantVoucherSnapshot.empty) {
                        const record = merchantVoucherSnapshot.docs[0].data();

                        CommonStore.update(s => {
                            s.selectedVoucher = record;
                            s.isLoading = false;
                        });

                        setMerchantVoucherLoadingDict({
                            ...merchantVoucherLoadingDict,
                            [item.uniqueId]: false,
                        });

                        props.navigation.navigate("VoucherDetails", { merchantVouchersDict: record });
                    }
                }
                else {
                    Alert.alert('Error', 'Sorry, please try again later.');

                    CommonStore.update(s => {
                        s.isLoading = false;
                    });
                }
            });
        }
        else {
            CommonStore.update(s => {
                s.selectedVoucher = item;
            });

            props.navigation.navigate("VoucherDetails", { merchantVouchersDict: item });
        }
    };

    var voucherDetailsFontSize = Platform.OS == 'ios' ? 13 : 15;

    if (Dimensions.get('screen').width <= 360) {
        voucherDetailsFontSize = Platform.OS == 'ios' ? 8 : 10;
    }

    const fontSizeCondition = {
        fontSize: voucherDetailsFontSize,
    };

    var tAndcFont = Platform.OS == 'ios' ? 11 : 12;

    if (Dimensions.get('screen').width <= 360) {
        tAndcFont = Platform.OS == 'ios' ? 8.5 : 9.5;
    }

    const tAndcFontCondition = {
        fontSize: tAndcFont,
    };

    if (Dimensions.get('screen').width <= 360) {
        var redeemStyleWidth = 80;
    }

    if (Dimensions.get('screen').width <= 360) {
        var redeemStyleHeight = 40;
    }




    // function end
    const renderItem = (param, index) => {

        const item = param.item;

        var isValid = false;


        if (pageFrom === 'CartScreen') {
            if (selectedOutlet && selectedOutlet.merchantId === item.merchantId) {
                isValid = true;
            }
        }
        else {
            isValid = true;
        }

        if (item && isValid) {
            return (
                <TouchableOpacity
                    disabled={isLoading}
                    onPress={() => {
                        redeemVoucher(item);
                    }}>
                    <View style={[styles.card]}>
                        <View style={styles.cornerleft}></View>
                        <View style={{ marginRight: 10, flex: 2 }}>
                            <View style={styles.pic}>
                                {!(item.customLogo || item.merchantLogo) ?
                                    <Image
                                        source={require('../asset/image/extend.png')}
                                        style={{ width: 90, height: Platform.OS == 'ios' ? 90 : 90, borderRadius: 10 }}
                                    />
                                    : null}
                                {item.customLogo ?
                                    <Image
                                        source={{ uri: item.customLogo }}
                                        style={{ width: 90, height: Platform.OS == 'ios' ? 90 : 90, borderRadius: 10 }}
                                    />
                                    : null}
                                {item.merchantLogo ?
                                    <Image
                                        source={{ uri: item.merchantLogo }}
                                        style={{ width: 90, height: Platform.OS == 'ios' ? 90 : 90, borderRadius: 10 }}
                                    />
                                    : null}
                            </View>
                        </View>
                        <View
                            style={{
                                flex: 3,
                                alignContent: 'center',
                                alignItems: 'center',
                                justifyContent: 'center',
                            }}>
                            <View style={{
                                // alignSelf: 'flex-end',
                                marginRight: '12%',
                                // backgroundColor: 'red',
                                // paddingLeft: 4,
                            }}>
                                <Text
                                    numberOfLines={2}
                                    style={[styles.title, Platform.OS === 'ios' ? {
                                        fontSize: 22,
                                        marginRight: '-15%',
                                    } : {
                                        // backgroundColor: 'red',
                                        paddingLeft: 8,
                                        marginLeft: '0%',
                                        fontSize: 20,
                                    }]}>
                                    {/* {item.GiftCard == null ? null : (item.GiftCard.merchantId !== 'legacy' ? item.GiftCard.title : item.GiftCard.description)} */}
                                    {item.campaignName}
                                </Text>
                            </View>

                            <View style={{
                                alignSelf: 'flex-end',
                                marginRight: Platform.OS == 'ios' ? '5%' : ' 12%',
                                // marginTop: -5,
                            }}>
                                <Text
                                    style={{
                                        color: Colors.whiteColor,
                                        fontSize: Platform.OS == 'ios' ? 15 : 15,
                                        fontFamily: 'NunitoSans-Regular',
                                    }}>
                                    {item.merchantName}
                                </Text>
                            </View>
                        </View>
                        <View style={styles.cornerleft1}></View>
                    </View>
                    <View style={[styles.card1, {
                        // backgroundColor: 'red',
                    }]}>
                        <View style={styles.cornerright}></View>

                        <View
                            style={{
                                marginRight: 10,
                                flex: 4,
                                alignItems: 'flex-start',
                                justifyContent: 'center',

                                marginLeft: 10,
                                marginTop: -10,
                            }}>
                            <Text style={styles.title1}>Valid Period</Text>
                            <Text style={[styles.title2, fontSizeCondition]}>
                                {moment(item.promoDateStart).format('Do MMMM')} to {moment(item.promoDateEnd).format('Do MMMM')}
                            </Text>
                            <TouchableOpacity onPress={() => { }}>
                                <Text style={[styles.title3, tAndcFontCondition]}>Terms & Conditions Applied</Text>
                            </TouchableOpacity>
                        </View>

                        <View style={styles.cornerright1}></View>

                        <View style={{
                            position: 'absolute',
                            // backgroundColor: 'red',
                            bottom: '2%',
                            left: '1%',
                            width: '98%',
                            height: '80%',

                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center',
                        }}>
                            <Image
                                source={require('../asset/image/voucher-border.png')}
                                resizeMode={'stretch'}
                                style={{
                                    width: '100%',
                                    height: '100%',
                                }}
                            />
                        </View>
                    </View>
                </TouchableOpacity>
            );
        }
    }

    const renderModal = () => {
        return (
            <Modal
                style={{ flex: 1 }}
                visible={qrCodeModalVisibility}
                transparent={true}
                animationType="slide">
                <View
                    style={{
                        // backgroundColor: 'rgba(0,0,0,0.5)',
                        backgroundColor: Colors.primaryColor,
                        flex: 1,
                        justifyContent: 'center',
                        alignItems: 'center',
                    }}>
                    <View style={styles.confirmBox}>
                        <TouchableOpacity
                            style={{
                                // right: '-15%',
                                right: '-20%',
                                top: '-90%',
                            }}
                            onPress={() => {
                                // setState({ qrCodeModalVisibility: false });
                                setQrCodeModalVisibility(false);
                            }}>
                            <View
                                style={{
                                    alignSelf: 'flex-end',
                                    padding: 13,
                                }}>
                                <Close name="closecircle" size={28} />
                            </View>
                        </TouchableOpacity>

                        {selectedItem &&
                            <View style={{
                                top: '-30%',
                            }}>
                                {/* <QRCode
                  value={`https://api-dev.mykoodoo.com/sa/redeemGiftCard?userGiftCardId=${selectedItem.id}`}
                  size={Styles.width * 0.55}
                  logoBackgroundColor='transparent'
                /> */}
                            </View>
                        }

                        <View style={{
                            position: 'absolute',
                            right: 0,
                            left: 0,
                            bottom: '-15%',
                            // width: Dimensions.get('screen').width * 0.9,
                        }}>
                            <Text style={[styles.title, {
                                textAlign: 'center',
                                fontSize: 20,
                                // width: Dimensions.get('screen').width * 0.9,
                            }]}>Show this QR code to counter to redeem your item.</Text>
                        </View>
                    </View>
                </View>
            </Modal>
        );
    }

    const onViewableVouchersChanged = useCallback(({ viewableItems, changed }) => {
        console.log('viewable');
        console.log(viewableItems);
        console.log(changed);

        for (var i = 0; i < changed.length; i++) {
            if (changed[i].isViewable) {
                var viewedVoucherId = changed[i].item;

                const body = {
                    userId: firebaseUid,
                    voucherId: viewedVoucherId,
                };

                ApiClient.POST(API.viewMerchantVoucher, body).then(async (result) => {
                    // do nothing 
                });
            }
        }
    }, []);

    // function end

    return (
        <>

            <View style={{
                flexDirection: 'row',
                alignItems: 'center',
                marginTop: 10,
                marginBottom: 10,
                marginLeft: 5,
                paddingLeft: 10,
            }}>
                <Icon name="search" size={20} color={Colors.primaryColor} style={{
                    position: 'absolute',
                    left: 21,
                    zIndex: 10,
                }} />

                <TextInput
                    underlineColorAndroid={Colors.fieldtBgColor}
                    clearButtonMode='while-editing'
                    placeholder="search voucher"
                    style={styles.textInput}
                    onChangeText={text => {
                        setSearchText(text);
                    }}
                    value={searchText}
                />
            </View>


            {/*************************** Finished for search bar ***************************/}

            <View style={{
                display: 'flex',
                alignItems: 'center',
                flexDirection: 'row',
                marginLeft: 5,
            }}>

                {renderModal()}

                {(!availableTaggableVouchers || availableTaggableVouchers.length === 0) &&
                    <View style={styles.centerTextHolder}>
                        <Text
                            style={{
                                color: Colors.descriptionColor,
                                textAlign: "center",
                            }}
                        >
                            You have no vouchers now
                        </Text>
                    </View>
                }


                <View style={{ flex: 1 }}>
                    <View style={styles.container1}>
                        <View style={styles.container}>
                            <ScrollView
                                ref={ref}
                            >
                                <FlatList
                                    style={{ marginBottom: 10 }}
                                    data={availableTaggableVouchers}
                                    //extraData={voucherIdValidList}
                                    renderItem={renderItem}
                                    keyExtractor={(param, index) => String(index)}
                                    onViewableItemsChanged={onViewableVouchersChanged}
                                    viewabilityConfig={{
                                        minimumViewTime: 1000,
                                        viewAreaCoveragePercentThreshold: 50,
                                        // waitForInteraction: true,
                                    }}
                                />

                                <View style={{ height: 180 }}></View>
                            </ScrollView>
                        </View>
                    </View>
                </View>
            </View>
        </>
    );
}


const styles = StyleSheet.create({
    container1: {
        backgroundColor: '#ffffff',
        // backgroundColor: 'red',
        height: Dimensions.get('window').height,
    },
    container: {
        backgroundColor: '#ffffff',
        padding: 16,
    },
    card: {
        flex: 1,
        minWidth: Styles.width - 64,
        minHeight: 100,
        // backgroundColor: Colors.primaryColor,
        backgroundColor: '#416D5C',
        flexDirection: 'row',
        marginTop: 20,
        alignContent: 'center',
        alignItems: 'center',
        borderTopLeftRadius: 20,
        borderTopRightRadius: 20,
    },
    card1: {
        flex: 1,
        minWidth: Styles.width - 64,
        minHeight: 100,
        backgroundColor: Colors.fieldtBgColor,
        flexDirection: 'row',
        marginBottom: 20,
        alignContent: 'center',
        alignItems: 'center',
        borderBottomLeftRadius: 20,
        borderBottomRightRadius: 20,
        marginBottom: 10,
    },
    cornerleft: {
        backgroundColor: 'white',
        height: 30,
        width: 30,
        alignSelf: 'flex-start',
        marginTop: '22%',
        borderTopRightRadius: 50,
    },
    cornerright: {
        backgroundColor: 'white',
        height: 30,
        width: 30,
        alignSelf: 'flex-start',
        borderBottomRightRadius: 50,

        // borderStyle: 'dashed',
        // borderWidth: 1,
        // borderColor: 'black',
    },

    cornerleft1: {
        backgroundColor: 'white',
        height: 30,
        width: 30,
        alignSelf: 'flex-start',
        marginTop: '22%',
        borderTopLeftRadius: 50,
    },
    cornerright1: {
        backgroundColor: 'white',
        height: 30,
        width: 30,
        alignSelf: 'flex-start',
        borderBottomLeftRadius: 50,
    },
    text: {
        fontSize: 13,
        color: Colors.fieldtTxtColor,
        fontFamily: 'NunitoSans-Bold'
    },
    title: {
        color: Colors.whiteColor,
        // fontSize: Platform.OS == 'ios' ? 30 : 20,
        fontSize: Platform.OS == 'ios' ? 30 : 22,
        fontFamily: 'NunitoSans-SemiBold',
        marginLeft: '1%',
        // marginTop: 10,
        marginBottom: 5,
        textAlign: 'right',
    },
    title1: {
        color: Colors.blackColor,
        fontSize: 16,
        fontFamily: 'NunitoSans-Bold'
    },
    title2: {
        color: Colors.primaryColor,
        fontFamily: 'NunitoSans-SemiBold',
        // fontWeight: 'bold',
        marginBottom: 8,
    },
    title3: {
        color: Colors.descriptionColor,
        fontFamily: 'NunitoSans-SemiBold',
        // fontWeight: 'bold',
    },
    pic: {
        backgroundColor: Colors.secondaryColor,
        width: 90,
        height: 90,
        borderRadius: 10,
        alignSelf: 'center',
    },
    centerTextHolder: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        justifyContent: 'center',
        alignItems: 'center',
    },

    textInput: {
        height: 45,
        paddingHorizontal: 50,
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 10,

        color: Colors.fieldtTxtColor,
        fontFamily: "NunitoSans-Regular",
        fontSize: 14,
        lineHeight: 20,

        // backgroundColor: 'blue',
        width: '95%',
    },
});

export default VoucherScreen;
