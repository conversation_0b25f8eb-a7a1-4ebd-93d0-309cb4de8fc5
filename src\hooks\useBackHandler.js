const listeners = [];
const addEventListener = (event, listener) => {
    listeners.unshift(listener);
    return { remove: () => removeEventListener(event, listener) };
};

const removeEventListener = (event, listener) => {
    listeners.splice(listeners.indexOf(listener), 1);
};

const push = window.history.pushState;
let count = 0;
window.history.pushState = (date, title, url) => {
    push(date, title, url);
    count++;
};

const back = window.history.back;
window.history.back = () => {
    const handled = listeners.find((l) => l());
    if (!handled) {
        back();
        count--;
    }
};

// Hack for Chrome. See this issue: https://stackoverflow.com/questions/64001278/history-pushstate-on-chrome-is-being-ignored?noredirect=1#comment113174647_64001278
const onFirstPress = () => {
    if (count === 0) window.history.pushState(null, "", window.location.href);
};
window.addEventListener("focusin", onFirstPress);

const exitApp = () => {
    listeners.length = 0;
    window.history.previous();
};

export default {
    addEventListener,
    exitApp,
    removeEventListener
};