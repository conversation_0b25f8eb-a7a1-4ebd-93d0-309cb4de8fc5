import React, { Component, useReducer, useState, useEffect } from 'react';
import {
    StyleSheet,
    ScrollView,
    Image,
    View,
    Text,
    TouchableOpacity,
    TextInput,
    FlatList,
    RefreshControl,
    Alert,
    Modal,
    Dimensions,
    Platform,
    useWindowDimensions
} from 'react-native';
// import firebase from 'firebase';
import { getFirestore, collection, query, where, getDocs, limit } from "firebase/firestore";
import { signInAnonymously } from "firebase/auth";
import Colors from '../constant/Colors';
// import Autocomplete from 'react-google-autocomplete'
import AntDesign from 'react-native-vector-icons/AntDesign';
import DatePicker from "react-datepicker";
import { ReactComponent as GCalendar } from '../svg/GCalendar.svg'
import "react-datepicker/dist/react-datepicker.css";
import "../constant/datePicker.css";
import * as Cart from '../util/Cart';
import * as User from '../util/User';
import Styles from '../constant/Styles';
// import ActionSheet from 'react-native-actionsheet';
import ApiClient from '../util/ApiClient';
import API from '../constant/API';
import Ionicons from 'react-native-vector-icons/Ionicons';
import Icon from 'react-native-vector-icons/Ionicons';
import Icons from 'react-native-vector-icons/Feather';
// import NumericInput from 'react-native-numeric-input';
// import { color } from 'react-native-reanimated';
// import QRCode from 'react-native-qrcode-svg';
import Close from 'react-native-vector-icons/AntDesign';
import { CommonStore } from '../store/commonStore';
import { UserStore } from '../store/userStore';
import moment from 'moment';
import AsyncImage from '../components/asyncImage';
// import LinearGradient from 'react-native-linear-gradient';
import LinearGradient from 'react-native-web-linear-gradient';
import { googleCloudApiKey, prefix } from "../constant/env";
import { useFocusEffect, useLinkTo } from "@react-navigation/native";
import { DataStore } from '../store/dataStore';
import Hashids from 'hashids';
import { ORDER_REGISTER_QR_SALT } from '../constant/common';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Collections } from '../constant/firebase';
import IconAnt from 'react-native-vector-icons/AntDesign';
import { isMobile, setRouteFrom, mondayFirst } from "../util/commonFuncs";
import imgLogo from '../asset/image/logo.png';

import { ReactComponent as Arrow } from '../svg/arrow.svg';
import { Switch } from 'react-native-web';
import { LOYALTY_CAMPAIGN_CREDIT_TRANSACTION_TYPE_PARSED } from '../constant/loyalty';

const hashids = new Hashids(ORDER_REGISTER_QR_SALT);

const OutletRewardScreen = (props) => {
    const { navigation, route } = props;
    const { height: windowHeight, width: windowWidth } = useWindowDimensions();

    const linkTo = useLinkTo();

    const [blockSMS, setBlockSMS] = useState(false);
    const [blockEmail, setBlockEmail] = useState(false);

    const loyaltyHistoryCrmUser = CommonStore.useState(s => s.loyaltyHistoryCrmUser);
    const loyaltyHistoryLCCTransactions = CommonStore.useState(s => s.loyaltyHistoryLCCTransactions);
    const loyaltyHistoryLCCBalance = CommonStore.useState(s => s.loyaltyHistoryLCCBalance);
    const email = UserStore.useState((s) => s.email);
    const userNumber = UserStore.useState((s) => s.number);
    const name = UserStore.useState((s) => s.name);
    const userPointsTransactions = UserStore.useState(s => s.userPointsTransactions)
    const userPointsBalance = UserStore.useState(s => s.userPointsBalance);

    const selectedOutlet = CommonStore.useState((s) => s.selectedOutlet);

    const availableTaggableVouchers = CommonStore.useState((s) => s.availableTaggableVouchers,);
    const availableUserTaggableVouchers = CommonStore.useState((s) => s.availableUserTaggableVouchers,);
    const [searchText, setSearchText] = useState('');
    const REWARD_SECTION = {
        POINT: 'POINT',
        CREDIT: 'CREDIT',
        STAMP: 'STAMP',
    };
    const [rewardSection, setRewardSection] = useState(REWARD_SECTION.CREDIT);
    const [stampsModal, setStampsModal] = useState(false);
    const [pointsSelected, setPointsSelected] = useState(10);

    const isLoading = CommonStore.useState(s => s.isLoading);

    navigation.setOptions({
        headerLeft: () => (
            <TouchableOpacity
                style={{}}
                onPress={() => {
                    props.navigation.goBack();
                    // link&&linkTo(``);
                }}>
                <View
                    style={{
                        marginLeft: 10,
                        display: 'flex',
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'flex-start',
                    }}>
                    <Ionicons
                        name="chevron-back"
                        size={26}
                        color={Colors.fieldtTxtColor}
                        style={{}}
                    />

                    <Text
                        style={{
                            color: Colors.fieldtTxtColor,
                            fontSize: 16,
                            textAlign: 'center',
                            fontFamily: 'NunitoSans-Regular',
                            lineHeight: 22,
                            marginTop: -1,
                        }}>
                        Back
                    </Text>
                </View>
            </TouchableOpacity>
        ),
        headerRight: () => (
            <TouchableOpacity
                onPress={() => {
                    // props.navigation.navigate('Profile');
                }}
                style={{}}>
                <View style={{ marginRight: 15 }}>
                    {/* <Ionicons name="menu" size={30} color={Colors.primaryColor} /> */}
                </View>
            </TouchableOpacity>
        ),
        headerTitle: () => (
            <View
                style={{
                    justifyContent: 'center',
                    alignItems: 'center',
                    bottom: -1,
                }}>
                <Text
                    style={{
                        fontSize: 20,
                        lineHeight: 25,
                        textAlign: 'center',
                        fontFamily: 'NunitoSans-Bold',
                        color: Colors.mainTxtColor,
                    }}>
                    Outlet Rewards
                </Text>
            </View>
        ),
    });

    const [outletId, setOutletId] = useState('');
    const [outletName, setOutletName] = useState('');
    const [outletCover, setOutletCover] = useState('');
    const [merchantId, setMerchantId] = useState('');
    const [merchantName, setMerchantName] = useState('');


    useEffect(() => {
        if (linkTo) {
            DataStore.update(s => {
                s.linkToFunc = linkTo;
            });
        }

        console.log('route');
        console.log(route);
        console.log('window.location.href');
        console.log(window.location.href);

        if (route.params === undefined ||
            // route.params.outletId === undefined ||
            // route.params.orderId === undefined ||
            // route.params.tableCode === undefined ||
            // route.params.tablePax === undefined ||
            // route.params.waiterId === undefined ||
            // route.params.outletId.length !== 36 ||
            // route.params.tableId.length !== 36 ||
            // route.params.qrDateTimeEncrypted === undefined
            route.params.userIdHuman == undefined
        ) {
            // still valid, can proceed to register user

            // linkTo && linkTo(`${prefix}/error`);

            console.log('general');
        }
        else {
            // firebase.auth().signInAnonymously()
            signInAnonymously(global.auth)
                .then((result) => {
                    const firebaseUid = result.user.uid;

                    ApiClient.GET(API.getTokenKWeb + firebaseUid).then(async (result) => {
                        console.log('getTokenKWeb');
                        console.log(result);

                        if (result && result.token) {
                            await AsyncStorage.setItem('accessToken', result.token);
                            await AsyncStorage.setItem('refreshToken', result.refreshToken);

                            global.accessToken = result.token;

                            UserStore.update(s => {
                                s.firebaseUid = result.userId;
                                s.userId = result.userId;
                                s.role = result.role;
                                s.refreshToken = result.refreshToken;
                                s.token = result.token;
                                s.name = '';
                                s.email = '';
                                s.number = '';
                            });

                            // CommonStore.update(s => {
                            //   s.selectedOutletTableQRUrl = window.location.href;
                            // });

                            // const crmUserSnapshot = await firebase.firestore().collection(Collections.CRMUser)
                            //   .where('userIdHuman', '==', route.params.userIdHuman)
                            //   .limit(1)
                            //   .get();

                            const crmUserSnapshot = await getDocs(
                                query(
                                    collection(global.db, Collections.CRMUser),
                                    where('userIdHuman', '==', route.params.userIdHuman),
                                    limit(1),
                                )
                            );

                            var crmUser = null;

                            if (!crmUserSnapshot.empty) {
                                crmUser = crmUserSnapshot.docs[0].data();
                            }

                            if (crmUser) {
                                await AsyncStorage.setItem('loyaltyHistoryCrmUserIdHuman', route.params.userIdHuman);

                                setBlockSMS(crmUser.blockSMS || false);
                                setBlockEmail(crmUser.blockEmail || false);

                                // const outletSnapshot = await firebase.firestore().collection(Collections.Outlet)
                                //   .where('uniqueId', '==', crmUser.outletId)
                                //   .limit(1)
                                //   .get();

                                const outletSnapshot = await getDocs(
                                    query(
                                        collection(global.db, Collections.Outlet),
                                        where('uniqueId', '==', crmUser.outletId),
                                        limit(1),
                                    )
                                );

                                var outlet = {};
                                if (!outletSnapshot.empty) {
                                    outlet = outletSnapshot.docs[0].data();
                                }

                                if (outlet) {
                                    // valid, can proceed to register user

                                    setOutletId(outlet.uniqueId);
                                    setOutletName(outlet.name);
                                    setOutletCover(outlet.cover);
                                    setMerchantId(outlet.merchantId);
                                    setMerchantName(crmUser.merchantName);

                                    CommonStore.update(s => {
                                        s.loyaltyHistoryCrmUser = crmUser;
                                    });

                                    //////////////////////////////////////////

                                    // const loyaltyCampaignCreditTransactionSnapshot = await firebase.firestore()
                                    //   .collection(Collections.LoyaltyCampaignCreditTransaction)
                                    //   .where('email', '==', crmUser.email)
                                    //   .where('outletId', '==', outlet.uniqueId)
                                    //   .where('deletedAt', '==', null)
                                    //   .get();

                                    // const userPointsTransactionsSnapshot = await getDocs(
                                    //     query(
                                    //         collection(global.db, Collections.UserPointsTransaction),
                                    //         where('phone', '==', crmUser.number),
                                    //         where('outletId', '==', outlet.uniqueId),
                                    //         where('deletedAt', '==', null),
                                    //     )
                                    // );

                                    // var userPointsTransactionsTemp = [];
                                    // var userPointsBalanceTemp = 0;

                                    // if (!userPointsTransactionsSnapshot.empty) {
                                    //     for (var i = 0; i < userPointsTransactionsSnapshot.size; i++) {
                                    //         const record = userPointsTransactionsSnapshot.docs[i].data();

                                    //         userPointsTransactionsTemp.push(record);

                                    //         userPointsBalanceTemp += record.amount;
                                    //     }
                                    // }

                                    // userPointsTransactions.sort((a, b) => b.createdAt - a.createdAt);

                                    // CommonStore.update(s => {
                                    //     s.selectedOutlet = outlet;
                                    // });

                                    // UserStore.update(s => {
                                    //     s.userPointsTransactions = userPointsTransactionsTemp;
                                    //     s.userPointsBalance = userPointsBalanceTemp;
                                    // });

                                    //////////////////////////////////////////

                                    // CommonStore.update(s => {
                                    //   s.registerUserOrder = userOrder;
                                    // });

                                    // await AsyncStorage.setItem('latestOutletId', outlet.uniqueId);

                                    // await updateUserCart(
                                    //   {
                                    //     ...route.params,
                                    //     outletId: outlet.uniqueId,
                                    //     tableId: tableId,
                                    //     tablePax: outletTable.seated,
                                    //     tableCode: outletTable.code,
                                    //   }
                                    //   , outlet, firebaseUid);

                                    // linkTo && linkTo('/web/outlet');
                                }
                                else {
                                    // linkTo && linkTo(`${prefix}/error`);
                                }
                            }
                            else {
                                // linkTo && linkTo(`${prefix}/error`);
                            }
                        }
                        else {
                            CommonStore.update(s => {
                                s.alertObj = {
                                    title: 'Error',
                                    message: 'Unauthorized access',
                                };

                                // s.isAuthenticating = false;
                            });

                            // linkTo && linkTo(`${prefix}/error`);
                        }
                    });
                });
        }
    }, [linkTo, route]);

    useEffect(() => {
        CommonStore.update(s => {
            s.availableTaggableVouchers = availableUserTaggableVouchers;
        });
    }, [availableUserTaggableVouchers]);

    useEffect(() => {
        if (linkTo) {
            DataStore.update((s) => {
                s.linkToFunc = linkTo;
            });
        }
    }, [linkTo]);

    useEffect(() => {
        const subdomain = AsyncStorage.getItem("latestSubdomain");
        if ((selectedOutlet.subdomain === 'hominsanttdi' || selectedOutlet.subdomain === 'hominsan-ss15' || selectedOutlet.subdomain === 'kenneth-cafe')) { // now only show for ttdi
            if (!email || !userNumber) {
                if (!subdomain) {
                    linkTo && linkTo(`${prefix}/outlet/menu`);
                }
                else {
                    linkTo && linkTo(`${prefix}/outlet/${subdomain}/menu`);
                }
            }
        }
        else {
            if (!subdomain) {
                linkTo && linkTo(`${prefix}/outlet/menu`);
            }
            else {
                linkTo && linkTo(`${prefix}/outlet/${subdomain}/menu`);
            }
        }
    }, [selectedOutlet, email, userNumber])

    useEffect(() => {
        if (selectedOutlet === null) {
            readStates();
        }

        readCommonStates();
    }, [selectedOutlet]);

    const readStates = async () => {
        if (selectedOutlet === null) {
            const commonStoreDataRaw = await AsyncStorage.getItem("@commonStore");
            if (commonStoreDataRaw !== null) {
                const commonStoreData = JSON.parse(commonStoreDataRaw);

                const latestOutletId = await AsyncStorage.getItem("latestOutletId");

                if (
                    commonStoreData.selectedOutlet &&
                    latestOutletId === commonStoreData.selectedOutlet.uniqueId
                ) {
                    // check if it's the same outlet user scanned

                    // if (
                    //   commonStoreData.orderType === ORDER_TYPE.DINEIN &&
                    //   commonStoreData.userCart.uniqueId === undefined
                    // ) {
                    //   // logout the user

                    //   linkTo && linkTo(`${prefix}/scan`);
                    // }

                    CommonStore.replace(commonStoreData);

                    const userStoreDataRaw = await AsyncStorage.getItem("@userStore");
                    if (userStoreDataRaw !== null) {
                        const userStoreData = JSON.parse(userStoreDataRaw);

                        UserStore.replace(userStoreData);
                    }

                    // const dataStoreDataRaw = await AsyncStorage.getItem('@dataStore');
                    // if (dataStoreDataRaw !== null) {
                    //   const dataStoreData = JSON.parse(dataStoreDataRaw);

                    //   DataStore.replace(dataStoreData);
                    // }
                }
            }
        }
    };

    const renderViewHistory = ({ item }) => {
        return (
            <View
                style={{
                    flexDirection: 'row',
                    paddingVertical: 10,
                    alignItems: 'center',
                    paddingHorizontal: 10,
                }}>
                <View style={{ flexDirection: 'column', width: '42.5%' }}>
                    <Text style={{ fontSize: isMobile() ? 10 : 16 }}>
                        {
                            LOYALTY_CAMPAIGN_CREDIT_TRANSACTION_TYPE_PARSED[
                            item.transactionType
                            ]
                        }
                    </Text>
                    <Text style={{ fontSize: isMobile() ? 10 : 16 }}>
                        {item.amount.toFixed(2)}
                    </Text>
                </View>
                <View style={{ flexDirection: 'column', width: '42.5%' }}>
                    <Text style={{ fontSize: isMobile() ? 10 : 16 }}>
                        {moment(item.createdAt).format('DD MMM YYYY')}
                    </Text>
                    <Text style={{ fontSize: isMobile() ? 10 : 16 }}>
                        {moment(item.createdAt).format('hh:mm A')}
                    </Text>
                </View>
                <View style={{ width: '15%', alignItems: 'center', justifyContent: 'center' }}>
                    <Text
                        style={{
                            fontSize: isMobile() ? 10 : 16,
                            fontFamily: 'NunitoSans-SemiBold',
                            backgroundColor: Colors.primaryColor,
                            color: Colors.whiteColor,
                            padding: 8,
                            borderRadius: 5,
                        }}>
                        Success
                    </Text>
                </View>
            </View>
        );
    };

    const renderCredit = ({ item }) => {
        return (
            <View
                style={{
                    flexDirection: 'row',
                    paddingVertical: 10,
                    alignItems: 'center',
                    paddingHorizontal: 10,
                }}>
                <View style={{ flexDirection: 'column', width: '42.5%' }}>
                    <Text style={{ fontSize: isMobile() ? 10 : 16 }}>
                        {
                            LOYALTY_CAMPAIGN_CREDIT_TRANSACTION_TYPE_PARSED[
                            item.transactionType
                            ]
                        }
                    </Text>
                    <Text style={{ fontSize: isMobile() ? 10 : 16 }}>
                        RM {item.amount.toFixed(2)}
                    </Text>
                </View>
                <View style={{ flexDirection: 'column', width: '42.5%' }}>
                    <Text style={{ fontSize: isMobile() ? 10 : 16 }}>
                        {moment(item.createdAt).format('DD MMM YYYY')}
                    </Text>
                    <Text style={{ fontSize: isMobile() ? 10 : 16 }}>
                        {moment(item.createdAt).format('hh:mm A')}
                    </Text>
                </View>
                <View style={{ width: '15%', alignItems: 'center', justifyContent: 'center' }}>
                    <Text
                        style={{
                            fontSize: isMobile() ? 10 : 16,
                            fontFamily: 'NunitoSans-SemiBold',
                            backgroundColor: Colors.primaryColor,
                            color: Colors.whiteColor,
                            padding: 8,
                            borderRadius: 5,
                        }}>
                        Success
                    </Text>
                </View>
            </View>
        );
    };

    const readCommonStates = async () => {
        // if (!linkToFunc) {
        //   const dataStoreDataRaw = await AsyncStorage.getItem('@dataStore');
        //   if (dataStoreDataRaw !== null) {
        //     const dataStoreData = JSON.parse(dataStoreDataRaw);
        //     DataStore.replace(dataStoreData);
        //   }
        // }
    };
    const renderItem = ({ item, index }) => (
        <>
            <View style={{
                backgroundColor: 'white',
                flexDirection: 'row',
                marginTop: 10,
                alignContent: 'center',
                alignItems: 'center',
                borderRadius: 20,
            }}>
                <View style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    width: windowWidth * 0.9,
                    margin: 10,
                    justifyContent: 'space-between'
                }}>
                    <View
                        style={{
                            flexDirection: 'row',
                            alignItems: 'center',
                            width: windowWidth * 0.9,
                            margin: 10,
                            justifyContent: 'space-between'
                        }}>
                        <View style={{ flexDirection: 'row' }}>
                            <View style={{ justifyContent: 'center' }}>
                                {item.image ? (
                                    <>
                                        <AsyncImage
                                            source={{ uri: item.image }}
                                            item={item}
                                            style={{
                                                width: windowHeight * 0.07,
                                                height: windowHeight * 0.07,
                                                borderRadius: 10,
                                            }}
                                        />
                                    </>
                                ) :
                                    <View style={{ backgroundColor: Colors.secondaryColor, width: windowHeight * 0.07, height: windowHeight * 0.07, alignItems: 'center', alignSelf: 'center', justifyContent: 'center', borderRadius: 10, }}>
                                        <Ionicons
                                            name="fast-food-outline"
                                            // size={45}
                                            size={windowHeight * 0.04}
                                        />
                                    </View>
                                }
                            </View>

                            <View>
                                <Text
                                    style={{
                                        // marginTop: 15,
                                        marginLeft: 10,
                                        fontSize: 20,
                                        fontFamily: 'NunitoSans-Bold',
                                        //color: Colors.whiteColor,
                                    }}
                                    numberOfLines={1}>
                                    {item.campaignName}
                                </Text>

                                <Text
                                    style={{
                                        // marginTop: 10,
                                        marginLeft: 10,
                                        fontSize: 12,
                                        fontFamily: 'NunitoSans-SemiBold',
                                        //color: Colors.whiteColor,
                                    }}>
                                    Expiry Date:
                                </Text>

                                <Text
                                    style={{
                                        //marginTop: 10,
                                        marginLeft: 10,
                                        fontSize: 12,
                                        fontFamily: 'NunitoSans-Regular',
                                        //color: Colors.whiteColor,
                                    }}>
                                    {moment(item.promoDateEnd).format('dddd, Do MMM YYYY')}
                                </Text>
                            </View>
                        </View>
                    </View>
                </View>
            </View>
        </>
    );

    useEffect(() => {
        CommonStore.update(s => {
            s.routeName = route.name;
        });
    }, [route]);

    const renderSearch = (item) => {
        return (
            <View style={{ flexDirection: "column" }}>
                <Text style={{ fontWeight: "700", fontSize: 14, marginBottom: 5 }}>
                    {item.structured_formatting.main_text}
                </Text>

                <Text style={{ fontSize: 12, color: "grey" }}>{item.description}</Text>
            </View>
        );
    };

    return (
        <View style={{ height: windowHeight * 0.9, alignItems: 'center', justifyContent: 'center', backgroundColor: 'light-gray', }}>
            <Modal
                style={{ flex: 1 }}
                visible={stampsModal}
                transparent={true}
                animationType="slide"
            >
                <View
                    style={{
                        backgroundColor: Colors.modalBgColor,
                        flex: 1,
                        justifyContent: "center",
                        alignItems: "center",
                        minHeight: windowHeight,
                    }}
                >
                    <View
                        style={{
                            width: windowWidth,
                            height: windowHeight,
                            backgroundColor: Colors.whiteColor,
                        }}
                    >
                        <TouchableOpacity
                            onPress={() => {
                                setStampsModal(false);
                            }}
                        >
                            <View
                                style={{
                                    position: "absolute",
                                    left: 15,
                                    top: 20,
                                    index: 9000,
                                }}
                            >
                                <AntDesign
                                    name="left"
                                    size={25}
                                    color={Colors.blackColor}
                                />
                            </View>
                        </TouchableOpacity>
                        <View style={{ justifyContent: 'center', zIndex: -1 }}>
                            <View style={{ backgroundColor: Colors.secondaryColor, width: windowWidth, height: windowHeight * 0.35, alignItems: 'center', alignSelf: 'center', justifyContent: 'center', borderRadius: 10, }}>
                                <Ionicons
                                    name="fast-food-outline"
                                    // size={45}
                                    size={windowHeight * 0.1}
                                />
                            </View>
                        </View>
                    </View>
                </View>
            </Modal>

            {/* <View style={{
                width: isMobile() ? windowWidth : windowWidth,
                height: 160,
                backgroundColor: Colors.darkBgColor,
            }}>
                <Image style={{
                    width: 100,
                    height: 47,
                    alignSelf: 'center',
                    //marginBottom: 25.
                }} resizeMode="contain" source={imgLogo} />
                <Text style={{
                    fontFamily: 'NunitoSans-Bold',
                    color: Colors.whiteColor,
                    fontSize: 30,
                    textAlign: 'center',
                    marginTop: 30,
                    // width: '100%',
                }}>
                    {loyaltyHistoryCrmUser.number ? loyaltyHistoryCrmUser.number : 'N/A'}
                </Text>
            </View> */}

            <View style={{
                justifyContent: 'center',
                alignContent: 'center',
                borderRadius: 10,
                borderColor: '#E5E5E5',

                shadowOffset: {
                    width: 0,
                    height: 2,
                },
                shadowOpacity: 0.22,
                shadowRadius: 3.22,
                elevation: 1,
                padding: 20,
                marginTop: '3%',

                height: windowHeight * 0.85,
                width: isMobile() ? windowWidth * 0.9 : windowWidth * 0.4,
            }}>
                <ScrollView showsVerticalScrollIndicator={false}>
                    <View style={{ marginTop: '1%', alignItems: 'center', justifyContent: 'space-between', alignSelf: 'center', flexDirection: 'row', width: '90%' }}>
                        <TouchableOpacity
                            style={{
                                width: '31%',
                                alignItems: 'flex-start',
                                justifyContent: 'center',
                                borderWidth: 1,
                                borderRadius: 5,
                                borderColor: Colors.primaryColor,
                                backgroundColor: 'white',
                                padding: 8,
                            }}
                            onPress={() => setRewardSection(REWARD_SECTION.CREDIT)}>
                            <Text
                                style={{
                                    fontSize: 16,
                                    fontFamily: 'NunitoSans-SemiBold',
                                    backgroundColor: Colors.whiteColor,
                                    color: Colors.primaryColor,
                                    textAlign: 'left',
                                }}>
                                Credits
                            </Text>
                            <Text
                                style={{
                                    fontSize: 14,
                                    fontFamily: 'NunitoSans-SemiBold',
                                    backgroundColor: Colors.whiteColor,
                                    color: Colors.primaryColor,
                                    textAlign: 'left',
                                }}>
                                GET STARTED
                            </Text>
                        </TouchableOpacity>

                        <TouchableOpacity
                            style={{
                                width: '31%',
                                alignItems: 'flex-start',
                                justifyContent: 'center',
                                borderWidth: 1,
                                borderRadius: 5,
                                borderColor: Colors.primaryColor,
                                backgroundColor: 'white',
                                padding: 8,
                            }}
                            onPress={() => setRewardSection(REWARD_SECTION.STAMP)}>
                            <Text
                                style={{
                                    fontSize: 16,
                                    fontFamily: 'NunitoSans-SemiBold',
                                    color: Colors.primaryColor,
                                    textAlign: 'left',
                                }}>
                                Stamps
                            </Text>
                            <Text
                                style={{
                                    fontSize: 16,
                                    fontFamily: 'NunitoSans-SemiBold',
                                    color: Colors.primaryColor,
                                    textAlign: 'left',
                                }}>
                                0/10
                            </Text>
                        </TouchableOpacity>

                        <TouchableOpacity
                            style={{
                                width: '31%',
                                alignItems: 'flex-start',
                                justifyContent: 'center',
                                borderWidth: 1,
                                borderRadius: 5,
                                borderColor: Colors.primaryColor,
                                backgroundColor: 'white',
                                padding: 8,
                            }}
                            onPress={() => setRewardSection(REWARD_SECTION.POINT)}>
                            <Text
                                style={{
                                    fontSize: 16,
                                    fontFamily: 'NunitoSans-SemiBold',
                                    backgroundColor: Colors.whiteColor,
                                    color: Colors.primaryColor,
                                    textAlign: 'left',
                                }}>
                                Points
                            </Text>
                            <Text
                                style={{
                                    fontSize: 16,
                                    fontFamily: 'NunitoSans-SemiBold',
                                    backgroundColor: Colors.whiteColor,
                                    color: Colors.primaryColor,
                                    textAlign: 'left',
                                }}>
                                {userPointsBalance ? userPointsBalance.toFixed(0) : 0}
                            </Text>
                        </TouchableOpacity>
                    </View>
                    <View style={{ marginTop: '2%', alignItems: 'center', justifyContent: 'center', }}>
                        <Text style={{
                            fontFamily: 'NunitoSans-Bold',
                            color: 'black',
                            fontSize: 18,
                            textAlign: 'center',
                        }}>
                            {`Available Points (${selectedOutlet.name})`}
                        </Text>
                        <Text style={{
                            fontFamily: 'NunitoSans-Bold',
                            color: 'Black',
                            fontSize: 20,
                            textAlign: 'center',
                        }}>
                            {userPointsBalance ? userPointsBalance.toFixed(0) : 0}
                        </Text>
                    </View>
                    <View style={{
                        flexDirection: 'row',
                        marginTop: '3%',
                        justifyContent: 'space-evenly'
                    }}>
                        <View style={{
                            flexDirection: 'column',
                            marginTop: '3%',
                            marginRight: '50%'
                            //justifyContent: 'space-evenly'
                        }}>
                            <View style={{ justifyContent: 'flex-start' }}>
                                <Text style={{
                                    fontFamily: 'NunitoSans-Regular',
                                    color: Colors.descriptionColor,
                                    fontSize: 14,
                                    textAlign: 'left',
                                    marginTop: 0,
                                    marginLeft: 5,
                                    // width: '100%',
                                }}>
                                    Name
                                </Text>
                                <Text style={{
                                    fontFamily: 'NunitoSans-Bold',
                                    color: 'black',
                                    fontSize: 16,
                                    textAlign: 'left',
                                    marginTop: 0,
                                    marginLeft: 5,
                                    // width: '100%',
                                }}>
                                    {name ? name : '-'}
                                </Text>
                            </View>
                        </View>
                        <View style={{
                            flexDirection: 'column',
                            marginTop: '3%',
                            //justifyContent: 'space-evenly'
                        }}>
                            <View style={{ justifyContent: 'flex-end' }}>
                                <Text style={{
                                    fontFamily: 'NunitoSans-Regular',
                                    color: Colors.descriptionColor,
                                    fontSize: 14,
                                    textAlign: 'left',
                                    marginTop: 0,
                                    marginLeft: 5,
                                    // width: '100%',
                                }}>
                                    Phone Number
                                </Text>
                                <Text style={{
                                    fontFamily: 'NunitoSans-Bold',
                                    color: 'black',
                                    fontSize: 16,
                                    textAlign: 'right',
                                    marginTop: 0,
                                    marginLeft: 5,
                                    // width: '100%',
                                }}>
                                    {userNumber ? userNumber : '-'}
                                </Text>
                            </View>
                        </View>
                    </View>
                    {rewardSection === REWARD_SECTION.POINT ?
                        <>
                            <View style={{ marginTop: '2%', alignContent: 'center', width: isMobile() ? '100%' : '80%', alignSelf: 'center', height: windowHeight * 0.27, paddingBottom: 10, }}>
                                <View
                                    style={{
                                        flexDirection: 'row',
                                        padding: 10,
                                        marginTop: 20,
                                        borderRadius: 5,
                                    }}>
                                    <Text
                                        style={{
                                            fontSize: isMobile() ? 10 : 16,
                                            fontFamily: 'NunitoSans-Bold',
                                            width: '42.5%',
                                        }}>
                                        Action
                                    </Text>
                                    <Text
                                        style={{
                                            fontSize: isMobile() ? 10 : 16,
                                            fontFamily: 'NunitoSans-Bold',
                                            width: '42.5%',
                                        }}>
                                        Date
                                    </Text>
                                    <Text
                                        style={{
                                            fontSize: isMobile() ? 10 : 16,
                                            fontFamily: 'NunitoSans-Bold',
                                            width: '15%',
                                            textAlign: 'center',
                                        }}>
                                        Status
                                    </Text>
                                </View>
                                {/* List */}

                                <FlatList
                                    showsVerticalScrollIndicator={false}
                                    data={userPointsTransactions}
                                    // extraData={selectedCustomerLCCTransactions}
                                    renderItem={renderViewHistory}
                                    keyExtractor={(item, index) => index}
                                    contentContainerStyle={{}}
                                />
                            </View>

                            <View style={{ marginTop: '5%', alignItems: 'center', justifyContent: 'center', width: isMobile() ? '100%' : '80%', alignSelf: 'center', height: windowHeight * 0.2, }}>
                                <Text style={{
                                    fontFamily: 'NunitoSans-Bold',
                                    color: 'black',
                                    fontSize: 18,
                                    textAlign: 'center',
                                }}>
                                    Available Rewards
                                </Text>
                                {availableTaggableVouchers && availableTaggableVouchers.length > 0 ? (
                                    <>
                                        {availableTaggableVouchers && availableTaggableVouchers.length > 0 && (
                                            <View style={{ height: '100%', width: '100%', paddingBottom: 10, marginTop: 10, }}>
                                                <FlatList
                                                    showsVerticalScrollIndicator={false}
                                                    data={availableTaggableVouchers}
                                                    style={{}}
                                                    //extraData={availableTaggableVouchers}
                                                    renderItem={renderItem}
                                                    keyExtractor={(item, index) => String(index)}
                                                />
                                            </View>
                                        )}
                                    </>
                                ) : (
                                    <View
                                        style={{
                                            justifyContent: 'center',
                                            alignItems: 'center',
                                            // marginTop: 30,
                                            alignSelf: 'center',
                                        }}>
                                        <Text style={{
                                            fontFamily: 'NunitoSans-Regular',
                                            color: Colors.descriptionColor,
                                            fontSize: 14,
                                            textAlign: 'center',
                                            marginTop: '2%',
                                        }}>
                                            This outlet currently don't have any rewards available
                                        </Text>
                                    </View>
                                )}

                            </View>
                            <View style={{
                                flexDirection: 'row',
                                marginTop: '5%',
                                justifyContent: 'space-between',
                                alignItems: 'center',
                                width: isMobile() ? '100%' : '80%',
                                alignSelf: 'center',
                            }}>
                                <View style={{
                                    flexDirection: 'column',
                                    marginTop: '2%',
                                }}>
                                    <View style={{ justifyContent: 'flex-start' }}>
                                        <Text style={{
                                            fontFamily: 'NunitoSans-Bold',
                                            color: 'black',
                                            fontSize: 18,
                                            textAlign: 'left',
                                        }}>
                                            Receive SMS Rewards
                                        </Text>
                                    </View>
                                </View>
                                <View style={{
                                    flexDirection: 'column',
                                    marginTop: '2%',
                                    //justifyContent: 'space-evenly'
                                }}>
                                    <View style={{ justifyContent: 'flex-end' }}>
                                        <Switch
                                            width={35}
                                            height={20}
                                            handleDiameter={30}
                                            uncheckedIcon={false}
                                            checkedIcon={false}
                                            boxShadow="0px 1px 5px rgba(0, 0, 0, 0.6)"
                                            activeBoxShadow="0px 0px 1px 10px rgba(0, 0, 0, 0.2)"
                                            value={!blockSMS}
                                            onValueChange={async value => {
                                                setBlockSMS(!value);

                                                if (loyaltyHistoryCrmUser && loyaltyHistoryCrmUser.uniqueId) {
                                                    const body = {
                                                        crmUserId: loyaltyHistoryCrmUser.uniqueId,
                                                        blockSMS: !value,

                                                        blockSMSFromUserSide: !value,
                                                    };

                                                    ApiClient.POST(API.switchCRMUserBlockSMSFromUserWeb, body).then((result) => {
                                                        if (result && result.status === "success") {

                                                        }
                                                    });
                                                }
                                            }}
                                        // onChange={async () => {

                                        // }}
                                        />
                                    </View>
                                </View>
                            </View>

                            <View style={{
                                flexDirection: 'row',
                                marginTop: '2%',
                                justifyContent: 'space-between',
                                alignItems: 'center',
                                width: isMobile() ? '100%' : '80%',
                                alignSelf: 'center',
                            }}>
                                <View style={{
                                    flexDirection: 'column',
                                    marginTop: '2%',
                                }}>
                                    <View style={{ justifyContent: 'flex-start' }}>
                                        <Text style={{
                                            fontFamily: 'NunitoSans-Bold',
                                            color: 'black',
                                            fontSize: 18,
                                            textAlign: 'left',
                                        }}>
                                            Receive Email Rewards
                                        </Text>
                                    </View>
                                </View>
                                <View style={{
                                    flexDirection: 'column',
                                    marginTop: '2%',
                                    //justifyContent: 'space-evenly'
                                }}>
                                    <View style={{ justifyContent: 'flex-end' }}>
                                        <Switch
                                            width={35}
                                            height={20}
                                            handleDiameter={30}
                                            uncheckedIcon={false}
                                            checkedIcon={false}
                                            boxShadow="0px 1px 5px rgba(0, 0, 0, 0.6)"
                                            activeBoxShadow="0px 0px 1px 10px rgba(0, 0, 0, 0.2)"
                                            value={!blockEmail}
                                            onValueChange={async value => {
                                                setBlockEmail(!value);

                                                if (loyaltyHistoryCrmUser && loyaltyHistoryCrmUser.uniqueId) {
                                                    const body = {
                                                        crmUserId: loyaltyHistoryCrmUser.uniqueId,
                                                        blockEmail: !value,

                                                        blockEmailFromUserSide: !value,
                                                    };

                                                    ApiClient.POST(API.switchCRMUserBlockEmailFromUserWeb, body).then((result) => {
                                                        if (result && result.status === "success") {

                                                        }
                                                    });
                                                }
                                            }}
                                        // onChange={async () => {

                                        // }}
                                        />
                                    </View>
                                </View>
                            </View>
                            <View style={{ height: 20 }} />
                        </>
                        : <></>}
                    {rewardSection === REWARD_SECTION.CREDIT ?
                        <>
                            <View style={{ marginTop: '2%', alignContent: 'center', width: isMobile() ? '100%' : '80%', alignSelf: 'center', height: windowHeight * 0.27, paddingBottom: 10, }}>
                                <View
                                    style={{
                                        flexDirection: 'row',
                                        padding: 10,
                                        marginTop: 20,
                                        borderRadius: 5,
                                    }}>
                                    <Text
                                        style={{
                                            fontSize: isMobile() ? 10 : 16,
                                            fontFamily: 'NunitoSans-Bold',
                                            width: '42.5%',
                                        }}>
                                        Action
                                    </Text>
                                    <Text
                                        style={{
                                            fontSize: isMobile() ? 10 : 16,
                                            fontFamily: 'NunitoSans-Bold',
                                            width: '42.5%',
                                        }}>
                                        Date
                                    </Text>
                                    <Text
                                        style={{
                                            fontSize: isMobile() ? 10 : 16,
                                            fontFamily: 'NunitoSans-Bold',
                                            width: '15%',
                                            textAlign: 'center',
                                        }}>
                                        Status
                                    </Text>
                                </View>
                                {/* List */}

                                <FlatList
                                    showsVerticalScrollIndicator={false}
                                    data={userPointsTransactions}
                                    // extraData={selectedCustomerLCCTransactions}
                                    renderItem={renderCredit}
                                    keyExtractor={(item, index) => index}
                                    contentContainerStyle={{}}
                                />
                            </View>

                            <View style={{ marginTop: '5%', alignItems: 'center', justifyContent: 'center', width: isMobile() ? '100%' : '80%', alignSelf: 'center', height: windowHeight * 0.2, }}>
                                <Text style={{
                                    fontFamily: 'NunitoSans-Bold',
                                    color: 'black',
                                    fontSize: 18,
                                    textAlign: 'center',
                                }}>
                                    Amount To TopUp
                                </Text>

                                <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginTop: "2%" }}>
                                    <TouchableOpacity
                                        onPress={() => { setPointsSelected(10) }}
                                        style={{
                                            borderWidth: 1,
                                            borderColor: Colors.primaryColor,
                                            borderRadius: 10,
                                            width: isMobile() ? windowWidth * 0.2 : windowWidth * 0.1,
                                            paddingVertical: 8,
                                            marginHorizontal: 5,
                                            backgroundColor: pointsSelected === 10 ? Colors.primaryColor : 'white',
                                        }}>
                                        <Text
                                            style={{
                                                alignSelf: "center",
                                                color: pointsSelected === 10 ? 'white' : Colors.primaryColor,
                                                fontSize: 16,
                                                fontFamily: "NunitoSans-Bold",
                                            }}
                                        >
                                            10
                                        </Text>
                                    </TouchableOpacity>
                                    <TouchableOpacity
                                        onPress={() => { setPointsSelected(30) }}
                                        style={{
                                            borderWidth: 1,
                                            borderColor: Colors.primaryColor,
                                            borderRadius: 10,
                                            width: isMobile() ? windowWidth * 0.2 : windowWidth * 0.1,
                                            paddingVertical: 8,
                                            marginHorizontal: 5,
                                            backgroundColor: pointsSelected === 30 ? Colors.primaryColor : 'white',
                                        }}>
                                        <Text
                                            style={{
                                                alignSelf: "center",
                                                color: pointsSelected === 30 ? 'white' : Colors.primaryColor,
                                                fontSize: 16,
                                                fontFamily: "NunitoSans-Bold",
                                            }}
                                        >
                                            30
                                        </Text>
                                    </TouchableOpacity>
                                    <TouchableOpacity
                                        onPress={() => { setPointsSelected(50) }}
                                        style={{
                                            borderWidth: 1,
                                            borderColor: Colors.primaryColor,
                                            borderRadius: 10,
                                            width: isMobile() ? windowWidth * 0.2 : windowWidth * 0.1,
                                            paddingVertical: 8,
                                            marginHorizontal: 5,
                                            backgroundColor: pointsSelected === 50 ? Colors.primaryColor : 'white',
                                        }}>
                                        <Text
                                            style={{
                                                alignSelf: "center",
                                                color: pointsSelected === 50 ? 'white' : Colors.primaryColor,
                                                fontSize: 16,
                                                fontFamily: "NunitoSans-Bold",
                                            }}
                                        >
                                            50
                                        </Text>
                                    </TouchableOpacity>
                                </View>
                                <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginTop: "2%" }}>
                                    <TouchableOpacity
                                        onPress={() => { setPointsSelected(100) }}
                                        style={{
                                            borderWidth: 1,
                                            borderColor: Colors.primaryColor,
                                            borderRadius: 10,
                                            width: isMobile() ? windowWidth * 0.2 : windowWidth * 0.1,
                                            paddingVertical: 8,
                                            marginHorizontal: 5,
                                            backgroundColor: pointsSelected === 100 ? Colors.primaryColor : 'white',
                                        }}>
                                        <Text
                                            style={{
                                                alignSelf: "center",
                                                color: pointsSelected === 100 ? 'white' : Colors.primaryColor,
                                                fontSize: 16,
                                                fontFamily: "NunitoSans-Bold",
                                            }}
                                        >
                                            100
                                        </Text>
                                    </TouchableOpacity>
                                    <TouchableOpacity
                                        onPress={() => { setPointsSelected(200) }}
                                        style={{
                                            borderWidth: 1,
                                            borderColor: Colors.primaryColor,
                                            borderRadius: 10,
                                            width: isMobile() ? windowWidth * 0.2 : windowWidth * 0.1,
                                            paddingVertical: 8,
                                            marginHorizontal: 5,
                                            backgroundColor: pointsSelected === 200 ? Colors.primaryColor : 'white',
                                        }}>
                                        <Text
                                            style={{
                                                alignSelf: "center",
                                                color: pointsSelected === 200 ? 'white' : Colors.primaryColor,
                                                fontSize: 16,
                                                fontFamily: "NunitoSans-Bold",
                                            }}
                                        >
                                            200
                                        </Text>
                                    </TouchableOpacity>
                                </View>
                                <TouchableOpacity
                                    onPress={() => { }}
                                    style={{
                                        borderRadius: 25,
                                        // paddingHorizontal: 10,
                                        paddingVertical: 8,
                                        marginHorizontal: 5,
                                        backgroundColor: Colors.primaryColor,
                                        marginTop: '3%',
                                        width: isMobile() ? windowWidth * 0.6 : windowWidth * 0.25,
                                    }}>
                                    <Text
                                        style={{
                                            alignSelf: "center",
                                            color: 'white',
                                            fontSize: 20,
                                            fontFamily: "NunitoSans-Bold",
                                        }}
                                    >
                                        Top Up
                                    </Text>
                                </TouchableOpacity>

                            </View>
                            <View style={{
                                flexDirection: 'row',
                                marginTop: '5%',
                                justifyContent: 'space-between',
                                alignItems: 'center',
                                width: isMobile() ? '100%' : '80%',
                                alignSelf: 'center',
                            }}>
                                <View style={{
                                    flexDirection: 'column',
                                    marginTop: '2%',
                                }}>
                                    <View style={{ justifyContent: 'flex-start' }}>
                                        <Text style={{
                                            fontFamily: 'NunitoSans-Bold',
                                            color: 'black',
                                            fontSize: 18,
                                            textAlign: 'left',
                                        }}>
                                            Receive SMS Rewards
                                        </Text>
                                    </View>
                                </View>
                                <View style={{
                                    flexDirection: 'column',
                                    marginTop: '2%',
                                    //justifyContent: 'space-evenly'
                                }}>
                                    <View style={{ justifyContent: 'flex-end' }}>
                                        <Switch
                                            width={35}
                                            height={20}
                                            handleDiameter={30}
                                            uncheckedIcon={false}
                                            checkedIcon={false}
                                            boxShadow="0px 1px 5px rgba(0, 0, 0, 0.6)"
                                            activeBoxShadow="0px 0px 1px 10px rgba(0, 0, 0, 0.2)"
                                            value={!blockSMS}
                                            onValueChange={async value => {
                                                setBlockSMS(!value);

                                                if (loyaltyHistoryCrmUser && loyaltyHistoryCrmUser.uniqueId) {
                                                    const body = {
                                                        crmUserId: loyaltyHistoryCrmUser.uniqueId,
                                                        blockSMS: !value,

                                                        blockSMSFromUserSide: !value,
                                                    };

                                                    ApiClient.POST(API.switchCRMUserBlockSMSFromUserWeb, body).then((result) => {
                                                        if (result && result.status === "success") {

                                                        }
                                                    });
                                                }
                                            }}
                                        // onChange={async () => {

                                        // }}
                                        />
                                    </View>
                                </View>
                            </View>

                            <View style={{
                                flexDirection: 'row',
                                marginTop: '2%',
                                justifyContent: 'space-between',
                                alignItems: 'center',
                                width: isMobile() ? '100%' : '80%',
                                alignSelf: 'center',
                            }}>
                                <View style={{
                                    flexDirection: 'column',
                                    marginTop: '2%',
                                }}>
                                    <View style={{ justifyContent: 'flex-start' }}>
                                        <Text style={{
                                            fontFamily: 'NunitoSans-Bold',
                                            color: 'black',
                                            fontSize: 18,
                                            textAlign: 'left',
                                        }}>
                                            Receive Email Rewards
                                        </Text>
                                    </View>
                                </View>
                                <View style={{
                                    flexDirection: 'column',
                                    marginTop: '2%',
                                    //justifyContent: 'space-evenly'
                                }}>
                                    <View style={{ justifyContent: 'flex-end' }}>
                                        <Switch
                                            width={35}
                                            height={20}
                                            handleDiameter={30}
                                            uncheckedIcon={false}
                                            checkedIcon={false}
                                            boxShadow="0px 1px 5px rgba(0, 0, 0, 0.6)"
                                            activeBoxShadow="0px 0px 1px 10px rgba(0, 0, 0, 0.2)"
                                            value={!blockEmail}
                                            onValueChange={async value => {
                                                setBlockEmail(!value);

                                                if (loyaltyHistoryCrmUser && loyaltyHistoryCrmUser.uniqueId) {
                                                    const body = {
                                                        crmUserId: loyaltyHistoryCrmUser.uniqueId,
                                                        blockEmail: !value,

                                                        blockEmailFromUserSide: !value,
                                                    };

                                                    ApiClient.POST(API.switchCRMUserBlockEmailFromUserWeb, body).then((result) => {
                                                        if (result && result.status === "success") {

                                                        }
                                                    });
                                                }
                                            }}
                                        // onChange={async () => {

                                        // }}
                                        />
                                    </View>
                                </View>
                            </View>
                            <View style={{ height: 20 }} />
                        </>
                        : <></>}
                    {rewardSection === REWARD_SECTION.STAMP ?
                        <>
                            <TouchableOpacity
                                onPress={() => { setStampsModal(true) }}
                                style={{
                                    backgroundColor: 'white',
                                    flexDirection: 'row',
                                    marginTop: 10,
                                    alignContent: 'center',
                                    alignItems: 'center',
                                    borderRadius: 20,
                                }}>
                                <View style={{
                                    flexDirection: 'row',
                                    alignItems: 'center',
                                    width: windowWidth * 0.9,
                                    margin: 10,
                                    justifyContent: 'space-between'
                                }}>
                                    <View
                                        style={{
                                            flexDirection: 'row',
                                            alignItems: 'center',
                                            width: windowWidth * 0.9,
                                            margin: 10,
                                            justifyContent: 'space-between'
                                        }}>
                                        <View style={{ flexDirection: 'row' }}>
                                            <View style={{ justifyContent: 'center' }}>
                                                <View style={{ backgroundColor: Colors.secondaryColor, width: windowHeight * 0.07, height: windowHeight * 0.07, alignItems: 'center', alignSelf: 'center', justifyContent: 'center', borderRadius: 10, }}>
                                                    <Ionicons
                                                        name="fast-food-outline"
                                                        // size={45}
                                                        size={windowHeight * 0.04}
                                                    />
                                                </View>
                                            </View>

                                            <View>
                                                <Text
                                                    style={{
                                                        // marginTop: 15,
                                                        marginLeft: 10,
                                                        fontSize: 20,
                                                        fontFamily: 'NunitoSans-Bold',
                                                        //color: Colors.whiteColor,
                                                    }}
                                                    numberOfLines={1}>
                                                    *name*
                                                </Text>
                                                <Text
                                                    style={{
                                                        // marginTop: 15,
                                                        marginLeft: 10,
                                                        fontSize: 20,
                                                        fontFamily: 'NunitoSans-Bold',
                                                        //color: Colors.whiteColor,
                                                    }}
                                                    numberOfLines={1}>
                                                    0/10 *progress*
                                                </Text>

                                            </View>
                                        </View>
                                    </View>
                                </View>
                            </TouchableOpacity>
                        </>
                        : <></>}
                </ScrollView>
            </View>
            <View style={{ flex: 1 }}></View>
        </View >

    )
}
const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.highlightColor,
        flexDirection: 'row',
        fontFamily: 'NunitoSans-Regular',
    },
    list: {
        backgroundColor: Colors.whiteColor,
        // width: windowWidth * 0.85,
        // height: windowHeight,
        marginTop: 40,
        marginHorizontal: 30,
        //alignSelf: 'center',
        //justifyContent: 'center',
        borderRadius: 5,
        shadowOpacity: 0,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
    },
    listItem: {
        fontFamily: 'NunitoSans-Regular',
        marginLeft: 20,
        color: Colors.descriptionColor,
        fontSize: 16,
    },
    textInput: {
        fontFamily: 'NunitoSans-Regular',
        height: 50,
        backgroundColor: Colors.fieldtBgColor,
        //borderRadius: 10,
        paddingLeft: 20,
    },
    textInputLocation: {
        fontFamily: 'NunitoSans-Regular',
        width: 300,
        height: 100,
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 10,
        marginRight: 10,
    },
    textSize: {
        fontSize: 19,
        fontFamily: 'NunitoSans-SemiBold',
    },
    merchantDisplayView: {
        flexDirection: 'row',
        flex: 1,
        marginLeft: '17%',
    },
    shiftText: {
        marginLeft: '15%',
        color: Colors.primaryColor,
        fontFamily: 'NunitoSans-SemiBold',
        fontSize: 25,
    },
    confirmBox: {
        width: '30%',
        height: '30%',
        borderRadius: 30,
        backgroundColor: Colors.whiteColor,
    },
    closeButton: {
        position: 'absolute',
        right: 10,
        top: 10,
        elevation: 1000,
        zIndex: 1000,
    },
    modalContainer: {
        flex: 1,
        backgroundColor: Colors.modalBgColor,
        alignItems: 'center',
        justifyContent: 'center',
    },
    modalView: {
        height: 200,
        width: 500,
        backgroundColor: Colors.whiteColor,
        borderRadius: 12,
        padding: 25,
        //alignItems: 'center',
        //justifyContent: 'center'
    },
});

export default OutletRewardScreen;