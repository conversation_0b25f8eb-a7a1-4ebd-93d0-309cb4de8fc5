import React, { Component, useReducer, useState, useEffect } from 'react';
import {
  StyleSheet,
  ScrollView,
  Image,
  View,
  Text,
  TouchableOpacity,
  TextInput,
  FlatList,
  RefreshControl,
  Alert,
  Modal,
  Dimensions,
  Platform,
  useWindowDimensions
} from 'react-native';
import Colors from '../constant/Colors';
import * as Cart from '../util/Cart';
import * as User from '../util/User';
import Styles from '../constant/Styles';
// import ActionSheet from 'react-native-actionsheet';
import ApiClient from '../util/ApiClient';
import API from '../constant/API';
import Ionicons from 'react-native-vector-icons/Ionicons';
import Icon from 'react-native-vector-icons/Ionicons';
import Icons from 'react-native-vector-icons/Feather';
// import NumericInput from 'react-native-numeric-input';
// import { color } from 'react-native-reanimated';
// import QRCode from 'react-native-qrcode-svg';
import Close from 'react-native-vector-icons/AntDesign';
import { CommonStore } from '../store/commonStore';
import { UserStore } from '../store/userStore';
import moment from 'moment';
import AsyncImage from '../components/asyncImage';
// import LinearGradient from 'react-native-linear-gradient';
import LinearGradient from 'react-native-web-linear-gradient';
import { prefix } from "../constant/env";
import { useFocusEffect, useLinkTo } from "@react-navigation/native";

const TaggableVoucherListPurchaseScreen = (props) => {
  const { navigation, route } = props;

  const { width: windowWidth, height: windowHeight } = useWindowDimensions();
  const linkTo = useLinkTo();

  var pageFromParam = null;
  if (route && route.params) {
    pageFromParam = route.params.pageFrom;
  }

  const [pageFrom, setPageFrom] = useState(pageFromParam);
  const selectedOutlet = CommonStore.useState((s) => s.selectedOutlet);
  const availableTaggableVouchers = CommonStore.useState(
    (s) => s.availableTaggableVouchers,
  );
  const selectedOutletTaggableVouchers = CommonStore.useState(
    (s) => s.selectedOutletTaggableVouchers,
  );
  const selectedOutletPromotion = CommonStore.useState(
    (s) => s.selectedOutletPromotion,
  );

  const isFromCartPage = CommonStore.useState(
    (s) => s.isFromCartPage,
  );

  const [searchText, setSearchText] = useState('');
  ////////////////////////////////////////////////////////////

  // 2022-08-02 - Buy vouchers online

  useEffect(() => {
    CommonStore.update(s => {
      s.availableTaggableVouchers = selectedOutletTaggableVouchers;
    });
  }, [selectedOutletTaggableVouchers]);

  ////////////////////////////////////////////////////////////

  navigation.setOptions({
    headerLeft: () => (
      <TouchableOpacity
        style={{}}
        onPress={() => {
          props.navigation.goBack();
          // link&&linkTo(``);
        }}>
        <View
          style={{
            marginLeft: 10,
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'flex-start',
          }}>
          <Ionicons
            name="chevron-back"
            size={26}
            color={Colors.fieldtTxtColor}
            style={{}}
          />

          <Text
            style={{
              color: Colors.fieldtTxtColor,
              fontSize: 16,
              textAlign: 'center',
              fontFamily: 'NunitoSans-Regular',
              lineHeight: 22,
              marginTop: -1,
            }}>
            Back
          </Text>
        </View>
      </TouchableOpacity>
    ),
    headerRight: () => (
      <TouchableOpacity
        onPress={() => {
          // props.navigation.navigate('Profile');
        }}
        style={{}}>
        <View style={{ marginRight: 15 }}>
          {/* <Ionicons name="menu" size={30} color={Colors.primaryColor} /> */}
        </View>
      </TouchableOpacity>
    ),
    headerTitle: () => (
      <View
        style={{
          justifyContent: 'center',
          alignItems: 'center',
          bottom: -1,
        }}>
        <Text
          style={{
            fontSize: 20,
            lineHeight: 25,
            textAlign: 'center',
            fontFamily: 'NunitoSans-Bold',
            color: Colors.mainTxtColor,
          }}>
          Buy Vouchers
        </Text>
      </View>
    ),
  });

  const renderItem = ({ item, index }) => (
    <TouchableOpacity
      key={index}
      onPress={() => {
        CommonStore.update((s) => {
          s.selectedTaggableVoucherPurchase = item;

          s.tvCartItems = [
            {
              taggableVoucherId: item.uniqueId,

              remarks: '',
              cartItemDate: Date.now(),
              image: item.image,
              name: item.campaignName,
              itemName: item.campaignName,
              price: item.priceToBuy,
              totalPrice: item.priceToBuy,
            },
          ];
        });

        linkTo && linkTo(`${prefix}/outlet/${selectedOutlet.subdomain}/voucher-cart`);

        // if (isFromCartPage) {
        //   linkTo && linkTo(`${prefix}/outlet/cart`)

        //   CommonStore.update((s) => {
        //     s.isFromCartPage = false;
        //   });
        // }
        // else {
        //   linkTo && linkTo(`${prefix}/outlet/voucher-details`)
        // }
      }}>
      <View style={[styles.card]}>
        <LinearGradient
          colors={[Colors.primaryColor, '#8FBC8F']}
          style={{ flex: 2, borderTopLeftRadius: 20, borderTopRightRadius: 20 }}
          start={{ x: 1.0, y: 0.35 }}
          end={{ x: 0.5, y: 1.0 }}
          locations={[0.0, 9.0, 5.6]}>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              width: windowWidth * 0.9,
              margin: 10,
              justifyContent: 'space-between'
            }}>
            <View style={{ flexDirection: 'row' }}>
              <View style={[styles.pic, {
                ...!item.image && {
                  width: 0,
                },
              }]}>
                {item.image ? (
                  <AsyncImage
                    source={{ uri: item.image }}
                    item={item}
                    style={{
                      width: 80,
                      height: Platform.OS == 'ios' ? 80 : 80,
                      borderRadius: 10,
                    }}
                  />
                ) : null}
              </View>

              <View>
                <Text
                  style={{
                    marginLeft: 10,
                    fontSize: 20,
                    fontFamily: 'NunitoSans-Bold',
                    color: Colors.whiteColor,
                  }}
                  numberOfLines={1}>
                  {item.campaignName}
                </Text>

                <Text
                  style={{
                    marginTop: 10,
                    marginLeft: 10,
                    fontSize: 12,
                    fontFamily: 'NunitoSans-SemiBold',
                    color: Colors.whiteColor,
                  }}>
                  Expiry Date:
                </Text>

                <Text
                  style={{
                    marginLeft: 10,
                    fontSize: 12,
                    fontFamily: 'NunitoSans-SemiBold',
                    color: Colors.whiteColor,
                  }}>
                  {moment(item.expirationDate).format('dddd, Do MMM YYYY')}
                </Text>
              </View>
            </View>

            <View>
              <Text
                style={{
                  marginLeft: 10,
                  fontSize: 20,
                  fontFamily: 'NunitoSans-Bold',
                  color: Colors.whiteColor,
                }}
                numberOfLines={1}>
                {'RM ' + (item.priceToBuy ? item.priceToBuy.toFixed(2) : '0.00')}
              </Text>

              {/* <Text
                style={{
                  marginTop: 10,
                  marginLeft: 10,
                  fontSize: 12,
                  fontFamily: 'NunitoSans-SemiBold',
                  color: Colors.whiteColor,
                }}>
                Expiry Date:
              </Text>

              <Text
                style={{
                  marginLeft: 10,
                  fontSize: 12,
                  fontFamily: 'NunitoSans-SemiBold',
                  color: Colors.whiteColor,
                }}>
                {moment(item.expirationDate).format('dddd, Do MMM YYYY')}
              </Text> */}
            </View>
          </View>
        </LinearGradient>
      </View>
      <View style={[styles.card1]}>
        <View
          style={{
            flexDirection: 'column',
            justifyContent: 'flex-start',
            padding: 10,

            paddingLeft: 20,
          }}>
          <Text
            style={{
              marginTop: 0,
              fontSize: 14,
              fontFamily: 'NunitoSans-Bold',
              //color: Colors.whiteColor,
            }}>
            Description:
          </Text>
          <Text
            style={{
              marginTop: 0,
              fontSize: 12,
              fontFamily: 'NunitoSans-Regular',
              //color: Colors.whiteColor,
            }}>
            {item.campaignDescription}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  return (
    <>

      <View style={{
        flexDirection: 'row',
        alignItems: 'center',
        alignSelf: 'center',
        justifyContent: 'center',
        marginTop: 10,
        marginBottom: 10,
        width: '100%',
      }}>
        <Icon name="search" size={20} color={Colors.primaryColor} style={{
          position: 'absolute',
          left: windowWidth * 0.017,
          zIndex: 10,
        }} />

        <TextInput
          underlineColorAndroid={Colors.fieldtBgColor}
          clearButtonMode='while-editing'
          placeholder="Search voucher here..."
          style={styles.textInput}
          onChangeText={text => {
            setSearchText(text);
          }}
          value={searchText}
        />
      </View>

      {availableTaggableVouchers && availableTaggableVouchers.length > 0 ? (
        <>
          {availableTaggableVouchers && availableTaggableVouchers.length > 0 && (
            <ScrollView>
              {/* <View style={styles.container1}> */}
              <View style={styles.container}>
                <FlatList
                  data={availableTaggableVouchers.filter((voucher) => {
                    if (searchText !== '') {
                      const searchLowerCase = searchText.toLowerCase();

                      if (
                        voucher.campaignName
                          .toLowerCase()
                          .includes(searchLowerCase) ||
                        voucher.campaignDescription
                          .toLowerCase()
                          .includes(searchLowerCase)
                      ) {
                        return true;
                      }
                    } else {
                      return true;
                    }
                  })}
                  style={{}}
                  //extraData={availableTaggableVouchers}
                  renderItem={renderItem}
                  keyExtractor={(item, index) => String(index)}
                />
              </View>
              {/* </View> */}
            </ScrollView>
          )}
        </>
      ) : (
        <View
          style={{
            justifyContent: 'center',
            alignItems: 'center',
            marginTop: 100,
            alignSelf: 'center',
          }}>
          <Text style={{ fontSize: 14, fontFamily: 'NunitoSans-Regular' }}>
            No buyable vouchers at the moment
          </Text>
          <Ionicons
            name={'sad-outline'}
            size={30}
            style={{ marginTop: 10, marginBottom: 20 }}
          />
          {/* <Text style={{ fontSize: 14, fontFamily: 'NunitoSans-Regular' }}>
            Let us know
          </Text>
          <Text style={{ fontSize: 14, fontFamily: 'NunitoSans-Regular' }}>
            What would you like to recommend
          </Text> */}
          {/* <TouchableOpacity
      style={{
        borderRadius: 15,
        backgroundColor: Colors.primaryColor,
        padding: 15,
        marginTop: 20
      }}
      onPress={() => {
        navigation.navigate('CreateFeedback')
      }}
      >
        <Text style={{ fontSize: 14, fontFamily: 'NunitoSans-Regular', color: Colors.whiteColor }}>Feedback</Text>
    </TouchableOpacity> */}
        </View>
      )}
    </>
  );
};

const styles = StyleSheet.create({
  container1: {
    backgroundColor: '#ffffff',
    // backgroundColor: 'red',
    height: Dimensions.get('window').height,
    //marginBottom: 50
  },
  container: {
    backgroundColor: '#f2f2f2',
    //height: Dimensions.get('window').height,
    padding: 16,
  },
  searchBar: {
    marginHorizontal: 16,
    backgroundColor: Colors.fieldtBgColor,
    flexDirection: 'row',
    padding: 12,
    borderRadius: 10,
    alignContent: 'center',
  },
  searchBarText: {
    color: Colors.fieldtTxtColor,
    marginLeft: 10,
  },
  card: {
    flex: 1,
    minWidth: Styles.width - 64,
    // minWidth: (Styles.width / 2) - 64,
    minHeight: 100,
    // backgroundColor: Colors.primaryColor,
    backgroundColor: '#416D5C',
    flexDirection: 'row',
    marginTop: 10,
    alignContent: 'center',
    alignItems: 'center',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 3,
    },
    shadowOpacity: 0.29,
    shadowRadius: 4.65,

    elevation: 7,
  },
  card1: {
    flex: 1,
    minWidth: Styles.width - 64,
    minHeight: 100,
    backgroundColor: Colors.fieldtBgColor,
    flexDirection: 'row',
    marginBottom: 20,
    alignContent: 'center',
    //alignItems: 'center',
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
    borderTopWidth: 1,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 3,
    },
    shadowOpacity: 0.29,
    shadowRadius: 4.65,

    elevation: 7,
  },
  cornerleft: {
    backgroundColor: 'white',
    height: 30,
    width: 30,
    alignSelf: 'flex-start',
    marginTop: '22%',
    borderTopRightRadius: 50,
  },
  cornerright: {
    backgroundColor: 'white',
    height: 30,
    width: 30,
    alignSelf: 'flex-start',
    borderBottomRightRadius: 50,

    // borderStyle: 'dashed',
    // borderWidth: 1,
    // borderColor: 'black',
  },

  cornerleft1: {
    backgroundColor: 'white',
    height: 30,
    width: 30,
    alignSelf: 'flex-start',
    marginTop: '22%',
    borderTopLeftRadius: 50,
  },
  cornerright1: {
    backgroundColor: 'white',
    height: 30,
    width: 30,
    alignSelf: 'flex-start',
    borderBottomLeftRadius: 50,
  },
  text: {
    fontSize: 13,
    color: Colors.fieldtTxtColor,
    fontFamily: 'NunitoSans-Bold',
  },
  title: {
    color: Colors.whiteColor,
    // fontSize: Platform.OS == 'ios' ? 30 : 20,
    fontSize: Platform.OS == 'ios' ? 30 : 22,
    fontFamily: 'NunitoSans-SemiBold',
    marginLeft: '1%',
    // marginTop: 10,
    marginBottom: 5,
    textAlign: 'right',
  },
  title1: {
    color: Colors.blackColor,
    fontSize: 16,
    fontFamily: 'NunitoSans-Bold',
  },
  title2: {
    color: Colors.primaryColor,
    fontSize: Platform.OS == 'ios' ? 13 : 15,
    fontFamily: 'NunitoSans-SemiBold',
    // fontWeight: 'bold',
    marginBottom: 8,
  },
  title3: {
    color: Colors.descriptionColor,
    fontSize: Platform.OS == 'ios' ? 13 : 14,
    fontFamily: 'NunitoSans-SemiBold',
    // fontWeight: 'bold',
  },
  viewContainer: {
    marginTop: 10,
    justifyContent: 'space-between',
    flexDirection: 'row',
    flex: 10,
  },
  total: {
    fontSize: 14,
    color: Colors.blackColor,
    fontFamily: 'NunitoSans-Bold',
  },
  price: {
    fontSize: 14,
    color: Colors.primaryColor,
    fontFamily: 'NunitoSans-Regular',
  },
  totalBorder: {
    paddingTop: 5,
    justifyContent: 'space-between',
    flex: 1,
    flexDirection: 'row',
    borderTopWidth: StyleSheet.hairlineWidth,
    borderColor: Colors.blackColor,
    borderRadius: 1,
  },
  pic: {
    backgroundColor: Colors.secondaryColor,
    width: 80,
    height: 80,
    borderRadius: 10,
    alignSelf: 'center',
  },
  searchBar1: {
    backgroundColor: Colors.fieldtBgColor,
    flexDirection: 'row',
    padding: 12,
    borderRadius: 10,
    alignContent: 'center',
    marginBottom: '2%',
  },

  centerTextHolder: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  textInput: {
    height: 45,
    paddingHorizontal: 50,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 10,

    color: Colors.fieldtTxtColor,
    fontFamily: "NunitoSans-Regular",
    fontSize: 14,
    lineHeight: 20,

    // backgroundColor: 'blue',
    width: '98%',
  },
});

export default TaggableVoucherListPurchaseScreen;
