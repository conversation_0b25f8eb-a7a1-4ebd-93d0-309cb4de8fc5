const { test, expect } = require('@playwright/test');

test('Add 1 item with 2 quantity w/o variant to Cart Test', async ({ page, isMobile }) => {
    // await page.setViewportSize({ width: 1600, height: 900 }); //modify to adjust window size
    await page.goto('http://localhost:5400/web/outlet/hominsanttdi/takeaway');

    await page.waitForTimeout(3000);
    if (isMobile) {
        await page.evaluate(() => { window.scrollBy(0, 100); });
    }
    else {
        await page.mouse.wheel(0, 100);
    }

    await page.waitForTimeout(500);

    await page.getByTestId('startOrdering').click();

    await page.waitForTimeout(500);
    await page.getByTestId('categoryName-2').click();

    await page.waitForTimeout(500);
    if (isMobile) {
        await page.evaluate(() => {
            // Replace the following code with custom JavaScript code specific to the webpage's scrolling mechanism
            // Example: Scroll a specific element with an ID
            const element = document.getElementById('my-element');
            if (element) {
                element.scrollTop += 100; // Scrolls the element vertically down by 100 units
            }
        });
    }
    else {
        await page.mouse.wheel(0, 100);
    }

    await page.waitForTimeout(500);
    await page.getByTestId('productName-0').click();

    const storeProductName = await page.getByTestId('productName-0');
    const storedName = await storeProductName.textContent();
    await page.waitForTimeout(500);
    await page.getByTestId('menuItemDetailPlus').click();

    await page.waitForTimeout(500);
    const storeProductQuantity = await page.getByTestId('menuItemDetailQuantity');
    const storedQuantity = await storeProductQuantity.textContent();

    await page.getByTestId('addToCart').click();

    await page.waitForTimeout(500);
    await page.getByTestId('cartIcon').click();

    await page.waitForTimeout(500);

    const cartProductName = await page.getByTestId('cartProductName-0');
    const storedCartName = await cartProductName.textContent();

    const cartProductQuantity = await page.getByTestId('cartQuantity-0');
    const storedCartQuantity = await cartProductQuantity.textContent();

    if (storedName === storedCartName && storedQuantity === storedCartQuantity) {
        console.log('match with order product passed')
    }

});