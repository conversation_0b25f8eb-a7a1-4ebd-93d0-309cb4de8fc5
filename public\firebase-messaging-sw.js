// Scripts for firebase and firebase messaging
importScripts("https://www.gstatic.com/firebasejs/8.2.0/firebase-app.js");
importScripts("https://www.gstatic.com/firebasejs/8.2.0/firebase-messaging.js");

// Initialize the Firebase app in the service worker by passing the generated config
// const firebaseConfig = {
//   apiKey: "AIzaSyB_nKKIGIcEJ6ffsVBdYeIstW8KekjVXa8",
//   authDomain: "saasdev-56bf0.firebaseapp.com",
//   databaseURL: "https://saasdev-56bf0.firebaseio.com",
//   projectId: "saasdev-56bf0",
//   storageBucket: "saasdev-56bf0.appspot.com",
//   messagingSenderId: "1095286986673",
//   appId: "1:1095286986673:web:41aec1e67c556cb8e14f90",
// };

function fetchFirebaseConfig() {
  return fetch('./config/firebase-web.json')
    .then(response => {
      if (!response.ok) {
        throw new Error('Failed to fetch firebase-config.json. HTTP status: ' + response.status);
      }
      return response.json();
    })
    .then(firebaseConfig => {
      console.log("firebase-config.json fetched successfully");

      firebase.initializeApp(firebaseConfig);

      // Retrieve firebase messaging
      // const messaging = firebase.messaging();

      let messaging = null;
      if (
        firebase &&
        firebase.messaging.isSupported() &&
        typeof firebase.messaging.onMessage === 'function'
      ) {
        messaging = firebase.messaging;

        messaging.onMessage(function (payload) {
          console.log("Received foreground message ", payload);
        });

        messaging.onBackgroundMessage(function (payload) {
          console.log("Received background message ", payload);

          if (payload && payload.data &&
            payload.data.ti &&
            payload.data.bo) {
            const notificationTitle = payload.data.ti;
            const notificationOptions = {
              body: payload.data.bo,
            };

            global.newsw.showNotification(notificationTitle, notificationOptions);
          }
        });
      }
    })
    .catch(error => {
      console.log("Error fetching firebase-config.json:", error);
    });
}

fetchFirebaseConfig();