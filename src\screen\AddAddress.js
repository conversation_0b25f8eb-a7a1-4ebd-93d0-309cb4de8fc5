import React, { Component, useState, useEffect } from "react";

import {
  StyleSheet,
  ScrollView,
  Image,
  View,
  Text,
  TouchableOpacity,
  Alert,
  TextInput,
} from "react-native";
import Colors from "../constant/Colors";
import ApiClient from "../util/ApiClient";
import API from "../constant/API";
import Octicons from "react-native-vector-icons/Octicons";
import Entypo from "react-native-vector-icons/Entypo";
import AntDesign from "react-native-vector-icons/AntDesign";
import Ionicons from "react-native-vector-icons/Ionicons";
import Styles from "../constant/Styles";
import * as Cart from "../util/Cart";
import * as User from "../util/User";
import Icon from "react-native-vector-icons/MaterialIcons";
// import { FlatList } from 'react-native-gesture-handler';
import moment from "moment";
import Icons from "react-native-vector-icons/EvilIcons";
// import DateTimePickerModal from 'react-native-modal-datetime-picker';
import { CommonStore } from "../store/commonStore";
import { prefix } from "../constant/env";
import { CommonActions, useLinkTo } from "@react-navigation/native";
import { DataStore } from "../store/dataStore";
import AsyncStorage from "@react-native-async-storage/async-storage";

/**
 * AddAdress
 * function
 * *fill in address to be added
 *
 * route.params
 * *test: indicate the type of address to be added (Home, Work or Other)
 * *address: the address selected from GooglePlaceAutocomplete
 * *name: name of the address label, only available to Other address
 * *location: the lat and lng of the address
 */

const AddAddress = (props) => {
  const { navigation, route } = props;

  const linkTo = useLinkTo();

  const testParam = route.params.test;
  const addressParam = route.params.address;
  const nameParam = route.params.name;
  const locationParam = route.params.location;

  const [test, setTest] = useState(testParam);
  const [address, setAddress] = useState(addressParam);
  const [name, setName] = useState(nameParam);
  const [location, setLocation] = useState(locationParam);
  const [details, setDetails] = useState("");
  const [note, setNote] = useState("");
  const [userName, setUserName] = useState("");
  const [userPhone, setUserPhone] = useState("");

  const selectedOutlet = CommonStore.useState((s) => s.selectedOutlet);

  const selectedAddressType = CommonStore.useState(
    (s) => s.selectedAddressType
  );

  const selectedAddress = CommonStore.useState((s) => s.selectedAddress);

  const linkToFunc = DataStore.useState((s) => s.linkToFunc);

  const setState = () => { };

  // navigation.setOptions({
  //   headerBackTitle: 'Back',
  //   headerRight: () => (
  //     <View style={{ marginRight: 15 }}></View>
  //   ),
  //   headerTitle: () => (
  //     <View style={{ justifyContent: 'center', alignItems: 'center', marginTop: '1%' }}>
  //       <Text
  //         style={{
  //           fontWeight: 'bold',
  //           fontSize: 20,
  //           textAlign: 'center',
  //         }}>
  //         Add to Saved Places
  //         </Text>
  //     </View>
  //   ),
  // });

  navigation.setOptions({
    headerLeft: () => (
      <TouchableOpacity
        style={{}}
        onPress={async () => {
          // props.navigation.goBack();
          const subdomain = await AsyncStorage.getItem("latestSubdomain");
          global.linkToFunc && global.linkToFunc(`${prefix}/outlet/${subdomain}/cart`);
        }}
      >
        <View
          style={{
            marginLeft: 10,
            display: "flex",
            flexDirection: "row",
            alignItems: "center",
            justifyContent: "flex-start",
          }}
        >
          <Ionicons
            name="chevron-back"
            size={26}
            color={Colors.fieldtTxtColor}
            style={{}}
          />

          <Text
            style={{
              color: Colors.fieldtTxtColor,
              fontSize: 16,
              textAlign: "center",
              fontFamily: "NunitoSans-Regular",
              lineHeight: 22,
              marginTop: -1,
            }}
          >
            Back
          </Text>
        </View>
      </TouchableOpacity>
    ),
    headerRight: () => (
      <TouchableOpacity
        onPress={() => {
          // props.navigation.navigate('Profile')
        }}
        style={{}}
      >
        <View style={{ marginRight: 15 }}>
          <Ionicons name="menu" size={30} color={Colors.primaryColor} />
        </View>
      </TouchableOpacity>
    ),
    headerTitle: () => (
      <View
        style={{
          justifyContent: "center",
          alignItems: "center",
          bottom: -1,
        }}
      >
        <Text
          style={{
            fontSize: 20,
            lineHeight: 25,
            textAlign: "center",
            fontFamily: "NunitoSans-Bold",
            color: Colors.mainTxtColor,
          }}
        >
          Add To Saved Places
        </Text>
      </View>
    ),
  });

  // function here
  const check = () => {
    if (test == 1) {
      return "Home";
    } else if (test == 2) {
      return "Work";
    } else {
      return "New";
    }
  };

  const createAddress = () => {
    // var label = check() == 'New' ? name : check()
    // var body = {
    //   userId: User.getUserId(),
    //   address: address,
    //   // type: label,
    //   type: selectedAddressType,
    //   details: details,
    //   note: note,
    //   lat: location.lat(),
    //   lng: location.lng(),
    // };
    // console.log(body, 'here');
    // ApiClient.POST(API.createUserAddress, body).then((result) => {
    //   console.log("RESLT: ", result);
    //   if (result !== null) {
    //     console.log(selectedOutlet)
    //     let text = "Successful\nNew address has been added";
    //     if (window.confirm(text) == true) {
    //       // linkTo &&
    //       //   linkTo(`${prefix}/outlet/${selectedOutlet.shortcode}/address`);
    //     } else {
    //       // linkTo &&
    //         // linkTo(`${prefix}/outlet/${selectedOutlet.shortcode}/address`);
    //     }
    // window.confirm(
    //   "Successful\nNew address has been added"
    // [
    //   { text: "OK", onPress: () => {
    //     // navigation.navigate('Address - KooDoo Web Order', { testing: test });
    //     console.log("HEREHREHREHRHE")
    //     linkTo && linkTo(`${prefix}/outlet/address`)
    //   }
    // }
    // ],
    // { cancelable: false },
    // );
    // alert('Successful\nNew address has been added')
    // props.navigation.navigate('Address - KooDoo Web Order', { testing: test });
    // linkTo && linkTo(`${prefix}/outlet/${selectedOutlet.shortcode}/address`)
    // }
    // });
  };
  // function end

  return (
    <View style={{ flex: 1 }}>
      <ScrollView style={{ backgroundColor: Colors.whiteColor }}>
        <View style={{ width: "90%", alignSelf: "center", marginTop: 20 }}>
          <View>
            <Text
              style={{
                // fontWeight: "400",
                fontSize: 17,
                fontFamily: "NunitoSans-Bold",
              }}
            >
              *Name
            </Text>

            <TextInput
              underlineColorAndroid={Colors.fieldtBgColor}
              clearButtonMode="while-editing"
              autoFocus={true}
              placeholder={check()}
              style={styles.textInput}
              onChangeText={(text) => setState({ name: text })}
              value={name}
            />
            <Text
              style={{
                fontSize: 15,
                color: "#aba9a9",
                fontFamily: "NunitoSans-Regular",
                marginTop: 10,
                marginLeft: 5,
                marginBottom: 15,
              }}
            >
              Label this address for easy reference
            </Text>
          </View>

          <View
            style={{
              marginTop: 10,
              marginBottom: 15,
            }}
          >
            <Text
              style={{
                // fontWeight: "400",
                fontSize: 17,
                fontFamily: "NunitoSans-Bold",
              }}
            >
              *Address
            </Text>

            <TextInput
              underlineColorAndroid={Colors.fieldtBgColor}
              clearButtonMode="while-editing"
              autoFocus={true}
              placeholder="📍  KL Eco City, 3 Jalan Bangsar, Kuala Lumpur, Wilayah....."
              style={styles.textInput1}
              onChangeText={(text) => setState({ address: text })}
              value={address}
            />
          </View>

          <View
            style={{
              marginTop: 10,
              marginBottom: 15,
            }}
          >
            <Text
              style={{
                // fontWeight: "400",
                fontSize: 17,
                fontFamily: "NunitoSans-Bold",
              }}
            >
              Address Details
            </Text>

            <TextInput
              underlineColorAndroid={Colors.fieldtBgColor}
              clearButtonMode="while-editing"
              autoFocus={true}
              placeholder="e.g floor, unit number"
              style={styles.textInput1}
              onChangeText={(text) => setDetails(text)}
              value={details}
            />
          </View>

          <View
            style={{
              marginTop: 10,
            }}
          >
            <Text
              style={{
                // fontWeight: "400",
                fontSize: 17,
                fontFamily: "NunitoSans-Bold",
              }}
            >
              Note To Driver
            </Text>

            <TextInput
              underlineColorAndroid={Colors.fieldtBgColor}
              clearButtonMode="while-editing"
              autoFocus={true}
              placeholder="e.g meet me at the lobby"
              style={styles.textInput1}
              onChangeText={(text) => setNote(text)}
              value={note}
            />
            <Text
              style={{
                fontSize: 15,
                color: "#aba9a9",
                fontFamily: "NunitoSans-Regular",
                marginTop: 10,
                marginLeft: 5,
                marginBottom: 15,
              }}
            >
              Put delivery instruction or directions here
            </Text>
          </View>

          <View
            style={{
              marginTop: 10,
            }}
          >
            <Text
              style={{
                // fontWeight: "400",
                fontSize: 17,
                fontFamily: "NunitoSans-Bold",
              }}
            >
              Receiver Name
            </Text>

            <TextInput
              underlineColorAndroid={Colors.fieldtBgColor}
              clearButtonMode="while-editing"
              autoFocus={true}
              placeholder="e.g John"
              style={styles.textInput1}
              onChangeText={(text) => setUserName(text)}
              value={userName}
            />
            <Text
              style={{
                fontSize: 15,
                color: "#aba9a9",
                fontFamily: "NunitoSans-Regular",
                marginTop: 10,
                marginLeft: 5,
                marginBottom: 15,
              }}
            >
              Put your name here
            </Text>
          </View>

          <View
            style={{
              marginTop: 10,
            }}
          >
            <Text
              style={{
                // fontWeight: "400",
                fontSize: 17,
                fontFamily: "NunitoSans-Bold",
              }}
            >
              Phone Number
            </Text>

            <TextInput
              underlineColorAndroid={Colors.fieldtBgColor}
              clearButtonMode="while-editing"
              autoFocus={true}
              placeholder="e.g 012-3456789"
              style={styles.textInput1}
              onChangeText={(text) => setUserPhone(text)}
              value={userPhone}
              keyboardType="decimal-pad"
            />
            <Text
              style={{
                fontSize: 15,
                color: "#aba9a9",
                fontFamily: "NunitoSans-Regular",
                marginTop: 10,
                marginLeft: 5,
                marginBottom: 15,
              }}
            >
              Put your phone number here
            </Text>
          </View>

          <TouchableOpacity
            style={{
              marginTop: 30,
              backgroundColor: Colors.primaryColor,
              width: "90%",
              // height: 60,
              alignSelf: "center",
              alignItems: "center",
              borderRadius: 10,
              justifyContent: "center",

              // marginHorizontal: 48,
              // marginTop: 24,
              // marginBottom: 24,
              padding: 20,
              paddingVertical: 16,
            }}
            onPress={() => {
              // createAddress();

              if (userName && userPhone) {
                var userPhoneParsed = '';

                if (userPhone.replaceAll) {
                  userPhoneParsed = userPhone.replaceAll('-', '');
                }

                CommonStore.update((s) => {
                  s.selectedAddress = address;
                  s.selectedAddressDetails = details;
                  s.selectedAddressNote = note;

                  s.selectedAddressLocation = {
                    lat: locationParam.lat,
                    lng: locationParam.lng,
                  };

                  s.selectedAddressUserName = userName;
                  s.selectedAddressUserPhone = userPhoneParsed;
                });
                linkTo &&
                  linkTo(`${prefix}/outlet/${selectedOutlet.shortcode}/cart`);
              }
              else {
                window.confirm(
                  "Info\nReceiver name and phone number cannot be empty."
                );
              }
            }}
          >
            <Text
              style={{
                color: "#ffffff",
                fontSize: 21,
                fontFamily: "NunitoSans-Regular",
              }}
            >
              SAVE ADDRESS
            </Text>
          </TouchableOpacity>
        </View>

        {/* <View style={{ height: 120 }}></View> */}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: "#ffffff",
    height: 70,
    alignSelf: "center",
    justifyContent: "center",
    width: "90%",
    borderBottomWidth: StyleSheet.hairlineWidth,
  },
  searchBar: {
    backgroundColor: Colors.fieldtBgColor,
    flexDirection: "row",
    padding: 12,
    borderRadius: 10,
    alignContent: "center",
  },
  searchBarText: {
    color: Colors.fieldtTxtColor,
    width: "80%",
  },
  BarText: {
    color: Colors.primaryColor,
    marginLeft: 10,
    fontSize: 15,
    fontWeight: "bold",
    textAlign: "center",
  },

  text: {
    fontSize: 15,
    fontWeight: "700",
  },
  text1: {
    fontSize: 13,
    fontWeight: "700",
    marginTop: "2%",
  },
  textInput: {
    // height: 60,
    // paddingVertical: 10,
    paddingLeft: 15,
    paddingRight: 15,
    width: "100%",
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    marginTop: 8,
    fontSize: 15,
    borderWidth: StyleSheet.hairlineWidth,
    borderColor: "#dedede",
  },
  textInput1: {
    // height: 60,
    paddingLeft: 15,
    paddingRight: 15,
    width: "100%",
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    marginTop: 8,
    fontSize: 15,
    borderWidth: StyleSheet.hairlineWidth,
    borderColor: "#dedede",
  },
  text2: {
    fontSize: 24,
    fontWeight: "700",
    marginTop: "8%",
  },
  title: {
    color: Colors.blackColor,
    fontSize: 20,
    fontWeight: "bold",
  },
  viewContainer: {
    marginTop: 10,
    justifyContent: "space-between",
    flexDirection: "row",
    flex: 10,
  },
  total: {
    fontSize: 14,
    color: Colors.blackColor,
    fontWeight: "700",
  },
  price: {
    fontSize: 14,
    color: Colors.primaryColor,
    fontWeight: "700",
  },
  totalBorder: {
    paddingTop: 5,
    justifyContent: "space-between",
    flex: 1,
    flexDirection: "row",
    borderTopWidth: StyleSheet.hairlineWidth,
    borderColor: Colors.blackColor,
    borderRadius: 1,
  },
  pic: {
    width: 90,
    height: 90,
    borderRadius: 10,
    marginBottom: 5,
    alignItems: "center",
    justifyContent: "center",
  },
  Bar1: {
    flexDirection: "row",
    padding: 12,
    borderRadius: 10,
    alignContent: "center",
    marginBottom: "2%",
  },
});
export default AddAddress;
