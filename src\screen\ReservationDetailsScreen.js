import React, { useState, Component, useEffect } from 'react';
import { StyleSheet, ScrollView, Image, Text, View, TextInput, TouchableOpacity, Alert, Modal, KeyboardAvoidingView, Dimensions, ActivityIndicator, useWindowDimensions, TouchableHighlight, Linking } from 'react-native';
// import firebase from 'firebase';
import { getFirestore, collection, query, where, getDocs, limit, updateDoc, increment, FieldValue, doc } from "firebase/firestore";
import { signInAnonymously, signInWithCustomToken } from "firebase/auth";
import AsyncStorage from '@react-native-async-storage/async-storage';
//import EvilIcons from 'react-native-vector-icons/EvilIcons';
import Colors from '../constant/Colors';
import Styles from '../constant/Styles';
import API from '../constant/API';
import { CommonStore } from '../store/commonStore';
import { UserStore } from '../store/userStore';
import ApiClient from '../util/ApiClient';
// import AwesomeAlert from 'react-native-awesome-alerts';
import { useLinkTo, useRoute } from "@react-navigation/native";

import Ionicons from "react-native-vector-icons/Ionicons";
import AntDesign from 'react-native-vector-icons/AntDesign';
import DatePicker2 from "react-horizontal-datepicker";
//import DatePicker from "react-native-datepicker";
// import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
// import "../constant/datePicker.css";
import moment, { now } from "moment";

import { ReactComponent as Location } from '../svg/location.svg';
import { ReactComponent as Clock } from '../svg/clock.svg';
import { ReactComponent as Call } from '../svg/call.svg';
import { ReactComponent as Bell } from '../svg/bell.svg';
import { ReactComponent as Cldr } from '../svg/calendar.svg';
//import { ReactComponent as ChevronD } from '../svg/chevrondown.svg';

import imgLogo from '../asset/image/logo.png';
import {
  isMobile,
  //sendReservationEmail 
} from '../util/commonFuncs';
import { DataStore } from '../store/dataStore';
// import { color, set } from 'react-native-reanimated';
import { Picker } from 'react-native-web';

//import Icon from "react-native-vector-icons/MaterialIcons";

//import Dropdown from 'react-dropdown';

// import DayPickerInput from 'react-day-picker/DayPickerInput';
// import { DateUtils } from 'react-day-picker';
// import 'react-day-picker/lib/style.css';
import dateFnsFormat from 'date-fns/format';
import dateFnsParse from 'date-fns/parse';
import { TempStore } from '../store/tempStore';
import { ORDER_TYPE, ORDER_REGISTER_QR_SALT } from '../constant/common';
import { prefix } from '../constant/env';
import Select from 'react-select';
import loadable from '@loadable/component';
import { TableStore } from '../store/tableStore';
import { PaymentStore } from '../store/paymentStore';

import Hashids from 'hashids';
import { Collections } from '../constant/firebase';
import { idbGet } from '../util/db';

const hashids = new Hashids(ORDER_REGISTER_QR_SALT);

// const SelectLoadable = loadable.lib(() => import("react-select"));

const ReservationDetailsScreen = props => {
  const {
    route,
  } = props;

  console.log('render scan!');

  console.log('route');
  console.log(route);

  // this.goToLoginState = this.goToLoginState.bind(this);

  const linkTo = useLinkTo();
  // const windowDimensions = useWindowDimensions();
  const {
    width: windowWidth,
    height: windowHeight,
  } = useWindowDimensions();

  const isPlacingReservation = CommonStore.useState(s => s.isPlacingReservation);

  const reservationUpsellingItems = CommonStore.useState(s => s.reservationUpsellingItems);
  const upsellingCampaignsReservation = CommonStore.useState(s => s.upsellingCampaignsReservation);

  const selectedReservationId = CommonStore.useState(s => s.selectedReservationId);
  const selectedReservationStartTime = CommonStore.useState(s => s.selectedReservationStartTime);
  const selectedReservationPax = CommonStore.useState(s => s.selectedReservationPax);
  const selectedReservationPaxPrev = CommonStore.useState(s => s.selectedReservationPaxPrev);
  const selectedReservationStartTimePrev = CommonStore.useState(s => s.selectedReservationStartTimePrev);

  const selectedReservationOutletSectionId = CommonStore.useState(s => s.selectedReservationOutletSectionId);
  const selectedReservationOutletSectionName = CommonStore.useState(s => s.selectedReservationOutletSectionName);

  const selectedOutlet = CommonStore.useState(s => s.selectedOutlet);
  const selectedMerchant = CommonStore.useState(s => s.selectedMerchant);

  const userPhone = CommonStore.useState(s => s.selectedAddressUserPhone);
  const userEmail = CommonStore.useState(s => s.selectedUserEmail);
  const userFirstName = CommonStore.useState(s => s.selectedUserFirstName);
  const userLastName = CommonStore.useState(s => s.selectedUserLastName);
  const userRemarks = CommonStore.useState(s => s.selectedUserRemarks);
  const dRestrictions = CommonStore.useState(s => s.selectedUserDiet);
  const sOccasions = CommonStore.useState(s => s.selectedUserOccasion);

  const merchantAddress = CommonStore.useState(s => s.selectedAddress);

  const cartItems = CommonStore.useState(s => s.cartItems);

  const [firstName, setFirstName] = useState(userFirstName == null ? '' : userFirstName);
  const [lastName, setLastName] = useState(userLastName == null ? '' : userLastName);
  const [phoneNum, setPhoneNum] = useState(userPhone == null ? '' : userPhone);
  const [email, setEmail] = useState(userEmail == null ? '' : userEmail);
  const [remarks, setRemarks] = useState(userRemarks == null ? '' : userRemarks);
  const [dietaryRestrictions, setDietaryRestrictions] = useState(dRestrictions == null ? '' : dRestrictions);
  const [specialOccasions, setSpecialOccasions] = useState(sOccasions == null ? '' : sOccasions);

  const [showDesposit, setShowDeposit] = useState(false);
  const [depositRequired, setdepositRequired] = useState(true); //for testing ui purpose only

  // const [showConfirmation, setShowConfirmation] = useState(false);
  const [resId, setResId] = useState('');
  const [agree, setAgree] = useState(false);

  const resShowConfirmationPage = CommonStore.useState(s => s.resShowConfirmationPage);

  const reservationConfig = CommonStore.useState((s) => s.reservationConfig);

  useEffect(() => {
    if (linkTo) {
      DataStore.update(s => {
        s.linkToFunc = linkTo;
      });

      global.linkToFunc = linkTo;
    }

    if (route.params === undefined ||
      route.params.subdomain === undefined) {
      linkTo && linkTo(`${prefix}/error`);
    } else {
      CommonStore.update(s => {
        s.isLoading = true;
      });

      const subdomain = route.params.subdomain;

      if (subdomain) {
        // means all got, can login this user to check all info valid or not

        try {
          // firebase
          //   .auth()
          //   .signInAnonymously()
          signInAnonymously(global.auth)
            .then((result) => {
              // TempStore.update(s => {
              //   s.firebaseAuth = true;
              // });

              const firebaseUid = result.user.uid;

              ApiClient.GET(API.getTokenKWeb + firebaseUid).then(
                async (result) => {
                  console.log("getTokenKWeb");
                  console.log(result);

                  if (result && result.token) {
                    await AsyncStorage.setItem("accessToken", result.token);
                    await AsyncStorage.setItem(
                      "refreshToken",
                      result.refreshToken
                    );

                    global.accessToken = result.token;

                    UserStore.update((s) => {
                      s.firebaseUid = result.userId;
                      s.userId = result.userId;
                      s.role = result.role;
                      s.refreshToken = result.refreshToken;
                      s.token = result.token;
                      s.name = '';
                      s.email = '';
                      s.number = '';
                    });

                    var outletSnapshot = null;

                    if (subdomain) {
                      if (subdomain === "192") {
                        // outletSnapshot = await firebase
                        //   .firestore()
                        //   .collection(Collections.Outlet)
                        //   .where(
                        //     "uniqueId",
                        //     "==",
                        //     "b422c1d9-d30b-4de7-ad49-2e601d950919"
                        //   )
                        //   .limit(1)
                        //   .get();

                        outletSnapshot = await getDocs(
                          query(
                            collection(global.db, Collections.Outlet),
                            where(
                              "uniqueId",
                              "==",
                              "b422c1d9-d30b-4de7-ad49-2e601d950919"
                            ),
                            limit(1),
                          )
                        );
                      } else {
                        // outletSnapshot = await firebase
                        //   .firestore()
                        //   .collection(Collections.Outlet)
                        //   .where("subdomain", "==", subdomain)
                        //   .limit(1)
                        //   .get();

                        outletSnapshot = await getDocs(
                          query(
                            collection(global.db, Collections.Outlet),
                            where("subdomain", "==", subdomain),
                            limit(1),
                          )
                        );
                      }
                    } else {
                      CommonStore.update(s => {
                        s.isLoading = false;
                      });

                      console.log("web scan 3");
                      linkTo && linkTo(`${prefix}/error`);
                    }

                    var outlet = {};
                    if (!outletSnapshot.empty) {
                      outlet = outletSnapshot.docs[0].data();
                    }

                    if (
                      outlet &&
                      (outlet.subdomain === subdomain || subdomain === "192")
                    ) {
                      // show pax modal before proceed

                      await AsyncStorage.setItem(
                        "latestOutletId",
                        outlet.uniqueId
                      );
                      await AsyncStorage.setItem("latestSubdomain", subdomain);

                      document.title = outlet.name;
                      document.getElementsByTagName("META")[2].content =
                        outlet.address;

                      CommonStore.update(
                        (s) => {
                          s.selectedOutlet = outlet;
                        }
                      );

                      ////////////////////////////////////////

                      // 2023-06-07 - Get reservation obj (if got)

                      if (route.params.reservationId) {
                        var reservationId = hashids.decodeHex(route.params.reservationId).toString().insert(8, '-').insert(13, '-').insert(18, '-').insert(23, '-');

                        // const userReservationSnapshot = await firebase.firestore().collection(Collections.UserReservation)
                        //     .where('uniqueId', '==', reservationId)
                        //     .limit(1)
                        //     .get();

                        const userReservationSnapshot = await getDocs(
                          query(
                            collection(global.db, Collections.UserReservation),
                            where('uniqueId', '==', reservationId),
                            limit(1),
                          )
                        );

                        var userReservation = null;

                        if (!userReservationSnapshot.empty) {
                          userReservation = userReservationSnapshot.docs[0].data();
                        }

                        if (userReservation) {
                          CommonStore.update(s => {
                            // s.currPage = 'Reservation';

                            s.selectedReservationId = userReservation.uniqueId;

                            ///////////////////////////////////////////////////////////////

                            // s.selectedReservationStartTime = userReservation.reservationTime;
                            // s.selectedReservationPax = typeof userReservation.pax === 'number' ? userReservation.pax.toFixed(0) : '1';

                            // s.editUserReservation = userReservation;

                            // s.selectedAddressUserPhone = userReservation.userPhone;
                            // s.selectedUserEmail = userReservation.userEmail;
                            // s.selectedUserFirstName = userReservation.userFirstName;
                            // s.selectedUserLastName = userReservation.userLastName;
                            // s.selectedUserRemarks = userReservation.remarks;
                            // s.selectedUserDiet = userReservation.dRestrictions;
                            // s.selectedUserOccasion = userReservation.sOccasions;

                            ///////////////////////////////////////////////////////////////

                            // s.resFirstName = '';
                            // s.resLastName = '';
                            // s.resPhoneNum = '';
                            // s.resEmail = '';
                            // s.resRemarks = '';
                            // s.resDietaryRestrictions = '';
                            // s.resSpecialOccasions = '';

                            s.isPlacingReservation = false;
                            s.isPlacingReservationUpselling = false;

                            s.isDepositOnly = false;

                            // s.resShowConfirmationPage = false;
                          });
                        }
                      }
                      else {
                        CommonStore.update(s => {
                          // s.currPage = 'Reservation';

                          s.selectedReservationId = '';
                          // s.selectedReservationStartTime = null;
                          // s.selectedReservationPax = '1';

                          s.editUserReservation = {};

                          // s.reservationIdTemp = '';

                          // s.selectedAddressUserPhone = '';
                          // s.selectedUserEmail = '';
                          // s.selectedUserFirstName = '';
                          // s.selectedUserLastName = '';
                          // s.selectedUserRemarks = '';
                          // s.selectedUserDiet = '';
                          // s.selectedUserOccasion = '';

                          // s.resFirstName = '';
                          // s.resLastName = '';
                          // s.resPhoneNum = '';
                          // s.resEmail = '';
                          // s.resRemarks = '';
                          // s.resDietaryRestrictions = '';
                          // s.resSpecialOccasions = '';

                          s.isPlacingReservation = false;
                          s.isPlacingReservationUpselling = false;

                          s.isDepositOnly = false;

                          // s.resShowConfirmationPage = false;
                        });
                      }

                      ////////////////////////////////////////

                      // end

                      CommonStore.update(s => {
                        s.isLoading = false;
                      });

                      ////////////////////////////////////////
                    } else {
                      CommonStore.update(s => {
                        s.isLoading = false;
                      });

                      console.log("web scan 3");
                      linkTo && linkTo(`${prefix}/error`);
                    }
                  } else {
                    CommonStore.update((s) => {
                      s.alertObj = {
                        title: "Error",
                        message: "Unauthorized access",
                      };

                      s.isLoading = false;
                    });

                    console.log("web scan 4");
                    linkTo && linkTo(`${prefix}/error`);
                  }
                }
              ).catch(ex => {
                console.error(ex);

                CommonStore.update(s => {
                  s.isLoading = false;
                });
              });
            }).catch(ex => {
              console.error(ex);

              CommonStore.update(s => {
                s.isLoading = false;
              });
            });
        }
        catch (ex) {
          console.error(ex);
        }
      } else {
        CommonStore.update(s => {
          s.isLoading = false;
        });

        linkTo && linkTo(`${prefix}/error`);
      }
    }
  }, [linkTo, route]);

  useEffect(() => {
    CommonStore.update(s => {
      s.routeName = route.name;
    });
  }, [route]);

  useEffect(() => {
    global.currPageStack = [
      ...global.currPageStack,
      'RerservationDetails',
    ];
  }, []);

  useEffect(() => {
    setFirstName(userFirstName == null ? '' : userFirstName);
    setLastName(userLastName == null ? '' : userLastName);
    setPhoneNum(userPhone == null ? '' : userPhone);
    setEmail(userEmail == null ? '' : userEmail);
    setRemarks(userRemarks == null ? '' : userRemarks);
    setDietaryRestrictions(dRestrictions == null ? '' : dRestrictions);
    setSpecialOccasions(sOccasions == null ? '' : sOccasions);
  }, [userFirstName, userLastName, userPhone, userEmail, userRemarks, dRestrictions, sOccasions]);

  useEffect(() => {
    CommonStore.update(s => {
      s.currPage = 'ReservationDetails';
    });

    const parent = props.navigation.dangerouslyGetParent();
    parent.setOptions({
      tabBarVisible: false,
    });
    return () =>
      parent.setOptions({
        tabBarVisible: true,
      });
  }, []);

  useEffect(() => {
    if (selectedOutlet === null) {
      readStates();
    }

    readCommonStates();
  }, [selectedOutlet]);

  const readStates = async () => {
    console.log('global.selectedoutlet = readStates (1) (==test==)');

    if (selectedOutlet === null) {
      console.log('global.selectedoutlet = readStates (2) (==test==)');

      // const commonStoreDataRaw = await AsyncStorage.getItem("@commonStore");
      const commonStoreDataRaw = await idbGet('@commonStore');
      if (commonStoreDataRaw !== undefined) {
        console.log('global.selectedoutlet = readStates (3) (==test==)');

        const commonStoreData = JSON.parse(commonStoreDataRaw);

        const latestOutletId = await AsyncStorage.getItem("latestOutletId");

        console.log('latestOutletId');
        console.log(latestOutletId);
        console.log('commonStoreData.selectedOutlet');
        console.log(commonStoreData.selectedOutlet);

        if (
          commonStoreData.selectedOutlet &&
          latestOutletId === commonStoreData.selectedOutlet.uniqueId
        ) {
          // check if it's the same outlet user scanned

          console.log('global.selectedoutlet = readStates (4) (==test==)');

          if (isPlacingReservation) {
          } else {
            // if (
            //   commonStoreData.orderType === ORDER_TYPE.DINEIN 
            //   // 2022-10-08 - Try to disable this
            //   // &&
            //   // commonStoreData.userCart.uniqueId === undefined
            // ) {
            //   // logout the user

            //   linkTo && linkTo(`${prefix}/scan`);
            // }
          }

          console.log('global.selectedoutlet = commonStoreData.selectedOutlet (3) (==test==)');

          global.selectedOutlet = commonStoreData.selectedOutlet;

          CommonStore.replace(commonStoreData);

          // const userStoreDataRaw = await AsyncStorage.getItem("@userStore");
          const userStoreDataRaw = await idbGet('@userStore');
          if (userStoreDataRaw !== undefined) {
            const userStoreData = JSON.parse(userStoreDataRaw);

            UserStore.replace(userStoreData);
          }

          // const dataStoreDataRaw = await AsyncStorage.getItem('@dataStore');
          const dataStoreDataRaw = await idbGet('@dataStore');
          if (dataStoreDataRaw !== undefined) {
            const dataStoreData = JSON.parse(dataStoreDataRaw);

            DataStore.replace(dataStoreData);
            // DataStore.replace({
            //   ...dataStoreData,
            //   ...dataStoreData.linkToFunc !== undefined && {
            //     linkToFunc: dataStoreData.linkToFunc,
            //   },
            // });
          }

          // const tableStoreDataRaw = await AsyncStorage.getItem("@tableStore");
          const tableStoreDataRaw = await idbGet('@tableStore');
          if (tableStoreDataRaw !== undefined) {
            const tableStoreData = JSON.parse(tableStoreDataRaw);

            TableStore.replace(tableStoreData);
          }

          // const paymentStoreDataRaw = await AsyncStorage.getItem("@paymentStore");
          const paymentStoreDataRaw = await idbGet('@paymentStore');
          if (paymentStoreDataRaw !== undefined) {
            const paymentStoreData = JSON.parse(paymentStoreDataRaw);

            PaymentStore.replace(paymentStoreData);
          }

          // const dataStoreDataRaw = await AsyncStorage.getItem('@dataStore');
          // if (dataStoreDataRaw !== undefined) {
          //   const dataStoreData = JSON.parse(dataStoreDataRaw);

          //   DataStore.replace(dataStoreData);
          // }
        }
      }
    }
  };

  const readCommonStates = async () => {
    // if (!linkToFunc) {
    //   const dataStoreDataRaw = await AsyncStorage.getItem('@dataStore');
    //   if (dataStoreDataRaw !== undefined) {
    //     const dataStoreData = JSON.parse(dataStoreDataRaw);
    //     DataStore.replace(dataStoreData);
    //   }
    // }
  };

  // useEffect(() => {
  //   firebase.auth().onAuthStateChanged((user) => {
  //     if (user) {
  //       // User is signed in, see docs for a list of available properties
  //       // https://firebase.google.com/docs/reference/js/firebase.User

  //       console.log('auth changed!');
  //       console.log(user);

  //       var uid = user.uid;
  //       // ...                
  //     } else {
  //       // User is signed out
  //       // ...
  //     }
  //   });
  // }, []);

  // function here

  const responseGoogle = e => {
    console.log(e);

    if (e && e.profileObj && e.profileObj.email) {
      const domainName = e.profileObj.email.slice(e.profileObj.email.indexOf('@') + 1);

      console.log(domainName);

      if (domainName === 'mykoodoo.com' || domainName === 'perksense.com') {
        console.log('valid');

        CommonStore.update(s => {
          s.isAuthenticating = true;
        });

        UserStore.update(s => {
          s.email = e.profileObj.email;
          s.name = e.profileObj.name;
          s.googleId = e.profileObj.googleId;
          s.imageUrl = e.profileObj.imageUrl;

          s.tokenId = e.tokenId;
        });

        try {
          // firebase.auth().signInAnonymously()
          signInAnonymously(global.auth)
            .then((result) => {
              // Signed in..

              console.log('signed in!');
              console.log(result);

              var body = {
                email: e.profileObj.email,
                name: e.profileObj.name,
                googleId: e.profileObj.googleId,
                imageUrl: e.profileObj.imageUrl,
                tokenId: e.profileObj.tokenId,

                firebaseUid: result.user.uid,
              };

              ApiClient.POST(API.loginKoodooCRMByGoogleAccount, body).then(async result => {
                // if (result === true)
                // getOrderHistory()
                //console.log('getOrderHistory');
                //console.log(getOrderHistory);

                console.log('loginKoodooCRMByGoogleAccount');
                console.log(result);

                if (result && result.customToken) {
                  var firebaseToken = result.idToken;

                  try {
                    // result = await firebase.auth().signInWithCustomToken(result.customToken);
                    result = await signInWithCustomToken(global.auth, result.customToken);
                  }
                  catch (error) {
                    console.log('Failed to login with custom token');

                    CommonStore.update(s => {
                      s.alertObj = {
                        title: 'Error',
                        message: 'Unauthorized access',
                      };

                      s.isAuthenticating = false;
                    });
                  }

                  // ApiClient.GET(API.getTokenKCRM + firebaseToken).then(async (result) => {
                  ApiClient.GET(API.getTokenKCRM + e.profileObj.email).then(async (result) => {
                    console.log('getTokenKCRM');
                    console.log(result);

                    if (result && result.token) {
                      await AsyncStorage.setItem('accessToken', result.token);
                      await AsyncStorage.setItem('refreshToken', result.refreshToken);

                      global.accessToken = result.token;

                      UserStore.update(s => {
                        s.userId = result.userId;
                        s.role = result.role;
                        s.refreshToken = result.refreshToken;
                        s.token = result.token;
                      });

                      CommonStore.update(s => {
                        s.isAuthenticating = false;
                      });

                      linkTo('/genagmt/dashboard');
                    }
                    else {
                      CommonStore.update(s => {
                        s.alertObj = {
                          title: 'Error',
                          message: 'Unauthorized access',
                        };

                        s.isAuthenticating = false;
                      });
                    }
                  });
                }
              }).catch(err => {
                console.error(err);

                // setShowAlertLogin(false);

                CommonStore.update(s => {
                  s.alertObj = {
                    title: 'Error',
                    message: 'Unauthorized access',
                  };

                  s.isAuthenticating = false;
                });
              });
            })
            .catch((error) => {
              var errorCode = error.code;
              var errorMessage = error.message;
              // ...

              CommonStore.update(s => {
                s.alertObj = {
                  title: 'Error',
                  message: 'Unauthorized access',
                };

                s.isAuthenticating = false;
              });
            });
        }
        catch (ex) {
          console.error(ex);
        }
      }
      else {
        console.log('invalid');

        // setShowAlertLogin(true);

        CommonStore.update(s => {
          s.alertObj = {
            title: 'Error',
            message: 'Unauthorized access',
          };
        });
      }
    }
    else {
      console.log('invalid');
    }
  };

  const country = [
    { label: '+11', value: 'abc' },
  ];

  const [value, setValue] = useState('')

  const handleChange = (event) => {
    setValue(event.target.value);
  };

  const countrySelect = {
    control: (base, state) => ({
      ...base,
      background: "#ECECEC",
      borderRadius: 5,
      //borderWidth: 1,
      //borderColor: '#000',
      width: 75,
      height: 40,
      //marginTop: 30,
      borderTopRightRadius: 0,
      borderBottomRightRadius: 0,
      borderColor: 'transparent',
    }),
    menu: base => ({
      ...base,
      // match border radius
      borderRadius: 5,
      // remove the gap on top of list
      marginTop: 0,
      background: "#ECECEC",
      zIndex: 1,
    }),
    menuList: base => ({
      ...base,
      // removes white space on first and last option
      padding: 0
    }),
    indicatorSeparator: base => ({
      ...base,
      display: 'none'
    }),
    dropdownIndicator: base => ({
      ...base,
      color: "black"
    }),
  };

  const noGuestSelect = {
    control: (base, state) => ({
      ...base,
      background: "#ECECEC",
      borderRadius: 5,
      //borderWidth: 1,
      //borderColor: '#000',
      width: windowWidth * 0.3,
      height: 40,
    }),
    menu: base => ({
      ...base,
      // match border radius
      borderRadius: 5,
      // remove the gap on top of list
      marginTop: 0,
      background: "#ECECEC",
      zIndex: 1,
    }),
    menuList: base => ({
      ...base,
      // removes white space on first and last option
      padding: 0
    }),
    indicatorSeparator: base => ({
      ...base,
      display: 'none'
    }),
    dropdownIndicator: base => ({
      ...base,
      color: "black"
    }),
  };

  // validate and create reservation

  useEffect(() => {
    console.log('SWEE', selectedOutlet)
  })

  const [isLoading, setIsLoading] = useState(false);

  const handleCreateReservation = async () => {
    try{
      setIsLoading(true);

      if (firstName.length === 0 || lastName.length === 0 || phoneNum.length === 0 || email.length === 0) {
        CommonStore.update(s => {
          s.alertObj = {
            title: 'Info',
            message: 'Please fill in the following fields:\n\n1. First Name\n2. Last Name\n3. Phone Number\n4. Email Address',
          };
        });
        setShowDeposit(false);
        return;
      }
  
      const phoneNumParsed = phoneNum.replace(/\D/g, '');
  
      // validate phone number
      if (phoneNumParsed.length === 9 || phoneNumParsed.length === 10 || phoneNumParsed.length === 11 ||
        (
          (phoneNumParsed.startsWith('011') && phoneNumParsed.length === 11)
          ||
          (phoneNumParsed.startsWith('6011') && phoneNumParsed.length === 12)
        )
      ) {
  
      }
      else {
        CommonStore.update(s => {
          s.alertObj = {
            title: 'Error',
            message: 'Incorrect Phone number format',
          };
        });
        setShowDeposit(false);
        return;
      }
  
      let combinedRemarks = remarks;
      if (dietaryRestrictions) {
        combinedRemarks += '\n\nDietary Restrictions: \n' + dietaryRestrictions;
      }
      if (specialOccasions) {
        combinedRemarks += '\n\nSpecial occasions: \n' + specialOccasions;
      }
  
      if (selectedReservationId) {
        // update
  
        const body = {
          // pax: selectedReservationPax,
          // reservationTime: selectedReservationStartTime, // unix
          pax: selectedReservationPax,
          reservationTime: selectedReservationStartTime, // unix
          // reservationAvailabilityId: selectedReservationId,
          reservationAvailabilityId: '',
  
          merchantLogo: selectedMerchant.logo,
          merchantId: selectedOutlet.merchantId,
          merchantName: selectedMerchant.name,
          outletCover: selectedOutlet.cover,
          outletId: selectedOutlet.uniqueId,
          outletName: selectedOutlet.name,
  
          userName: firstName + ' ' + lastName,
          userFirstName: firstName,
          userLastName: lastName,
          userPhone: !phoneNumParsed.startsWith('6') ? `6${phoneNumParsed}` : phoneNumParsed,
          userEmail: email,
  
          remarks: remarks,
  
          dRestrictions: dietaryRestrictions,
          sOccasions: specialOccasions,
  
          outletSectionId: selectedReservationOutletSectionId,
          outletSectionName: selectedReservationOutletSectionName,
  
          reservationId: selectedReservationId,
        };
  
        console.log('body', body);
  
        ////////////////////////////////////////////////////////////////////////////////////////////
  
        let isOverlapped = false;
        let maxPaxObjToUseList = [];
  
        let isDateChanged = false;
        let isTimeChanged = false;
        let resDatePrev = null;
        let resMaxPaxObjListPrev = [];
  
        if (reservationConfig && reservationConfig.maxPaxList) {
          const maxPaxListParsed = Object.entries(reservationConfig.maxPaxList).map(
            ([key, value]) => {
              return {
                ...value,
                key,
              };
            },
          );
  
          if (maxPaxListParsed && maxPaxListParsed.length > 0) {
            const turnTime = (typeof reservationConfig.turnTime === 'number') ? reservationConfig.turnTime : 60;
  
            const compareDt = moment();
            const bookedTimeStart = moment(compareDt)
              .set('hour', moment(selectedReservationStartTime).get('hour'))
              .set('minute', moment(selectedReservationStartTime).get('minute'))
              .valueOf();
            const bookedTimeEnd = moment(bookedTimeStart).add(turnTime, 'minute').valueOf();
  
            const bookedTimeStartPrev = moment(compareDt)
              .set('hour', moment(selectedReservationStartTimePrev).get('hour'))
              .set('minute', moment(selectedReservationStartTimePrev).get('minute'))
              .valueOf();
            const bookedTimeEndPrev = moment(bookedTimeStartPrev).add(turnTime, 'minute').valueOf();
  
            for (let rcIndex = 0; rcIndex < maxPaxListParsed.length; rcIndex++) {
              const currMaxPaxObj = maxPaxListParsed[rcIndex];
  
              if (currMaxPaxObj && currMaxPaxObj.start && currMaxPaxObj.end &&
                currMaxPaxObj.maxPax) {
  
                const checkTimeStart = moment(currMaxPaxObj.start, 'h:mm A')
                  .set('year', moment(compareDt).get('year'))
                  .set('month', moment(compareDt).get('month'))
                  .set('date', moment(compareDt).get('date'))
                  .valueOf();
                const checkTimeEnd = moment(currMaxPaxObj.end, 'h:mm A')
                  .set('year', moment(compareDt).get('year'))
                  .set('month', moment(compareDt).get('month'))
                  .set('date', moment(compareDt).get('date'))
                  .valueOf();
  
                if (
                  (moment(bookedTimeStart).isSameOrAfter(checkTimeStart, 'minute') && moment(bookedTimeStart).isBefore(checkTimeEnd, 'minute'))
                  ||
                  (moment(bookedTimeEnd).isBefore(checkTimeEnd, 'minute') && moment(bookedTimeEnd).isAfter(checkTimeStart, 'minute'))
                ) {
                  // means overlapped
  
                  isOverlapped = true;
  
                  // maxPaxObjToUse = currMaxPaxObj;
                  maxPaxObjToUseList.push(currMaxPaxObj);
  
                  // break;
                }
  
                if (
                  (moment(bookedTimeStartPrev).isSameOrAfter(checkTimeStart, 'minute') && moment(bookedTimeStartPrev).isBefore(checkTimeEnd, 'minute'))
                  ||
                  (moment(bookedTimeEndPrev).isBefore(checkTimeEnd, 'minute') && moment(bookedTimeEndPrev).isAfter(checkTimeStart, 'minute'))
                ) {
                  resMaxPaxObjListPrev.push(currMaxPaxObj);
                }
                console.log('res max pax obj list prev', resMaxPaxObjListPrev);
              }
            }
  
            //////////////////////////////////////////
  
            // check if date/time changed
  
            const reservationDateStrPrev = moment(selectedReservationStartTimePrev).format('YYYYMMDD'); // like seatingPaxPrev, stored the old value first
            const reservationDateStrNew = moment(selectedReservationStartTime).format('YYYYMMDD');
  
            const reservationTimeStrPrev = moment(selectedReservationStartTimePrev).format('h:mm A'); // like seatingPaxPrev, stored the old value first
            const reservationTimeStrNew = moment(selectedReservationStartTime).format('h:mm A');
  
            if (reservationDateStrNew !== reservationDateStrPrev) {
              // means date changed
              isDateChanged = true;
              resDatePrev = reservationDateStrPrev;
            }
  
            if (reservationTimeStrPrev !== reservationTimeStrNew) {
              // means time changed
              isTimeChanged = true;
              resDatePrev = reservationDateStrPrev;
            }
  
            //////////////////////////////////////////
          }
        }
  
        ////////////////////////////////////////////////////////////////////////////////////////////
  
        let isCheckPaxValid = false;
        let maxPaxObjError = null;
        let currPaxError = 0;
  
        if (isOverlapped && maxPaxObjToUseList.length > 0) {
          for (let mpoIndex = 0; mpoIndex < maxPaxObjToUseList.length; mpoIndex++) {
            const maxPaxObjToUse = maxPaxObjToUseList[mpoIndex];
  
            console.log('maxpaxobj', maxPaxObjToUse);
            const periodPax = maxPaxObjToUse.currPax[moment(selectedReservationStartTime).format('YYYYMMDD')];
  
            let atomValue = parseInt(selectedReservationPax);
            if (selectedReservationId) {
              // means is editing
  
              atomValue = parseInt(selectedReservationPax) - parseInt(selectedReservationPaxPrev);
            }
            else {
              // means is new, do nothing here
            }
  
            console.log('periodPax', periodPax);
  
            if (isDateChanged || isTimeChanged) {
              atomValue = parseInt(selectedReservationPax);
            }
  
            if (periodPax === undefined || (parseInt(periodPax) + atomValue) <= maxPaxObjToUse.maxPax) {
              // since valid, do nothing
  
              isCheckPaxValid = true;
            }
            else {
              maxPaxObjError = maxPaxObjToUse;
              currPaxError = periodPax;
  
              isCheckPaxValid = false;
  
              break;
            }
          }
        }
        else {
          isCheckPaxValid = true;
        }
  
        if (isCheckPaxValid) {
          // means can proceed
        }
        else {
          alert(`The max pax from ${maxPaxObjError.start} to ${maxPaxObjError.end} is ${maxPaxObjError.maxPax}, the current pax is ${currPaxError}, please choose the other date or time.`);
  
          CommonStore.update((s) => {
            s.isLoading = false;
          });
  
          return;
        }
        ////////////////////////////////////////////////////////////////////////////////////////////
  
        const res = await ApiClient.POST(API.updateUserReservation, body);
        console.log('ressss', res)
  
        if (res) {
          var reservationIdEncrypted = hashids.encodeHex(
            res.uniqueId.replaceAll('-', ''),
          );
          setResId(reservationIdEncrypted)
  
          // CommonStore.update(s => {
          //   s.alertObj = {
          //     title: 'Success',
          //     message: 'Reservation updated successfully',
          //   };
          // });
          // 06-01-2025 change to alert ( suspect alertObj issue cant click the ok );
          alert('Success\n\nReservation updated successfully');
  
          ////////////////////////////////////////////////////////////////////////////////////////////
  
          try {
            setTimeout(async () => {
  
              const reservationConfigBatch = getFirestore().batch();
  
              if (isOverlapped && maxPaxObjToUseList) {
  
                let atomValue = parseInt(selectedReservationPax);
  
                if (selectedReservationId) {
                  // means is editing
  
                  atomValue = parseInt(selectedReservationPax) - parseInt(selectedReservationPaxPrev);
                }
                else {
                  // means is new, do nothing here
                }
                if (atomValue !== 0 || isDateChanged || isTimeChanged) {
                  if (isDateChanged || isTimeChanged) {
                    atomValue = parseInt(selectedReservationPax);
                  }
  
                  for (let mpoIndex = 0; mpoIndex < maxPaxObjToUseList.length; mpoIndex++) {
                    const maxPaxObjToUse = maxPaxObjToUseList[mpoIndex];
  
                    reservationConfigBatch.update(doc(global.db, Collections.ReservationConfig, reservationConfig.uniqueId), {
                      [`maxPaxList.${maxPaxObjToUse.key}.currPax.${moment(selectedReservationStartTime).format('YYYYMMDD')}`]: increment(atomValue),
  
                      updatedAt: Date.now(),
                    });
                  }
                }
              }
              ////////////////////////////////////////////////
  
              const atomValuePrev = parseInt(selectedReservationPaxPrev);
              if ((isDateChanged || isTimeChanged)) {
                for (let mpoIndex = 0; mpoIndex < resMaxPaxObjListPrev.length; mpoIndex++) {
                  const maxPaxObjToUse = resMaxPaxObjListPrev[mpoIndex];
  
                  reservationConfigBatch.update(doc(global.db, Collections.ReservationConfig, reservationConfig.uniqueId), {
                    [`maxPaxList.${maxPaxObjToUse.key}.currPax.${resDatePrev}`]: increment(-atomValuePrev),
  
                    updatedAt: Date.now(),
                  });
                }
              }
  
              ////////////////////////////////////////////////
  
              reservationConfigBatch.commit();
  
            }, 0);
  
          }
          catch (ex) {
            console.error(ex);
          }
  
          ////////////////////////////////////////////////////////////////////////////////////////////
  
          setShowDeposit(false);
  
          const subdomain = await AsyncStorage.getItem("latestSubdomain");
          linkTo && linkTo(`${prefix}/outlet/${subdomain}/reservation-details/${reservationIdEncrypted}`);
  
          // setShowConfirmation(true);
          // CommonStore.update(s => {
          //   s.resShowConfirmationPage = true;
          // });
        }
      }
      else {
        // create
  
        const body = {
          pax: selectedReservationPax,
          reservationTime: selectedReservationStartTime, // unix
          // reservationAvailabilityId: selectedReservationId,
          reservationAvailabilityId: '',
  
          merchantLogo: selectedMerchant.logo,
          merchantId: selectedOutlet.merchantId,
          merchantName: selectedMerchant.name,
          outletCover: selectedOutlet.cover,
          outletId: selectedOutlet.uniqueId,
          outletName: selectedOutlet.name,
  
          userName: firstName + ' ' + lastName,
          userFirstName: firstName,
          userLastName: lastName,
          userPhone: !phoneNumParsed.startsWith('6') ? `6${phoneNumParsed}` : phoneNumParsed,
          userEmail: email,
  
          remarks: remarks,
  
          dRestrictions: dietaryRestrictions,
          sOccasions: specialOccasions,
  
          outletSectionId: selectedReservationOutletSectionId,
          outletSectionName: selectedReservationOutletSectionName,
        };
  
        console.log('body', body);
  
        ////////////////////////////////////////////////////////////////////////////////////////////
  
        let isOverlapped = false;
        let maxPaxObjToUse = null;
  
        if (reservationConfig && reservationConfig.maxPaxList) {
          const maxPaxListParsed = Object.entries(reservationConfig.maxPaxList).map(
            ([key, value]) => {
              return {
                ...value,
                key,
              };
            },
          );
  
          if (maxPaxListParsed && maxPaxListParsed.length > 0) {
            const turnTime = (typeof reservationConfig.turnTime === 'number') ? reservationConfig.turnTime : 60;
  
            const compareDt = moment();
            const bookedTimeStart = moment(compareDt)
              .set('hour', moment(selectedReservationStartTime).get('hour'))
              .set('minute', moment(selectedReservationStartTime).get('minute'))
              .set('second', moment(selectedReservationStartTime).get('second'))
              .valueOf();
            const bookedTimeEnd = moment(bookedTimeStart).add(turnTime, 'minute').valueOf();
  
            for (let rcIndex = 0; rcIndex < maxPaxListParsed.length; rcIndex++) {
              const currMaxPaxObj = maxPaxListParsed[rcIndex];
  
              if (currMaxPaxObj && currMaxPaxObj.start && currMaxPaxObj.end &&
                currMaxPaxObj.maxPax) {
  
                const checkTimeStart = moment(currMaxPaxObj.start, 'h:mm A')
                  .set('year', moment(compareDt).get('year'))
                  .set('month', moment(compareDt).get('month'))
                  .set('date', moment(compareDt).get('date'))
                  .valueOf();
                const checkTimeEnd = moment(currMaxPaxObj.end, 'h:mm A')
                  .set('year', moment(compareDt).get('year'))
                  .set('month', moment(compareDt).get('month'))
                  .set('date', moment(compareDt).get('date'))
                  .valueOf();
  
                if (
                  (moment(bookedTimeStart).isSameOrAfter(checkTimeStart, 'minute') && moment(bookedTimeStart).isBefore(checkTimeEnd, 'minute'))
                  ||
                  (moment(bookedTimeEnd).isBefore(checkTimeEnd, 'minute') && moment(bookedTimeEnd).isAfter(checkTimeStart, 'minute'))
                ) {
                  // means overlapped
  
                  isOverlapped = true;
  
                  maxPaxObjToUse = currMaxPaxObj;
  
                  break;
                }
              }
            }
  
  
          }
        }
  
        ////////////////////////////////////////////////////////////////////////////////////////////
  
        if (isOverlapped && maxPaxObjToUse) {
          console.log('maxpaxobj', maxPaxObjToUse);
          const periodPax = maxPaxObjToUse.currPax[moment(selectedReservationStartTime).format('YYYYMMDD')] ? maxPaxObjToUse.currPax[moment(selectedReservationStartTime).format('YYYYMMDD')] : 0;
  
          let atomValue = parseInt(selectedReservationPax);
          if (selectedReservationId) {
            // means is editing
  
            atomValue = parseInt(selectedReservationPax) - parseInt(selectedReservationPaxPrev);
          }
          else {
            // means is new, do nothing here
          }
  
          console.log('periodPax', periodPax);
          if (periodPax === undefined || (parseInt(periodPax) + atomValue) <= maxPaxObjToUse.maxPax) {
            // means can proceed
          }
          else {
            alert(`The max pax from ${maxPaxObjToUse.start} to ${maxPaxObjToUse.end} is ${maxPaxObjToUse.maxPax}, the current pax is ${periodPax}, please choose the other date or time.`);
  
            return;
          }
        }
        ////////////////////////////////////////////////////////////////////////////////////////////
  
        const res = await ApiClient.POST(API.createUserReservation, body);
        console.log('ressss', res)
  
        if (res) {
          var reservationIdEncrypted = hashids.encodeHex(
            res.uniqueId.replaceAll('-', ''),
          );
          setResId(reservationIdEncrypted)
  
          CommonStore.update(s => {
            s.alertObj = {
              title: 'Success',
              message: 'Reservation created successfully',
            };
          });
  
          setShowDeposit(false);
  
          const subdomain = await AsyncStorage.getItem("latestSubdomain");
          var resUrl = (`https://mykoodoo.com/${prefix}/outlet/${subdomain}/reservation-details/${reservationIdEncrypted}`);
  
          // setShowConfirmation(true);
  
          //sendReservationEmail(res, email, merchantAddress, resUrl);
  
          CommonStore.update(s => {
            // for upselling support (to link upsold order into the reservation)
            s.selectedReservationId = (res && res.uniqueId) ? res.uniqueId : '';
            s.isPlacingReservationUpselling = true;
  
            s.resShowConfirmationPage = true;
  
            s.isPlacingReservation = true;
          });
  
          ////////////////////////////////////////////////////////////////////////////////////////////
  
          try {
            setTimeout(async () => {
              if (isOverlapped && maxPaxObjToUse) {
                let atomValue = parseInt(selectedReservationPax);
                if (selectedReservationId) {
                  // means is editing
  
                  atomValue = parseInt(selectedReservationPax) - parseInt(selectedReservationPaxPrev);
                }
                else {
                  // means is new, do nothing here
                }
                if (atomValue !== 0) {
                  updateDoc(doc(global.db, Collections.ReservationConfig, reservationConfig.uniqueId), {
                    [`maxPaxList.${maxPaxObjToUse.key}.currPax.${moment(selectedReservationStartTime).format('YYYYMMDD')}`]: increment(atomValue),
  
                    updatedAt: Date.now(),
                  });
                }
              }
            }, 0);
  
          }
          catch (ex) {
            console.error(ex);
          }
  
          ////////////////////////////////////////////////////////////////////////////////////////////
  
          if (upsellingCampaignsReservation.length > 0) {
            CommonStore.update((s) => {
              s.currPage = "UpsellReservation";
            });
  
            linkTo &&
              linkTo(`${prefix}/outlet/${selectedOutlet.subdomain}/upsell-reservation`);
          };
        }
      }
    }
    catch(ex){
      console.error(ex);
    }
    finally{
      setIsLoading(false);
    }
  }

  const redirectTo = () => {
    Linking.openURL('https://mykoodoo.com/privacy-policy/');
  };

  const redirectTo2 = () => {
    Linking.openURL('https://mykoodoo.com/terms/');
  };

  console.log('res show confirm state', resShowConfirmationPage);

  // function end    


  return (
    <ScrollView showsHorizontalScrollIndicator={false}>
      <Modal
        visible={showDesposit}
        transparent={true}
        supportedOrientations={['portrait', 'landscape']}
        animationType="fade"
        onRequestClose={() => { setShowDeposit(false) }}
      >
        <ScrollView showsHorizontalScrollIndicator={false}>
          <View style={{
            backgroundColor: 'rgba(0,0,0,0.5)',
            height: windowHeight,
            width: windowWidth,
            justifyContent: 'center',
          }}>
            <View style={{
              alignSelf: 'center',
              justifyContent: 'center',
              // width: windowWidth <= 1251 ? windowWidth * (windowWidth / 1150) : windowWidth * 0.980,
              width: selectedOutlet && selectedOutlet.reservationDepositAmount <= 1.01 ? windowWidth * 0.8 : windowWidth * 0.9,
              height: selectedOutlet && selectedOutlet.reservationDepositAmount <= 1.01 ? windowHeight * 0.9 : windowHeight * 0.2,
              //height: windowHeight * 0.65,
              backgroundColor: 'white',

              borderRadius: 10,
              borderColor: '#E5E5E5',

              shadowOffset: {
                width: 0,
                height: 2,
              },
              shadowOpacity: 0.22,
              shadowRadius: 3.22,
              elevation: 1,
              zIndex: -1,

              marginTop: windowHeight * 0.001,

              position: 'relative',
            }}>
              <TouchableOpacity
                onPress={() => { setShowDeposit(false) }}
              >
                <View style={{
                  marginTop: 10,
                  marginRight: 10,
                  alignSelf: 'flex-end',
                  height: 20,
                }}>
                  <AntDesign name="closecircle" size={25} color={Colors.fieldtTxtColor} />
                </View>
              </TouchableOpacity>

              {
                selectedOutlet && selectedOutlet.reservationDepositAmount >= 1.01
                  ?
                  <>
                    <Text style={{
                      fontFamily: 'NunitoSans-Bold',
                      color: 'black',
                      fontSize: 18,
                      textAlign: 'center',
                      marginBottom: 10,
                      // width: '100%',
                    }}>
                      Deposit Notice
                    </Text>
                    <ScrollView
                      persistentScrollbar={true}
                      showsVerticalScrollIndicator={true}
                    >
                      <View style={{
                        backgroundColor: Colors.descriptionColor,
                        // height: windowHeight * 0.8,
                        width: windowWidth <= 1251 ? windowWidth * 0.75 : windowWidth * 0.95,
                        //height: windowHeight * 0.4,
                        //margin: 15,
                        borderRadius: 12,
                        //marginLeft: 20,
                        //marginRight: 40,
                        //marginTop: 20,
                        alignSelf: 'center'
                      }}>
                        <Text style={{
                          fontSize: 14,
                          fontFamily: 'NunitoSans-Regular',
                          paddingHorizontal: 50,
                          paddingBottom: 10,
                        }}>
                          <h3>Before you reserve</h3>
                          POPUP TIME: 𝐂𝐇𝐑𝐈𝐒𝐓𝐌𝐀𝐒 𝐎𝐍 𝐂𝐀𝐍𝐀𝐋 𝐒𝐓. - 𝕀𝕤 𝕓𝕒𝕔𝕜!!!!!!!
                          <br /><br />
                          The entire Deadfall (ground floor) has been transformed into a full on 𝑯𝒐𝒍𝒊𝒅𝒂𝒚 𝑾𝒐𝒏𝒅𝒆𝒓𝒍𝒂𝒏𝒅. A new version of the special food menu and of course your favourite holiday cocktails are back, even better than last year!!!
                          <br /><br />
                          Please note that the normal operations menu items are sadly not available during this time but festive variations of everyone's favourites will be available the entire month of December.
                          <br /><br />
                          Singapore may not have snow for you to slosh through, but we can get you sloshed through the entire season none the less!!
                          <br /><br />
                          We look forward to sharing this holiday season with you and yours.
                          <br /><br />
                          xoxoxo,
                          <br /><br />
                          𝓣𝓮𝓪𝓶 𝓑𝓪𝓻𝓫𝓪𝓻𝔂 𝓒𝓸𝓪𝓼𝓽
                          <br /><br />

                          𝐔𝐏𝐃𝐀𝐓𝐄𝐃 𝐂𝐎𝐕𝐈𝐃 𝐑𝐄𝐒𝐓𝐑𝐈𝐂𝐓𝐈𝐎𝐍𝐒
                          <br /><br />
                          𝕌ℕ𝕍𝔸ℂℂ𝕀ℕ𝔸𝕋𝔼𝔻 𝔾𝕌𝔼𝕊𝕋𝕊
                          We are so sorry we cannot accommodate you at this moment in time. Please be safe and we hope to host you soon!!
                          <br /><br />
                          𝕍𝔸ℂℂ𝕀ℕ𝔸𝕋𝔼𝔻 𝔾𝕌𝔼𝕊𝕋𝕊
                          Fully Vaccinated Guests can seat together up to 5pax<br />
                          "Vaccination" is valid only after 2-week incubation period<br />
                          All guests must show Trace Together App for verification<br />
                          Trace Together Token users will need to open app as token is not valid by itself<br />
                          PET: guests with negative test results may enter up until the 24hr mark of the test. If the test expires during visit we will sadly have to ask you to depart the premises
                          <br /><br />
                          𝐓𝐡𝐚𝐧𝐤 𝐲𝐨𝐮 𝐟𝐨𝐫 𝐲𝐨𝐮𝐫 𝐩𝐚𝐭𝐢𝐞𝐧𝐜𝐞 𝐚𝐧𝐝 𝐬𝐮𝐩𝐩𝐨𝐫𝐭. 𝐇𝐨𝐩𝐞𝐟𝐮𝐥𝐥𝐲 𝐭𝐡𝐢𝐬 𝐰𝐢𝐥𝐥 𝐩𝐚𝐬𝐬 𝐬𝐨𝐨𝐧 𝐚𝐧𝐝 𝐰𝐞 𝐜𝐚𝐧 𝐚𝐥𝐥 𝐠𝐞𝐭 𝐛𝐚𝐜𝐤 𝐭𝐨 𝐧𝐨𝐫𝐦𝐚𝐥
                          <br /><br />
                          𝐘𝐎𝐔 𝐖𝐈𝐋𝐋 𝐑𝐄𝐂𝐄𝐈𝐕𝐄 𝐀𝐍 𝐄𝐌𝐀𝐈𝐋 𝐂𝐎𝐍𝐅𝐈𝐑𝐌𝐀𝐓𝐈𝐎𝐍.<br />
                          (𝐎𝐔𝐑 𝐓𝐄𝐀𝐌 𝐖𝐈𝐋𝐋 𝐌𝐄𝐒𝐒𝐀𝐆𝐄 𝐕𝐈𝐀 𝐖𝐇𝐀𝐓𝐒𝐀𝐏𝐏 𝐈𝐅 𝐀𝐍𝐘 𝐂𝐇𝐀𝐍𝐆𝐄𝐒)
                          <br /><br />
                          -For same day reservation changes please send whatsapp to +𝟞𝟝 𝟠𝟠𝟞𝟡 𝟜𝟟𝟡𝟠
                          <br /><br />
                          -Please note that due to circuit breaker measures, our last order will be at 10pm.
                          <br /><br />
                          -All reservations are subject to a strict 2 hour return time. We will endeavour to extend your reservation time and/or secure another seat in one of the two venues, however, we are unable to make promises at this time.
                          <br /><br />
                          We thank you for your support and understanding!
                        </Text>
                      </View>
                    </ScrollView>
                    <View style={{
                      flexDirection: 'row',
                      justifyContent: 'center',
                      alignContent: 'center',
                      //width: '100%'
                      marginTop: 20,
                    }}>
                      <input
                        onChange={() => { setAgree(true) }}
                        style={{
                          alignSelf: 'center',
                          justifyContent: 'center',
                          borderRadius: 15,

                          marginRight: 10,
                          backgroundColor: Colors.primaryColor,

                          width: 20,
                          height: 20,
                        }}
                        color={Colors.primaryColor}
                        type={'checkbox'}
                        checked={agree}
                      />
                      <Text style={{
                        fontSize: 18,
                        fontFamily: 'NunitoSans-Regular',
                        textAlign: 'center',
                        //color: Colors.whiteColor,
                        marginRight: 5
                      }}>
                        I agree to Koodoo's
                      </Text>
                      <TouchableOpacity
                        style={{}}
                        onPress={redirectTo2}
                      >
                        <Text style={{
                          fontSize: 18,
                          fontFamily: 'NunitoSans-Regular',
                          textAlign: 'center',
                          color: Colors.primaryColor,
                        }}>
                          Terms of Use
                        </Text>
                      </TouchableOpacity>
                      <Text style={{
                        fontSize: 18,
                        fontFamily: 'NunitoSans-Regular',
                        textAlign: 'center',
                        //color: Colors.whiteColor,
                        marginHorizontal: 5,
                      }}>
                        &
                      </Text>
                      <TouchableOpacity
                        style={{}}
                        onPress={redirectTo}
                      >
                        <Text style={{
                          fontSize: 18,
                          fontFamily: 'NunitoSans-Regular',
                          textAlign: 'center',
                          color: Colors.primaryColor,
                        }}>
                          Privacy Policy
                        </Text>
                      </TouchableOpacity>
                    </View>
                  </>
                  :
                  <>
                    <Text style={{
                      fontFamily: 'NunitoSans-Bold',
                      color: 'black',
                      fontSize: 20,
                      textAlign: 'center',
                      marginTop: 0,
                      // width: '100%',
                    }}>
                      Please select your choice
                    </Text></>
              }

              <View style={{
                flexDirection: 'row',
                justifyContent: 'center',
                alignContent: 'center'
              }}>
                {
                  true
                    ?
                    <TouchableOpacity
                      style={[styles.btnGreen, {
                        marginTop: 20,
                        width: 270,
                        alignSelf: 'center',
                        marginBottom: 50,

                        marginHorizontal: '1%',
                      }]}
                      onPress={() => {
                        // go to outlet menu screen

                        setShowDeposit(false);

                        CommonStore.update(s => {
                          s.resFirstName = firstName;
                          s.resLastName = lastName;
                          s.resPhoneNum = !phoneNum.startsWith('6') ? `6${phoneNum}` : phoneNum;
                          s.resEmail = email;
                          s.resRemarks = remarks;
                          s.resDietaryRestrictions = dietaryRestrictions;
                          s.resSpecialOccasions = specialOccasions;

                          s.isPlacingReservation = true;

                          s.isDepositOnly = false;
                        });

                        CommonStore.update(s => {
                          s.orderType = ORDER_TYPE.DINEIN;
                        });

                        linkTo &&
                          linkTo(`${prefix}/outlet/${selectedOutlet.subdomain}/dinein`);
                      }}
                    >
                      <View>
                        <View style={{
                          flexDirection: 'row',
                          justifyContent: 'center',
                          alignContent: 'center',
                        }}>
                          {/* <Bell
                                                width={20}
                                                height={20}
                                                color={Colors.whiteColor}
                                            /> */}
                          <Text style={{
                            // marginLeft: 10,
                            fontSize: 18,
                            fontFamily: 'NunitoSans-Regular',
                            textAlign: 'center',
                            color: Colors.whiteColor,
                          }}>
                            Choose your supplements
                          </Text>
                        </View>
                      </View>
                    </TouchableOpacity>
                    :
                    <></>
                }

                {
                  // no need deposit first
                  // selectedOutlet && selectedOutlet.reservationDepositAmount >= 1.01
                  false
                    ?
                    <>
                      <TouchableOpacity
                        style={[styles.btnGreen, {
                          marginTop: 20,
                          width: 270,
                          alignSelf: 'center',
                          marginBottom: 50,

                          marginHorizontal: '1%',
                        }]}
                        onPress={() => {
                          // go to cart screen, for deposit

                          setShowDeposit(false);

                          CommonStore.update(s => {
                            s.resFirstName = firstName;
                            s.resLastName = lastName;
                            s.resPhoneNum = !phoneNum.startsWith('6') ? `6${phoneNum}` : phoneNum;
                            s.resEmail = email;
                            s.resRemarks = remarks;
                            s.resDietaryRestrictions = dietaryRestrictions;
                            s.resSpecialOccasions = specialOccasions;

                            s.isPlacingReservation = true;

                            s.isDepositOnly = true;
                          });

                          CommonStore.update(s => {
                            s.orderType = ORDER_TYPE.DINEIN;
                          });

                          linkTo &&
                            linkTo(`${prefix}/outlet/${selectedOutlet.subdomain}/dinein`);
                        }}
                      >
                        <View>
                          <View style={{
                            flexDirection: 'row',
                            justifyContent: 'center',
                            alignContent: 'center',
                          }}>
                            <Bell
                              width={20}
                              height={20}
                              color={Colors.whiteColor}
                            />
                            <Text style={{
                              marginLeft: 10,
                              fontSize: 18,
                              fontFamily: 'NunitoSans-Regular',
                              textAlign: 'center',
                              color: Colors.whiteColor,
                            }}>
                              Continue to the payment
                              {/* Continue */}
                            </Text>
                          </View>
                        </View>
                      </TouchableOpacity>
                    </>
                    :
                    <>

                      <TouchableOpacity
                        style={[styles.btnGreen, {
                          //marginTop: 20,
                          // width: 270,
                          paddingLeft: 10,
                          paddingRight: 10,
                          alignSelf: 'center',
                          marginBottom: 30,
                        }]}
                        disabled={isLoading}
                        onPress={() => {
                          // since no need deposit, create reservation immediately, without the needs to go to other screens

                          handleCreateReservation();
                        }}
                      >
                        <View>
                          <View style={{
                            flexDirection: 'row',
                            justifyContent: 'center',
                            alignContent: 'center',
                            alignItems: 'center',
                          }}>
                            <Bell
                              width={20}
                              height={20}
                              color={Colors.whiteColor}
                            />
                            <Text style={{
                              marginLeft: 10,
                              fontSize: 18,
                              fontFamily: 'NunitoSans-Regular',
                              textAlign: 'center',
                              color: Colors.whiteColor,
                            }}>
                              Add me to the reservation
                              {/* Continue */}
                            </Text>
                          </View>
                        </View>
                      </TouchableOpacity>
                    </>
                }
              </View>
            </View>
          </View>
        </ScrollView>
      </Modal>

      {!resShowConfirmationPage ?
        <View style={{
          width: isMobile() ? windowWidth : windowWidth,
          height: windowHeight,
          //backgroundColor: 'blue'
        }}>
          <View style={{
            width: isMobile() ? windowWidth : windowWidth,
            //height: windowHeight,
            backgroundColor: Colors.darkBgColor,
            flexDirection: 'row',
          }}>

            <View
              style={{
                // marginLeft: 10,
                // display: "flex",
                flexDirection: "row",
                alignItems: "center",
                justifyContent: "flex-start",
                backgroundColor: Colors.darkBgColor,
                height: 100,
                //paddingLeft: 10
              }}
            >
              <TouchableOpacity
                style={{
                  opacity: 100,
                  marginLeft: 10,
                  flexDirection: "row",
                  justifyContent: "center",
                }}
                onPress={async () => {
                  // props.navigation.goBack();

                  var hashedReservationId = '';
                  if (route && route.params && route.params.reservationId) {
                    hashedReservationId = route.params.reservationId;
                  }

                  const subdomain = await AsyncStorage.getItem("latestSubdomain");
                  linkTo && linkTo(`${prefix}/outlet/${subdomain}/reservation${hashedReservationId ? ('/' + hashedReservationId) : ''}`);
                }}
              >
                <Ionicons
                  name="chevron-back"
                  size={26}
                  color={Colors.fieldtTxtColor}
                  style={{}}
                />

                <Text
                  style={{
                    color: 'white',
                    fontSize: 16,
                    textAlign: "center",
                    fontFamily: "NunitoSans-Regular",
                    //lineHeight: 22,
                    marginTop: 3,
                  }}
                >
                  Back
                </Text>
              </TouchableOpacity>
            </View>

            <View style={{
              width: isMobile() ? windowWidth : windowWidth,
              height: 100,
              backgroundColor: Colors.darkBgColor,
            }}>
              <View style={{
                marginRight: 140,
                marginTop: 10,
              }}>
                <Image style={{
                  width: 100,
                  height: 47,
                  alignSelf: 'center',
                  //marginBottom: 25.
                }} resizeMode="contain" source={imgLogo} />
                <Text style={{
                  fontFamily: 'NunitoSans-Bold',
                  color: Colors.whiteColor,
                  fontSize: 16,
                  textAlign: 'center',
                  marginTop: 0,
                  // width: '100%',
                }}>
                  Reservation Details
                </Text>
              </View>
            </View>

          </View>
          <View style={{
            //justifyContent: 'center',
            alignItems: 'center',

            width: isMobile() ? windowWidth : windowWidth,
            height: windowHeight,
          }}>
            <Text style={{
              fontFamily: 'NunitoSans-Regular',
              //color: Colors.whiteColor,
              fontSize: 18,
              textAlign: 'center',
              marginTop: 30,
              marginHorizontal: windowWidth < 769 ? windowWidth * 0.1 : 142,
            }}>
              Reservation for <b>{selectedReservationPax}</b> people at <br /><b>{moment(selectedReservationStartTime).format('hh:mm A')}, </b>
              <b>{moment(selectedReservationStartTime).format('MMMM Do YYYY')}</b>
              <br />in the {selectedOutlet ? selectedOutlet.name : ''}
            </Text>
            <View>
              <Text style={{
                fontFamily: 'NunitoSans-SemiBold',
                color: 'black',
                fontSize: 20,
                //textAlign: 'center',
                marginTop: 30,
                marginHorizontal: windowWidth < 769 ? windowWidth * 0.1 : 142,

                //alignSelf: 'center',
                //marginLeft: windowWidth * 0.095,
              }}>
                Contact Details
              </Text>
              <Text style={{
                fontFamily: 'NunitoSans-Regular',
                color: 'grey',
                fontSize: 16,
                //textAlign: 'center',
                marginTop: 5,
                marginHorizontal: windowWidth < 769 ? windowWidth * 0.1 : 142,
              }}>
                Fill in your details so we can reach you in case of any notifications regarding your reservation
              </Text>
            </View>

            <View style={{
              //height: windowHeight < 769 ? windowHeight * 0.83 : windowHeight * 0.6,
              height: 580,
              paddingHorizontal: 10,
              //justifyContent: 'center',
              alignItems: 'center',
              marginTop: 15,
            }}>
              <View style={{
                // justifyContent: 'center',
                // alignContent: 'center',
                // alignSelf: 'center',
                // marginLeft: windowWidth < 769 ? windowWidth * 0.1 : 142,

                // borderWidth: 1,
                borderRadius: 5,
                // borderColor: 'grey',

                // width: windowWidth < 769 ? 200 : windowWidth * 0.3,
                width:
                  isMobile()
                    ? windowWidth < 769
                      ? windowWidth * 0.8
                      : windowWidth * 0.849
                    : windowWidth * 0.4,
                height: 40,
                backgroundColor: '#ECECEC',
                marginTop: 15,

                shadowOffset: {
                  width: 0,
                  height: 2,
                },
                shadowOpacity: 0.22,
                shadowRadius: 3.22,
                elevation: 1,
              }}>
                <TextInput
                  style={{
                    height: 40,
                    // width: windowWidth < 769 ? 200 : windowWidth * 0.3,
                    width:
                      isMobile()
                        ? windowWidth < 769
                          ? windowWidth * 0.8
                          : windowWidth * 0.849
                        : windowWidth * 0.4,
                    fontFamily: 'NunitoSans-Regular',
                    fontSize: 16,
                    //textAlign: 'center',
                    paddingHorizontal: 15,
                    paddingVertical: 15,
                  }}
                  placeholder={userFirstName ? userFirstName : "First Name"}
                  value={
                    // userFirstName != null? 
                    // setFirstName(userFirstName) 
                    // :
                    firstName
                  }
                  onChangeText={(text) => setFirstName(text)}
                />
              </View>
              <View style={{
                // justifyContent: 'center',
                // alignContent: 'center',
                // alignSelf: 'center',
                // marginRight: windowWidth < 769 ? windowWidth * 0.1 : 142,

                //borderWidth: 1,
                borderRadius: 5,
                // borderColor: 'grey',

                // width: windowWidth < 769 ? 200 : windowWidth * 0.3,
                width:
                  isMobile()
                    ? windowWidth < 769
                      ? windowWidth * 0.8
                      : windowWidth * 0.849
                    : windowWidth * 0.4,
                height: 40,
                backgroundColor: '#ECECEC',
                marginTop: 15,

                shadowOffset: {
                  width: 0,
                  height: 2,
                },
                shadowOpacity: 0.22,
                shadowRadius: 3.22,
                elevation: 1,
              }}>
                <TextInput
                  style={{
                    height: 40,
                    //width: windowWidth < 769 ? 200 : windowWidth * 0.3,
                    width:
                      isMobile()
                        ? windowWidth < 769
                          ? windowWidth * 0.8
                          : windowWidth * 0.849
                        : windowWidth * 0.4,
                    fontFamily: 'NunitoSans-Regular',
                    fontSize: 16,
                    //textAlign: 'center',
                    paddingHorizontal: 15,
                    paddingVertical: 15,
                  }}
                  placeholder={userLastName ? userLastName : "Last Name"}
                  value={
                    // userLastName != null? 
                    // setLastName(userLastName) 
                    // :
                    lastName
                  }
                  onChangeText={(text) => setLastName(text)}
                />
              </View>
              <View style={{
                // justifyContent: 'center',
                // alignContent: 'center',
                // alignSelf: 'center',
                flexDirection: 'row',
                // marginLeft: windowWidth < 769 ? windowWidth * 0.1 : 142,

                //borderWidth: 1,
                borderRadius: 5,
                //borderColor: 'grey',

                // width: windowWidth < 769 ? 200 : windowWidth * 0.3,
                width:
                  isMobile()
                    ? windowWidth < 769
                      ? windowWidth * 0.8
                      : windowWidth * 0.849
                    : windowWidth * 0.4,
                height: 40,
                backgroundColor: '#ECECEC',
                marginTop: 15,
                shadowOffset: {
                  width: 0,
                  height: 2,
                },
                shadowOpacity: 0.22,
                shadowRadius: 3.22,
                elevation: 1,
              }}>
                {/* <SelectLoadable fallback={<></>}>
                {({ default: Select }) =>
                  <Select
                    options={country}
                    value={value}
                    onChange={handleChange}
                    styles={countrySelect}
                    placeholder="+60"
                    isDisabled={true}
                  />}
              </SelectLoadable> */}

                {/* No need first */}
                {/* <Select
                  options={country}
                  value={value}
                  onChange={handleChange}
                  styles={countrySelect}
                  placeholder="+60"
                  isDisabled={true}
                /> */}

                <TextInput
                  style={{
                    height: 40,
                    //width: windowWidth < 769 ? 200 : windowWidth * 0.3,
                    width:
                      isMobile()
                        ? windowWidth < 769
                          ? windowWidth * 0.8
                          : windowWidth * 0.849
                        : windowWidth * 0.4,
                    fontFamily: 'NunitoSans-Regular',
                    fontSize: 16,
                    //textAlign: 'center',
                    paddingHorizontal: 15,
                    paddingVertical: 15,
                  }}
                  placeholder={userPhone ? userPhone : "Phone Number"}
                  value={
                    // userPhone != null? 
                    // setPhoneNum(userPhone) 
                    // :
                    phoneNum
                  }
                  onChangeText={(text) => setPhoneNum(text)}
                />
              </View>
              <View style={{
                // justifyContent: 'center',
                // alignContent: 'center',
                // alignSelf: 'center',
                // marginRight: windowWidth < 769 ? windowWidth * 0.1 : 142,

                //borderWidth: 1,
                borderRadius: 5,
                //borderColor: 'grey',

                // width: windowWidth < 769 ? 200 : windowWidth * 0.3,
                width:
                  isMobile()
                    ? windowWidth < 769
                      ? windowWidth * 0.8
                      : windowWidth * 0.849
                    : windowWidth * 0.4,
                height: 40,
                backgroundColor: '#ECECEC',
                marginTop: 15,

                shadowOffset: {
                  width: 0,
                  height: 2,
                },
                shadowOpacity: 0.22,
                shadowRadius: 3.22,
                elevation: 1,
              }}>
                <TextInput
                  style={{
                    height: 40,
                    //width: windowWidth < 769 ? 200 : windowWidth * 0.3,
                    width:
                      isMobile()
                        ? windowWidth < 769
                          ? windowWidth * 0.8
                          : windowWidth * 0.849
                        : windowWidth * 0.4,
                    fontFamily: 'NunitoSans-Regular',
                    fontSize: 16,
                    //textAlign: 'center',
                    paddingHorizontal: 15,
                    paddingVertical: 15,
                  }}
                  placeholder={userEmail ? userEmail : "Email Address"}
                  value={
                    // userEmail != null? 
                    // setEmail(userEmail) 
                    // :
                    email
                  }
                  onChangeText={(text) => setEmail(text)}
                />
              </View>
              <View style={{
                // justifyContent: 'center',
                // alignContent: 'center',
                // alignSelf: 'center',
                flexDirection: 'row',
                // marginLeft: windowWidth < 769 ? windowWidth * 0.1 : 142,
                // marginRight: windowWidth < 769 ? windowWidth * 0.1 : 142,

                //borderWidth: 1,
                borderRadius: 5,
                //borderColor: 'grey',

                width:
                  isMobile()
                    ? windowWidth < 769
                      ? windowWidth * 0.8
                      : windowWidth * 0.849
                    : windowWidth * 0.4,
                height: 80,
                backgroundColor: '#ECECEC',
                marginTop: 15,
                shadowOffset: {
                  width: 0,
                  height: 2,
                },
                shadowOpacity: 0.22,
                shadowRadius: 3.22,
                elevation: 1,
              }}>
                <TextInput
                  multiline={true}
                  style={{
                    height: 80,
                    width:
                      isMobile()
                        ? windowWidth < 769
                          ? windowWidth * 0.8
                          : windowWidth * 0.849
                        : windowWidth * 0.4,
                    fontFamily: 'NunitoSans-Regular',
                    fontSize: 16,
                    //textAlign: 'center',
                    paddingHorizontal: 15,
                    paddingTop: 10,
                    paddingVertical: 15,
                    // marginTop: 5,
                  }}
                  placeholder={userRemarks ? userRemarks : "Any special requests? (optional)"}
                  value={remarks}
                  onChangeText={(text) => setRemarks(text)}
                />
              </View>
              <View
                style={{
                  // flexDirection: 'row',
                  // justifyContent: 'space-between',
                  // marginTop: 30,
                  // marginBottom: -20
                  width:
                    isMobile()
                      ? windowWidth < 769
                        ? windowWidth * 0.8
                        : windowWidth * 0.849
                      : windowWidth * 0.4,
                }}>
                <Text style={{
                  fontFamily: 'NunitoSans-SemiBold',
                  //color: Colors.whiteColor,
                  fontSize: 20,
                  textAlign: 'left',
                  marginTop: 35,
                  // marginLeft: windowWidth < 769 ? windowWidth * 0.1 : 142,

                  //width: windowWidth < 769 ? 200 : windowWidth * 0.3,
                }}>
                  Dietary restrictions
                </Text>
              </View>
              <View style={{
                // justifyContent: 'center',
                // alignContent: 'center',
                // alignSelf: 'center',
                // flexDirection: 'row',
                // marginLeft: windowWidth < 769 ? windowWidth * 0.1 : 142,

                //borderWidth: 1,
                borderRadius: 5,
                // borderColor: 'grey',

                // width: windowWidth < 769 ? 200 : windowWidth * 0.3,
                width:
                  isMobile()
                    ? windowWidth < 769
                      ? windowWidth * 0.8
                      : windowWidth * 0.849
                    : windowWidth * 0.4,
                height: 80,
                backgroundColor: '#ECECEC',
                // marginTop: 30,
                shadowOffset: {
                  width: 0,
                  height: 2,
                },
                shadowOpacity: 0.22,
                shadowRadius: 3.22,
                elevation: 1,

                marginTop: 10,
              }}>
                <TextInput
                  multiline={true}
                  style={{
                    height: 80,
                    // width: windowWidth < 769 ? 200 : windowWidth * 0.3,
                    width:
                      isMobile()
                        ? windowWidth < 769
                          ? windowWidth * 0.8
                          : windowWidth * 0.849
                        : windowWidth * 0.4,
                    fontFamily: 'NunitoSans-Regular',
                    fontSize: 16,
                    // textAlign: 'center',
                    paddingHorizontal: 10,
                    paddingTop: 10,
                    paddingVertical: 5,
                    // marginTop: 5,
                  }}
                  placeholder="Add dietary restrictions here (optional)"
                  value={dRestrictions ? dRestrictions : dietaryRestrictions}
                  onChangeText={(text) => setDietaryRestrictions(text)}
                />
                {/* <TouchableOpacity style={{ marginTop: 20, marginRight: 20 }}>
                <View style={{
                  borderWidth: 1,
                  borderRadius: 5,
                  justifyContent: 'flex-end',
                  padding: 10
                }}>
                  <Text
                    style={{
                      //color: 'white',
                      fontSize: 16,
                      textAlign: "center",
                      fontFamily: "NunitoSans-Regular",
                      //lineHeight: 22,
                      //marginTop: -1,
                    }}
                  >
                    Add+
                  </Text>
                </View>
              </TouchableOpacity> */}
              </View>
              <View
                style={{
                  // flexDirection: 'row',
                  // justifyContent: 'space-between',
                  // marginTop: 30,
                  // marginBottom: -20
                  width:
                    isMobile()
                      ? windowWidth < 769
                        ? windowWidth * 0.8
                        : windowWidth * 0.849
                      : windowWidth * 0.4,
                }}>
                <Text style={{
                  fontFamily: 'NunitoSans-SemiBold',
                  //color: Colors.whiteColor,
                  fontSize: 20,
                  textAlign: 'left',
                  marginTop: 25,
                  // marginRight: windowWidth < 769 ? windowWidth * 0.1 : 142,
                  // width: windowWidth < 769 ? 200 : windowWidth * 0.3,
                }}>
                  Special occasions
                </Text>
              </View>
              <View style={{
                // justifyContent: 'center',
                // alignContent: 'center',
                // alignSelf: 'center',
                // flexDirection: 'row',
                // marginLeft: windowWidth < 769 ? windowWidth * 0.1 : 142,

                //borderWidth: 1,
                borderRadius: 5,
                //borderColor: 'grey',

                // width: windowWidth < 769 ? 200 : windowWidth * 0.3,
                width:
                  isMobile()
                    ? windowWidth < 769
                      ? windowWidth * 0.8
                      : windowWidth * 0.849
                    : windowWidth * 0.4,
                height: 80,
                backgroundColor: '#ECECEC',
                // marginTop: 30,
                shadowOffset: {
                  width: 0,
                  height: 2,
                },
                shadowOpacity: 0.22,
                shadowRadius: 3.22,
                elevation: 1,

                marginTop: 10,
              }}>
                <TextInput
                  multiline={true}
                  style={{
                    height: 80,
                    // width: windowWidth < 769 ? 200 : windowWidth * 0.3,
                    width:
                      isMobile()
                        ? windowWidth < 769
                          ? windowWidth * 0.8
                          : windowWidth * 0.849
                        : windowWidth * 0.4,
                    fontFamily: 'NunitoSans-Regular',
                    fontSize: 16,
                    // textAlign: 'center',
                    paddingHorizontal: 10,
                    paddingTop: 10,
                    paddingVertical: 5,
                    // marginTop: 5,
                  }}
                  placeholder="Add special occasions here (Optional)"
                  value={sOccasions ? sOccasions : specialOccasions}
                  onChangeText={(text) => setSpecialOccasions(text)}
                />
              </View>
            </View>

            {/* <View style={{
            backgroundColor: Colors.descriptionColor,
            // height: windowHeight * 0.8,
            // width: windowWidth <= 1586 ? windowWidth * 0.93 : windowWidth * 0.980,
            // margin: 15,
            borderRadius: 50,
            // marginLeft: 20,
            // marginRight: 40,
            marginTop: 20,
            marginHorizontal: 20,
          }}>
            <Text style={{
              fontSize: 14,
              fontFamily: 'NunitoSans-Regular',
              padding: 40,
              textAlign: 'justify',
            }}>
              <h3>Before you reserve</h3>
              POPUP TIME: 𝐂𝐇𝐑𝐈𝐒𝐓𝐌𝐀𝐒 𝐎𝐍 𝐂𝐀𝐍𝐀𝐋 𝐒𝐓. - 𝕀𝕤 𝕓𝕒𝕔𝕜!!!!!!!
              <br /><br />
              The entire Deadfall (ground floor) has been transformed into a full on 𝑯𝒐𝒍𝒊𝒅𝒂𝒚 𝑾𝒐𝒏𝒅𝒆𝒓𝒍𝒂𝒏𝒅. A new version of the special food menu and of course your favourite holiday cocktails are back, even better than last year!!!
              <br /><br />
              Please note that the normal operations menu items are sadly not available during this time but festive variations of everyone's favourites will be available the entire month of December.
              <br /><br />
              Singapore may not have snow for you to slosh through, but we can get you sloshed through the entire season none the less!!
              <br /><br />
              We look forward to sharing this holiday season with you and yours.
              <br /><br />
              xoxoxo,
              <br /><br />
              𝓣𝓮𝓪𝓶 𝓑𝓪𝓻𝓫𝓪𝓻𝔂 𝓒𝓸𝓪𝓼𝓽
              <br /><br />

              𝐔𝐏𝐃𝐀𝐓𝐄𝐃 𝐂𝐎𝐕𝐈𝐃 𝐑𝐄𝐒𝐓𝐑𝐈𝐂𝐓𝐈𝐎𝐍𝐒
              <br /><br />
              𝕌ℕ𝕍𝔸ℂℂ𝕀ℕ𝔸𝕋𝔼𝔻 𝔾𝕌𝔼𝕊𝕋𝕊
              We are so sorry we cannot accommodate you at this moment in time. Please be safe and we hope to host you soon!!
              <br /><br />
              𝕍𝔸ℂℂ𝕀ℕ𝔸𝕋𝔼𝔻 𝔾𝕌𝔼𝕊𝕋𝕊
              Fully Vaccinated Guests can seat together up to 5pax<br />
              "Vaccination" is valid only after 2-week incubation period<br />
              All guests must show Trace Together App for verification<br />
              Trace Together Token users will need to open app as token is not valid by itself<br />
              PET: guests with negative test results may enter up until the 24hr mark of the test. If the test expires during visit we will sadly have to ask you to depart the premises
              <br /><br />
              𝐓𝐡𝐚𝐧𝐤 𝐲𝐨𝐮 𝐟𝐨𝐫 𝐲𝐨𝐮𝐫 𝐩𝐚𝐭𝐢𝐞𝐧𝐜𝐞 𝐚𝐧𝐝 𝐬𝐮𝐩𝐩𝐨𝐫𝐭. 𝐇𝐨𝐩𝐞𝐟𝐮𝐥𝐥𝐲 𝐭𝐡𝐢𝐬 𝐰𝐢𝐥𝐥 𝐩𝐚𝐬𝐬 𝐬𝐨𝐨𝐧 𝐚𝐧𝐝 𝐰𝐞 𝐜𝐚𝐧 𝐚𝐥𝐥 𝐠𝐞𝐭 𝐛𝐚𝐜𝐤 𝐭𝐨 𝐧𝐨𝐫𝐦𝐚𝐥
              <br /><br />
              𝐘𝐎𝐔 𝐖𝐈𝐋𝐋 𝐑𝐄𝐂𝐄𝐈𝐕𝐄 𝐀𝐍 𝐄𝐌𝐀𝐈𝐋 𝐂𝐎𝐍𝐅𝐈𝐑𝐌𝐀𝐓𝐈𝐎𝐍.<br />
              (𝐎𝐔𝐑 𝐓𝐄𝐀𝐌 𝐖𝐈𝐋𝐋 𝐌𝐄𝐒𝐒𝐀𝐆𝐄 𝐕𝐈𝐀 𝐖𝐇𝐀𝐓𝐒𝐀𝐏𝐏 𝐈𝐅 𝐀𝐍𝐘 𝐂𝐇𝐀𝐍𝐆𝐄𝐒)
              <br /><br />
              -For same day reservation changes please send whatsapp to +𝟞𝟝 𝟠𝟠𝟞𝟡 𝟜𝟟𝟡𝟠
              <br /><br />
              -Please note that due to circuit breaker measures, our last order will be at 10pm.
              <br /><br />
              -All reservations are subject to a strict 2 hour return time. We will endeavour to extend your reservation time and/or secure another seat in one of the two venues, however, we are unable to make promises at this time.
              <br /><br />
              We thank you for your support and understanding!
            </Text>
          </View> */}

            <View
              style={{
                // flexDirection: 'row',
                // justifyContent: 'space-between',
                marginTop: 70,
                // marginBottom: -20
                width:
                  isMobile()
                    ? windowWidth < 769
                      ? windowWidth * 0.8
                      : windowWidth * 0.849
                    : windowWidth * 0.4,
              }}>
              <Text style={{
                fontFamily: 'NunitoSans-SemiBold',
                //color: Colors.whiteColor,
                fontSize: 20,
                textAlign: 'left',
                //marginTop: 25,
                // marginRight: windowWidth < 769 ? windowWidth * 0.1 : 142,
                // width: windowWidth < 769 ? 200 : windowWidth * 0.3,
              }}>
                Reservation policy
              </Text>

              <View style={{
                flexDirection: 'row',
                justifyContent: 'center',
                alignSelf: 'center',
                width: '100%',
                // height: 60,
                // width: windowWidth < 769 ? windowWidth * 0.8 : windowWidth * 0.849,
                marginTop: 20,
                marginHorizontal: 20,
              }}>
                <input
                  onChange={() => {
                    setAgree(!agree)
                  }}
                  style={{
                    //alignSelf: 'center',
                    justifyContent: 'center',
                    //borderRadius: 50,

                    marginRight: 10,
                    backgroundColor: Colors.primaryColor,

                    width: 20,
                    height: 20,
                  }}
                  color={Colors.primaryColor}
                  type={'checkbox'}
                  checked={agree}
                />
                <View style={{}}>
                  <View style={{
                    flexDirection: 'row',
                    // width:
                    //   isMobile()
                    //     ? windowWidth < 769
                    //       ? windowWidth * 0.8
                    //       : windowWidth * 0.849
                    //     : windowWidth * 0.4,
                  }}>
                    <Text style={{
                      fontSize: 16,
                      fontFamily: 'NunitoSans-Regular',
                      // textAlign: 'center',
                      // color: Colors.whiteColor,
                      marginRight: 5,
                    }}>
                      I agree to Koodoo's
                    </Text>
                    <TouchableOpacity
                      style={{
                      }}
                      onPress={redirectTo2}
                    >
                      <Text style={{
                        fontSize: 16,
                        fontFamily: 'NunitoSans-Regular',
                        // textAlign: 'center',
                        color: Colors.primaryColor,
                      }}>
                        Terms of Use
                      </Text>
                    </TouchableOpacity>
                    <Text style={{
                      fontSize: 16,
                      fontFamily: 'NunitoSans-Regular',
                      // textAlign: 'center',
                      // color: Colors.whiteColor,
                      marginHorizontal: 5,
                    }}>
                      &
                    </Text>
                    {
                      isMobile()
                        ? null
                        :
                        <TouchableOpacity
                          style={{}}
                          onPress={redirectTo}
                        >
                          <Text style={{
                            fontSize: 16,
                            fontFamily: 'NunitoSans-Regular',
                            // textAlign: 'center',
                            color: Colors.primaryColor,
                          }}>
                            Privacy Policy
                          </Text>
                        </TouchableOpacity>
                    }
                  </View>
                  {
                    isMobile() ?
                      <TouchableOpacity
                        style={{}}
                        onPress={redirectTo}
                      >
                        <Text style={{
                          fontSize: 16,
                          fontFamily: 'NunitoSans-Regular',
                          // textAlign: 'center',
                          color: Colors.primaryColor,
                        }}>
                          Privacy Policy
                        </Text>
                      </TouchableOpacity>
                      : null
                  }
                </View>

              </View>
            </View>
            <View style={{
              flexDirection: 'row',
              justifyContent: 'center',
              alignContent: 'center'
            }}>
              <TouchableOpacity
                style={[styles.btnGreen, {
                  marginTop: 20,
                  width:
                    isMobile()
                      ? windowWidth < 769
                        ? windowWidth * 0.8
                        : windowWidth * 0.849
                      : windowWidth * 0.4,
                  alignSelf: 'center',
                  marginBottom: 50,
                }]}
                disabled={isLoading}
                onPress={() => {
                  // handleCreateReservation();

                  // if (firstName.length === 0 || lastName.length === 0 || phoneNum.length === 0 || email.length === 0) {
                  //     CommonStore.update(s => {
                  //         s.alertObj = {
                  //             title: 'Error',
                  //             message: 'Please fill all the fields',
                  //         };
                  //     });
                  //     return;
                  // }
                  // // validate phone number
                  // if ((phoneNum.length !== 10 && phoneNum.length != 11) || !phoneNum.match(/^[0-9]+$/)) {
                  //     CommonStore.update(s => {
                  //         s.alertObj = {
                  //             title: 'Error',
                  //             message: 'Incorrect Phone number format',
                  //         };
                  //     });
                  //     return;
                  // }

                  //setShowDeposit(true);

                  // TempStore.update(s => {
                  //   s.reservationFirstName = firstName;
                  //   s.reservationLastName = lastName;
                  //   s.reservationPhoneNum = !phoneNum.startsWith('0') ? `0${phoneNum}` : phoneNum;
                  //   s.reservationEmail = email;
                  //   s.reservationRemarks = remarks;
                  //   s.reservationDietaryRestrictions = dietaryRestrictions;
                  //   s.reservationSpecialOccasions = specialOccasions;

                  //   s.isPlacingReservation = true;

                  //   s.isDepositOnly = true;
                  // });

                  // CommonStore.update(s => {
                  //   s.orderType = ORDER_TYPE.DINEIN;
                  // });

                  // linkTo &&
                  //   linkTo(`${prefix}/outlet/${selectedOutlet.subdomain}/menu`);

                  if (agree && !isLoading) {
                    // 2023-06-06 - Check cart items first

                    // commented to test first

                    if (cartItems && cartItems.length > 0) {
                      if (firstName.length === 0 || lastName.length === 0 || phoneNum.length === 0 || email.length === 0) {
                        CommonStore.update(s => {
                          s.alertObj = {
                            title: 'Info',
                            message: 'Please fill in the following fields:\n\n1. First Name\n2. Last Name\n3. Phone Number\n4. Email Address',
                          };
                        });
                        setShowDeposit(false);
                        return;
                      }

                      const phoneNumParsed = phoneNum.replace(/\D/g, '');

                      // validate phone number
                      if (phoneNumParsed.length === 9 || phoneNumParsed.length === 10 || phoneNumParsed.length === 11 ||
                        (
                          (phoneNumParsed.startsWith('011') && phoneNumParsed.length === 11)
                          ||
                          (phoneNumParsed.startsWith('6011') && phoneNumParsed.length === 12)
                        )
                      ) {

                      }
                      else {
                        CommonStore.update(s => {
                          s.alertObj = {
                            title: 'Error',
                            message: 'Incorrect phone number format',
                          };
                        });
                        setShowDeposit(false);
                        return;
                      }

                      CommonStore.update(s => {
                        s.resFirstName = firstName;
                        s.resLastName = lastName;
                        s.resPhoneNum = !phoneNumParsed.startsWith('6') ? `6${phoneNumParsed}` : phoneNumParsed;
                        s.resEmail = email;
                        s.resRemarks = remarks;
                        s.resDietaryRestrictions = dietaryRestrictions;
                        s.resSpecialOccasions = specialOccasions;

                        s.isPlacingReservation = true;

                        s.isDepositOnly = false;

                        /////////////////////////////////////

                        s.orderType = ORDER_TYPE.DINEIN;
                        s.molpayResult = null;
                        s.payHybridBody = null;
                        s.paymentDetails = null;
                        s.orderIdCreated = '';

                        s.payTopupCreditBody = null;

                        s.selectedOutletTableId = '';
                        s.selectedOutletWaiterId = '';
                        s.selectedOutletTablePax = 1;
                        s.selectedOutletTableCode = '';
                      });

                      linkTo &&
                        linkTo(`${prefix}/outlet/${selectedOutlet.subdomain}/cart`);

                      // handleCreateReservation();
                    }
                    else {
                      handleCreateReservation();
                    }

                    // mock up testing code
                    // CommonStore.update(s => {
                    //   s.selectedReservationId = '6dc2c506-9e09-49da-b195-6150cea12ce1';

                    //   s.isPlacingReservation = true;

                    //   s.resShowConfirmationPage = true;
                    // });

                    // if (upsellingCampaignsReservation.length > 0) {
                    //   linkTo &&
                    //     linkTo(`${prefix}/outlet/${selectedOutlet.subdomain}/upsell-reservation`);
                    // };
                  }
                  else {
                    alert('Please agree to the Koodoo\'s Terms of Use & Privacy Policy');
                  }
                }}
              >
                <View>
                  <View style={{
                    flexDirection: 'row',
                    justifyContent: 'center',
                    alignContent: 'center',
                  }}>
                    {/* <Bell
                                        width={20}
                                        height={20}
                                        color={Colors.whiteColor}
                                    /> */}
                    <Text style={{
                      fontSize: 18,
                      fontFamily: 'NunitoSans-Regular',
                      textAlign: 'center',
                      color: Colors.whiteColor,
                    }}>
                      Confirm Reservation Details
                    </Text>
                  </View>
                </View>
              </TouchableOpacity>
            </View>
          </View>
        </View>
        :
        <View style={{
          width: isMobile() ? windowWidth : windowWidth,
          height: windowHeight,
          //backgroundColor: 'blue'
        }}>
          <View style={{
            width: isMobile() ? windowWidth : windowWidth,
            //height: windowHeight,
            backgroundColor: Colors.darkBgColor,
            flexDirection: 'row',
          }}>
            <View style={{
              width: isMobile() ? windowWidth : windowWidth,
              height: 100,
              backgroundColor: Colors.darkBgColor,
            }}>
              <View style={{
                //marginRight: 107,
                marginTop: 10,
              }}>
                <Image style={{
                  width: 100,
                  height: 47,
                  alignSelf: 'center',
                  //marginBottom: 25.
                }} resizeMode="contain" source={imgLogo} />
                <Text style={{
                  fontFamily: 'NunitoSans-Bold',
                  color: Colors.whiteColor,
                  fontSize: 16,
                  textAlign: 'center',
                  marginTop: 0,
                  // width: '100%',
                }}>
                  Confirmation
                </Text>
              </View>
            </View>

          </View>
          <View style={{
            //justifyContent: 'center',
            alignItems: 'center',

            width: isMobile() ? windowWidth : windowWidth,
            height: windowHeight,
          }}>
            <Text style={{
              fontFamily: 'NunitoSans-Regular',
              //color: Colors.whiteColor,
              fontSize: 18,
              textAlign: 'center',
              marginTop: 30,
              marginHorizontal: windowWidth < 769 ? windowWidth * 0.1 : 142,
            }}>
              Reservation for <b>{selectedReservationPax}</b> people at <br /><b>{moment(selectedReservationStartTime).format('hh:mm A')}, </b>
              <b>{moment(selectedReservationStartTime).format('MMMM Do YYYY')}</b>
              <br />in the <b>{selectedOutlet ? selectedOutlet.name : ''}</b> is confirmed
            </Text>
            <View>
              <Text style={{
                fontFamily: 'NunitoSans-Regular',
                //color: 'grey',
                fontSize: 16,
                textAlign: 'center',
                marginTop: 30,
                //marginHorizontal: windowWidth < 769 ? windowWidth * 0.1 : 142,
                marginHorizontal: 20,
              }}>
                We've sent you an email with all the information, <br />
                In case you cannot find it, please check your junk mail folder.
              </Text>

              <View style={{
                flexDirection: 'row',
                justifyContent: 'center',
                alignContent: 'center'
              }}>
                <TouchableOpacity
                  style={{
                    //backgroundColor: Colors.primaryColor,
                    borderRadius: 5,
                    borderWidth: 1,
                    borderColor: Colors.primaryColor,

                    height: 50,
                    justifyContent: 'center',
                    alignContent: 'center',
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 1,
                    zIndex: -1,
                    marginTop: 20,
                    width:
                      isMobile()
                        ? windowWidth < 769
                          ? windowWidth * 0.8
                          : windowWidth * 0.849
                        : windowWidth * 0.4,
                    alignSelf: 'center',
                    marginBottom: 50,
                  }}
                  onPress={() => {
                    // setShowConfirmation(false);

                    CommonStore.update(s => {
                      s.resShowConfirmationPage = false;
                    });

                    linkTo &&
                      linkTo(`${prefix}/outlet/${selectedOutlet.subdomain}/reservation`);

                    //handleCreateReservation();
                  }}
                >
                  <View>
                    <View style={{
                      flexDirection: 'row',
                      justifyContent: 'center',
                      alignContent: 'center',
                    }}>
                      <Text style={{
                        fontSize: 18,
                        fontFamily: 'NunitoSans-Regular',
                        textAlign: 'center',
                        color: Colors.primaryColor,
                      }}>
                        Make another reservation
                      </Text>
                    </View>
                  </View>
                </TouchableOpacity>
              </View>
            </View>

            <View
              style={{
                // flexDirection: 'row',
                // justifyContent: 'space-between',
                marginTop: 70,
                // marginBottom: -20
                width:
                  isMobile()
                    ? windowWidth < 769
                      ? windowWidth * 0.8
                      : windowWidth * 0.849
                    : windowWidth * 0.4,
                height: windowHeight * 0.3
              }}>
              <Text style={{
                fontFamily: 'NunitoSans-SemiBold',
                //color: Colors.whiteColor,
                fontSize: 20,
                textAlign: 'left',
                //marginTop: 25,
                // marginRight: windowWidth < 769 ? windowWidth * 0.1 : 142,
                // width: windowWidth < 769 ? 200 : windowWidth * 0.3,
              }}>

              </Text>

              <Text style={{
                fontFamily: 'NunitoSans-Regular',
                //color: Colors.whiteColor,
                fontSize: 16,
                textAlign: 'left',
                marginTop: 10,
                // marginRight: windowWidth < 769 ? windowWidth * 0.1 : 142,
                // width: windowWidth < 769 ? 200 : windowWidth * 0.3,
              }}>

              </Text>

              <View style={{
                flexDirection: 'row',
                justifyContent: 'center',
                alignSelf: 'center',
                width: '100%',
                // height: 60,
                // width: windowWidth < 769 ? windowWidth * 0.8 : windowWidth * 0.849,
                marginTop: 20,
                marginHorizontal: 20,
              }}>

              </View>
            </View>
            <View style={{
              flexDirection: 'row',
              justifyContent: 'center',
              alignContent: 'center'
            }}>
              <TouchableOpacity
                style={[styles.btnGreen, {
                  marginTop: 20,
                  width:
                    isMobile()
                      ? windowWidth < 769
                        ? windowWidth * 0.8
                        : windowWidth * 0.849
                      : windowWidth * 0.4,
                  alignSelf: 'center',
                  position: 'absolute',
                  top: 100
                  //marginBottom: 50,
                }]}
                onPress={() => {
                  // handleCreateReservation();

                  // if (firstName.length === 0 || lastName.length === 0 || phoneNum.length === 0 || email.length === 0) {
                  //     CommonStore.update(s => {
                  //         s.alertObj = {
                  //             title: 'Error',
                  //             message: 'Please fill all the fields',
                  //         };
                  //     });
                  //     return;
                  // }
                  // // validate phone number
                  // if ((phoneNum.length !== 10 && phoneNum.length != 11) || !phoneNum.match(/^[0-9]+$/)) {
                  //     CommonStore.update(s => {
                  //         s.alertObj = {
                  //             title: 'Error',
                  //             message: 'Incorrect Phone number format',
                  //         };
                  //     });
                  //     return;
                  // }

                  //setShowDeposit(true);

                  // TempStore.update(s => {
                  //   s.reservationFirstName = firstName;
                  //   s.reservationLastName = lastName;
                  //   s.reservationPhoneNum = !phoneNum.startsWith('0') ? `0${phoneNum}` : phoneNum;
                  //   s.reservationEmail = email;
                  //   s.reservationRemarks = remarks;
                  //   s.reservationDietaryRestrictions = dietaryRestrictions;
                  //   s.reservationSpecialOccasions = specialOccasions;

                  //   s.isPlacingReservation = true;

                  //   s.isDepositOnly = true;
                  // });

                  // CommonStore.update(s => {
                  //   s.orderType = ORDER_TYPE.DINEIN;
                  // });

                  // setShowConfirmation(false);

                  CommonStore.update(s => {
                    s.resShowConfirmationPage = false;
                  });

                  var reservationIdEncrypted = hashids.encodeHex(
                    selectedReservationId.replaceAll('-', ''),
                  );

                  linkTo && linkTo(`${prefix}/outlet/${selectedOutlet.subdomain}/reservation-details/${reservationIdEncrypted}`);

                  //handleCreateReservation();
                }}
              >
                <View>
                  <View style={{
                    flexDirection: 'row',
                    justifyContent: 'center',
                    alignContent: 'center',
                  }}>
                    {/* <Bell
                                        width={20}
                                        height={20}
                                        color={Colors.whiteColor}
                                    /> */}
                    <Text style={{
                      fontSize: 18,
                      fontFamily: 'NunitoSans-Regular',
                      textAlign: 'center',
                      color: Colors.whiteColor,
                    }}>
                      Done
                    </Text>
                  </View>
                </View>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      }
    </ScrollView >
  );
};

const styles = StyleSheet.create({
  btnWhite: {
    borderColor: Colors.primaryColor,
    borderWidth: 1,
    borderRadius: 5,
    height: 50,
    justifyContent: 'center',
    alignContent: 'center',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 1,
    zIndex: -1,
  },
  btnGreen: {
    backgroundColor: Colors.primaryColor,
    borderRadius: 5,
    height: 50,
    justifyContent: 'center',
    alignContent: 'center',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 1,
    zIndex: -1,
  },
  daysWrd: {
    fontSize: 16,
    fontFamily: 'NunitoSans-Regular',
    textAlign: 'center',
  },
  dateWrd: {
    fontSize: 18,
    fontFamily: 'NunitoSans-Regular',
    textAlign: 'center',
    color: Colors.primaryColor,
  },
})

export default ReservationDetailsScreen;