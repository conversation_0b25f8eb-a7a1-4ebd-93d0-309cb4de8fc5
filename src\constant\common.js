export const APP_TYPE = {
    MERCHANT: 'MERCHANT',
    WAITER: 'WAITER',
    USER: 'USER',

    SERVICES: 'SERVICES',
    RETAIL: 'RETAIL',

    WEB_ORDER: 'WEB_ORDER',
};

export const WEEK = {
    0: 'sunday',
    1: 'monday',
    2: 'tuesday',
    3: 'wednesday',
    4: 'thursday',
    5: 'friday',
    6: 'saturday',

};

export const USER_ORDER_STATUS = {
    ORDER_RECEIVED: 'ORDER_RECEIVED',

    ORDER_AUTHORIZED: 'ORDER_AUTHORIZED',

    ORDER_PREPARING: 'ORDER_PREPARING',
    ORDER_PREPARED: 'ORDER_PREPARED',
    ORDER_DELIVERED: 'ORDER_DELIVERED',
    ORDER_COMPLETED: 'ORDER_COMPLETED',

    ORDER_REJECTED_BY_MERCHANT: 'ORDER_REJECTED_BY_MERCHANT',

    ORDER_CANCELLED_BY_MERCHANT: 'ORDER_CANCELLED_BY_MERCHANT',
    ORDER_CANCELLED_BY_USER: 'ORDER_CANCELLED_BY_USER',

    ORDER_SENDER_REJECTED: 'ORDER_SENDER_REJECTED',
    ORDER_SENDER_CANCELED: 'ORDER_SENDER_CANCELED',
    ORDER_SENDER_EXPIRED: 'ORDER_SENDER_EXPIRED',
};

export const ORDER_TYPE = {
    DINEIN: 'DINEIN',
    DELIVERY: 'DELIVERY',
    PICKUP: 'PICKUP',
    BEER_DOCKET: 'BEER_DOCKET',

    INVALID: 'INVALID',
};

export const ORDER_TYPE_SUB = {
    NORMAL: 'NORMAL',
    OTHER_DELIVERY: 'OTHER_DELIVERY',
};

export const ORDER_TYPE_PARSED = {
    DINEIN: 'Dine In',
    DELIVERY: 'Delivery',
    PICKUP: 'Takeaway',
    BEER_DOCKET: 'Beer Docket',
};

export const CHANNEL_TYPE = {
    DINEIN_QR: 'DINEIN_QR',
    DINEIN_POS: 'DINEIN_POS',

    PICKUP_QR: 'PICKUP_QR',
    PICKUP_POS: 'PICKUP_POS',
};

export const ADDRESS_TYPE = {
    HOME: 'HOME',
    WORK: 'WORK',
    OTHER: 'OTHER',
};

export const USER_RESERVATION_STATUS = {
    PENDING: 'PENDING',
    ACCEPTED: 'ACCEPTED',
    CANCELED: 'CANCELED',
    SEATED: 'SEATED',
};

export const USER_QUEUE_STATUS = {
    PENDING: 'PENDING',
    ACCEPTED: 'ACCEPTED',
    CANCELED: 'CANCELED',
    SEATED: 'SEATED',
};

export const DEFAULT_USER_QUEUE_CATEGORIES = [
    {
        name: '1 - 2 Pax',
        minPax: 1,
        maxPax: 2
    },
    {
        name: '3 - 4 Pax',
        minPax: 3,
        maxPax: 4
    },
    {
        name: '5 - 8 Pax',
        minPax: 5,
        maxPax: 8
    },
    {
        name: '8 Pax & Above',
        minPax: 8,
        maxPax: null
    }
];

export const USER_RING_STATUS = {
    PENDING: 'PENDING',
    ACCEPTED: 'ACCEPTED',
    CANCELED: 'CANCELED',
    ATTENDED: 'ATTENDED',
};

export const COURIER_CODE = {
    LALAMOVE: 'LALAMOVE',
    // Mr Speedy excluded
    // MRSPEEDY: 'MRSPEEDY',
    TELEPORT: 'TELEPORT',
};

export const SENDER_DROPDOWN_LIST = [
    {
        label: 'Lalamove',
        value: COURIER_CODE.LALAMOVE,
        // img: require('../asset/image/courier-lalamove.png'),
    },
    // Mr Speedy excluded
    // {
    //     label: 'MrSpeedy',
    //     value: COURIER_CODE.MRSPEEDY,
    //     img: require('../asset/image/courier-mrspeedy.png'),
    // },
    {
        label: 'Teleport',
        value: COURIER_CODE.TELEPORT,
        // img: require('../asset/image/courier-teleport.png'),
    },
];

export const COURIER_INFO_DICT = {
    [COURIER_CODE.LALAMOVE]: {
        name: 'Lalamove',
        // img: require('../asset/image/courier-lalamove.png'),
    },
    // Mr Speedy excluded
    // [COURIER_CODE.MRSPEEDY]: {
    //     name: 'MrSpeedy',
    //     img: require('../asset/image/courier-mrspeedy.png'),
    // },
    [COURIER_CODE.TELEPORT]: {
        name: 'Teleport',
        // img: require('../asset/image/courier-teleport.png'),
    },
}

export const CREDIT_CARD_TYPE = {
    VISA: 'Visa',
    MASTER_CARD: 'MasterCard',
};

export const MERCHANT_VOUCHER_TYPE = {
    CASH_VOUCHER: 'CASH_VOUCHER',
};

export const MERCHANT_VOUCHER_STATUS = {
    ACTIVE: 'ACTIVE',
    DISABLED: 'DISABLED',
    EXPIRED: 'EXPIRED',
};

export const LALAMOVE_STATUS = {
    ASSIGNING_DRIVER: 'ASSIGNING_DRIVER',
    ON_GOING: 'ON_GOING',
    PICKED_UP: 'PICKED_UP',
    COMPLETED: 'COMPLETED',
    REJECTED: 'REJECTED',
    CANCELED: 'CANCELED',
    EXPIRED: 'EXPIRED',
};
export const MRSPEEDY_STATUS = {
    new: 'new',
    available: 'available',
    active: 'active',
    completed: 'completed',
    reactivated: 'reactivated',
    draft: 'draft',
    canceled: 'canceled',
    delayed: 'delayed',
};

export const TELEPORT_STATUS = {
    new: 'new',
    available: 'available',
    active: 'active',
    completed: 'completed',
    reactivated: 'reactivated',
    draft: 'draft',
    canceled: 'canceled',
    delayed: 'delayed',
};

export const VERIFALIA_STATUS = {
    DELIVERABLE: 'DELIVERABLE',

    API_NOT_AVAILABLE: 'API_NOT_AVAILABLE',
};

export const USER_ORDER_PAYMENT_OPTIONS = {
    ONLINE: 'ONLINE',
    OFFLINE: 'OFFLINE',
    ALL: 'ALL',
};

export const QR_SCANNING_TYPE = {
    DINEIN: 'DINEIN',
    BEER_DOCKET: 'BEER_DOCKET',
};

export const NOTIFICATIONS_TYPE = {
    USER_ORDER: 'USER_ORDER',
    USER_RING: 'USER_RING',
    PROMOTION_NOTIFICATION_MANUAL: 'PROMOTION_NOTIFICATION_MANUAL',
    PROMOTION_NOTIFICATION_AUTO: 'PROMOTION_NOTIFICATION_AUTO',
    USER_ORDER_COURIER_ACTION: 'USER_ORDER_COURIER_ACTION',
};


export const NOTIFICATIONS_ID = {
    USER_ORDER: '1',
    USER_RING: '2',
    PROMOTION_NOTIFICATION_MANUAL: '3',
    PROMOTION_NOTIFICATION_AUTO: '4',
    USER_ORDER_COURIER_ACTION: '5',
};

const NOTIFICATIONS_CHANNELS_VERSIONS = 'v1.01'; // v3

export const NOTIFICATIONS_CHANNEL = {
    USER_ORDER: `com.mykoodoo.merchant.${NOTIFICATIONS_ID.USER_ORDER}.${NOTIFICATIONS_CHANNELS_VERSIONS}`,
    USER_RING: `com.mykoodoo.merchant.${NOTIFICATIONS_ID.USER_RING}.${NOTIFICATIONS_CHANNELS_VERSIONS}`,
    PROMOTION_NOTIFICATION_MANUAL: `com.mykoodoo.merchant.${NOTIFICATIONS_ID.PROMOTION_NOTIFICATION_MANUAL}.${NOTIFICATIONS_CHANNELS_VERSIONS}`,
    PROMOTION_NOTIFICATION_AUTO: `com.mykoodoo.merchant.${NOTIFICATIONS_ID.PROMOTION_NOTIFICATION_AUTO}.${NOTIFICATIONS_CHANNELS_VERSIONS}`,
    USER_ORDER_COURIER_ACTION: `com.mykoodoo.merchant.${NOTIFICATIONS_ID.USER_ORDER_COURIER_ACTION}.${NOTIFICATIONS_CHANNELS_VERSIONS}`,
};

export const NOTIFICATIONS_CHANNEL_LIST = [
    {
        channelId: NOTIFICATIONS_CHANNEL.USER_ORDER,
        channelName: 'User Order',
        id: NOTIFICATIONS_ID.USER_ORDER,
    },
    {
        channelId: NOTIFICATIONS_CHANNEL.USER_RING,
        channelName: 'User Ring',
        id: NOTIFICATIONS_ID.USER_RING,
    },
    {
        channelId: NOTIFICATIONS_CHANNEL.PROMOTION_NOTIFICATION_MANUAL,
        channelName: 'Promotion Notification Manual',
        id: NOTIFICATIONS_ID.PROMOTION_NOTIFICATION_MANUAL,
    },
    {
        channelId: NOTIFICATIONS_CHANNEL.PROMOTION_NOTIFICATION_AUTO,
        channelName: 'Promotion Notification Auto',
        id: NOTIFICATIONS_ID.PROMOTION_NOTIFICATION_AUTO,
    },
    {
        channelId: NOTIFICATIONS_CHANNEL.USER_ORDER_COURIER_ACTION,
        channelName: 'User Order Courier Action',
        id: NOTIFICATIONS_ID.USER_ORDER_COURIER_ACTION,
    },
];

export const MPS_CHANNEL = {
    fpx_amb: 'fpx_amb',
    fpx_bimb: 'fpx_bimb',
    fpx_cimbclicks: 'fpx_cimbclicks',
    fpx_hlb: 'fpx_hlb',
    fpx_mb2u: 'fpx_mb2u',
    fpx_pbb: 'fpx_pbb',
    fpx_rhb: 'fpx_rhb',
    FPX_OCBC: 'FPX_OCBC',
    FPX_SCB: 'FPX_SCB',
    FPX_ABB: 'FPX_ABB',
    FPX_BKRM: 'FPX_BKRM',
    FPX_BMMB: 'FPX_BMMB',
    FPX_KFH: 'FPX_KFH',
    FPX_BSN: 'FPX_BSN',
    FPX_ABMB: 'FPX_ABMB',
    FPX_UOB: 'FPX_UOB',
    credit: 'credit',

    credit3: 'credit3',
    crossborder: 'crossborder',
    credit5: 'credit5',
    creditO: 'creditO',
    credit18: 'credit18',
    creditAN: 'creditAN',
    creditAI: 'creditAI',
    credit10: 'credit10',

    BOOST: 'BOOST',
    'MB2U_QRPay-Push': 'MB2U_QRPay-Push',
    GrabPay: 'GrabPay',
    'TNG-EWALLET': 'TNG-EWALLET',
    ShopeePay: 'ShopeePay',
};

export const MPS_CHANNEL_PARSED = {
    fpx_amb: 'FPX Am Bank (Am Online)',
    fpx_bimb: 'FPX Bank Islam',
    fpx_cimbclicks: 'FPX CIMB Bank(CIMB Clicks)',
    fpx_hlb: 'FPX Hong Leong Bank(HLB Connect)',
    fpx_mb2u: 'FPX Maybank(Maybank2u)',
    fpx_pbb: 'FPX PublicBank (PBB Online)',
    fpx_rhb: 'FPX RHB Bank(RHB Now)',
    FPX_OCBC: 'FPX OCBC Bank',
    FPX_SCB: 'FPX Standard Chartered Bank',
    FPX_ABB: 'FPX Affin Bank Berhad',
    FPX_BKRM: 'FPX Bank Kerjasama Rakyat Malaysia Berhad',
    FPX_BMMB: 'FPX Bank Muamalat',
    FPX_KFH: 'FPX Kuwait Finance House',
    FPX_BSN: 'FPX Bank Simpanan Nasional',
    FPX_ABMB: 'FPX Alliance Bank Malaysia Berhad',
    FPX_UOB: 'FPX United Overseas Bank',
    credit: 'Credit Card/ Debit Card',

    credit3: 'Credit Card/ Debit Card',
    crossborder: 'Credit Card/ Debit Card', // might workable
    credit5: 'Credit Card/ Debit Card',
    creditO: 'Credit Card/ Debit Card',
    credit18: 'Credit Card/ Debit Card',
    creditAN: 'Credit Card/ Debit Card (AN)', // might workable
    creditAI: 'Credit Card/ Debit Card',
    credit10: 'Credit Card/ Debit Card (eBPG)',

    BOOST: 'BOOST',
    'MB2U_QRPay-Push': 'Maybank QRPay',
    GrabPay: 'GrabPay',
    'TNG-EWALLET': `Touch 'N Go`,
    ShopeePay: 'ShopeePay',
};

export const MPS_CHANNEL_LIST = [
    {
        label: MPS_CHANNEL_PARSED.fpx_amb,
        value: MPS_CHANNEL.fpx_amb,
        // image: require('../asset/image/banks/logo-ambank.png'),
    },
    {
        label: MPS_CHANNEL_PARSED.fpx_bimb,
        value: MPS_CHANNEL.fpx_bimb,
        // image: require('../asset/image/banks/logo-bank-islam.png'),
    },
    {
        label: MPS_CHANNEL_PARSED.fpx_cimbclicks,
        value: MPS_CHANNEL.fpx_cimbclicks,
        // image: require('../asset/image/banks/logo-cimb-bank.png'),
    },
    {
        label: MPS_CHANNEL_PARSED.fpx_hlb,
        value: MPS_CHANNEL.fpx_hlb,
        // image: require('../asset/image/banks/logo-hong-leong-bank.png'),
    },
    {
        label: MPS_CHANNEL_PARSED.fpx_mb2u,
        value: MPS_CHANNEL.fpx_mb2u,
        // image: require('../asset/image/banks/logo-maybank.png'),
    },
    {
        label: MPS_CHANNEL_PARSED.fpx_pbb,
        value: MPS_CHANNEL.fpx_pbb,
        // image: require('../asset/image/banks/logo-public-bank.png'),
    },
    {
        label: MPS_CHANNEL_PARSED.fpx_rhb,
        value: MPS_CHANNEL.fpx_rhb,
        // image: require('../asset/image/banks/logo-rhb.png'),
    },
    {
        label: MPS_CHANNEL_PARSED.FPX_OCBC,
        value: MPS_CHANNEL.FPX_OCBC,
        // image: require('../asset/image/banks/logo-ocbc.png'),
    },
    {
        label: MPS_CHANNEL_PARSED.FPX_SCB,
        value: MPS_CHANNEL.FPX_SCB,
        // image: require('../asset/image/banks/logo-standard-chartered.png'),
    },
    {
        label: MPS_CHANNEL_PARSED.FPX_ABB,
        value: MPS_CHANNEL.FPX_ABB,
        // image: require('../asset/image/banks/logo-affinbank.png'),
    },
    {
        label: MPS_CHANNEL_PARSED.FPX_BKRM,
        value: MPS_CHANNEL.FPX_BKRM,
        // image: require('../asset/image/banks/logo-bank-rakyat.png'),
    },
    {
        label: MPS_CHANNEL_PARSED.FPX_BMMB,
        value: MPS_CHANNEL.FPX_BMMB,
        // image: require('../asset/image/banks/logo-bank-muamalat.png'),
    },
    {
        label: MPS_CHANNEL_PARSED.FPX_KFH,
        value: MPS_CHANNEL.FPX_KFH,
        // image: require('../asset/image/banks/logo-kuwait-finance.png'),
    },
    {
        label: MPS_CHANNEL_PARSED.FPX_BSN,
        value: MPS_CHANNEL.FPX_BSN,
        // image: require('../asset/image/banks/logo-bsn.png'),
    },
    {
        label: MPS_CHANNEL_PARSED.FPX_ABMB,
        value: MPS_CHANNEL.FPX_ABMB,
        // image: require('../asset/image/banks/logo-alliance-bank.png'),
    },
    {
        label: MPS_CHANNEL_PARSED.FPX_UOB,
        value: MPS_CHANNEL.FPX_UOB,
        // image: require('../asset/image/banks/logo-uob.png'),
    },

    {
        label: MPS_CHANNEL_PARSED.creditAN,
        value: MPS_CHANNEL.creditAN,
        image: require('../asset/image/banks/logo-visa.png'),
        // image2: require('../asset/image/banks/logo-mastercard.png'),
    },

    // {
    //     label: MPS_CHANNEL_PARSED.BOOST,
    //     value: MPS_CHANNEL.BOOST,
    //     image: require('../asset/image/offline_payment_method_boost.png'),
    //     image2: require('../asset/image/offline_payment_method_boost.png'),
    // },
    // {
    //     label: MPS_CHANNEL_PARSED['MB2U_QRPay-Push'],
    //     value: MPS_CHANNEL['MB2U_QRPay-Push'],
    //     image: require('../asset/image/offline_payment_method_maybank_qrpay.png'),
    //     image2: require('../asset/image/offline_payment_method_maybank_qrpay.png'),
    // },
    // {
    //     label: MPS_CHANNEL_PARSED.GrabPay,
    //     value: MPS_CHANNEL.GrabPay,
    //     image: require('../asset/image/offline_payment_method_grabpay.png'),
    //     image2: require('../asset/image/offline_payment_method_grabpay.png'),
    // },
    // {
    //     label: MPS_CHANNEL_PARSED['TNG-EWALLET'],
    //     value: MPS_CHANNEL['TNG-EWALLET'],
    //     image: require('../asset/image/offline_payment_method_touchngo_ewallet.png'),
    //     image2: require('../asset/image/offline_payment_method_touchngo_ewallet.png'),
    // },
    // {
    //     label: MPS_CHANNEL_PARSED.ShopeePay,
    //     value: MPS_CHANNEL.ShopeePay,
    //     image: require('../asset/image/offline_payment_method_shopeepay.png'),
    //     image2: require('../asset/image/offline_payment_method_shopeepay.png'),
    // },

];

export const MPS_CHANNEL_LIST_FULL = [
    {
        label: MPS_CHANNEL_PARSED.fpx_amb,
        value: MPS_CHANNEL.fpx_amb,
        // image: require('../asset/image/banks/logo-ambank.png'),
    },
    {
        label: MPS_CHANNEL_PARSED.fpx_bimb,
        value: MPS_CHANNEL.fpx_bimb,
        // image: require('../asset/image/banks/logo-bank-islam.png'),
    },
    {
        label: MPS_CHANNEL_PARSED.fpx_cimbclicks,
        value: MPS_CHANNEL.fpx_cimbclicks,
        // image: require('../asset/image/banks/logo-cimb-bank.png'),
    },
    {
        label: MPS_CHANNEL_PARSED.fpx_hlb,
        value: MPS_CHANNEL.fpx_hlb,
        // image: require('../asset/image/banks/logo-hong-leong-bank.png'),
    },
    {
        label: MPS_CHANNEL_PARSED.fpx_mb2u,
        value: MPS_CHANNEL.fpx_mb2u,
        // image: require('../asset/image/banks/logo-maybank.png'),
    },
    {
        label: MPS_CHANNEL_PARSED.fpx_pbb,
        value: MPS_CHANNEL.fpx_pbb,
        // image: require('../asset/image/banks/logo-public-bank.png'),
    },
    {
        label: MPS_CHANNEL_PARSED.fpx_rhb,
        value: MPS_CHANNEL.fpx_rhb,
        // image: require('../asset/image/banks/logo-rhb.png'),
    },
    {
        label: MPS_CHANNEL_PARSED.FPX_OCBC,
        value: MPS_CHANNEL.FPX_OCBC,
        // image: require('../asset/image/banks/logo-ocbc.png'),
    },
    {
        label: MPS_CHANNEL_PARSED.FPX_SCB,
        value: MPS_CHANNEL.FPX_SCB,
        // image: require('../asset/image/banks/logo-standard-chartered.png'),
    },
    {
        label: MPS_CHANNEL_PARSED.FPX_ABB,
        value: MPS_CHANNEL.FPX_ABB,
        // image: require('../asset/image/banks/logo-affinbank.png'),
    },
    {
        label: MPS_CHANNEL_PARSED.FPX_BKRM,
        value: MPS_CHANNEL.FPX_BKRM,
        // image: require('../asset/image/banks/logo-bank-rakyat.png'),
    },
    {
        label: MPS_CHANNEL_PARSED.FPX_BMMB,
        value: MPS_CHANNEL.FPX_BMMB,
        // image: require('../asset/image/banks/logo-bank-muamalat.png'),
    },
    {
        label: MPS_CHANNEL_PARSED.FPX_KFH,
        value: MPS_CHANNEL.FPX_KFH,
        // image: require('../asset/image/banks/logo-kuwait-finance.png'),
    },
    {
        label: MPS_CHANNEL_PARSED.FPX_BSN,
        value: MPS_CHANNEL.FPX_BSN,
        // image: require('../asset/image/banks/logo-bsn.png'),
    },
    {
        label: MPS_CHANNEL_PARSED.FPX_ABMB,
        value: MPS_CHANNEL.FPX_ABMB,
        // image: require('../asset/image/banks/logo-alliance-bank.png'),
    },
    {
        label: MPS_CHANNEL_PARSED.FPX_UOB,
        value: MPS_CHANNEL.FPX_UOB,
        // image: require('../asset/image/banks/logo-uob.png'),
    },

    {
        label: MPS_CHANNEL_PARSED.creditAN,
        value: MPS_CHANNEL.creditAN,
        // image: require('../asset/image/banks/logo-visa.png'),
        // image2: require('../asset/image/banks/logo-mastercard.png'),
    },

    {
        label: MPS_CHANNEL_PARSED.BOOST,
        value: MPS_CHANNEL.BOOST,
        // image: require('../asset/image/offline_payment_method_boost.png'),
        // image2: require('../asset/image/offline_payment_method_boost.png'),
    },
    // {
    //     label: MPS_CHANNEL_PARSED['MB2U_QRPay-Push'],
    //     value: MPS_CHANNEL['MB2U_QRPay-Push'],
    //     // image: require('../asset/image/offline_payment_method_maybank_qrpay.png'),
    //     // image2: require('../asset/image/offline_payment_method_maybank_qrpay.png'),
    // },
    {
        label: MPS_CHANNEL_PARSED.GrabPay,
        value: MPS_CHANNEL.GrabPay,
        // image: require('../asset/image/offline_payment_method_grabpay.png'),
        // image2: require('../asset/image/offline_payment_method_grabpay.png'),
    },
    {
        label: MPS_CHANNEL_PARSED['TNG-EWALLET'],
        value: MPS_CHANNEL['TNG-EWALLET'],
        // image: require('../asset/image/offline_payment_method_touchngo_ewallet.png'),
        // image2: require('../asset/image/offline_payment_method_touchngo_ewallet.png'),
    },
    {
        label: MPS_CHANNEL_PARSED.ShopeePay,
        value: MPS_CHANNEL.ShopeePay,
        // image: require('../asset/image/offline_payment_method_shopeepay.png'),
        // image2: require('../asset/image/offline_payment_method_shopeepay.png'),
    },
];

export const MPS_CHANNEL2 = {
    fpx_amb: 'fpx_amb',
    fpx_bimb: 'fpx_bimb',
    fpx_cimbclicks: 'fpx_cimbclicks',
    fpx_mb2u: 'fpx_mb2u',
    fpx_pbb: 'fpx_pbb',
    fpx_rhb: 'fpx_rhb',
    FPX_OCBC: 'FPX_OCBC',
    FPX_ABMB: 'FPX_ABMB',
    fpx_hlb: 'fpx_hlb',
    FPX_SCB: 'FPX_SCB',
    FPX_ABB: 'FPX_ABB',
    FPX_BKRM: 'FPX_BKRM',
    FPX_BMMB: 'FPX_BMMB',
    FPX_KFH: 'FPX_KFH',
    FPX_BSN: 'FPX_BSN',

    FPX_UOB: 'FPX_UOB',
    credit: 'credit',

    credit3: 'credit3',
    crossborder: 'crossborder',
    credit5: 'credit5',
    creditO: 'creditO',
    credit18: 'credit18',
    creditAN: 'creditAN',
    creditAI: 'creditAI',
    credit10: 'credit10',

    GrabPay: 'GrabPay',
    'TNG-EWALLET': 'TNG-EWALLET',
    ShopeePay: 'ShopeePay',
    'MB2U_QRPay-Push': 'MB2U_QRPay-Push',
    BOOST: 'BOOST',
};

export const MPS_CHANNEL_PARSED2 = {
    fpx_amb: 'AmBank',
    fpx_bimb: 'Bank Islam',
    fpx_cimbclicks: 'CIMB Bank',
    fpx_mb2u: 'Maybank',
    fpx_pbb: 'Public Bank',
    fpx_rhb: 'RHB Bank',
    FPX_OCBC: 'OCBC Bank',
    FPX_ABMB: 'Alliance Bank',
    fpx_hlb: 'Hong Leong',
    FPX_SCB: 'Standard Chartered Bank',
    FPX_ABB: 'Affin Bank Berhad',
    FPX_BKRM: 'Bank Rakyat',
    FPX_BMMB: 'Bank Muamalat',
    FPX_KFH: 'Kuwait Finance House',
    FPX_BSN: 'Bank Simpanan Nasional',
    FPX_UOB: 'United Overseas Bank',
    credit: 'Credit Card/Debit Card',

    credit3: 'Credit Card/ Debit Card',
    crossborder: 'Credit Card/ Debit Card', // might workable
    credit5: 'Credit Card/ Debit Card',
    creditO: 'Credit Card/ Debit Card',
    credit18: 'Credit Card/ Debit Card',
    creditAN: 'Credit Card', // might workable
    creditAI: 'Credit Card/ Debit Card',
    credit10: 'Credit Card/ Debit Card (eBPG)',

    GrabPay: 'GrabPay',
    'TNG-EWALLET': 'Touch n Go',
    ShopeePay: 'ShopeePay',
    'MB2U_QRPay-Push': 'Maybank QRPay',
    BOOST: 'Boost',
};

export const MPS_CHANNEL_LIST2 = [
    {
        label: MPS_CHANNEL_PARSED2.fpx_mb2u,
        value: MPS_CHANNEL2.fpx_mb2u,
        // image: require('../asset/image/banksV2/logo-maybank.png'),
    },
    {
        label: MPS_CHANNEL_PARSED2.fpx_cimbclicks,
        value: MPS_CHANNEL2.fpx_cimbclicks,
        // image: require('../asset/image/banksV2/logo-cimb-bank.png'),
    },
    {
        label: MPS_CHANNEL_PARSED2.fpx_hlb,
        value: MPS_CHANNEL2.fpx_hlb,
        // image: require('../asset/image/banksV2/logo-hong-leong-bank.png'),
    },
    {
        label: MPS_CHANNEL_PARSED2.fpx_bimb,
        value: MPS_CHANNEL2.fpx_bimb,
        // image: require('../asset/image/banksV2/logo-bank-islam.png'),
    },
    {
        label: MPS_CHANNEL_PARSED2.fpx_amb,
        value: MPS_CHANNEL2.fpx_amb,
        // image: require('../asset/image/banksV2/logo-ambank.png'),
    },
    {
        label: MPS_CHANNEL_PARSED2.fpx_pbb,
        value: MPS_CHANNEL2.fpx_pbb,
        // image: require('../asset/image/banksV2/logo-public-bank.png'),
    },
    {
        label: MPS_CHANNEL_PARSED2.fpx_rhb,
        value: MPS_CHANNEL2.fpx_rhb,
        // image: require('../asset/image/banksV2/logo-rhb.png'),
    },
    {
        label: MPS_CHANNEL_PARSED2.FPX_OCBC,
        value: MPS_CHANNEL2.FPX_OCBC,
        // image: require('../asset/image/banksV2/logo-ocbc.png'),
    },
    {
        label: MPS_CHANNEL_PARSED2.FPX_BKRM,
        value: MPS_CHANNEL2.FPX_BKRM,
        // image: require('../asset/image/banksV2/logo-bank-rakyat.png'),
    },
    {
        label: MPS_CHANNEL_PARSED2.FPX_ABMB,
        value: MPS_CHANNEL2.FPX_ABMB,
        // image: require('../asset/image/banksV2/logo-alliance-bank.png'),
    },
    {
        label: MPS_CHANNEL_PARSED2.FPX_ABB,
        value: MPS_CHANNEL2.FPX_ABB,
        // image: require('../asset/image/banksV2/logo-affinbank.png'),
    },
    {
        label: MPS_CHANNEL_PARSED2.FPX_SCB,
        value: MPS_CHANNEL2.FPX_SCB,
        // image: require('../asset/image/banksV2/logo-standard-chartered.png'),
    },

    {
        label: MPS_CHANNEL_PARSED2.FPX_BMMB,
        value: MPS_CHANNEL2.FPX_BMMB,
        // image: require('../asset/image/banksV2/logo-bank-muamalat.png'),
    },
    {
        label: MPS_CHANNEL_PARSED2.FPX_KFH,
        value: MPS_CHANNEL2.FPX_KFH,
        // image: require('../asset/image/banksV2/logo-kuwait-finance.png'),
    },
    {
        label: MPS_CHANNEL_PARSED2.FPX_BSN,
        value: MPS_CHANNEL2.FPX_BSN,
        // image: require('../asset/image/banksV2/logo-bsn.png'),
    },
    {
        label: MPS_CHANNEL_PARSED2.FPX_UOB,
        value: MPS_CHANNEL2.FPX_UOB,
        // image: require('../asset/image/banksV2/logo-uob.png'),
    },
    {
        label: MPS_CHANNEL_PARSED2.creditAN,
        value: MPS_CHANNEL2.creditAN,
        // image: require('../asset/image/banksV2/logo-credit-card.png'),
        //image2: require('../asset/image/banks/logo-mastercard.png'),
    },
];

export const EWALLET_LIST = [
    {
        label: MPS_CHANNEL_PARSED2.GrabPay,
        value: MPS_CHANNEL2.GrabPay,
        // image: require('../asset/image/offline_payment_method_grabpay.png'),
        // image2: require('../asset/image/offline_payment_method_grabpay.png'),
    },
    {
        label: MPS_CHANNEL_PARSED2['TNG-EWALLET'],
        value: MPS_CHANNEL2['TNG-EWALLET'],
        // image: require('../asset/image/offline_payment_method_touchngo_ewallet.png'),
        // image2: require('../asset/image/offline_payment_method_touchngo_ewallet.png'),
    },
    {
        label: MPS_CHANNEL_PARSED2.ShopeePay,
        value: MPS_CHANNEL2.ShopeePay,
        // image: require('../asset/image/offline_payment_method_shopeepay.png'),
        // image2: require('../asset/image/offline_payment_method_shopeepay.png'),
    },
    // {
    //     label: MPS_CHANNEL_PARSED2['MB2U_QRPay-Push'],
    //     value: MPS_CHANNEL2['MB2U_QRPay-Push'],
    //     // image: require('../asset/image/offline_payment_method_maybank_qrpay.png'),
    //     // image2: require('../asset/image/offline_payment_method_maybank_qrpay.png'),
    // },
    {
        label: MPS_CHANNEL_PARSED2.BOOST,
        value: MPS_CHANNEL2.BOOST,
        // image: require('../asset/image/offline_payment_method_maybank_qrpay.png'),
        // image2: require('../asset/image/offline_payment_method_maybank_qrpay.png'),
    },
];

export const TABLE_QR_SALT = 'com mykoodoo merchant table qr';
export const QUEUE_QR_SALT = 'com mykoodoo merchant queue qr';

export const ORDER_REGISTER_QR_SALT = 'com mykoodoo saas api order register qr';


export const RESERVATIONS_SHIFT_TYPE = {
    DOES_NOT_REPEAT: 'DOES_NOT_REPEAT',
    DAILY: 'DAILY',
    WEEKLY: 'WEEKLY',
    // MONTHLY: 'MONTHLY', // combined to weekly
    // YEARLY: 'YEARLY', // combined to weekly
};

export const CHARGES_TYPE = {
    'AMOUNT_BASED': 'AMOUNT_BASED',
    'PERCENTAGE_BASED': 'PERCENTAGE_BASED',
};

export const PRODUCT_PRICE_TYPE = {
    FIXED: 'FIXED',
    VARIABLE: 'VARIABLE',
    UNIT: 'UNIT',
};

export const USER_INFO_TO_COLLECTED_TYPE = {
    EMAIL: 'EMAIL',
    BIRTHDAY: 'BIRTHDAY',
};

export const OFFLINE_BILL_TYPE = {
    SUMMARY: 'SUMMARY',
    INDIVIDUAL: 'INDIVIDUAL',
    PRODUCT: 'PRODUCT',
};

export const USER_ORDER_PRIORITY = {
    NORMAL: 0,
    HIGH: 1,
};

export const UNIT_TYPE = {
    'KILOGRAM': 'Kilogram',
    'GRAM': 'Gram',
    'LITRE': 'Litre',
    'MILLILITRE': 'Millilitre',
};

export const UNIT_TYPE_SHORT = {
    'Kilogram': 'kg',
    'Gram': 'g',
    'Litre': 'l',
    'Millilitre': 'ml',
};

export const WEB_ORDER_VARIANT_LAYOUT = {
    HORIZONTAL: 'HORIZONTAL',
    VERTICAL: 'VERTICAL',
};

export const WEB_ORDER_UPSELLING_LAYOUT = {
    NORMAL: 'NORMAL',
    LARGE: 'LARGE',
}

export const ORDER_TYPE_DETAILS = {
    POS: 'POS',

    QR: 'QR',

    QR_ONLINE: 'QR_ONLINE',
    QR_OFFLINE: 'QR_OFFLINE',
};

export const UPSELL_BY_TYPE = {
    CUSTOMER: 'CUSTOMER',
    ORDER_ITEM: 'ORDER_ITEM',
};

export const KDS_ACTION_TYPE = {
    DELIVER: 'DELIVER',
    REJECT: 'REJECT',
    UNDO_DELIVER: 'UNDO_DELIVER',
    UNDO_REJECT: 'UNDO_REJECT',
};

export const KDS_ACTION_TYPE_PARSED = {
    DELIVER: 'Delivered',
    REJECT: 'Rejected',
    UNDO_DELIVER: 'Undo Delivered',
    UNDO_REJECT: 'Undo Rejected',
};

export const OUTLET_SHIFT_STATUS = {
    OPENED: 'OPENED',
    CLOSED: 'CLOSED',
};

export const UPSELLING_SECTION = {
    AFTER_CART: 'AFTER_CART',
    AFTER_CHECKOUT: 'AFTER_CHECKOUT',
    AFTER_CHECKOUT_RECOMMENDATION: 'AFTER_CHECKOUT_RECOMMENDATION',
    IN_CART: 'IN_CART',
};

export const UPSELLING_SECTION_PARSED = {
    AFTER_CART: 'After add to cart',
    AFTER_CHECKOUT: 'After clicked cart',
    AFTER_CHECKOUT_RECOMMENDATION: 'After clicked cart (dedicated)',
    IN_CART: 'In cart',
};

export const UPSELLING_SECTION_CODE = {
    AFTER_CART: '1',
    AFTER_CHECKOUT: '2A',
    AFTER_CHECKOUT_RECOMMENDATION: '2B',
    IN_CART: '3',
};

export const QR_SCAN_TYPE = {
    U_PIC: 'U_PIC',
    U_TOTAL: 'U_TOTAL',
};

export const GR_STATUS = {
    PENDING: 'PENDING',
    FAILED: 'FAILED',
    SUCCESS: 'SUCCESS',
};

export const QR_OPERATION_TIME = {
    FOLLOW_SHIFT: 'FOLLOW_SHIFT',
    FOLLOW_OPERATION_HOURS: 'FOLLOW_OPERATION_HOURS',
    FOLLOW_OPERATION_HOURS_AND_SHIFT: 'FOLLOW_OPERATION_HOURS_AND_SHIFT',
};

export const OUTLET_HOMEPAGE_SECTION = {
    HOME: 'HOME',
    SCAN_ORDER: 'SCAN_ORDER',
};

export const DISCOUNT_SEQUENCE_TYPES = {
    FIRST: 'FIRST',
    LOWEST_PRICE: 'LOWEST_PRICE',
    HIGHEST_PRICE: 'HIGHEST_PRICE',
};

export const REWARD_SECTION = {
    REWARDS: 'REWARDS',
    AVAILABLE_REWARDS: 'AVAILABLE_REWARDS',
    STAMP_CARDS: 'STAMP_CARDS',

    CREDIT: 'CREDIT',
};

export const OFFLINE_PAYMENT_METHOD_TYPE = {
    CREDIT_CARD: {
        name: 'Credit Card',
        channel: 'Offline-Credit-Card',
    },
    PAYPAL: {
        name: 'PayPal',
        channel: 'Offline-PayPal',
    },
    GRABPAY: {
        name: 'GrabPay',
        channel: 'Offline-GrabPay',
    },
    CASH: {
        name: 'Cash',
        channel: 'Offline-Cash',
    },
    USE_CREDIT: {
        name: 'Use Credit',
        channel: 'Offline-Use-Credit',
    },
    BANK_TRANSFER: {
        name: 'Bank Transfer',
        channel: 'Offline-Bank-Transfer',
    },
    ALIPAY: {
        name: 'Alipay',
        channel: 'Offline-Alipay',
    },
    BOOST: {
        name: 'Boost',
        channel: 'Offline-Boost',
    },
    FAVEPAY: {
        name: 'favePAY',
        channel: 'Offline-favePAY',
    },
    TOUCHNGO_EWALLET: {
        name: 'TouchnGo',
        channel: 'Offline-TouchnGo-eWallet',
    },
    WECHAT_PAY: {
        name: 'WeChat Pay',
        channel: 'Offline-WeChat-Pay',
    },
    AMEX: {
        name: 'Amex',
        channel: 'Offline-Credit-Amex',
    },
    BCARD_POINTS: {
        name: 'BCard',
        channel: 'Offline-Bcard-Points',
    },
    EPAY: {
        name: 'e-pay',
        channel: 'Offline-e-pay',
    },
    MAYBANK_QRPAY: {
        name: 'Maybank',
        channel: 'Offline-Maybank-QRPAY',
    },
    RAZER_CASH: {
        name: 'RAZER CASH',
        channel: 'Offline-RAZER-CASH',
    },
    RAZER_PAY: {
        name: 'RAZER PAY',
        channel: 'Offline-RAZER-PAY',
    },
    WEBCASH: {
        name: 'WEBCASH',
        channel: 'Offline-WEBCASH',
    },
    DEBIT_CARD: {
        name: 'Debit Card',
        channel: 'Offline-Debit-Card',
    },
    VISA: {
        name: 'VISA',
        channel: 'Offline-Credit-VISA',
    },
    MASTERCARD: {
        name: 'MasterCard',
        channel: 'Offline-Credit-MasterCard',
    },
};