import AsyncStorage from '@react-native-async-storage/async-storage'
var orderType = 0; // 0- dine in, 1 -take away, 2 -pickup
var tableNumber = null;
var outletId = null;
var cartItem = [];
var refreshCartPage = null;
var tax = 5;
var deliveryAddress = null;
var deliveryQuotation = null;
var parkingId = null;

var voucher = null;
var payment = null;

export function getOrderType() {
    return orderType;
}

export function setOrderType(param) {
    orderType = param
}

export function getTableNumber() {
    return tableNumber;
}

export function setTableNumber(param) {
    tableNumber = param
}

export function getTax() {
    return tax;
}

export function setTax(param) {
    tax = param
}

export function getDeliveryAddress() {
    return deliveryAddress;
}

export function setDeliveryAddress(param) {
    deliveryAddress = param
}

export function getDeliveryQuotation() {
    return deliveryQuotation;
}

export function setDeliveryQuotation(param) {
    deliveryQuotation= param
}

export function getOutletId() {
    AsyncStorage.getItem('outletId').then((value) => {
        outletId = JSON.parse(value)
    })
    return outletId;
}

export function setOutletId(param) {
    outletId = param
    AsyncStorage.setItem('outletId', JSON.stringify(outletId));
}

export function getParkingId() {
    return parkingId
}

export function setParkingId(param) {
    parkingId = param
}

export function setCartItem(data) {
    if (cartItem == null) {
        cartItem = []
    }
    if (data.options.length > 0) {
        const option = cartItem.find(obj => obj.itemId === data.itemId && obj.size === data.size && obj.remarks === data.remarks);
        if (option) {
            if ( JSON.stringify(option.options) === JSON.stringify(data.options) ){
                option.quantity = option.quantity + data.quantity;
            }
            else {
                cartItem.push(data);
            }
        }
        else {
            cartItem.push(data);
        }
    } else {
        const item = cartItem.find(obj => obj.itemId === data.itemId && obj.options.length === 0 && obj.size === data.size && obj.remarks === data.remarks);
        if (item) {
            item.quantity = item.quantity + data.quantity;
        }
        else {
            cartItem.push(data);
        }
    }
    AsyncStorage.setItem('cartItem', JSON.stringify(cartItem));
}

export function updateCartItem(data, newData) {
    if (cartItem == null) {
        cartItem = []
    }
    if (data.options.length > 0) {
        const option = cartItem.find(obj => obj.itemId === data.itemId && obj.options.itemName === data.options.itemName && obj.size === data.size && obj.remarks === data.remarks);
        if (option) {
            const index = cartItem.indexOf(data)
            cartItem.splice(index, 1, newData)
            //cartItem.push(newData);
        }
        else {
            cartItem.push(newData);
        }
    } else {
        const item = cartItem.find(obj => obj.itemId === data.itemId && obj.options.length === 0 && obj.size === data.size && obj.remarks === data.remarks);
        if (item) {
            const index = cartItem.indexOf(item)
            cartItem.splice(index, 1, newData)
            //cartItem.push(newData);
        }
        else {
            cartItem.push(newData);
        }
    }
    AsyncStorage.setItem('cartItem', JSON.stringify(cartItem));
}

export function getCartItem() {
    if (cartItem == null) {
        cartItem = []
    }
    AsyncStorage.getItem('cartItem').then((value) => {
        cartItem = JSON.parse(value)
    })
    return cartItem;
}

export function clearCart() {
    cartItem = [];
    tableNumber = null;
    outletId = null;
    tax = 5;
    AsyncStorage.setItem('cartItem', JSON.stringify([]));
}

export function setRefreshCartPage(param) {
    refreshCartPage = param
}

export function getRefreshCartPage() {
    if (refreshCartPage != null) {
        return refreshCartPage()
    } else {
        return true;
    }
}



export function deleteCartItem(id, item) {
    console.log(cartItem)
    console.log(item)
    var list = cartItem;
    var newList = [];
    if (list.length > 1) {
        list.forEach(element => {
            if (! (JSON.stringify(element) === JSON.stringify(item))) {
                newList.push(element);
                cartItem = newList;
            }
        });
    } else {
        if (list[0].itemId == id) {
            cartItem = [];
            clearCart();
        }
    }
    AsyncStorage.setItem('cartItem', JSON.stringify(cartItem));
}

export function getVoucher() {
    return voucher;
}

export function setVoucher(param) {
    voucher = param;
}

export function getPayment() {
    return payment;
}

export function setPayment(param) {
    payment = param;
}
