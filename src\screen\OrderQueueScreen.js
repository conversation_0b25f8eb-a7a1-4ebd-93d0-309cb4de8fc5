import React, { Component, useReducer, useState, useEffect, useRef, useCallback } from 'react';
import {
    StyleSheet,
    ScrollView,
    Image,
    View,
    Text,
    TouchableOpacity,
    TextInput,
    FlatList,
    RefreshControl,
    Alert,
    Modal,
    Dimensions,
    ActivityIndicator,
    useWindowDimensions,
} from 'react-native';
import { CheckBox } from 'react-native-web';
import Colors from '../constant/Colors';
import * as Cart from '../util/Cart';
import * as User from '../util/User';
import Styles from '../constant/Styles';
import ApiClient from '../util/ApiClient';
import API from '../constant/API';
import Feather from 'react-native-vector-icons/Feather';
import Icons from 'react-native-vector-icons/EvilIcons';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { CommonStore } from '../store/commonStore';
import { Collections } from '../constant/firebase';
//import firestore from '@react-native-firebase/firestore';
// import firebase from "firebase";
import { getFirestore, collection, query, where, getDocs, limit } from "firebase/firestore";
import { ORDER_TYPE_PARSED, USER_ORDER_STATUS, COURIER_CODE, LALAMOVE_STATUS, ORDER_TYPE, MRSPEEDY_STATUS, COURIER_INFO_DICT, SENDER_DROPDOWN_LIST, USER_ORDER_PRIORITY, APP_TYPE, OFFLINE_BILL_TYPE, UNIT_TYPE_SHORT, PRODUCT_PRICE_TYPE, ORDER_TYPE_DETAILS, KDS_ACTION_TYPE } from '../constant/common';
import AsyncImage from '../components/asyncImage';
import AntDesign from 'react-native-vector-icons/AntDesign';
import { Picker } from "react-native";
import { UserStore } from '../store/userStore';
import { CommonActions, useFocusEffect, useLinkTo } from "@react-navigation/native";
//import molpay from "molpay-mobile-xdk-reactnative-beta";
import moment from 'moment';
//import DropDownPicker from 'react-native-dropdown-picker';
//import RNPickerSelect from 'react-native-picker-select';
import { prefix } from "../constant/env";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { TempStore } from '../store/tempStore';
import { TableStore } from '../store/tableStore';
import 'react-native-get-random-values';
import { v4 as uuidv4 } from 'uuid';
import { customAlphabet } from 'nanoid';
import BigNumber from 'bignumber.js';
import { isMobile, listenToOrderQueueUserOrderChanges, safariChecking } from '../util/commonFuncs';
import { OutletStore } from '../store/outlet';
import Toastify from 'toastify-js';
import "toastify-js/src/toastify.css";
import { DataStore } from '../store/dataStore';
import { idbSet } from '../util/db';
import { PaymentStore } from '../store/paymentStore';
import { signInAnonymously } from 'firebase/auth';
import Marquee from 'react-fast-marquee';

const alphabet = '**********';
const nanoid = customAlphabet(alphabet, 12);

/////////////////////////////////////////////////////////////////////////

const OrderQueueScreen = props => {
    const {
        navigation,
        route,
    } = props;

    const linkTo = useLinkTo();
    const { width: windowWidth, height: windowHeight } = useWindowDimensions();
    const selectedOutlet = CommonStore.useState((s) => s.selectedOutlet);
    const userOrdersQueue = CommonStore.useState((s) => s.userOrdersQueue);
    const userOrdersPreparingRaw = CommonStore.useState((s) => s.userOrdersPreparing);

    const userIdAnonymous = UserStore.useState((s) => s.userIdAnonymous);

    ////////////////////////////////////////////

    const [userOrders, setUserOrders] = useState([]);
    const [userOrdersPreparing, setUserOrdersPreparing] = useState([]);

    const getFontSizes = () => {
        // Base sizes for reference width (e.g., 1920px for large displays)
        const baseWidth = 1920;

        // Calculate scale factor based on current screen width
        const scaleFactor = windowWidth / baseWidth;

        return {
            orderNumber: Math.max(48 * scaleFactor, 24), // minimum 24px
            outletName: Math.max(18 * scaleFactor, 12),  // minimum 12px
            mainOultetName: Math.max(24 * scaleFactor, 20),
            preparOrderNumber: Math.max(52 * scaleFactor, 30),
            headerText: Math.max(54 * scaleFactor, 32),  // minimum 32px
            mainOrderNumber: Math.max(72 * scaleFactor, 48), // minimum 48px
        };
    };
    const [fontSizes, setFontSizes] = useState(getFontSizes());
    const audioCtxRef = useRef(null);
    const [audioReady, setAudioReady] = useState(false);

    useEffect(() => {
        audioCtxRef.current = new (window.AudioContext || window.webkitAudioContext)();

        const unlockAudio = () => {
            if (audioCtxRef.current.state === 'suspended') {
                audioCtxRef.current.resume().then(() => {
                    console.log('✅ AudioContext unlocked');
                    setAudioReady(true); // 👈 很重要
                });
            } else {
                setAudioReady(true); // 已经 running
            }

            // 播放一个几乎听不到的声音，确保状态锁定
            const silentOsc = audioCtxRef.current.createOscillator();
            const silentGain = audioCtxRef.current.createGain();
            silentGain.gain.value = 0.00001;

            silentOsc.connect(silentGain);
            silentGain.connect(audioCtxRef.current.destination);
            silentOsc.start();
            silentOsc.stop(audioCtxRef.current.currentTime + 0.01);

            window.removeEventListener('click', unlockAudio);
            window.removeEventListener('touchstart', unlockAudio);
        };

        window.addEventListener('click', unlockAudio);
        window.addEventListener('touchstart', unlockAudio);

        return () => {
            window.removeEventListener('click', unlockAudio);
            window.removeEventListener('touchstart', unlockAudio);
        };
    }, []);

    // const playOrderNotification = () => {
    //     if (!audioReady || !audioCtxRef.current || audioCtxRef.current.state !== 'running') {
    //         console.warn('🔇 Audio not ready');
    //         return;
    //     }

    //     const audioCtx = audioCtxRef.current;

    //     const baseFreq = 2600;
    //     const partials = [1, 2, 3, 4.5]; // 模拟泛音组
    //     const duration = 0.7;

    //     partials.forEach((partial, idx) => {
    //         const osc = audioCtx.createOscillator();
    //         const gainNode = audioCtx.createGain();

    //         osc.type = 'sine';
    //         osc.frequency.setValueAtTime(baseFreq * partial, audioCtx.currentTime);

    //         osc.connect(gainNode);
    //         gainNode.connect(audioCtx.destination);

    //         // 控制每个频率的音量和消退速度
    //         const gainStart = 0.15 / (idx + 1);
    //         gainNode.gain.setValueAtTime(gainStart, audioCtx.currentTime);
    //         gainNode.gain.exponentialRampToValueAtTime(0.0001, audioCtx.currentTime + duration);

    //         osc.start(audioCtx.currentTime);
    //         osc.stop(audioCtx.currentTime + duration);
    //     });
    // };

    const playOrderNotification = () => {
        const audio = new Audio("https://koodooprod.s3.ap-southeast-1.amazonaws.com/audio/notification.mp3");
        audio.volume = 1.0; // 可调节音量（0~1）
        audio.play().catch(err => {
            console.warn("🔇 play failed", err);
        });
    };

    const speakWordByWord = (text, pauseTime = 20) => {
        if (!('speechSynthesis' in window)) {
            console.warn('didnt support voice text');
            return;
        }

        const chars = text.split('');
        let index = 0;

        // 中止当前的朗读（某些浏览器不会自动 cancel）
        window.speechSynthesis.cancel();

        const speakNext = () => {
            if (index >= chars.length) return;

            const utter = new SpeechSynthesisUtterance(chars[index]);
            utter.lang = 'en-US'; // 可改 zh-CN
            utter.rate = 2.0;      // 语速最大
            utter.pitch = 1;
            utter.volume = 1;

            utter.onend = () => {
                index++;
                setTimeout(speakNext, pauseTime);
            };

            window.speechSynthesis.speak(utter);
        };

        speakNext();
    };



    useEffect(() => {
        const dimensionsHandler = Dimensions.addEventListener('change', () => {
            setFontSizes(getFontSizes());
        });

        return () => {
            dimensionsHandler.remove();
        };
    }, []);

    useEffect(() => {
        let userOrdersTemp = [];

        userOrdersTemp = (userOrdersQueue && userOrdersQueue.length > 0 ? [...userOrdersQueue] : [])
            .sort((a, b) => (b.notifyAt ? b.notifyAt : b.createdAt) - (a.notifyAt ? a.notifyAt : a.createdAt));

        setUserOrders(userOrdersTemp);
    }, [
        userOrdersQueue,
    ]);

    useEffect(() => {
        let userOrdersPreparingTemp = [];

        if (selectedOutlet &&
            selectedOutlet.uniqueId &&
            userOrdersPreparingRaw &&
            userOrdersPreparingRaw.length > 0) {
            userOrdersPreparingTemp = userOrdersPreparingRaw.filter((order) => {
                let validStatus = false;

                if (order.orderType !== ORDER_TYPE.DINEIN &&
                    (
                        order.orderStatus === USER_ORDER_STATUS.ORDER_AUTHORIZED ||
                        order.orderStatus === USER_ORDER_STATUS.ORDER_PREPARING ||
                        order.orderStatus === USER_ORDER_STATUS.ORDER_PREPARED
                    )) {
                    validStatus = true;
                }
                else if (selectedOutlet.dineInRequiredAuthorization &&
                    order.orderType === ORDER_TYPE.DINEIN &&
                    (
                        order.orderStatus === USER_ORDER_STATUS.ORDER_AUTHORIZED ||
                        order.orderStatus === USER_ORDER_STATUS.ORDER_PREPARING ||
                        order.orderStatus === USER_ORDER_STATUS.ORDER_PREPARED
                    )) {
                    validStatus = true;
                }
                else if (!selectedOutlet.dineInRequiredAuthorization &&
                    order.orderType === ORDER_TYPE.DINEIN &&
                    (
                        order.orderStatus === USER_ORDER_STATUS.ORDER_RECEIVED ||
                        order.orderStatus === USER_ORDER_STATUS.ORDER_AUTHORIZED ||
                        order.orderStatus === USER_ORDER_STATUS.ORDER_PREPARING ||
                        order.orderStatus === USER_ORDER_STATUS.ORDER_PREPARED
                    )) {
                    validStatus = true;
                }

                return validStatus;
            });
        }

        userOrdersPreparingTemp.sort((a, b) => (b.notifyAt ? b.notifyAt : b.createdAt) - (a.notifyAt ? a.notifyAt : a.createdAt));

        setUserOrdersPreparing(userOrdersPreparingTemp);
    }, [
        userOrdersPreparingRaw,
        selectedOutlet,
    ]);

    useEffect(() => {
        if (selectedOutlet) {
            // Cleanup previous listener if exists
            typeof global.subscriberUserOrderUserOrders === 'function' && global.subscriberUserOrderUserOrders();
            global.subscriberUserOrderUserOrders = () => { };

            // Start new listener
            const unsubscribe = listenToOrderQueueUserOrderChanges(selectedOutlet.uniqueId, selectedOutlet.toggleOpenOrder);

            // Store the unsubscribe function globally
            global.subscriberUserOrderUserOrders = unsubscribe;

            // Cleanup on unmount
            return () => {
                typeof unsubscribe === 'function' && unsubscribe();
            };
        }
    }, [selectedOutlet]);

    ////////////////////////////////////////////

    useEffect(() => {
        (async () => {
            if (linkTo) {
                DataStore.update((s) => {
                    s.linkToFunc = linkTo;
                });

                global.linkToFunc = linkTo;
            }

            console.log('route');
            console.log(route);
            console.log('window.location.href');
            console.log(window.location.href);

            safariChecking();

            // const subdomain = window.location.host.split('.')[1] ? window.location.host.split('.')[0] : false;

            // Extract the part after 'outlet/' and before the next '/'
            const match = window.location.href.match(/\/outlet\/([^/]+)\//);
            const subdomain = match ? match[1] : null;

            if (
                // route.params === undefined
                !subdomain
            ) {
                // linkTo && linkTo(`${prefix}/error`);

                if (selectedOutlet && selectedOutlet.subdomain) {
                    linkTo && linkTo(`${prefix}/outlet/${selectedOutlet.subdomain}`);
                }
                else {
                    console.log('no subdomain');

                    // linkTo && linkTo(`${prefix}/error`);
                }
            } else {
                // const subdomain = route.params.subdomain;

                // const subdomain = window.location.host.split('.')[1] ? window.location.host.split('.')[0] : false;

                if (subdomain) {
                    // means all got, can login this user to check all info valid or not

                    console.log("subdomain");
                    console.log(subdomain);

                    // await AsyncStorage.setItem(
                    //   "latestOutletId",
                    //   outlet.uniqueId
                    // );
                    // await AsyncStorage.setItem("latestSubdomain", subdomain);          

                    // setCurrOutlet(outlet);

                    // setShowGenericQRModal(true);

                    // 2025-04-17 - no need
                    // TempStore.update(s => {
                    //     s.showGreetingPopup = true;
                    // });

                    global.subdomain = subdomain;

                    ////////////////////////////////////////////////////////////////////////////////
                    ////////////////////////////////////////////////////////////////////////////////
                    ////////////////////////////////////////////////////////////////////////////////

                    setTimeout(async () => {
                        // for loading outlet data

                        var outletSnapshot = null;

                        if (subdomain) {
                            if (subdomain === "192") {
                                // outletSnapshot = await firebase
                                //   .firestore()
                                //   .collection(Collections.Outlet)
                                //   .where(
                                //     "uniqueId",
                                //     "==",
                                //     "b422c1d9-d30b-4de7-ad49-2e601d950919"
                                //   )
                                //   .limit(1)
                                //   .get();

                                outletSnapshot = await getDocs(
                                    query(
                                        collection(global.db, Collections.Outlet),
                                        where('uniqueId', '==', 'b422c1d9-d30b-4de7-ad49-2e601d950919'),
                                        limit(1),
                                    )
                                );
                            } else {
                                // outletSnapshot = await firebase
                                //   .firestore()
                                //   .collection(Collections.Outlet)
                                //   .where("subdomain", "==", subdomain)
                                //   .limit(1)
                                //   .get();

                                outletSnapshot = await getDocs(
                                    query(
                                        collection(global.db, Collections.Outlet),
                                        where('subdomain', '==', subdomain),
                                        limit(1),
                                    )
                                );
                            }
                        } else {
                            console.log("web scan 3");
                            linkTo && linkTo(`${prefix}/error`);
                        }

                        var outlet = {};
                        if (!outletSnapshot.empty) {
                            outlet = outletSnapshot.docs[0].data();
                        }

                        if (
                            outlet &&
                            (outlet.subdomain === subdomain || subdomain === "192")
                        ) {
                            AsyncStorage.multiSet([
                                ['latestOutletId', outlet.uniqueId],
                                ['latestSubdomain', subdomain],
                                // ['latestTableId', route.params.tableId],
                                // ['latestUserId', result.userId],
                            ]);

                            document.title = outlet.name;
                            document.getElementsByTagName("META")[2].content =
                                outlet.address;

                            global.selectedOutlet = outlet;

                            await idbSet(`dso`, outlet.dso !== undefined ? outlet.dso : false);
                            await idbSet(`dsopn`, outlet.dsopn !== undefined ? outlet.dsopn : false);

                            PaymentStore.update(s => {
                                s.cartItemsPayment = [];
                            });

                            console.log('[stamp] clear');

                            CommonStore.update(
                                (s) => {
                                    s.selectedOutlet = outlet;

                                    // s.scannedQrData = json;

                                    s.orderType = ORDER_TYPE.PICKUP;

                                    // s.selectedOutletTableId = json.tableId;
                                    // s.selectedOutletWaiterId = json.waiterId;
                                    // s.selectedOutletTablePax = json.tablePax;
                                    // s.selectedOutletTableCode = json.tableCode;

                                    // 2022-10-08 - Reset cart items
                                    s.cartItems = [];
                                    s.cartItemsProcessed = [];
                                    s.cartOutletId = outlet.uniqueId;

                                    s.cartItemsReservation = [];
                                    s.cartItemsProcessedReservation = [];

                                    s.tcCartItems = [];
                                    s.tcCartItemsProcessed = [];

                                    s.selectedOutletItem = {};
                                    s.selectedOutletItemAddOn = {};
                                    s.selectedOutletItemAddOnChoice = {};
                                    s.onUpdatingCartItem = null;

                                    s.cachedAddOnChoiceIdDict = {};

                                    s.isLoading = false;

                                    s.molpayResult = null;

                                    s.payHybridBody = null;
                                    s.paymentDetails = null;
                                    s.orderIdCreated = '';

                                    s.payTopupCreditBody = null;

                                    s.currPage = '';

                                    s.menuItemDetailModal = false;

                                    ////////////////////////////////////

                                    // 2023-06-06 - To clear reservation states

                                    s.selectedReservationId = '';
                                    s.selectedReservationStartTime = null;
                                    s.selectedReservationPax = '1';
                                    s.selectedReservationOutletSectionId = '';
                                    s.selectedReservationOutletSectionName = '';

                                    s.editUserReservation = {};

                                    // s.reservationIdTemp = '';

                                    s.selectedAddressUserPhone = '';
                                    s.selectedUserEmail = '';
                                    s.selectedUserFirstName = '';
                                    s.selectedUserLastName = '';
                                    s.selectedUserRemarks = '';
                                    s.selectedUserDiet = '';
                                    s.selectedUserOccasion = '';

                                    s.resFirstName = '';
                                    s.resLastName = '';
                                    s.resPhoneNum = '';
                                    s.resEmail = '';
                                    s.resRemarks = '';
                                    s.resDietaryRestrictions = '';
                                    s.resSpecialOccasions = '';

                                    s.isPlacingReservation = false;
                                    s.isPlacingReservationUpselling = false;

                                    s.isDepositOnly = false;

                                    s.resShowConfirmationPage = false;

                                    s.selectedTaggableVoucher = null;
                                    s.selectedBundleTaggableVoucher = null;

                                    // s.userLoyaltyStamps = [];
                                    // s.redeemableStackedLoyaltyStamps = [];
                                    // s.toRedeemStackedLoyaltyStamp = {};

                                    ////////////////////////////////////
                                },
                                () => {
                                    if (outlet && outlet.uniqueId) {
                                        // 2025-04-17 - no need
                                        // linkTo &&
                                        //     linkTo(`${prefix}/outlet/${subdomain}/menu`);
                                    }
                                }
                            );

                            // firebase.analytics().logEvent(ANALYTICS.EVENT_QR_TAKEAWAY, {
                            //   eventNameParsed: ANALYTICS_PARSED.EVENT_QR_TAKEAWAY,

                            //   // merchantName: global.merchantName,
                            //   outletName: outlet.name,

                            //   merchantId: outlet.merchantId,
                            //   outletId: outlet.uniqueId,
                            //   // userId: global.userId,
                            // });

                            // logEvent(global.analytics, ANALYTICS.EVENT_QR_TAKEAWAY, {
                            //     eventNameParsed: ANALYTICS_PARSED.EVENT_QR_TAKEAWAY,

                            //     // merchantName: global.merchantName,
                            //     outletName: outlet.name,

                            //     merchantId: outlet.merchantId,
                            //     outletId: outlet.uniqueId,
                            //     // userId: global.userId,

                            //     webUserInfo: `${moment().format('YYYYMMDD-HHmm')}>${global.userName ? global.userName : 'N/A'} (${global.userIdAnonymous ? global.userIdAnonymous : ''})`,
                            // });
                        }
                        else {
                            console.log("web scan 3");
                            linkTo && linkTo(`${prefix}/error`);
                        }
                    }, 10);

                    ////////////////////////////////////////////////////////////////////////////////
                    ////////////////////////////////////////////////////////////////////////////////
                    ////////////////////////////////////////////////////////////////////////////////

                    PaymentStore.update(s => {
                        s.cartItemsPayment = [];
                    });

                    console.log('[stamp] clear');

                    CommonStore.update(
                        (s) => {
                            s.orderType = ORDER_TYPE.PICKUP;

                            s.cartItems = [];
                            s.cartItemsProcessed = [];

                            s.cartItemsReservation = [];
                            s.cartItemsProcessedReservation = [];

                            s.tcCartItems = [];
                            s.tcCartItemsProcessed = [];

                            s.selectedOutletItem = {};
                            s.selectedOutletItemAddOn = {};
                            s.selectedOutletItemAddOnChoice = {};
                            s.onUpdatingCartItem = null;

                            s.cachedAddOnChoiceIdDict = {};

                            s.isLoading = false;

                            s.molpayResult = null;

                            s.payHybridBody = null;
                            s.paymentDetails = null;
                            s.orderIdCreated = '';

                            s.payTopupCreditBody = null;

                            s.currPage = '';

                            s.menuItemDetailModal = false;

                            ////////////////////////////////////

                            // 2023-06-06 - To clear reservation states

                            s.selectedReservationId = '';
                            s.selectedReservationStartTime = null;
                            s.selectedReservationPax = '1';
                            s.selectedReservationOutletSectionId = '';
                            s.selectedReservationOutletSectionName = '';

                            s.editUserReservation = {};

                            // s.reservationIdTemp = '';

                            s.selectedAddressUserPhone = '';
                            s.selectedUserEmail = '';
                            s.selectedUserFirstName = '';
                            s.selectedUserLastName = '';
                            s.selectedUserRemarks = '';
                            s.selectedUserDiet = '';
                            s.selectedUserOccasion = '';

                            s.resFirstName = '';
                            s.resLastName = '';
                            s.resPhoneNum = '';
                            s.resEmail = '';
                            s.resRemarks = '';
                            s.resDietaryRestrictions = '';
                            s.resSpecialOccasions = '';

                            s.isPlacingReservation = false;
                            s.isPlacingReservationUpselling = false;

                            s.isDepositOnly = false;

                            s.resShowConfirmationPage = false;

                            s.selectedTaggableVoucher = null;
                            s.selectedBundleTaggableVoucher = null;

                            s.userLoyaltyStamps = [];
                            s.redeemableStackedLoyaltyStamps = [];
                            s.toRedeemStackedLoyaltyStamp = {};

                            ////////////////////////////////////
                        },
                        () => {
                            // 2025-04-17 - no need
                            // linkTo &&
                            //     linkTo(`${prefix}/outlet/${subdomain}/menu`);
                        }
                    );

                    ////////////////////////////////////////////////////////////////////////////////
                    ////////////////////////////////////////////////////////////////////////////////
                    ////////////////////////////////////////////////////////////////////////////////

                    // later first (for sign in, and get token)

                    // firebase
                    //   .auth()
                    //   .signInAnonymously()

                    try {
                        setTimeout(() => {
                            signInAnonymously(global.auth)
                                .then((result) => {
                                    TempStore.update(s => {
                                        s.firebaseAuth = true;
                                    });

                                    const firebaseUid = result.user.uid;

                                    ApiClient.GET(API.getTokenKWeb + firebaseUid).then(
                                        async (result) => {
                                            console.log("getTokenKWeb");
                                            console.log(result);

                                            if (result && result.token) {
                                                // await AsyncStorage.setItem("accessToken", result.token);
                                                // await AsyncStorage.setItem(
                                                //   "refreshToken",
                                                //   result.refreshToken
                                                // );

                                                AsyncStorage.multiSet([
                                                    ['accessToken', result.token],
                                                    ['refreshToken', result.refreshToken],
                                                ]);

                                                global.accessToken = result.token;

                                                UserStore.update((s) => {
                                                    s.firebaseUid = result.userId;
                                                    s.userId = result.userId;
                                                    s.role = result.role;
                                                    s.refreshToken = result.refreshToken;
                                                    s.token = result.token;
                                                    s.name = '';
                                                    s.email = '';
                                                    s.number = '';
                                                });
                                            } else {
                                                CommonStore.update((s) => {
                                                    s.alertObj = {
                                                        title: "Error",
                                                        message: "Unauthorized access",
                                                    };

                                                    // s.isAuthenticating = false;
                                                });

                                                console.log("web scan 4");
                                                linkTo && linkTo(`${prefix}/error`);
                                            }
                                        }
                                    );
                                });
                        }, 10);
                    }
                    catch (ex) {
                        console.error(ex);
                    }

                    ////////////////////////////////////////////////////////////////////////////////
                    ////////////////////////////////////////////////////////////////////////////////
                    ////////////////////////////////////////////////////////////////////////////////

                    // 2022-10-12 - Get and set user id anonymous

                    setTimeout(async () => {
                        const userIdAnonymousRaw = await AsyncStorage.getItem('userIdAnonymous');

                        if (userIdAnonymousRaw) {
                            // means existed

                            UserStore.update(s => {
                                s.userIdAnonymous = userIdAnonymousRaw;
                            });
                        }
                        else {
                            var userIdAnonymousTemp = uuidv4();
                            UserStore.update(s => {
                                s.userIdAnonymous = userIdAnonymousTemp;
                            });

                            await AsyncStorage.setItem('userIdAnonymous', userIdAnonymousTemp);
                        }

                        global.userIdAnonymousLoaded = true;
                    }, 100);

                    if (userIdAnonymous === 'none') {
                        // means haven't set before
                    }


                } else {
                    linkTo && linkTo(`${prefix}/error`);
                }
            }
        })();
    }, [linkTo, route]);

    ////////////////////////////////////////////

    navigation.setOptions({
        headerLeft: () => (
            // <TouchableOpacity style={{
            // }} onPress={async () => {
            //     // props.navigation.goBack();
            //     const subdomain = await AsyncStorage.getItem('latestSubdomain');

            //     if (!subdomain) {
            //         linkTo && linkTo(`${prefix}/outlet/menu`);
            //     }
            //     else {
            //         linkTo && linkTo(`${prefix}/outlet/${subdomain}/menu`);
            //     }
            // }}>
            //     <View style={{
            //         marginLeft: 10,
            //         display: 'flex',
            //         flexDirection: 'row',
            //         alignItems: 'center',
            //         justifyContent: 'flex-start',
            //     }}>
            //         <Ionicons
            //             name="chevron-back"
            //             size={26}
            //             color={Colors.fieldtTxtColor}
            //             style={{
            //             }}
            //         />

            //         <Text
            //             style={{
            //                 color: Colors.fieldtTxtColor,
            //                 fontSize: 16,
            //                 textAlign: 'center',
            //                 fontFamily: 'NunitoSans-Regular',
            //                 lineHeight: 22,
            //                 marginTop: -1,
            //             }}>
            //             Back
            //         </Text>
            //     </View>
            // </TouchableOpacity>
            <View style={{
                marginLeft: 20,
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'flex-start',
            }}>
                <Text
                    style={{
                        fontSize: 20,
                        lineHeight: 25,
                        textAlign: 'center',
                        fontFamily: 'NunitoSans-Bold',
                        color: Colors.mainTxtColor,
                    }}>
                    {selectedOutlet ? selectedOutlet.name : ''}
                </Text>
            </View>
        ),
        headerRight: () => (
            // <TouchableOpacity onPress={() => { props.navigation.navigate('Profile') }} style={{
            // }}>
            //     <View style={{ marginRight: 15 }}>
            //         <Ionicons name="menu" size={30} color={Colors.primaryColor} />
            //     </View>
            // </TouchableOpacity>
            <View></View>
        ),
        headerTitle: () => (
            <View style={{
                justifyContent: 'center',
                alignItems: 'center',
                bottom: -1,
                flexDirection: 'row',
            }}>
                <Text
                    style={{
                        fontSize: 20,
                        lineHeight: 25,
                        textAlign: 'center',
                        fontFamily: 'NunitoSans-Bold',
                        color: Colors.mainTxtColor,
                    }}>
                    Ordering Queue
                </Text>
                <TouchableOpacity
                    style={{
                        backgroundColor: Colors.primaryColor,
                        paddingHorizontal: 15,
                        paddingVertical: 5,
                        borderRadius: 5,
                        marginLeft: 10,
                    }}>
                    <Text style={{
                        color: Colors.whiteColor,
                        fontFamily: 'NunitoSans-Bold',
                        fontSize: 16,
                    }}>
                        INIT
                    </Text>
                </TouchableOpacity>
            </View>
        ),
        headerTintColor: "#000000",
    });

    ////////////////////////////////////////////////////////

    useEffect(() => {
        if (userOrders.length > 0) {

            let isValid = true;

            // console.log(`now: ${moment().format('YYYY-MM-DD, HH:mm:ss A')}`);
            // console.log(`order updatedAt: ${moment(userOrders[0].updatedAt).format('YYYY-MM-DD, HH:mm:ss A')}`);
            // console.log(`order notifyAt: ${moment(userOrders[0].notifyAt).format('YYYY-MM-DD, HH:mm:ss A')}`);
            // console.log(`order diff: ${moment().diff(userOrders[0].notifyAt ? userOrders[0].notifyAt : userOrders[0].updatedAt, 'second')}`);


            if (moment().diff(userOrders[0].notifyAt ? userOrders[0].notifyAt : userOrders[0].updatedAt, 'second') <=
                ((selectedOutlet && selectedOutlet.oqUDt) ? selectedOutlet.oqUDt : 10)
            ) {
                // isValid = false;
            }
            else {
                isValid = false;
            }

            if (userOrders.kdsActionHistory && userOrders.kdsActionHistory.length > 0 &&
                userOrders.kdsActionHistory.find(
                    (action) => KDS_ACTION_TYPE.REJECT && moment(userOrders.updatedAt).diff(action.actionDate, 'second') <=
                        ((selectedOutlet && selectedOutlet.oqRDt) ? selectedOutlet.oqRDt : 10)
                )
            ) {
                isValid = false;
            }

            if (isValid) {
                playOrderNotification();
                // setTimeout(() => {
                //     speakWordByWord(`${userOrders[0].orderType !== ORDER_TYPE.DINEIN ? 'T' : ''}${userOrders[0].orderId}`);
                // }, 500);

                Toastify({
                    text: `Order ${userOrders[0].orderType !== ORDER_TYPE.DINEIN ? 'T' : ''}${userOrders[0].orderId} is ready now`,
                    duration: 5000,
                    // destination: "https://github.com/apvarun/toastify-js",
                    newWindow: true,
                    close: false,
                    gravity: "top", // `top` or `bottom`
                    position: "left", // `left`, `center` or `right`
                    stopOnFocus: true, // Prevents dismissing of toast on hover
                    style: {
                        background: "linear-gradient(to right, #4E9F7D, #75bd9f)",
                        color: 'white',
                        fontSize: '35px', // Increased font size
                        padding: '40px 30px', // Increased padding
                        minWidth: '400px', // Set minimum width
                        textAlign: 'center',
                        // marginLeft: '15px !important',
                        // marginRight: '15px !important',
                    },
                    onClick: function () { } // Callback after click
                }).showToast();
            }
        }
    }, [userOrders]);

    useEffect(() => {
        global.currPageStack = [
            ...global.currPageStack,
            'OrderQueueScreen',
        ];
    }, []);

    // function end

    return (
        <View style={{ height: windowHeight * 0.91, backgroundColor: '#f5f5f5', alignSelf: "center", width: windowWidth }}>
            {(userOrders && userOrders.length > 0) || (userOrdersPreparing && userOrdersPreparing.length > 0) ?
                <View style={{
                    flexDirection: 'row',
                    height: '100%',
                }}>
                    {/* Left side - Latest number */}
                    {/* <View style={{
                        flex: 1,
                        backgroundColor: 'white',
                        borderRadius: 10,
                        marginRight: 10,
                        padding: 20,
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        shadowColor: '#000',
                        shadowOffset: {
                            width: 0,
                            height: 2,
                        },
                        shadowOpacity: 0.25,
                        shadowRadius: 3.84,
                        elevation: 5,
                        height: windowHeight * 0.87,
                    }}>
                        <View style={{ marginTop: 20, justifyContent: 'flex-start' }}>
                            <Text style={{
                                fontSize: 40,
                                fontFamily: 'NunitoSans-SemiBold',
                                textAlign: 'center',
                            }}>
                                {`Order ${userOrders[0].orderType !== ORDER_TYPE.DINEIN ? 'T' : ''}${userOrders[0].orderId} is ready now`}
                            </Text>
                        </View>
                        <View style={{ justifyContent: 'center', width: '100%', }}>
                            <Text numberOfLines={2}
                                style={{
                                    fontSize: 120,
                                    fontWeight: 'bold',
                                    textAlign: 'center',
                                    // marginBottom: 20,
                                }}>
                                {userOrders[0].orderType !== ORDER_TYPE.DINEIN ? 'T' : ''}{userOrders[0].orderId}
                            </Text>
                        </View>
                        <View style={{ marginBottom: 20, width: '100%' }}>
                            <Text numberOfLines={1} style={{
                                fontSize: 35,
                                color: '#666',
                                fontFamily: 'NunitoSans-Regular',
                                textAlign: 'center',
                            }}>
                                {userOrders[0].outletName}
                            </Text>
                        </View>
                    </View> */}

                    {/* Right side - Grid of previous numbers */}
                    {/* <View style={{
                        flex: 1,
                        flexDirection: 'row',
                        flexWrap: 'wrap',
                        // justifyContent: 'space-between',
                        alignContent: 'flex-start',
                        gap: 10,
                        paddingTop: 2,
                    }}>
                        {userOrders.slice(1, 17).map((order, index) => (
                            <View key={index} style={{
                                width: '23%',
                                height: windowHeight * 0.205,
                                aspectRatio: 1,
                                backgroundColor: 'white',
                                borderRadius: 8,
                                marginBottom: 15,
                                padding: 2,
                                justifyContent: 'space-between',
                                alignItems: 'center',
                                shadowColor: '#000',
                                shadowOffset: {
                                    width: 0,
                                    height: 1,
                                },
                                shadowOpacity: 0.2,
                                shadowRadius: 1.41,
                                elevation: 2,
                            }}>
                                <View><Text /></View>
                                <View style={{ width: '100%', }}>
                                    <Text numberOfLines={2}
                                        style={{
                                            fontSize: isMobile() ? 14 : 35,
                                            fontWeight: 'bold',
                                            textAlign: 'center',
                                            // marginBottom: 5,
                                        }}>
                                        {order.orderType !== ORDER_TYPE.DINEIN ? 'T' : ''}{order.orderId}
                                    </Text>
                                </View>
                                <View style={{ marginBottom: 10, width: '100%', }}>
                                    <Text numberOfLines={1} style={{
                                        fontSize: isMobile() ? 10 : 18,
                                        color: '#666',
                                        textAlign: 'center',
                                        fontFamily: 'NunitoSans-Regular',
                                    }}>
                                        {order.outletName}
                                    </Text>
                                </View>
                            </View>
                        ))}
                    </View> */}

                    {/* Left side - Preparing Orders */}
                    <View style={{
                        width: '50%',
                        borderRightWidth: 1,
                        borderRightColor: '#000',
                    }}>
                        <Text style={{
                            fontSize: fontSizes.headerText,
                            fontWeight: 'bold',
                            color: 'black',
                            marginBottom: 15,
                            textAlign: 'center',
                            fontFamily: 'NunitoSans-Bold',
                        }}>
                            Preparing
                        </Text>
                        <View style={{
                            flexDirection: 'row',
                            justifyContent: 'space-around'
                        }}>
                            {/* Left column of preparing orders */}
                            <View style={{ width: '43%' }}>
                                {userOrdersPreparing
                                    // .filter((order) => order.orderStatus !== USER_ORDER_STATUS.ORDER_COMPLETED)
                                    .slice(0, 9)
                                    .map((order, index) => (
                                        <Text key={index} style={{
                                            fontSize: fontSizes.preparOrderNumber,
                                            marginBottom: 15,
                                            fontWeight: '500',
                                            textAlign: 'center',
                                            fontFamily: 'NunitoSans-Bold',
                                        }}>
                                            {order.orderType !== ORDER_TYPE.DINEIN ? 'T' : ''}{order.orderId}
                                        </Text>
                                    ))}
                            </View>
                            {/* Right column of preparing orders */}
                            <View style={{ width: '43%' }}>
                                {userOrdersPreparing
                                    // .filter((order) => order.orderStatus !== USER_ORDER_STATUS.ORDER_COMPLETED)
                                    .slice(9, 18)
                                    .map((order, index) => (
                                        <Text key={index} style={{
                                            fontSize: fontSizes.preparOrderNumber,
                                            marginBottom: 15,
                                            fontWeight: '500',
                                            textAlign: 'center',
                                            fontFamily: 'NunitoSans-Bold',
                                        }}>
                                            {order.orderType !== ORDER_TYPE.DINEIN ? 'T' : ''}{order.orderId}
                                        </Text>
                                    ))}
                            </View>
                        </View>
                    </View>

                    {/* Right side - Ready Orders */}
                    <View style={{ width: '50%', }}>
                        <Text style={{
                            fontSize: fontSizes.headerText,
                            fontWeight: 'bold',
                            color: 'black',
                            marginBottom: 15,
                            textAlign: 'center',
                            fontFamily: 'NunitoSans-Bold',
                        }}>
                            Ready to pickup
                        </Text>

                        {/* Latest order - big box */}
                        {userOrders.length > 0 && (
                            <View style={{
                                width: windowWidth * 0.3,
                                height: windowHeight * 0.25,
                                backgroundColor: 'white',
                                borderRadius: 8,
                                padding: 5,
                                marginBottom: 20,
                                justifyContent: 'space-between',
                                alignItems: 'center',
                                shadowColor: '#000',
                                shadowOffset: { width: 0, height: 2 },
                                shadowOpacity: 0.25,
                                shadowRadius: 3.84,
                                elevation: 5,
                                alignSelf: 'center',
                            }}>
                                <Text />
                                <Text style={{
                                    fontSize: fontSizes.mainOrderNumber,
                                    fontWeight: 'bold',
                                    textAlign: 'center',
                                    fontFamily: 'NunitoSans-Bold',
                                }}>
                                    {userOrders[0].orderType !== ORDER_TYPE.DINEIN ? 'T' : ''}{userOrders[0].orderId}
                                </Text>
                                <Marquee>
                                    <Text
                                        numberOfLines={1}
                                        ellipsizeMode="tail"
                                        style={{
                                            fontSize: fontSizes.mainOultetName,
                                            color: '#666',
                                            textAlign: 'center',
                                            fontFamily: 'NunitoSans-Regular',
                                        }}>
                                        {userOrders[0].categoryNamePrepared ? userOrders[0].categoryNamePrepared : userOrders[0].outletName}
                                    </Text>
                                </Marquee>
                            </View>
                        )}

                        {/* Grid of previous orders - 9 smaller boxes */}
                        <View style={{
                            flexDirection: 'row',
                            flexWrap: 'wrap',
                            alignSelf: 'cneter',
                            alignItems: 'center',
                            justifyContent: "flex-start",
                            paddingLeft: windowWidth * 0.02,
                            gap: 15,
                        }}>
                            {userOrders.slice(1, 10).map((order, index) => (
                                <View key={index} style={{
                                    width: '30%',
                                    height: windowHeight * 0.16,
                                    backgroundColor: 'white',
                                    borderRadius: 8,
                                    padding: 5,
                                    justifyContent: 'space-between',
                                    alignItems: 'center',
                                    shadowColor: '#000',
                                    shadowOffset: { width: 0, height: 1 },
                                    shadowOpacity: 0.2,
                                    shadowRadius: 1.41,
                                    elevation: 2,
                                }}>
                                    <Text></Text>
                                    <Text
                                        numberOfLines={1}
                                        style={{
                                            fontSize: fontSizes.orderNumber,
                                            fontWeight: 'bold',
                                            textAlign: 'center',
                                            fontFamily: 'NunitoSans-Bold',
                                        }}>
                                        {order.orderType !== ORDER_TYPE.DINEIN ? 'T' : ''}{order.orderId}
                                    </Text>
                                    <Marquee
                                        speed={50}
                                        gradient={false}
                                    >
                                        <Text
                                            numberOfLines={1}
                                            ellipsizeMode="tail"
                                            style={{
                                                fontSize: fontSizes.outletName,
                                                color: '#666',
                                                textAlign: 'center',
                                                fontFamily: 'NunitoSans-Regular',
                                                marginRight: 30,
                                            }}>
                                            {order.categoryNamePrepared ? order.categoryNamePrepared : order.outletName}
                                        </Text>
                                    </Marquee>
                                </View>
                            ))}
                        </View>
                    </View>
                </View >
                :
                <View style={{
                    flex: 1,
                    justifyContent: 'center',
                    alignItems: 'center',
                }}>
                    <Text style={{
                        fontSize: 60,
                        fontWeight: 'bold',
                        color: '#666',
                        fontFamily: 'NunitoSans-Bold',
                        textAlign: 'center',
                    }}>
                        No orders in queue currently.
                    </Text>
                </View>
            }
        </View >
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#ffffff',
        padding: 10,
        paddingTop: 5,
    },
    titleDetail: {
        marginTop: 5,
        justifyContent: 'center',
        alignContent: 'center',
        alignItems: 'center',
    },
    orderTitle: {
        marginTop: 10,
        borderBottomWidth: StyleSheet.hairlineWidth,
        borderBottomColor: Colors.fieldtTxtColor,
    },
    totalContainer: {
        flexDirection: 'row',
        borderTopWidth: StyleSheet.hairlineWidth,
        borderTopColor: Colors.fieldtTxtColor,
        // marginTop: 10,
    },
    description: {
        paddingVertical: 5,
        fontSize: 10,
        fontFamily: "NunitoSans-Bold",
    },
    descriptionReceipt: {
        paddingVertical: 5,
        fontSize: 12,
        fontFamily: "NunitoSans-Bold",
    },
    PromotionTitle: {
        paddingVertical: 5,
        fontSize: 13,
        fontFamily: "NunitoSans-Bold",
        color: Colors.fieldtTxtColor
    },
    PromotionTitleReceipt: {
        paddingVertical: 5,
        fontSize: 10,
        fontFamily: "NunitoSans-Bold",
        color: Colors.fieldtTxtColor
    },
    price: {
        paddingVertical: 5,
        fontSize: 10,
        alignSelf: 'flex-end',
        fontFamily: "NunitoSans-Bold",
    },
    priceReceipt: {
        paddingVertical: 5,
        fontSize: 10,
        alignSelf: 'flex-end',
        fontFamily: "NunitoSans-Bold",
    },
    promotionDescription: {
        paddingVertical: 5,
        fontSize: 13,
        fontFamily: "NunitoSans-Bold",
        color: Colors.fieldtTxtColor,
    },
    promotionDescriptionReceipt: {
        paddingVertical: 2,
        fontSize: 10,
        fontFamily: "NunitoSans-Bold",
        color: Colors.fieldtTxtColor,
    },
    totalPrice: {
        paddingVertical: 5,
        fontSize: 15,
        fontWeight: '700',
        marginTop: 5,
        fontFamily: "NunitoSans-Bold",
    },
    totalPriceReceipt: {
        paddingVertical: 5,
        fontSize: 15,
        fontWeight: '700',
        marginTop: 5,
        fontFamily: "NunitoSans-Bold",
    },
    total: {
        paddingVertical: 5,
        fontSize: 15,
        fontWeight: '700',
        marginTop: 5,
        fontFamily: "NunitoSans-Bold",
    },
    totalReceipt: {
        paddingVertical: 5,
        fontSize: 15,
        fontWeight: '700',
        marginTop: 5,
        fontFamily: "NunitoSans-Bold",
    },

    outletCover: {
        width: "100%",
        alignSelf: 'center',
        height: undefined,
        aspectRatio: 2,
        borderRadius: 5
    },
    infoTab: {
        backgroundColor: Colors.fieldtBgColor,
        padding: 16,
        justifyContent: 'center',
        alignContent: 'center',
        alignItems: 'center'
    },
    workingHourTab: {
        padding: 16,
        flexDirection: 'row'
    },
    outletAddress: {
        textAlign: 'center',
        color: Colors.mainTxtColor
    },
    outletName: {
        fontWeight: 'bold',
        fontSize: 20,
        marginBottom: 10
    },
    logo: {
        width: 100,
        height: 100
    },
    headerLogo: {
        width: Styles.width * 0.8,
        //height: '100%'
        // backgroundColor: 'red',
        position: 'absolute',
        top: '-95%',
        alignSelf: 'center',
    },
    headerLogo1: {
        // width: Styles.width * 0.9,
        width: '100%',
        // backgroundColor: 'red',
        position: 'absolute',
        bottom: '-2%',
        alignSelf: 'center',
    },
    actionTab: {
        flexDirection: 'row',
        marginTop: 20
    },
    actionView: {
        width: Styles.width / 4,
        height: Styles.width / 4,
        justifyContent: 'center',
        alignItems: 'center',
        alignContent: 'center'
    },
    actionBtn: {
        borderRadius: 50,
        width: 70,
        height: 70,
        borderColor: Colors.secondaryColor,
        borderWidth: StyleSheet.hairlineWidth,
        justifyContent: 'center',
        alignItems: 'center'
    },
    actionText: {
        fontSize: 12,
        marginTop: 10
    },
    modalContainer: {
        flex: 1,
        backgroundColor: Colors.modalBgColor,
        alignItems: 'center',
        justifyContent: 'center'
    },
    modalView: {
        height: Styles.width * 0.7,
        width: Styles.width * 0.8,
        backgroundColor: Colors.whiteColor,
        borderRadius: 12,
        padding: Styles.width * 0.03,
        //alignItems: 'center',
        //justifyContent: 'center'
    },
    modalContainerReceipt: {
        flex: 1,
        backgroundColor: Colors.modalBgColor,
        alignItems: 'center',
        justifyContent: 'center'
    },
    modalViewReceipt: {
        //height: Styles.height * 0.9,
        width: Styles.width * 0.85,
        backgroundColor: Colors.whiteColor,
        borderRadius: 12,
        //padding: Styles.width * 0.03,
        alignItems: 'center',
        justifyContent: 'center'
    },
    modalView1: {
        //height: Styles.width * 1,
        width: Styles.width * 0.85,
        backgroundColor: Colors.whiteColor,
        borderRadius: 12,
        padding: Styles.width * 0.03,
        //alignItems: 'center',
        //justifyContent: 'center'
    },
    closeButton: {
        position: 'absolute',
        right: Styles.width * 0.02,
        top: Styles.width * 0.02,

        elevation: 1000,
        zIndex: 1000,
    },
    textInput: {
        height: 150,
        paddingHorizontal: 5,
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 5,
        marginTop: 10,
        textAlignVertical: "top",
        fontSize: 13
    },
    starStyle: {
        width: 35,
        height: 35,
    }
});

export default OrderQueueScreen;
