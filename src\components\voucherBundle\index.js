import React, { Component, useState, useEffect } from "react";
import {
    StyleSheet,
    ScrollView,
    Image,
    View,
    Text,
    TouchableOpacity,
    Alert,
    Dimensions,
    Modal,
    ActivityIndicator,
    FlatList,
    BackHandler,
    useWindowDimensions,
    InteractionManager,
    Animated,
    StatusBar,
} from "react-native";
import Colors from "../../constant/Colors";
import Styles from "../../constant/Styles";
import ApiClient from "../../util/ApiClient";
import API from "../../constant/API";
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";
import Entypo from "react-native-vector-icons/Entypo";
import Ionicons from "react-native-vector-icons/Ionicons";
import FontAwesome from "react-native-vector-icons/FontAwesome";
import * as Cart from "../../util/Cart";
import Draggable from "react-native-draggable";
import Back from "react-native-vector-icons/EvilIcons";

import Icon from "react-native-vector-icons/Ionicons";
import { idbGet } from "../../util/db";


// import { FlatList } from 'react-native-gesture-handler';
// import StickyParallaxHeader from 'react-native-sticky-parallax-header'
import Close from "react-native-vector-icons/AntDesign";
import { CommonStore } from "../../store/commonStore";
import {
    APP_TYPE,
    CHARGES_TYPE,
    ORDER_TYPE,
    PRODUCT_PRICE_TYPE,
    UNIT_TYPE,
    UNIT_TYPE_SHORT,
    UPSELLING_SECTION,
    UPSELL_BY_TYPE,
    USER_ORDER_STATUS,
    WEB_ORDER_VARIANT_LAYOUT,
} from "../../constant/common";
import { UserStore } from "../../store/userStore";
import AsyncStorage from "@react-native-async-storage/async-storage";
import AsyncImage from "../asyncImage";
import { prefix } from "../../constant/env";
import { useFocusEffect, useLinkTo } from "@react-navigation/native";
import { useCallback } from "react/cjs/react.production.min";
import { DataStore } from "../../store/dataStore";
import { isMobile, isTestingOutlet } from "../../util/commonFuncs";
import { TempStore } from "../../store/tempStore";
import moment from "moment";
import { TextInput } from "react-native-web";
import { Collections } from "../../constant/firebase";
import Feather from 'react-native-vector-icons/Feather';
import RecommendedItems from "../recommendedItems";
import { TableStore } from "../../store/tableStore";
import { ReactComponent as DefaultImage } from '../../svg/DefaultImage.svg';
import loadable from '@loadable/component';
import Checkbox from 'rc-checkbox';
import { PaymentStore } from "../../store/paymentStore";
import { HeaderHeightContext } from '@react-navigation/stack';
// const CheckboxLoadable = loadable.lib(() => import("rc-checkbox"));

import { SafeAreaProvider, SafeAreaView } from 'react-native-safe-area-context';
import { ANALYTICS, ANALYTICS_PARSED } from "../../constant/analytics";
import { logEvent } from "firebase/analytics";
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { idbDel, idbSet, safelyExecuteIdb } from "../../util/db";
import { TouchableWithoutFeedback } from "react-native-web";
import BigNumber from "bignumber.js";

const VoucherBundle = props => {

    const {
        navigation,
        route,
    } = props;

    const [scrollY, setScrollY] = useState(new Animated.Value(0));
    const imageSource = 'https://picsum.photos/500/500';
    const placeHolderContent = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11];
    const voucherBundle = TempStore.useState((s) => s.voucherBundle);
    const {
        width: windowWidth,
        height: windowHeight,
    } = useWindowDimensions();

    const useHeaderHeight = () => {
        const height = React.useContext(HeaderHeightContext);

        if (height === undefined) {
            return 0;
        }

        return height;
    };

    const upsellingCampaignsAfterCheckout = DataStore.useState((s) => s.upsellingCampaignsAfterCheckout);
    const upsellingCampaignsAfterCheckoutRecommendation = DataStore.useState((s) => s.upsellingCampaignsAfterCheckoutRecommendation);
    const upsellingCampaignsAfterCart = DataStore.useState(s => s.upsellingCampaignsAfterCart);

    const linkTo = useLinkTo();

    // const [menuItem, setMenuItem] = useState(menuItemParam);
    // const [totalState, setTotalState] = useState(menuItemParam.price);
    // const [totalState, setTotalState] = useState(0);

    const [remark, setRemark] = useState('');
    // const [outletData, setOutletData] = useState(outletDataParam);
    const [visible, setVisible] = useState(false);
    const [optional, setOptional] = useState(0);
    const [optional1, setOptional1] = useState(1);
    const [quantity1, setQuantity1] = useState(1);
    const [qty, setQty] = useState(null);
    const [detail, setDetail] = useState([]);
    // const [cartItem, setCartItem] = useState([]);
    const [choice, setChoice] = useState(null);
    // const [cartItem, setCartItem] = useState(cartItemParam);
    const [size, setSize] = useState("small");
    const [clicked, setClicked] = useState(1);
    const [clicked1, setClicked1] = useState(0);
    const [clicked2, setClicked2] = useState(0);
    const [cartIcon, setCartIcon] = useState(false);
    const [expandChoice, setExpandChoice] = useState(false);

    const [quantity, setQuantity] = useState(1);

    const [totalPrice, setTotalPrice] = useState(0);
    const [addOnPrice, setAddOnPrice] = useState(0);
    const [totalTax, setTotalTax] = useState(0);
    const [totalDiscount, setTotalDiscount] = useState(0);
    const [totalSc, setTotalSc] = useState(0);
    const [deliveryQuotation, setDeliveryQuotation] = useState({});
    const [usePointsToRedeem, setUsePointsToRedeem] = useState(false);
    const [usePointsToRedeemLCC, setUsePointsToRedeemLCC] = useState(false);
    const isPlacingReservation = CommonStore.useState(s => s.isPlacingReservation);
    const [pointsToRedeemDiscount, setPointsToRedeemDiscount] = useState(0);
    const [pointsToRedeemDiscountLCC, setPointsToRedeemDiscountLCC] = useState(0);

    //   //////////////////////////////////////////////////////////////////////////////////////

    //   const [temp, setTemp] = useState('');

    const [variablePrice, setVariablePrice] = useState('0.00');

    //   //////////////////////////////////////////////////////////////////////////////////////

    const firebaseUid = UserStore.useState(s => s.firebaseUid);

    const selectedOutlet = CommonStore.useState(s => s.selectedOutlet);
    const outletsItemAddOnDict = CommonStore.useState(s => s.outletsItemAddOnDict);
    const outletsItemAddOnChoiceDict = CommonStore.useState(s => s.outletsItemAddOnChoiceDict);
    const selectedOutletItem = CommonStore.useState(s => s.selectedOutletItem);
    const selectedOutletItems = CommonStore.useState(s => s.selectedOutletItems);
    const selectedOutletItemAddOn = CommonStore.useState(s => s.selectedOutletItemAddOn);
    const selectedOutletItemAddOnChoice = CommonStore.useState(s => s.selectedOutletItemAddOnChoice);

    const selectedOutletItemCategoriesDict = CommonStore.useState(s => s.selectedOutletItemCategoriesDict);

    const isLoading = CommonStore.useState((s) => s.isLoading);
    const cartItems = CommonStore.useState(s => s.cartItems);
    const cartItemChoices = CommonStore.useState(s => s.cartItemChoices);

    const [addOnMinMaxMessage, setAddOnMinMaxMessage] = useState('');
    const cartItemsProcessed = CommonStore.useState(s => s.cartItemsProcessed);

    //   const userCart = CommonStore.useState(s => s.userCart);

    //   const [qualifiedLoyaltyStamps, setQualifiedLoyaltyStamps] = useState([]);
    //   const [qualifiedLoyaltyStampBuy, setQualifiedLoyaltyStampBuy] = useState({});

    const orderType = CommonStore.useState(s => s.orderType);

    //   const loyaltyStampBuyItemSkuDict = CommonStore.useState(s => s.loyaltyStampBuyItemSkuDict);
    //   const loyaltyStampBuyCategoryNameDict = CommonStore.useState(s => s.loyaltyStampBuyCategoryNameDict);
    //   const selectedOutletLoyaltyStamps = CommonStore.useState(s => s.selectedOutletLoyaltyStamps);

    const selectedOutletTableId = CommonStore.useState(s => s.selectedOutletTableId);
    const selectedOutletTablePax = CommonStore.useState(s => s.selectedOutletTablePax);
    //   const selectedOutletWaiterId = CommonStore.useState(s => s.selectedOutletWaiterId);

    const overrideItemPriceSkuDict = CommonStore.useState(s => s.overrideItemPriceSkuDict);
    const amountOffItemSkuDict = CommonStore.useState(s => s.amountOffItemSkuDict);

    const overrideCategoryPriceNameDict = CommonStore.useState(s => s.overrideCategoryPriceNameDict);
    const amountOffCategoryNameDict = CommonStore.useState(s => s.amountOffCategoryNameDict);

    //   //////////////////////////////////////////////////////////////////////////////////////

    const [newSelectedOutletItemAddOn, setNewSelectedOutletItemAddOn] = useState({});
    const [newSelectedOutletItemAddOnDetails, setNewSelectedOutletItemAddOnDetails] = useState({});

    const isOrdering = CommonStore.useState((s) => s.isOrdering);
    const [addOnVerified, setAddOnVerified] = useState(false);
    const outletCustomTaxList = CommonStore.useState((s) => s.outletCustomTaxList);



    //   //////////////////////////////////////////////////////////////////////////////////////

    //   const isPlacingReservation = CommonStore.useState((s) => s.isPlacingReservation);

    //   //////////////////////////////////////////////////////////////////////////////////////


    //   //////////////////////////////////////////////////////////////////////////////////////

    //   const setState = () => { };

    //   useFocusEffect(
    //     useCallback(() => {
    //       // setTimeout(() => {
    //       //   if (global.selectedOutlet === null) {
    //       //     linkTo && linkTo(`${prefix}/scan`);
    //       //   }
    //       // }, 10000);

    //       // setIsMounted(true);
    //       // return () => {
    //       //   setIsMounted(false);
    //       // };
    //     }, [])
    //   );

    //   useEffect(() => {
    //     global.currPageStack = [
    //       ...global.currPageStack,
    //       'UpsellingAfterCheckoutScreen',
    //     ];
    //   }, []);

    //   useEffect(() => {
    //     if (linkTo) {
    //       DataStore.update(s => {
    //         s.linkToFunc = linkTo;
    //       });

    //       global.linkToFunc = linkTo;
    //     }
    //   }, [linkTo]);

    //   useEffect(() => {
    //     if (selectedOutlet === null) {
    //       readStates();
    //     }

    //     readCommonStates();
    //   }, [selectedOutlet]);

    //   const readStates = async () => {
    //     console.log('global.selectedoutlet = readStates (1) (==test==)');

    //     if (selectedOutlet === null) {
    //       console.log('global.selectedoutlet = readStates (2) (==test==)');

    //       // const commonStoreDataRaw = await AsyncStorage.getItem("@commonStore");
    //       const commonStoreDataRaw = await idbGet('@commonStore');
    //       if (commonStoreDataRaw !== undefined) {
    //         console.log('global.selectedoutlet = readStates (3) (==test==)');

    //         const commonStoreData = JSON.parse(commonStoreDataRaw);

    //         const latestOutletId = await AsyncStorage.getItem("latestOutletId");

    //         console.log('latestOutletId');
    //         console.log(latestOutletId);
    //         console.log('commonStoreData.selectedOutlet');
    //         console.log(commonStoreData.selectedOutlet);

    //         if (
    //           commonStoreData.selectedOutlet &&
    //           latestOutletId === commonStoreData.selectedOutlet.uniqueId
    //         ) {
    //           // check if it's the same outlet user scanned

    //           console.log('global.selectedoutlet = readStates (4) (==test==)');

    //           if (isPlacingReservation) {
    //           } else {
    //             // if (
    //             //   commonStoreData.orderType === ORDER_TYPE.DINEIN 
    //             //   // 2022-10-08 - Try to disable this
    //             //   // &&
    //             //   // commonStoreData.userCart.uniqueId === undefined
    //             // ) {
    //             //   // logout the user

    //             //   linkTo && linkTo(`${prefix}/scan`);
    //             // }
    //           }

    //           console.log('global.selectedoutlet = commonStoreData.selectedOutlet (3) (==test==)');

    //           global.selectedOutlet = commonStoreData.selectedOutlet;

    //           CommonStore.replace(commonStoreData);

    //           // const userStoreDataRaw = await AsyncStorage.getItem("@userStore");
    //           const userStoreDataRaw = await idbGet('@userStore');
    //           if (userStoreDataRaw !== undefined) {
    //             const userStoreData = JSON.parse(userStoreDataRaw);

    //             UserStore.replace(userStoreData);
    //           }

    //           // const dataStoreDataRaw = await AsyncStorage.getItem('@dataStore');
    //           const dataStoreDataRaw = await idbGet('@dataStore');
    //           if (dataStoreDataRaw !== undefined) {
    //             const dataStoreData = JSON.parse(dataStoreDataRaw);

    //             DataStore.replace(dataStoreData);
    //             // DataStore.replace({
    //             //   ...dataStoreData,
    //             //   ...dataStoreData.linkToFunc !== undefined && {
    //             //     linkToFunc: dataStoreData.linkToFunc,
    //             //   },
    //             // });
    //           }

    //           // const tableStoreDataRaw = await AsyncStorage.getItem("@tableStore");
    //           const tableStoreDataRaw = await idbGet('@tableStore');
    //           if (tableStoreDataRaw !== undefined) {
    //             const tableStoreData = JSON.parse(tableStoreDataRaw);

    //             TableStore.replace(tableStoreData);
    //           }

    //           // const paymentStoreDataRaw = await AsyncStorage.getItem("@paymentStore");
    //           const paymentStoreDataRaw = await idbGet('@paymentStore');
    //           if (paymentStoreDataRaw !== undefined) {
    //             const paymentStoreData = JSON.parse(paymentStoreDataRaw);

    //             PaymentStore.replace(paymentStoreData);
    //           }

    //           // const dataStoreDataRaw = await AsyncStorage.getItem('@dataStore');
    //           // if (dataStoreDataRaw !== undefined) {
    //           //   const dataStoreData = JSON.parse(dataStoreDataRaw);

    //           //   DataStore.replace(dataStoreData);
    //           // }
    //         }
    //       }
    //     }
    //   };

    //   const readCommonStates = async () => {
    //     // if (!linkToFunc) {
    //     //   const dataStoreDataRaw = await AsyncStorage.getItem('@dataStore');
    //     //   if (dataStoreDataRaw !== undefined) {
    //     //     const dataStoreData = JSON.parse(dataStoreDataRaw);
    //     //     DataStore.replace(dataStoreData);
    //     //   }
    //     // }
    //   };

    const selectedBundleTaggableVoucher = CommonStore.useState(s => s.selectedBundleTaggableVoucher);
    const [effectiveDays, setEffectiveDays] = useState(moment().day());

    const dummyData = [
        { id: '1', name: 'Item 1' },
        { id: '2', name: 'Item 2' },
        { id: '3', name: 'Item 3' },
        { id: '4', name: 'Item 4' },
        { id: '5', name: 'Item 5' },
    ];

    const renderVariants = (dataItem, item, index) => {
        const item2 = dataItem.item;

        return (
            <>
                {//////////////////////////////////////////////////////////////////

                    // // 2022-07-19 - Added variant Image for item - Eric Cheng Num Keet
                }
                <View>
                    {
                        (selectedOutlet && selectedOutlet.merchantType === APP_TYPE.RETAIL)
                            ?
                            <>
                                {item2.image ? (
                                    <AsyncImage
                                        source={{ uri: item2.image }}
                                        style={{
                                            resizeMode: 'contain',
                                            width: 50,
                                            height: 50,
                                            borderRadius: 10,
                                            marginBottom: 15,
                                            alignSelf: 'center',
                                        }}
                                        hideLoading={true}
                                    />) : (
                                    <View
                                        style={{
                                            width: 50,
                                            height: 50,
                                            backgroundColor: Colors.secondaryColor,
                                            borderRadius: 10,
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            marginBottom: 15,
                                            alignSelf: 'center',
                                        }}>
                                        <DefaultImage />
                                    </View>
                                )
                                    //////////////////////////////////////////////////////////////////////
                                }
                            </>
                            :
                            <></>
                    }
                    <TouchableOpacity style={{
                        //width: "50%",
                        height: 20,
                        // backgroundColor: selectedOutletItemAddOnChoice[item2.uniqueId] ? Colors.primaryColor : Colors.whiteColor, justifyContent: 'center', borderRadius: 20, borderWidth: 1,
                        // borderColor: selectedOutletItemAddOnChoice[item2.uniqueId] ? 'transparent' : Colors.descriptionColor,
                        // borderColor: Colors.descriptionColor,
                        // marginHorizontal: 8,
                        // paddingHorizontal: 12,
                        marginVertical: 10,
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                    }}
                        onPress={() => {
                            if (global.outletName) {
                                logEvent(global.analytics, ANALYTICS.WO_ITEM_DETAILS_VARIANT_CLICK, {
                                    eventNameParsed: ANALYTICS_PARSED.WO_ITEM_DETAILS_VARIANT_CLICK,

                                    outletName: global.outletName ? `${global.outletName} (Web)` : '',

                                    webUserInfo: `${moment().format('YYYYMMDD-HHmm')}>${global.userName ? global.userName : 'N/A'} (${global.userIdAnonymous ? global.userIdAnonymous : ''})`,
                                });
                            }

                            /////////////////////////////////
                            // minSelect/maxSelect calculations

                            var selectedOutletItemAddOnChoiceRemoved = {};

                            if (item.minSelect === 1 && item.maxSelect === 1) {
                                // means can only choose 1 addon choice, the others need removed

                                for (var i = 0; i < outletsItemAddOnChoiceDict[item.uniqueId].length; i++) {
                                    selectedOutletItemAddOnChoiceRemoved[outletsItemAddOnChoiceDict[item.uniqueId][i].uniqueId] = false;
                                }
                            }

                            /////////////////////////////////

                            var chooseValue = false;
                            if (selectedOutletItemAddOnChoice[item2.uniqueId] === undefined ||
                                selectedOutletItemAddOnChoice[item2.uniqueId] === false) {
                                chooseValue = true;
                            }
                            else {
                                chooseValue = false;
                            }

                            //////////////////////////////////////////////////////////////////

                            // 2023-01-09 - Prevent user from select the choice num exceed the max choice num

                            var maxChoices = item.maxSelect;
                            var totalChoiceNum = 0;
                            var choseChoiceIdList = [];

                            if (chooseValue && selectedOutletItemAddOn[item.uniqueId]) {
                                for (
                                    var i = 0;
                                    i <
                                    outletsItemAddOnChoiceDict[
                                        item.uniqueId
                                    ].length;
                                    i++
                                ) {
                                    var currChoiceId = outletsItemAddOnChoiceDict[
                                        item.uniqueId
                                    ][i].uniqueId;

                                    if (selectedOutletItemAddOnChoice[currChoiceId]) {
                                        // means selected

                                        totalChoiceNum++;

                                        choseChoiceIdList.push(currChoiceId);
                                    }
                                }
                            }

                            if (chooseValue && totalChoiceNum >= maxChoices && choseChoiceIdList.length > 0) {
                                // try to remove one of the previous choice

                                selectedOutletItemAddOnChoiceRemoved[
                                    choseChoiceIdList[0]
                                ] = false;

                                // chooseValue = false;
                            }

                            //////////////////////////////////////////////////////////////////

                            CommonStore.update(s => {
                                s.selectedOutletItemAddOnChoice = {
                                    ...selectedOutletItemAddOnChoice,

                                    ...selectedOutletItemAddOnChoiceRemoved,

                                    [item2.uniqueId]: chooseValue,
                                };

                                if (selectedOutletItemAddOn[item.uniqueId] === undefined) {
                                    s.selectedOutletItemAddOn = {
                                        ...selectedOutletItemAddOn,
                                        // [item.uniqueId]: new Set(),
                                        [item.uniqueId]: {},
                                    };
                                }

                                s.selectedOutletItemAddOn = {
                                    ...selectedOutletItemAddOn,
                                    [item.uniqueId]: {
                                        ...(selectedOutletItemAddOn[item.uniqueId]),

                                        ...selectedOutletItemAddOnChoiceRemoved,

                                        [item2.uniqueId]: chooseValue,
                                    },
                                };

                                console.log({
                                    ...selectedOutletItemAddOn,
                                    [item.uniqueId]: {
                                        ...(selectedOutletItemAddOn[item.uniqueId]),

                                        ...selectedOutletItemAddOnChoiceRemoved,

                                        [item2.uniqueId]: chooseValue,
                                    },
                                });
                            });
                        }}>
                        <View>
                            <Text
                                testID={`${index}-variantName-${dataItem.index}`}
                                style={{
                                    alignSelf: "center",
                                    color: Colors.blackColor,
                                    fontFamily: 'NunitoSans-Regular',
                                    fontSize: 14,
                                }}>{`${item2.name}`}
                            </Text>
                        </View>

                        <View style={{ flexDirection: 'row' }}>
                            <Text style={{
                                alignSelf: "center",
                                color: Colors.blackColor,
                                fontFamily: 'NunitoSans-Regular',
                                fontSize: 14,
                                paddingRight: 5,
                            }}>{`+ RM ${item2.price.toFixed(2)}`}
                            </Text>

                            {/* <CheckboxLoadable fallback={<></>}>
                                {({ default: Checkbox }) =>
                                    <Checkbox checked={selectedOutletItemAddOnChoice[item2.uniqueId] ? true : false} />
                                }
                            </CheckboxLoadable> */}

                            <Checkbox checked={selectedOutletItemAddOnChoice[item2.uniqueId] ? true : false} />
                        </View>

                    </TouchableOpacity>
                </View>
            </>
        );
    };

    const [boxId, setBoxId] = useState(null);

    const [selectedBox, setSelectedBox] = useState(null);
    const [discPrice, setDiscPrice] = useState(0);

    // Function to handle box selection
    const handleBoxPress = (boxId) => {
        setSelectedBox(boxId);
    };

    const insets = useSafeAreaInsets();
    const [modalQuantity, setModalQuantity] = useState(1);
    const onUpdatingCartItem = CommonStore.useState(s => s.onUpdatingCartItem);

    const headerHeight = useHeaderHeight();
    const [modalVisible, setModalVisible] = useState(false);

    console.log('selected voucher check', selectedBundleTaggableVoucher);

    const updateUserCart = async (newCartItems) => {
        // const body = {
        //     userId: firebaseUid,
        //     outletId: selectedOutlet.uniqueId,
        //     tableId: selectedOutletTableId,
        //     tablePax: selectedOutletTablePax,
        //     cartItems: newCartItems,

        //     waiterId: selectedOutletWaiterId,
        // };

        // ApiClient.POST(API.updateUserCart, body).then((result) => {
        //     if (result && result.status === 'success') {
        //         console.log('ok');
        //     }
        // });
    };
    const [sortedVariantAddOnList, setSortedVariantAddOnList] = useState([]);

    useEffect(() => {
        let sortedVariantAddOnListTemp = [];

        if (selectedOutletItem && selectedOutletItem.uniqueId && outletsItemAddOnDict[selectedOutletItem.uniqueId]) {
            sortedVariantAddOnListTemp = outletsItemAddOnDict[selectedOutletItem.uniqueId].filter((items) => !(items.isHideAddOn === true || items.isHideVariant === true));

            sortedVariantAddOnListTemp = sortedVariantAddOnListTemp.sort((a, b) => {
                return (
                    ((a.orderIndex !== undefined)
                        ? a.orderIndex
                        : sortedVariantAddOnListTemp.length) -
                    ((b.orderIndex !== undefined)
                        ? b.orderIndex
                        : sortedVariantAddOnListTemp.length)
                );
            });
        }

        setSortedVariantAddOnList(sortedVariantAddOnListTemp);
    }, [selectedOutletItem, outletsItemAddOnDict]);

    useEffect(() => {
        if (selectedOutletItem && selectedOutletItem.price !== undefined) {
            var extraPrice = 0;
            if (
                orderType === ORDER_TYPE.DELIVERY &&
                selectedOutlet &&
                selectedOutlet.deliveryPrice
            ) {
                extraPrice = selectedOutlet.deliveryPrice;
            } else if (
                orderType === ORDER_TYPE.PICKUP &&
                selectedOutlet &&
                selectedOutlet.pickUpPrice
            ) {
                extraPrice = selectedOutlet.pickUpPrice;
            }

            if (orderType === ORDER_TYPE.DELIVERY && selectedOutletItem.deliveryChargesActive) {
                extraPrice = selectedOutletItem.deliveryCharges || 0;

                if (extraPrice && selectedOutletItem.deliveryChargesType === CHARGES_TYPE.PERCENTAGE_BASED) {
                    extraPrice = selectedOutletItem.price * extraPrice / 100;
                }

                if (!selectedOutletItem.deliveryChargesActive) {
                    extraPrice = 0;
                }
            }

            if (orderType === ORDER_TYPE.PICKUP && selectedOutletItem.pickUpChargesActive) {
                extraPrice = selectedOutletItem.pickUpCharges || 0;

                if (extraPrice && selectedOutletItem.pickUpChargesType === CHARGES_TYPE.PERCENTAGE_BASED) {
                    extraPrice = selectedOutletItem.price * extraPrice / 100;
                }

                if (!selectedOutletItem.pickUpChargesActive) {
                    extraPrice = 0;
                }
            }

            var overrideCategoryPrice = undefined;
            if (selectedOutletItemCategoriesDict[selectedOutletItem.categoryId] && overrideCategoryPriceNameDict[selectedOutletItemCategoriesDict[selectedOutletItem.categoryId].name] !== undefined) {
                overrideCategoryPrice = overrideCategoryPriceNameDict[selectedOutletItemCategoriesDict[selectedOutletItem.categoryId].name].overridePrice;
            }

            if (overrideItemPriceSkuDict[selectedOutletItem.sku] && overrideItemPriceSkuDict[selectedOutletItem.sku].overridePrice !== undefined) {
                setTotalPrice(overrideItemPriceSkuDict[selectedOutletItem.sku].overridePrice);
            }
            else if (overrideCategoryPrice !== undefined) {
                setTotalPrice(overrideCategoryPrice);
            }
            else {
                setTotalPrice(extraPrice + selectedOutletItem.price);
            }
        }
    }, [selectedOutletItem, overrideItemPriceSkuDict, overrideCategoryPriceNameDict]);

    useEffect(() => {
        const addOnList = Object.entries(selectedOutletItemAddOn).map(([key, value]) => ({ key: key, value: value }));

        var addOnPriceTemp = 0;

        for (var i = 0; i < addOnList.length; i++) {
            var isValid = false;

            if (outletsItemAddOnDict[selectedOutletItem.uniqueId] && outletsItemAddOnDict[selectedOutletItem.uniqueId].length > 0) {
                for (var j = 0; j < outletsItemAddOnDict[selectedOutletItem.uniqueId].length; j++) {
                    if (outletsItemAddOnDict[selectedOutletItem.uniqueId][j].outletItemId === selectedOutletItem.uniqueId) {
                        isValid = true;
                    }
                    if ((outletsItemAddOnDict[selectedOutletItem.uniqueId][j].outletItemIdList || []).includes(selectedOutletItem.uniqueId)) {
                        isValid = true;
                    }
                }
            }

            if (isValid && addOnList[i].value) {
                const addOnChoiceList = Object.entries(addOnList[i].value).map(
                    ([key, value]) => ({ key: key, value: value }),
                );
                for (var j = 0; j < addOnChoiceList.length; j++) {

                    if (addOnChoiceList[j].value) {
                        const actualAddOnChoiceList = outletsItemAddOnChoiceDict[addOnList[i].key];

                        if (actualAddOnChoiceList && actualAddOnChoiceList.length > 0) {
                            for (var k = 0; k < actualAddOnChoiceList.length; k++) {
                                if (addOnChoiceList[j].key === actualAddOnChoiceList[k].uniqueId) {
                                    // add this addon price

                                    addOnPriceTemp += actualAddOnChoiceList[k].price;
                                }
                            }
                        }
                    }
                }
            }
        }
        //////////////////////////////////////////

        var addOnMinMaxMessageTemp = '';

        // const newSelectedOutletItemAddOnList = Object.entries(newSelectedOutletItemAddOn).map(([key, value]) => ({ key: key, value: value }));
        const newSelectedOutletItemAddOnList = Object.entries(
            newSelectedOutletItemAddOnDetails,
        ).map(([key, value]) => ({ key: key, value: value }));

        for (var i = 0; i < newSelectedOutletItemAddOnList.length; i++) {
            if (newSelectedOutletItemAddOnList[i].value) {
                const addOnTemp = newSelectedOutletItemAddOnDetails[newSelectedOutletItemAddOnList[i].key];

                if (addOnTemp) {
                    addOnPriceTemp += addOnTemp.quantity * addOnTemp.price;

                    ///////////////////////////////

                    // checks addOn minSelect and maxSelect

                    if (addOnTemp.quantity < addOnTemp.minSelect || addOnTemp.quantity > addOnTemp.maxSelect) {
                        addOnMinMaxMessageTemp += `${addOnTemp.choiceName}'s quantity must within ${addOnTemp.minSelect} ~ ${addOnTemp.maxSelect}\n`;
                    }

                    ///////////////////////////////
                }
            }
        }

        setAddOnMinMaxMessage(addOnMinMaxMessageTemp);

        //////////////////////////////////////////

        setAddOnPrice(addOnPriceTemp);
    }, [
        selectedOutletItemAddOn,
        selectedOutletItem,
        outletsItemAddOnChoiceDict,
        outletsItemAddOnDict,

        newSelectedOutletItemAddOn,
        newSelectedOutletItemAddOnDetails,
    ]);

    useEffect(() => {
        if (!isLoading) {
            if (selectedOutletItem && selectedOutletItem.uniqueId) {
                const addOnList = outletsItemAddOnDict[selectedOutletItem.uniqueId]
                    ? outletsItemAddOnDict[selectedOutletItem.uniqueId].filter(
                        (item) => !(item.isHideAddOn === true || item.isHideVariant === true)
                    )
                    : [];

                if (addOnList && addOnList.length > 0) {
                    // got addons

                    var resultList = [];

                    for (var i = 0; i < addOnList.length; i++) {
                        if (addOnList[i].minSelect !== undefined && addOnList[i].maxSelect !== undefined) {
                            var result = false;

                            const addOnId = addOnList[i].uniqueId;
                            const minSelect = addOnList[i].minSelect;
                            const maxSelect = addOnList[i].maxSelect;

                            if (minSelect === 0) {
                                result = true;
                            } else if (selectedOutletItemAddOn[addOnId]) {
                                const selectedOutletItemAddOnValueList = Object.entries(selectedOutletItemAddOn[addOnId]).map(([key, value]) => (value));

                                const selectedCount = selectedOutletItemAddOnValueList.filter(value => value === true).length;

                                if (selectedCount >= minSelect && selectedCount <= maxSelect) {
                                    result = true;
                                }
                            }

                            resultList.push(result);
                        }
                        else {
                            resultList.push(true);
                        }
                    }

                    setAddOnVerified(resultList.filter(result => result === false).length === 0);
                }
                else {
                    setAddOnVerified(true);
                }
            }
        }
    }, [isLoading, selectedOutletItem, outletsItemAddOnDict, outletsItemAddOnChoiceDict, selectedOutletItemAddOn]);

    const renderAddons = (dataItem, item) => {
        const item2 = dataItem.item;

        return (
            <View style={{
                width: '100%',
                flexDirection: 'row',
                alignItems: 'center',
                marginTop: 10,
                marginBottom: 10,
                justifyContent: 'space-between',
            }}>
                <View style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    width: '45%',
                    // backgroundColor: 'red',
                }}>
                    {//////////////////////////////////////////////////////////////////

                        // // 2022-07-19 - Added addon Image for item - Eric Cheng Num Keet
                    }
                    {
                        (selectedOutlet && selectedOutlet.merchantType === APP_TYPE.RETAIL)
                            ?
                            <>
                                {item2.image ? (
                                    <AsyncImage
                                        source={{ uri: item2.image }}
                                        style={{
                                            resizeMode: 'contain',
                                            width: 50,
                                            height: 50,
                                            borderRadius: 10,
                                            marginRight: 20,
                                        }}
                                        hideLoading={true}
                                    />) : (
                                    <View
                                        style={{
                                            width: 50,
                                            height: 50,
                                            backgroundColor: Colors.secondaryColor,
                                            borderRadius: 10,
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            marginRight: 20,
                                        }}>
                                        <DefaultImage />
                                    </View>
                                )
                                    //////////////////////////////////////////////////////////////////////
                                }
                            </>
                            :
                            <></>
                    }

                    <Text style={{
                        fontFamily: 'NunitoSans-Bold',
                        fontSize: 14,
                        // marginLeft: 15,
                    }}>
                        {item2.name}
                    </Text>
                </View>

                <View style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    // backgroundColor: 'red',                                
                    justifyContent: 'space-between',
                    // width: windowWidth * 0.4,
                    width: '55%',
                }}>
                    <View style={{ flexDirection: "row", borderWidth: 1, borderRadius: 25, borderColor: Colors.primaryColor, alignItems: 'center', width: '35%', justifyContent: 'space-around' }}>
                        <TouchableOpacity
                            onPress={() => {
                                if (global.outletName) {
                                    logEvent(global.analytics, ANALYTICS.WO_ITEM_DETAILS_ADDON_CLICK, {
                                        eventNameParsed: ANALYTICS_PARSED.WO_ITEM_DETAILS_ADDON_CLICK,

                                        outletName: global.outletName ? `${global.outletName} (Web)` : '',

                                        webUserInfo: `${moment().format('YYYYMMDD-HHmm')}>${global.userName ? global.userName : 'N/A'} (${global.userIdAnonymous ? global.userIdAnonymous : ''})`,
                                    });
                                }

                                var quantityTemp = 0;

                                if (newSelectedOutletItemAddOnDetails[item2.uniqueId]) {
                                    quantityTemp = newSelectedOutletItemAddOnDetails[item2.uniqueId].quantity;

                                    quantityTemp = quantityTemp - 1 >= 0 ? quantityTemp - 1 : 0;

                                    setNewSelectedOutletItemAddOnDetails({
                                        ...newSelectedOutletItemAddOnDetails,
                                        [item2.uniqueId]: {
                                            ...newSelectedOutletItemAddOnDetails[item2.uniqueId],
                                            quantity: quantityTemp,
                                            price: item2.price,

                                            outletItemAddOnChoiceId: item2.uniqueId,
                                            outletItemAddOnId: item.uniqueId,

                                            choiceName: item2.name,
                                            addOnName: item.name,

                                            minSelect: item2.minSelect,
                                            maxSelect: item2.maxSelect,

                                            oi: item.orderIndex !== undefined ? item.orderIndex : 0,
                                        }
                                    });
                                }
                                else {
                                    setNewSelectedOutletItemAddOnDetails({
                                        ...newSelectedOutletItemAddOnDetails,
                                        [item2.uniqueId]: {
                                            quantity: quantityTemp,
                                            price: item2.price,

                                            outletItemAddOnChoiceId: item2.uniqueId,
                                            outletItemAddOnId: item.uniqueId,

                                            choiceName: item2.name,
                                            addOnName: item.name,

                                            minSelect: item2.minSelect,
                                            maxSelect: item2.maxSelect,

                                            oi: item.orderIndex !== undefined ? item.orderIndex : 0,
                                        }
                                    });
                                }
                            }}>
                            <View testID={`addOnMinus-${dataItem.index}`} style={[styles.addBtn, { height: 27, }]}>
                                <FontAwesome name="minus"
                                    color={Colors.primaryColor}
                                    size={10}
                                />
                            </View>
                        </TouchableOpacity>
                        <View style={[styles.addBtn, { height: 27, }]} >
                            <Text
                                testID={`addOnQuantity-${dataItem.index}`}
                                style={{
                                    fontSize: 14,
                                    fontFamily: "NunitoSans-Bold",
                                    color: Colors.primaryColor,
                                }}>
                                {newSelectedOutletItemAddOnDetails[item2.uniqueId] ?
                                    newSelectedOutletItemAddOnDetails[item2.uniqueId].quantity
                                    : 0
                                }
                            </Text>
                        </View>

                        <TouchableOpacity
                            onPress={() => {
                                if (global.outletName) {
                                    logEvent(global.analytics, ANALYTICS.WO_ITEM_DETAILS_ADDON_CLICK, {
                                        eventNameParsed: ANALYTICS_PARSED.WO_ITEM_DETAILS_ADDON_CLICK,

                                        outletName: global.outletName ? `${global.outletName} (Web)` : '',

                                        webUserInfo: `${moment().format('YYYYMMDD-HHmm')}>${global.userName ? global.userName : 'N/A'} (${global.userIdAnonymous ? global.userIdAnonymous : ''})`,
                                    });
                                }

                                var quantityTemp = 0;

                                if (newSelectedOutletItemAddOnDetails[item2.uniqueId]) {
                                    quantityTemp = newSelectedOutletItemAddOnDetails[item2.uniqueId].quantity;

                                    quantityTemp = quantityTemp + 1;

                                    setNewSelectedOutletItemAddOnDetails({
                                        ...newSelectedOutletItemAddOnDetails,
                                        [item2.uniqueId]: {
                                            ...newSelectedOutletItemAddOnDetails[item2.uniqueId],
                                            quantity: quantityTemp,
                                            price: item2.price,

                                            outletItemAddOnChoiceId: item2.uniqueId,
                                            outletItemAddOnId: item.uniqueId,

                                            choiceName: item2.name,
                                            addOnName: item.name,

                                            minSelect: item2.minSelect,
                                            maxSelect: item2.maxSelect,

                                            oi: item.orderIndex !== undefined ? item.orderIndex : 0,
                                        }
                                    });
                                }
                                else {
                                    setNewSelectedOutletItemAddOnDetails({
                                        ...newSelectedOutletItemAddOnDetails,
                                        [item2.uniqueId]: {
                                            quantity: 1,
                                            price: item2.price,

                                            outletItemAddOnChoiceId: item2.uniqueId,
                                            outletItemAddOnId: item.uniqueId,

                                            choiceName: item2.name,
                                            addOnName: item.name,

                                            minSelect: item2.minSelect,
                                            maxSelect: item2.maxSelect,

                                            oi: item.orderIndex !== undefined ? item.orderIndex : 0,
                                        }
                                    });
                                }
                            }}>
                            <View testID={`addOnPlus-${dataItem.index}`} style={[styles.addBtn, { height: 27, }]}>
                                <FontAwesome name="plus"
                                    color={Colors.primaryColor}
                                    size={10}
                                />
                            </View>
                        </TouchableOpacity>
                    </View>

                    <View style={{
                        width: '65%',
                        flexDirection: 'row',
                        // backgroundColor: 'blue',
                        justifyContent: 'flex-end',
                    }}>
                        <Text style={{
                            fontFamily: 'NunitoSans-Regular',
                            fontSize: 14,
                            marginLeft: 10,
                        }}>+ RM {((newSelectedOutletItemAddOnDetails[item2.uniqueId] ?
                            (newSelectedOutletItemAddOnDetails[item2.uniqueId].quantity > 0 ? newSelectedOutletItemAddOnDetails[item2.uniqueId].quantity : 1)
                            : 1) * item2.price).toFixed(2)
                            }</Text>
                    </View>
                </View>
            </View>
        );
    };
    const [selectedRowIndex, setSelectedRowIndex] = useState(null);

    const openModalWithItem = (item, rowIndex) => {
        const outletItem = selectedOutletItems.find(findItem => findItem.sku === item);
        let discountPercentage = 0;
        let rowCriteria = selectedBundleTaggableVoucher.criteriaList[rowIndex];
        if (rowCriteria) {
            if (rowCriteria.variation === 'PRODUCT_OF_CATEGORY') {
                if (rowCriteria.variationItems.includes(selectedOutletItem.categoryId)) {
                    discountPercentage = rowCriteria.percentageOff;
                }
            } else {
                if (rowCriteria.variationItemsSku.includes(selectedOutletItem.sku)) {
                    discountPercentage = rowCriteria.percentageOff;
                }
            }
        }

        var extraPrice = 0;
        if (
            orderType === ORDER_TYPE.DELIVERY &&
            selectedOutlet &&
            selectedOutlet.deliveryPrice
        ) {
            extraPrice = selectedOutlet.deliveryPrice;
        } else if (
            orderType === ORDER_TYPE.PICKUP &&
            selectedOutlet &&
            selectedOutlet.pickUpPrice
        ) {
            extraPrice = selectedOutlet.pickUpPrice;
        }

        if (orderType === ORDER_TYPE.DELIVERY && outletItem.deliveryChargesActive) {
            extraPrice = outletItem.deliveryCharges || 0;

            if (extraPrice && outletItem.deliveryChargesType === CHARGES_TYPE.PERCENTAGE_BASED) {
                extraPrice = outletItem.price * extraPrice / 100;
            }

            if (!outletItem.deliveryChargesActive) {
                extraPrice = 0;
            }
        }

        if (orderType === ORDER_TYPE.PICKUP && outletItem.pickUpChargesActive) {
            extraPrice = outletItem.pickUpCharges || 0;

            if (extraPrice && outletItem.pickUpChargesType === CHARGES_TYPE.PERCENTAGE_BASED) {
                extraPrice = outletItem.price * extraPrice / 100;
            }

            if (!outletItem.pickUpChargesActive) {
                extraPrice = 0;
            }
        }

        const originalPrice = outletItem ? (parseFloat(outletItem.price) + extraPrice) : 0;
        const discountedPrice = originalPrice - (originalPrice * (discountPercentage / 100));
        if (
            outletItem.isActive &&
            (outletItem.isAvailableDayActive
                ? (outletItem.effectiveTypeOptions.includes(effectiveDays) &&
                    outletItem.effectiveStartTime && outletItem.effectiveEndTime &&
                    moment().isSameOrAfter(
                        moment(outletItem.effectiveStartTime)
                            .year(moment().year())
                            .month(moment().month())
                            .date(moment().date())
                    )
                    &&
                    moment().isBefore
                        (moment(outletItem.effectiveEndTime)
                            .year(moment().year())
                            .month(moment().month())
                            .date(moment().date())
                        ))
                : true) &&
            (outletItem.isOnlineMenu !== undefined ? outletItem.isOnlineMenu : true) &&
            (outletItem.isStockCountActive !== undefined &&
                outletItem.isStockCountActive !== false &&
                outletItem.stockCount !== undefined &&
                outletItem.toSellIgnoreStock !== undefined
                ? outletItem.isStockCountActive &&
                outletItem.stockCount > 0 &&
                (outletItem.toSellIgnoreStock !== undefined
                    ? outletItem.toSellIgnoreStock
                    : true)
                : true)
        ) {
            CommonStore.update((s) => {
                s.selectedOutletItem = outletItem;

                // s.selectedAddOnIdForChoiceQtyDict = {};
                s.selectedOutletItemAddOn = {};
                s.selectedOutletItemAddOnChoice = {};
            });

            setDiscPrice(discountedPrice);
            setModalVisible(true);
            setSelectedRowIndex(rowIndex); // track the row index

            setNewSelectedOutletItemAddOn({});
            setNewSelectedOutletItemAddOnDetails({});
        }
    };

    const renderBox = (item, index, rowIndex) => {
        const outletItem = selectedOutletItems.find(findItem => findItem.sku === item);

        // Check if the outletItem should be rendered based on hideInOrderTypes and isActive
        if (outletItem &&
            (!outletItem.hideInOrderTypes || !outletItem.hideInOrderTypes.includes(orderType)) &&
            outletItem.isActive !== false) {

            let discountPercentage = 0;
            let rowCriteria = selectedBundleTaggableVoucher.criteriaList[rowIndex];
            if (rowCriteria) {
                if (rowCriteria.variation === 'PRODUCT_OF_CATEGORY') {
                    if (rowCriteria.variationItems.includes(outletItem.categoryId)) {
                        discountPercentage = rowCriteria.percentageOff;
                    }
                } else {
                    if (rowCriteria.variationItemsSku.includes(item)) {
                        discountPercentage = rowCriteria.percentageOff;
                    }
                }
            }

            var extraPrice = 0;
            if (
                orderType === ORDER_TYPE.DELIVERY &&
                selectedOutlet &&
                selectedOutlet.deliveryPrice
            ) {
                extraPrice = selectedOutlet.deliveryPrice;
            } else if (
                orderType === ORDER_TYPE.PICKUP &&
                selectedOutlet &&
                selectedOutlet.pickUpPrice
            ) {
                extraPrice = selectedOutlet.pickUpPrice;
            }

            if (orderType === ORDER_TYPE.DELIVERY && outletItem.deliveryChargesActive) {
                extraPrice = outletItem.deliveryCharges || 0;

                if (extraPrice && outletItem.deliveryChargesType === CHARGES_TYPE.PERCENTAGE_BASED) {
                    extraPrice = outletItem.price * extraPrice / 100;
                }

                if (!outletItem.deliveryChargesActive) {
                    extraPrice = 0;
                }
            }

            if (orderType === ORDER_TYPE.PICKUP && outletItem.pickUpChargesActive) {
                extraPrice = outletItem.pickUpCharges || 0;

                if (extraPrice && outletItem.pickUpChargesType === CHARGES_TYPE.PERCENTAGE_BASED) {
                    extraPrice = outletItem.price * extraPrice / 100;
                }

                if (!outletItem.pickUpChargesActive) {
                    extraPrice = 0;
                }
            }

            const originalPrice = outletItem ? (parseFloat(outletItem.price) + extraPrice) : 0;
            const discountedPrice = originalPrice - (originalPrice * (discountPercentage / 100));

            // Check if item is in cart for this specific row
            const isInCart = cartItems.some(cartItem =>
                cartItem.isBundleType &&
                cartItem.itemSku === outletItem.sku &&
                cartItem.rowIndex === rowIndex
            );

            return (
                <View key={index} style={{
                    padding: 5,
                    flexDirection: "row",
                    display: "flex",
                    width: isMobile() ? windowWidth * 0.45 : windowWidth * 0.17,
                    alignItems: 'center',
                    justifyContent: 'center',
                }}>
                    <TouchableOpacity onPress={() => {
                        openModalWithItem(typeof item.sku === 'string' ? item.sku : item, rowIndex)
                    }}
                        style={{
                            padding: 5,
                            paddingTop: 20,
                            flexDirection: "column",
                            alignContent: "center",
                            alignItems: "center",
                            width: '100%',
                            height: discountedPrice !== originalPrice ? windowWidth * 0.73 : windowWidth * 0.68,
                            justifyContent: "flex-start",
                            shadowColor: Colors.tabGrey,
                            borderRadius: 10,
                            shadowRadius: 4,
                            borderColor: isInCart ? Colors.primaryColor : 'white',
                            borderWidth: 1,
                        }}>
                        <View>
                            <View style={{
                                shadowOpacity: 0.3,
                                shadowRadius: 20,
                                width: isMobile() ? windowWidth * 0.30 : windowWidth * 0.05,
                                height: isMobile() ? windowWidth * 0.30 : windowWidth * 0.05,
                                borderRadius: 200,
                                alignItems: 'center',
                                justifyContent: 'center',
                                backgroundColor: Colors.secondaryColor,
                            }}>
                                {outletItem && outletItem.image ? (
                                    <AsyncImage source={{ uri: outletItem.image }}
                                        style={{
                                            width: isMobile() ? windowWidth * 0.30 : windowWidth * 0.05,
                                            height: isMobile() ? windowWidth * 0.30 : windowWidth * 0.05,
                                            borderRadius: 200,
                                        }} />
                                ) : (
                                    <View style={{
                                        width: isMobile() ? windowWidth * 0.30 : windowWidth * 0.05,
                                        height: isMobile() ? windowWidth * 0.30 : windowWidth * 0.05,
                                        borderRadius: 200,
                                        justifyContent: "center",
                                        alignItems: 'center',
                                    }}>
                                        <Ionicons name="fast-food-outline"
                                            size={isMobile() ? windowWidth * 0.12 : windowWidth * 0.02}
                                        />
                                    </View>
                                )}
                            </View>
                        </View>
                        <View style={{
                            width: "100%",
                            alignItems: 'center',
                            marginTop: 20,
                        }}>
                            <View style={{
                                alignItems: "center",
                                height: 40,
                                width: "100%",
                            }}>
                                <Text
                                    style={{
                                        fontSize: 14,
                                        textTransform: "uppercase",
                                        fontFamily: "NunitoSans-Bold",
                                        textAlign: 'center',
                                        justifyContent: "center",
                                        overflow: "hidden",
                                        width: '85%',
                                    }}
                                    numberOfLines={2}>
                                    {outletItem.name ? outletItem.name : ''}
                                </Text>
                            </View>
                            {discountedPrice === originalPrice ? (
                                <View style={{
                                    flexDirection: "row",
                                    alignItems: "center",
                                    justifyContent: "center",
                                    width: '90%',
                                    height: '20%',
                                    marginTop: 15,
                                    marginBottom: 5,
                                }}>
                                    <Text style={{
                                        color: Colors.primaryColor,
                                        fontFamily: "NunitoSans-Bold",
                                        fontSize: 17,
                                    }}>RM {originalPrice.toFixed(2)}</Text>
                                </View>
                            ) : (
                                <>
                                    <View style={{
                                        flexDirection: "row",
                                        alignItems: "center",
                                        justifyContent: "center",
                                        width: '90%',
                                        height: '20%',
                                        marginTop: 15,
                                        marginBottom: 5,
                                    }}>
                                        <Text style={{
                                            color: Colors.primaryColor,
                                            fontFamily: "NunitoSans-Bold",
                                            fontSize: 17,
                                            marginRight: 5,
                                        }}>RM {discountedPrice.toFixed(2)}</Text>
                                    </View>
                                    <View style={{
                                        flexDirection: "row",
                                        justifyContent: "space-between",
                                        alignItems: "center",
                                    }}>
                                        <Text style={{
                                            color: '#aaabaf',
                                            fontFamily: "NunitoSans-Bold",
                                            fontSize: 14,
                                            textDecorationLine: 'line-through',
                                            marginRight: 5,
                                        }}>
                                            RM {originalPrice.toFixed(2)}
                                        </Text>
                                        <Text style={{
                                            color: Colors.whiteColor,
                                            fontFamily: "NunitoSans-Bold",
                                            backgroundColor: Colors.primaryColor,
                                            borderRadius: 5,
                                            fontSize: 12,
                                            padding: 2,
                                            paddingHorizontal: 6,
                                        }}>
                                            -{discountPercentage}%
                                        </Text>
                                    </View>
                                </>
                            )}
                        </View>
                    </TouchableOpacity>
                </View>
            );
        }

        // If the conditions are not met, return null or an empty fragment
        return null;
    };

    const renderRow = ({ item, index }) => {
        // Check if the item variation matches PRODUCT_OF_CATEGORY
        const isProductOfCategory = item.variation === 'PRODUCT_OF_CATEGORY';

        // Filter selectedOutletItems if the condition is met
        const filteredItems = isProductOfCategory
            ? selectedOutletItems
                .filter(outletItem => item.variationItems.includes(outletItem.categoryId))
                .map(outletItem => ({
                    sku: outletItem.sku,
                    rowIndex: index  // Add row index to track items per row
                }))
            : (item.variationItemsSku.length > 0
                ? item.variationItemsSku.map(sku => ({
                    sku: sku,
                    rowIndex: index
                }))
                : selectedOutletItems
                    .filter(outletItem => item.variationItems.includes(outletItem.uniqueId))
                    .map(outletItem => ({
                        sku: outletItem.sku,
                        rowIndex: index
                    })));
        console.log(item);
        console.log(filteredItems);
        return (
            <View key={index} style={{ marginBottom: 20 }}>
                {console.log(item)}
                {console.log('==============================')}
                <Text style={{
                    margin: 5,
                    marginLeft: 20,
                    fontSize: 18,
                    color: Colors.primaryColor,
                    fontFamily: 'NunitoSans-Bold',
                }}>
                    {`Select Item ${index + 1}`}
                </Text>
                <ScrollView horizontal showsHorizontalScrollIndicator={false}
                    style={{
                        marginLeft: 10,
                    }}>
                    <View style={{ flexDirection: 'row' }}>
                        {/* Render boxes based on filtered items or original variationItemsSku */}
                        {((isProductOfCategory || item.variationItemsSku.length === 0) ? filteredItems : item.variationItemsSku).map((sku, idx) => renderBox(typeof sku.sku === 'string' ? sku.sku : sku, idx, index))}
                    </View>
                </ScrollView>
            </View>
        );
    };

    const handleAddToCart = async () => {
        // Find if there is an existing item in the cart with the same row index
        const existingItemIndex = cartItems.findIndex(cartItem => cartItem.rowIndex === selectedRowIndex);


        let discountPercentage = 0;
        let rowCriteria = selectedBundleTaggableVoucher.criteriaList[selectedRowIndex];
        if (rowCriteria) {
            if (rowCriteria.variation === 'PRODUCT_OF_CATEGORY') {
                if (rowCriteria.variationItems.includes(selectedOutletItem.categoryId)) {
                    discountPercentage = rowCriteria.percentageOff;
                }
            } else {
                if (rowCriteria.variationItemsSku.includes(selectedOutletItem.sku)) {
                    discountPercentage = rowCriteria.percentageOff;
                }
            }
        }

        var extraPrice = 0;
        if (
            orderType === ORDER_TYPE.DELIVERY &&
            selectedOutlet &&
            selectedOutlet.deliveryPrice
        ) {
            extraPrice = selectedOutlet.deliveryPrice;
        } else if (
            orderType === ORDER_TYPE.PICKUP &&
            selectedOutlet &&
            selectedOutlet.pickUpPrice
        ) {
            extraPrice = selectedOutlet.pickUpPrice;
        }

        if (orderType === ORDER_TYPE.DELIVERY && selectedOutletItem.deliveryChargesActive) {
            extraPrice = selectedOutletItem.deliveryCharges || 0;

            if (extraPrice && selectedOutletItem.deliveryChargesType === CHARGES_TYPE.PERCENTAGE_BASED) {
                // extraPrice = tempCartOutletItemsDict[tempCartItem.itemId].price * extraPrice / 100;
                extraPrice = BigNumber(selectedOutletItem.price).multipliedBy(extraPrice).dividedBy(100).toNumber();
            }
        }

        if (orderType === ORDER_TYPE.PICKUP && selectedOutletItem.pickUpChargesActive) {
            extraPrice = selectedOutletItem.pickUpCharges || 0;

            if (extraPrice && selectedOutletItem.pickUpChargesType === CHARGES_TYPE.PERCENTAGE_BASED) {
                // extraPrice = tempCartOutletItemsDict[tempCartItem.itemId].price * extraPrice / 100;
                extraPrice = BigNumber(selectedOutletItem.price).multipliedBy(extraPrice).dividedBy(100).toNumber();
            }
        }

        const originalPrice = (parseFloat(selectedOutletItem.price) + extraPrice);
        const discountedPrice = originalPrice - (originalPrice * (discountPercentage / 100));

        if (global.outletName) {
            logEvent(global.analytics, ANALYTICS.WO_ITEM_DETAILS_ADD_CLICK, {
                eventNameParsed: ANALYTICS_PARSED.WO_ITEM_DETAILS_ADD_CLICK,
                outletName: global.outletName ? `${global.outletName} (Web)` : '',
                webUserInfo: `${moment().format('YYYYMMDD-HHmm')}>${global.userName ? global.userName : 'N/A'} (${global.userIdAnonymous ? global.userIdAnonymous : ''})`,
            });
        }

        if (addOnVerified && addOnMinMaxMessage.length <= 0 && !isLoading) {
            if (!isOrdering) {
                CommonStore.update((s) => {
                    s.isOrdering = true;
                    s.isLoading = true;
                });

                let newCartItems = [...cartItems]; // Create a copy of the cartItems array

                if (selectedOutletItem) {
                    let tempCartItemChoices = {};

                    if (outletsItemAddOnDict[selectedOutletItem.uniqueId]) {
                        // const tempOutletsItemAddOnList = outletsItemAddOnDict[selectedOutletItem.uniqueId];

                        let tempOutletsItemAddOnList =
                            [...outletsItemAddOnDict[selectedOutletItem.uniqueId]];

                        tempOutletsItemAddOnList.sort((a, b) => {
                            return (
                                ((a.orderIndex !== undefined)
                                    ? a.orderIndex
                                    : tempOutletsItemAddOnList.length) -
                                ((b.orderIndex !== undefined)
                                    ? b.orderIndex
                                    : tempOutletsItemAddOnList.length)
                            );
                        })

                        for (var i = 0; i < tempOutletsItemAddOnList.length; i++) {
                            // check against the default item add on list

                            const tempAddOn = tempOutletsItemAddOnList[i];

                            if (selectedOutletItemAddOn[tempAddOn.uniqueId] !== undefined) {
                                // means this addon got choices selected before (could also means deselected, so need checks further as shown below)

                                const tempAddOnSelectedObjList = Object.entries(
                                    selectedOutletItemAddOn[tempAddOn.uniqueId],
                                ).map(([key, value]) => ({ key: key, value: value }));

                                for (var j = 0; j < tempAddOnSelectedObjList.length; j++) {
                                    if (tempAddOnSelectedObjList[j].value === true) {
                                        // means this addon's choice is selected

                                        tempCartItemChoices[tempAddOnSelectedObjList[j].key] = true;
                                    }
                                    else {
                                        tempCartItemChoices[tempAddOnSelectedObjList[j].key] = false;
                                    }
                                }
                            }
                        }
                    }

                    ///////////////////////////////////////////////////

                    // const newSelectedOutletItemAddOnList = Object.entries(newSelectedOutletItemAddOn).map(([key, value]) => ({ key: key, value: value }));
                    const newSelectedOutletItemAddOnList = Object.entries(
                        newSelectedOutletItemAddOnDetails,
                    ).map(([key, value]) => ({ key: key, value: value }))
                        .sort((a, b) => a.value.choiceName.localeCompare(b.value.choiceName));

                    var addOnGroupList = [];

                    for (var i = 0; i < newSelectedOutletItemAddOnList.length; i++) {
                        if (newSelectedOutletItemAddOnList[i].value &&
                            newSelectedOutletItemAddOnList[i].value.quantity > 0) {
                            const addOnTemp = newSelectedOutletItemAddOnDetails[newSelectedOutletItemAddOnList[i].key];

                            if (addOnTemp) {
                                addOnGroupList.push(addOnTemp);
                            }
                        }
                    }

                    ///////////////////////////////////////////////////
                    let tId = 'default';
                    let tRate = selectedOutlet.taxRate;
                    let tCode = selectedOutlet.taxCode ? selectedOutlet.taxCode : 'SST';
                    let tName = selectedOutlet.taxName ? selectedOutlet.taxName : 'SST';
                    let foundCustomTax = null;

                    foundCustomTax = outletCustomTaxList.find(customTax => customTax.uniqueId === selectedOutletItemCategoriesDict[selectedOutletItem.categoryId].customTaxId);

                    if (
                        selectedOutlet.multiTax === true &&
                        selectedOutletItemCategoriesDict[selectedOutletItem.categoryId] &&
                        selectedOutletItemCategoriesDict[selectedOutletItem.categoryId].customTaxId &&
                        selectedOutletItemCategoriesDict[selectedOutletItem.categoryId].customTaxId !== undefined &&
                        selectedOutletItemCategoriesDict[selectedOutletItem.categoryId].customTaxId !== 'default' &&
                        foundCustomTax
                    ) {
                        tId = foundCustomTax.uniqueId;
                        tRate = foundCustomTax.rate;
                        tCode = foundCustomTax.code;
                        tName = foundCustomTax.name;
                    }

                    if (existingItemIndex !== -1) {
                        // Replace the existing item in the cart
                        newCartItems[existingItemIndex] = {
                            tId: tId,
                            tRate: tRate,
                            tCode: tCode,
                            tName: tName,

                            priceTemp: (selectedOutletItem && selectedOutletItem.priceType === PRODUCT_PRICE_TYPE.VARIABLE)
                                ? parseFloat(variablePrice)
                                : (discountedPrice + addOnPrice) * modalQuantity,
                            itemId: selectedOutletItem.uniqueId,
                            choices: tempCartItemChoices,
                            remarks: remark,
                            fireOrder: false,
                            quantity: modalQuantity,
                            cartItemDate: Date.now(),
                            addOnGroupList: addOnGroupList,
                            itemSku: selectedOutletItem.sku,
                            categoryId: selectedOutletItem.categoryId,
                            printerAreaList: selectedOutletItem.printerAreaList || [],
                            isDocket: selectedOutletItem.isDocket || false,
                            printDocketQuantity: selectedOutletItem.printDocketQuantity || 1,
                            rowIndex: selectedRowIndex, // Track row index for future updates
                            ...(selectedOutletItem.priceType === PRODUCT_PRICE_TYPE.VARIABLE) && {
                                priceVariable: parseFloat(variablePrice),
                            },
                            priceType: selectedOutletItem.priceType ? selectedOutletItem.priceType : PRODUCT_PRICE_TYPE.FIXED,
                            unitType: selectedOutletItem.unitType ? selectedOutletItem.unitType : UNIT_TYPE.GRAM,
                            itemCostPrice: selectedOutletItem.itemCostPrice ? selectedOutletItem.itemCostPrice : 0,
                            ...selectedOutletItem.upsellingCampaignId && {
                                priceUpselling: selectedOutletItem.priceUpselling,
                                upsellingCampaignId: selectedOutletItem.upsellingCampaignId,
                                upc: selectedOutletItem.upc,
                            },
                            isBundleType: true,
                            discountedPrice: discountedPrice,
                            discountPercentage: discountPercentage,

                            priceOriginalWithExtra: originalPrice,
                            priceOriginalFull: (originalPrice + addOnPrice) * modalQuantity,
                            priceAddOn: addOnPrice,
                            // voucherId: selectedBundleTaggableVoucher.uniqueId,
                        };
                    } else {
                        // Add a new item to the cart
                        newCartItems.push({
                            tId: tId,
                            tRate: tRate,
                            tCode: tCode,
                            tName: tName,

                            priceTemp: (selectedOutletItem && selectedOutletItem.priceType === PRODUCT_PRICE_TYPE.VARIABLE)
                                ? parseFloat(variablePrice)
                                : (discountedPrice + addOnPrice) * modalQuantity,
                            itemId: selectedOutletItem.uniqueId,
                            choices: tempCartItemChoices,
                            remarks: remark,
                            fireOrder: false,
                            quantity: modalQuantity,
                            cartItemDate: Date.now(),
                            addOnGroupList: addOnGroupList,
                            itemSku: selectedOutletItem.sku,
                            categoryId: selectedOutletItem.categoryId,
                            printerAreaList: selectedOutletItem.printerAreaList || [],
                            isDocket: selectedOutletItem.isDocket || false,
                            printDocketQuantity: selectedOutletItem.printDocketQuantity || 1,
                            rowIndex: selectedRowIndex, // Track row index for future updates
                            ...(selectedOutletItem.priceType === PRODUCT_PRICE_TYPE.VARIABLE) && {
                                priceVariable: parseFloat(variablePrice),
                            },
                            priceType: selectedOutletItem.priceType ? selectedOutletItem.priceType : PRODUCT_PRICE_TYPE.FIXED,
                            unitType: selectedOutletItem.unitType ? selectedOutletItem.unitType : UNIT_TYPE.GRAM,
                            itemCostPrice: selectedOutletItem.itemCostPrice ? selectedOutletItem.itemCostPrice : 0,
                            ...selectedOutletItem.upsellingCampaignId && {
                                priceUpselling: selectedOutletItem.priceUpselling,
                                upsellingCampaignId: selectedOutletItem.upsellingCampaignId,
                                upc: selectedOutletItem.upc,
                            },
                            isBundleType: true,
                            discountedPrice: discountedPrice,
                            discountPercentage: discountPercentage,

                            priceOriginalWithExtra: originalPrice,
                            priceOriginalFull: (originalPrice + addOnPrice) * modalQuantity,
                            priceAddOn: addOnPrice,
                            // voucherId: selectedBundleTaggableVoucher.uniqueId,
                        });
                    }

                    //////////////////////////////////////////////////////////////////

                    // Save the updated cart items to IndexedDB
                    safelyExecuteIdb(() => {
                        idbSet(`cartItems`, JSON.stringify(newCartItems));
                        idbSet(`cartOutletId`, selectedOutlet.uniqueId);
                    });

                    CommonStore.update(s => {
                        s.cartItems = newCartItems;
                        s.cartOutletId = selectedOutlet.uniqueId;
                    });

                    PaymentStore.update(s => {
                        s.cartItemsPayment = newCartItems;
                        s.outletIdPayment = selectedOutlet.uniqueId;
                        s.dateTimePayment = Date.now();
                        s.orderTypePayment = orderType;
                    });

                    setModalVisible(false);
                    setTimeout(() => {
                        CommonStore.update((s) => {
                            s.isOrdering = false;
                            s.isLoading = false;
                        });
                    }, 1000);
                }
            }
        } else {
            alert(`Info, Please select your choice before proceed.\n\n${addOnMinMaxMessage}`);
        }
    };

    const checkBundleCriteria = () => {
        // Step 1: Filter cartItems to get only those with isBundleType set to true
        const bundleItems = cartItems.filter(item => item.isBundleType === true);

        // Step 2: Compare the length of the filtered bundle items with the criteriaList length
        const isBundleCriteriaMet = bundleItems.length === selectedBundleTaggableVoucher.criteriaList.length;

        // Return true if the criteria are met, otherwise return false
        return isBundleCriteriaMet;
    };

    // var overrideCategoryPrice = undefined;
    // if (selectedOutletItemCategoriesDict[selectedOutletItem.categoryId] && overrideCategoryPriceNameDict[selectedOutletItemCategoriesDict[selectedOutletItem.categoryId].name] !== undefined) {
    //     overrideCategoryPrice = overrideCategoryPriceNameDict[selectedOutletItemCategoriesDict[selectedOutletItem.categoryId].name].overridePrice;
    // }
    // var extraPrice = 0;
    // if (
    //     orderType === ORDER_TYPE.DELIVERY &&
    //     selectedOutlet &&
    //     selectedOutlet.deliveryPrice
    // ) {
    //     extraPrice = selectedOutlet.deliveryPrice;
    // } else if (
    //     orderType === ORDER_TYPE.PICKUP &&
    //     selectedOutlet &&
    //     selectedOutlet.pickUpPrice
    // ) {
    //     extraPrice = selectedOutlet.pickUpPrice;
    // }

    // if (orderType === ORDER_TYPE.DELIVERY) {
    //     extraPrice = selectedOutletItem.deliveryCharges || 0;

    //     if (extraPrice && selectedOutletItem.deliveryChargesType === CHARGES_TYPE.PERCENTAGE_BASED) {
    //         extraPrice = selectedOutletItem.price * extraPrice / 100;
    //     }

    //     if (!selectedOutletItem.deliveryChargesActive) {
    //         extraPrice = 0;
    //     }
    // }

    // if (orderType === ORDER_TYPE.PICKUP) {
    //     extraPrice = selectedOutletItem.pickUpCharges || 0;

    //     if (extraPrice && selectedOutletItem.pickUpChargesType === CHARGES_TYPE.PERCENTAGE_BASED) {
    //         extraPrice = selectedOutletItem.price * extraPrice / 100;
    //     }

    //     if (!selectedOutletItem.pickUpChargesActive) {
    //         extraPrice = 0;
    //     }
    // }

    return (
        <View style={{
            backgroundColor: '#fff',
            display: 'flex',
            flex: 1,
            height: windowHeight,
            alignContent: 'center',
            alignItems: 'center',
            justifyContent: 'center'
        }}>
            <StatusBar barStyle='light-content' />

            <SafeAreaView>
                <Modal
                    style={{
                        // flex: 1,
                        width: windowWidth,
                        height: windowHeight,
                    }}
                    visible={voucherBundle}
                    transparent={false}
                    animationType="none"
                >
                    <TouchableOpacity
                        onPress={() => {
                            TempStore.update((s) => {
                                s.voucherBundle = false;
                            });
                        }}
                    >
                        <View
                            style={{
                                position: "absolute",
                                left: 18,
                                top: 20,
                                zIndex: 9000,
                            }}
                        >
                            <Close name="closecircle" size={30} /* color={"#b0b0b0"} */ color={Colors.tabGrey} />
                        </View>
                    </TouchableOpacity>

                    <View style={{
                        // position: 'absolute',
                        width: windowWidth,

                        marginTop: 40,
                        marginBottom: 10,
                        justifyContent: 'center',
                        alignItems: 'center',
                        zIndex: -1,
                    }}>
                        <View style={{
                            borderRadius: 13,
                            shadowOpacity: 0.3,
                            shadowRadius: 20,
                            height: windowHeight * 0.28,
                            width: windowWidth * 0.6,
                        }}
                        >
                            {selectedBundleTaggableVoucher && selectedBundleTaggableVoucher.image ? (
                                <AsyncImage
                                    source={{ uri: selectedBundleTaggableVoucher.image }}
                                    item={selectedBundleTaggableVoucher}
                                    style={{
                                        height: windowHeight * 0.28,
                                        width: windowWidth * 0.6,
                                        borderRadius: 15,
                                    }}
                                />
                            ) : (
                                // <Ionicons name="fast-food-outline" size={50} />
                                <View
                                    style={{
                                        height: windowHeight * 0.28,
                                        width: windowWidth * 0.6,
                                        borderRadius: 15,
                                        backgroundColor: Colors.secondaryColor,
                                        justifyContent: 'center',
                                        alignItems: 'center',
                                    }}
                                >
                                    <Ionicons
                                        name="fast-food-outline"
                                        // size={45}
                                        size={
                                            isMobile() ? windowWidth * 0.2 : windowWidth * 0.02
                                        }
                                    />
                                </View>
                            )}
                        </View>
                    </View>

                    <ScrollView
                        showsVerticalScrollIndicator={false}
                        contentContainerStyle={{
                            backgroundColor: 'transparent',
                            // display: 'flex',
                            flexDirection: 'column',
                            // top: windowHeight * 0.35,
                            // zIndex: -1,
                        }}
                        // style={{ zIndex: -1, }}
                        style={{ flex: 1 }}
                    >

                        <View
                            style={[
                                {
                                    width: windowWidth,
                                    height: windowHeight * 0.07,
                                    fontSize: 20,
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    color: Colors.primaryColor,
                                    fontFamily: "NunitoSans-Bold",
                                    backgroundColor: '#fff',
                                }
                            ]}
                        >
                            {/* Cham Latte */}
                            {selectedBundleTaggableVoucher ? selectedBundleTaggableVoucher.campaignName : 'N/A'}
                        </View>


                        <View style={[
                            {
                                backgroundColor: '#fff',
                                width: windowWidth,
                                flex: 1,
                                // flexGrow: 1,
                                // height: selectedBundleTaggableVoucher ? selectedBundleTaggableVoucher.criteriaList : 1 * (windowHeight * 0.45),
                                minHeight: windowHeight * 0.45,
                            }
                        ]}>
                            <FlatList
                                data={selectedBundleTaggableVoucher ? selectedBundleTaggableVoucher.criteriaList : ''}
                                renderItem={renderRow}
                                keyExtractor={(item, index) => index.toString()}
                                contentContainerStyle={{ paddingBottom: 20 }}
                            />
                        </View>

                    </ScrollView>

                    <View style={{
                        // position: 'absolute',
                        bottom: headerHeight * 0.1,
                        width: '100%',
                        height: (windowHeight - headerHeight) * 0.10,
                        backgroundColor: Colors.whiteColor,

                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'space-around',
                    }}>
                        {/* <View style={{ flexDirection: "row", borderWidth: 1, borderRadius: 25, borderColor: Colors.primaryColor, alignItems: 'center', width: '25%', justifyContent: 'space-around', }}>
                            <TouchableOpacity
                                onPress={() => {
                                    

                                    setModalQuantity(modalQuantity - 1 >= 1 ? modalQuantity - 1 : 1);
                                }}>
                                <View
                                    testID="menuItemDetailMinus"
                                    style={[
                                        styles.addBtn,
                                        // { width: windowWidth * 0.075, },
                                    ]}
                                >
                                    <FontAwesome name="minus"
                                        color={Colors.primaryColor}
                                        size={isMobile() ? 12 : 12}
                                    />
                                </View>
                            </TouchableOpacity>
                            <View
                                style={[
                                    styles.addBtn,
                                    // { width: windowWidth * 0.075, },
                                ]}
                            >
                                <Text
                                    testID="menuItemDetailQuantity"
                                    style={{
                                        fontSize: 18,
                                        fontFamily: "NunitoSans-Bold",
                                        color: Colors.primaryColor,
                                        marginBottom: isMobile() ? 0 : 3,
                                    }}>
                                    {modalQuantity}
                                </Text>
                            </View>

                            <TouchableOpacity
                                onPress={() => {
                                    
                                    setModalQuantity(modalQuantity + 1);
                                }}
                                style={{}}
                            >
                                <View
                                    testID="menuItemDetailPlus"
                                    style={[
                                        styles.addBtn,
                                        {
                                            // left: -1,
                                            // width: windowWidth * 0.075,
                                        },
                                    ]}
                                >
                                    <FontAwesome name="plus"
                                        color={Colors.primaryColor}
                                        size={isMobile() ? 12 : 12}
                                    />
                                </View>
                            </TouchableOpacity>
                        </View> */}

                        <View style={{ flexDirection: "row", alignItems: 'center', width: '28%', justifyContent: 'space-around', }}>
                            <Text style={{
                                color: "black",
                                fontSize: 20,
                                // fontWeight: "bold",
                                fontFamily: 'NunitoSans-Bold',
                                color: Colors.primaryColor,
                                paddingLeft: 7,
                            }}>
                                RM {(
                                    cartItems.filter((item) => item.isBundleType === true).reduce((accum, item) => {
                                        return accum + item.priceTemp * item.quantity;
                                    }, 0)
                                    + (
                                        totalDiscount +
                                        (usePointsToRedeem ? pointsToRedeemDiscount : 0) +
                                        (usePointsToRedeemLCC ? pointsToRedeemDiscountLCC : 0)
                                    )
                                ).toFixed(2)}
                            </Text>
                        </View>

                        <View style={{ width: '60%', backgroundColor: Colors.primaryColor, alignItems: 'center', borderRadius: 20, }}>
                            <TouchableOpacity onPress={async () => {
                                if (cartItems.length > 0 && checkBundleCriteria()) {
                                    // navigationObj && navigationObj.navigate("Cart", { test: null, outletData: selectedOutlet });

                                    // //////////////////////////////////////////////////////////////////

                                    // // 2024-12-18 - to support lowest discount price configuration support

                                    // if (selectedBundleTaggableVoucher && selectedBundleTaggableVoucher.lowestDiscountActive) {
                                    //     //store the original sorting index first
                                    //     let cartItemIdOrderIndexDict = cartItems.reduce((dict, cartItem, cartItemIndex) => {
                                    //         dict[cartItem.itemId + cartItem.cartItemDate] = cartItemIndex;
                                    //         return dict;
                                    //     }, {});

                                    //     let cartItemsSorted = [...cartItems];

                                    //     cartItemsSorted.sort(
                                    //         (a, b) =>
                                    //             ((a.priceOriginalFull !== undefined ? a.priceOriginalFull : a.priceTemp) / a.quantity) -
                                    //             ((b.priceOriginalFull !== undefined ? b.priceOriginalFull : b.priceTemp) / b.quantity)
                                    //     );

                                    //     // do the discount logic here

                                    //     let cartItemWithDPList = cartItems.filter(cartItem => cartItem.discountPercentage !== undefined)
                                    //         .sort(
                                    //             (a, b) =>
                                    //                 b.discountPercentage -
                                    //                 a.discountPercentage
                                    //         );

                                    //     let cartItemsNew = [];

                                    //     for (let cartItemIndex = 0; cartItemIndex < cartItemsSorted.length; cartItemIndex++) {
                                    //         let currCartItem = cartItemsSorted[cartItemIndex];

                                    //         if (currCartItem.discountPercentage !== undefined) {
                                    //             let dpToUse = 0;

                                    //             for (let cartItemDpIndex = 0; cartItemDpIndex < cartItemWithDPList.length; cartItemDpIndex++) {
                                    //                 let currCartItemDp = cartItemWithDPList[cartItemDpIndex];

                                    //                 if (!currCartItemDp.consumed) {
                                    //                     cartItemWithDPList[cartItemDpIndex].consumed = true;

                                    //                     dpToUse = currCartItemDp.discountPercentage;

                                    //                     break;
                                    //                 }
                                    //                 else {
                                    //                     continue;
                                    //                 }
                                    //             }

                                    //             const originalPrice = currCartItem.priceOriginalWithExtra;
                                    //             const discountedPrice = originalPrice - (originalPrice * (dpToUse / 100));

                                    //             cartItemsNew.push({
                                    //                 ...currCartItem,

                                    //                 priceTemp: (discountedPrice + currCartItem.priceAddOn) * currCartItem.quantity,

                                    //                 discountedPrice: discountedPrice,
                                    //                 discountPercentage: dpToUse,

                                    //                 // priceOriginalWithExtra: originalPrice,
                                    //                 // priceOriginalFull: (originalPrice + addOnPrice) * modalQuantity,
                                    //                 // priceAddOn: addOnPrice,
                                    //             });
                                    //         }
                                    //         else {
                                    //             cartItemsNew.push(currCartItem);
                                    //         }
                                    //     }

                                    //     //here can undo the sorting back (need do operate on the tempCartItemsProccessed)
                                    //     cartItemsNew.sort((a, b) => {
                                    //         return cartItemIdOrderIndexDict[a.itemId + a.cartItemDate] -
                                    //             cartItemIdOrderIndexDict[b.itemId + b.cartItemDate];
                                    //     });
                                    // }

                                    // //////////////////////////////////////////////////////////////////

                                    TempStore.update((s) => {
                                        s.voucherBundle = false;
                                    })

                                    const subdomain = await AsyncStorage.getItem('latestSubdomain');

                                    global.clickedCartIcon = true;

                                    CommonStore.update((s) => {
                                        s.selectedTaggableVoucher = selectedBundleTaggableVoucher;
                                    })

                                    if (onUpdatingCartItem) {
                                        const subdomain = await AsyncStorage.getItem('latestSubdomain');

                                        await AsyncStorage.setItem('onUpdatingCartItem', '1');

                                        if (!subdomain) {
                                            linkTo && linkTo(`${prefix}/outlet/cart`);
                                        }
                                        else {
                                            linkTo && linkTo(`${prefix}/outlet/${subdomain}/cart`);
                                        }

                                        // linkTo(`${prefix}/outlet/cart`);
                                    }
                                    else {
                                        if (global.isFromRecommendedItems) {
                                            global.isFromRecommendedItems = false;

                                            const subdomain = await AsyncStorage.getItem('latestSubdomain');

                                            if (!subdomain) {
                                                linkTo && linkTo(`${prefix}/outlet/cart`);
                                            }
                                            else {
                                                linkTo && linkTo(`${prefix}/outlet/${subdomain}/cart`);
                                            }
                                        }
                                        else {
                                            const subdomain = await AsyncStorage.getItem('latestSubdomain');

                                            if (!subdomain) {
                                                linkTo && linkTo(`${prefix}/outlet/cart`);
                                            }
                                            else {
                                                // if (subdomain === 'hominsan-ss15' || subdomain === 'hominsanttdi') {
                                                if (true) {
                                                    if (
                                                        (upsellingCampaignsAfterCart && upsellingCampaignsAfterCart.length > 0)
                                                        ||
                                                        (global.upsellingCampaignsAfterCart && global.upsellingCampaignsAfterCart.length > 0)
                                                    ) {
                                                        CommonStore.update((s) => {
                                                            s.currPage = "UpsellMenu";
                                                        });

                                                        linkTo && linkTo(`${prefix}/outlet/${subdomain}/upsell-menu`);
                                                    }
                                                    else {
                                                        if (global.redirectFromPage === 'Reservation') {
                                                            global.redirectFromPage = '';

                                                            linkTo && linkTo(`${prefix}/outlet/${subdomain}/reservation`);
                                                        }
                                                        else {
                                                            linkTo && linkTo(`${prefix}/outlet/${subdomain}/cart`);
                                                        }
                                                    }
                                                }
                                                else {
                                                    linkTo && linkTo(`${prefix}/outlet/${subdomain}/menu`);
                                                }
                                            }

                                            // linkTo(`${prefix}/outlet/menu`); 
                                        }
                                    }
                                } else {
                                    alert("Info, Please add all items to your cart before proceeding.")
                                }
                            }}>
                                <View style={{
                                    paddingVertical: 12, flexDirection: 'row', alignItems: 'center'
                                }}>
                                    <Text
                                        testID="addToCart"
                                        style={{
                                            color: "#ffffff",
                                            fontSize: 18,
                                            fontWeight: "bold",
                                            fontFamily: 'NunitoSans-Regular',
                                        }}
                                    >
                                        {onUpdatingCartItem ? 'Update' : 'Done'}
                                    </Text>
                                    {/* <Text style={{
                                        color: "#ffffff",
                                        fontSize: 16,
                                        // fontWeight: "bold",
                                        fontFamily: 'NunitoSans-Bold',
                                        // paddingLeft: 7,
                                    }}>
                                        RM 67
                                    </Text> */}
                                </View>
                            </TouchableOpacity>
                        </View>
                    </View>
                </Modal>
            </SafeAreaView>

            <Modal
                transparent={true}
                animationType="fade"
                style={{ backgroundColor: 'rgba(0, 0, 0, 0.5)', }}
                visible={modalVisible}
                onRequestClose={() => setModalVisible(false)}
            >
                <TouchableWithoutFeedback onPress={() => setModalVisible(false)}>
                    <View style={styles.modalBackground}>
                        <View
                            style={{
                                flex: 1,
                                // minHeight: windowHeight,
                                width: windowWidth,
                                height: windowHeight,
                                position: 'relative',
                            }}
                        >
                            {/* return button I think no need to use(JJ's comment) */}
                            {/* <TouchableOpacity
                            onPress={() => {
                            global.isFromRecommendedItems = false;

                            CommonStore.update((s) => {
                                s.menuItemDetailModal = false;
                            });
                            setModalQuantity(1);

                            global.menuItemDetailModal = false;

                            if (global.outletName) {
                                logEvent(global.analytics, ANALYTICS.WO_ITEM_DETAILS_BACK_CLICK, {
                                    eventNameParsed: ANALYTICS_PARSED.WO_ITEM_DETAILS_BACK_CLICK,

                                    outletName: global.outletName ? `${global.outletName} (Web)` : '',

                                    webUserInfo: `${moment().format('YYYYMMDD-HHmm')}>${global.userName ? global.userName : 'N/A'} (${global.userIdAnonymous ? global.userIdAnonymous : ''})`,
                                });
                            }
                        }}
                    >
                        <View
                            style={{
                                position: "absolute",
                                left: 15,
                                top: 17,
                                index: 9000,
                            }}
                        >
                            <Close name="closecircle" size={selectedOutletItem && selectedOutletItem.image ? 30 : 25}  color={Colors.blackColor} />
                        </View>
                    </TouchableOpacity> */}

                            {/* The image, No need to use (JJ's comment) */}
                            {/* <View style={[{
                        backgroundColor: Colors.whiteColor,
                        width: selectedOutletItem && selectedOutletItem.image ? '100%' : 0,
                        height: selectedOutletItem && selectedOutletItem.image ? Dimensions.get('window').height * 0.43 : 0,
                        borderRadius: 10, zIndex: -1,
                    }, selectedOutletItem && selectedOutletItem.image ? {

                    } : {
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                    }]}>
                        {selectedOutletItem && selectedOutletItem.image ?
                            <AsyncImage source={{ uri: selectedOutletItem.image }} item={selectedOutletItem} style={{
                                width: '100%',
                                height: Dimensions.get('window').height * 0.43,
                            }} />
                            :
                            <></>
                        }
                    </View> */}
                            <TouchableWithoutFeedback>
                                <ScrollView style={{
                                    position: 'absolute', bottom: 0,

                                    width: windowWidth,
                                    paddingBottom: 80, paddingHorizontal: 20,
                                    backgroundColor: Colors.whiteColor,
                                    borderRadius: 25,
                                    height: windowHeight * 0.65, borderWidth: 0.1, borderColor: Colors.fieldtBgColor,

                                    zIndex: -1,

                                }}>
                                    {/* Mid Content part container (JJ's comment) */}
                                    <View>
                                        <View>
                                            {/* Container top part (JJ's comment) */}
                                            <View style={{
                                                flexDirection: 'row',
                                                justifyContent: 'space-between',
                                                flexShrink: 1,
                                                width: '100%',
                                                paddingTop: 20,
                                            }}>
                                                {/* Item Name (JJ's Comment) */}
                                                <View style={{ width: '70%' }}>
                                                    <Text
                                                        // numberOfLines={2}
                                                        style={{
                                                            fontSize: 20,
                                                            textTransform: 'uppercase',
                                                            fontFamily: "NunitoSans-Bold",
                                                        }}>
                                                        {selectedOutletItem && selectedOutletItem.name ? selectedOutletItem.name : ''}
                                                    </Text>
                                                </View>
                                                {/* Item Price (JJ's Comment) */}
                                                <View style={{ width: '30%', justifyContent: 'center', }}>
                                                    <Text style={{
                                                        color: Colors.primaryColor,
                                                        fontFamily: "NunitoSans-Bold",
                                                        textAlign: 'right',
                                                        // paddingTop: 5,
                                                        fontSize: 16,
                                                    }}>RM{discPrice.toFixed(2)}
                                                    </Text>
                                                </View>
                                            </View>
                                            {/* Item description (JJ's comment) */}
                                            <Text
                                                // numberOfLines={1} 
                                                style={{
                                                    fontSize: 14,
                                                    fontFamily: "NunitoSans-Regular",
                                                    paddingTop: 5,
                                                    color: '#86898f',
                                                }}>{selectedOutletItem && selectedOutletItem.description ? `(${selectedOutletItem.description})` : ''}
                                            </Text>
                                            {/* Dotted line (JJ's comment) */}
                                            <View style={{ borderBottomWidth: 2, borderStyle: 'dotted', marginVertical: 25, opacity: 0.6, borderColor: 'grey' }} />
                                        </View>

                                        {/*Add on part(JJ's Comment) */}
                                        {/* { */}
                                        {isLoading ?
                                            <View>
                                                <ActivityIndicator color={Colors.primaryColor} size={"large"} />
                                            </View>
                                            :
                                            <>
                                                {
                                                    (outletsItemAddOnDict[selectedOutletItem.uniqueId]
                                                        ? outletsItemAddOnDict[selectedOutletItem.uniqueId] : []).length > 0
                                                        ?
                                                        <Text
                                                            style={{
                                                                color: Colors.blackColor,
                                                                marginBottom: 10,
                                                                fontSize: 18,
                                                                fontFamily: 'NunitoSans-SemiBold',
                                                            }}
                                                        >
                                                            Let's make it better?
                                                        </Text>
                                                        :
                                                        <></>
                                                }
                                                {sortedVariantAddOnList
                                                    ? sortedVariantAddOnList.map((item, index) => {

                                                        if (item.minSelect !== undefined && item.maxSelect !== undefined) {
                                                            // means is variant

                                                            return (
                                                                <>
                                                                    <View style={{ marginBottom: 20, }}>
                                                                        <View style={{ flexDirection: "row", justifyContent: 'space-between' }}>
                                                                            <Text
                                                                                style={{
                                                                                    color: Colors.blackColor,
                                                                                    fontSize: 16,
                                                                                    fontFamily: 'NunitoSans-SemiBold',
                                                                                }}
                                                                            >
                                                                                {item.name ? item.name : 'N/A'}
                                                                            </Text>
                                                                            <Text
                                                                                style={{
                                                                                    color: '#282C3F',
                                                                                    fontSize: 12,
                                                                                    fontFamily: 'NunitoSans-SemiBold',
                                                                                    backgroundColor: '#56FD481A',
                                                                                    paddingVertical: 2,
                                                                                    paddingHorizontal: 10,
                                                                                    borderColor: Colors.primaryColor,
                                                                                    borderWidth: 1,
                                                                                    borderRadius: 5,
                                                                                    opacity: 0.7,
                                                                                }}
                                                                            >
                                                                                {item.minSelect === 0 ? 'Optional' : 'Compulsory'}
                                                                            </Text>
                                                                        </View>
                                                                        <Text
                                                                            style={{
                                                                                color: '#86898F',
                                                                                fontSize: 12,
                                                                                fontFamily: 'NunitoSans-SemiBold',
                                                                            }}
                                                                        >
                                                                            {item.maxSelect > 1 ? `(Please select up to ${item.maxSelect} choices)` : ''}
                                                                        </Text>

                                                                        <FlatList
                                                                            horizontal={false}
                                                                            nestedScrollEnabled={true}
                                                                            data={outletsItemAddOnChoiceDict[item.uniqueId] ? outletsItemAddOnChoiceDict[item.uniqueId].filter(choiceFilter => {
                                                                                return !choiceFilter.isHidden;
                                                                            }).sort((a, b) => a.name.localeCompare(b.name)) : []}
                                                                            renderItem={dataItem => renderVariants(dataItem, item)}
                                                                            showsHorizontalScrollIndicator={false}
                                                                            style={{
                                                                                marginTop: 10,

                                                                            }}
                                                                        />
                                                                    </View>
                                                                    <View style={{ borderBottomWidth: 2, borderStyle: 'dotted', marginBottom: 25, opacity: 0.6, borderColor: 'grey' }} />
                                                                </>
                                                            );
                                                        }
                                                        else {
                                                            // means is addon

                                                            return (
                                                                <View style={{ marginBottom: 15 }}>
                                                                    <Text
                                                                        style={{
                                                                            color: Colors.blackColor,
                                                                            fontSize: 16,
                                                                            fontFamily: 'NunitoSans-SemiBold',
                                                                        }}
                                                                    >
                                                                        {item.name ? item.name : 'N/A'}
                                                                    </Text>

                                                                    <FlatList
                                                                        horizontal={false}
                                                                        nestedScrollEnabled={true}
                                                                        data={outletsItemAddOnChoiceDict[item.uniqueId] ? outletsItemAddOnChoiceDict[item.uniqueId].filter(choiceFilter => {
                                                                            return !choiceFilter.isHidden;
                                                                        }).sort((a, b) => a.name.localeCompare(b.name)) : []}
                                                                        renderItem={dataItem => renderAddons(dataItem, item)}
                                                                        style={{
                                                                            marginTop: 10,
                                                                        }}
                                                                    />
                                                                </View>
                                                            );
                                                        }
                                                    })
                                                    : null}
                                            </>
                                        }

                                        {/* Special remark (JJ's Comment) */}
                                        <View style={{
                                            marginBottom: 15,
                                            marginTop: 5,
                                        }}>
                                            <Text
                                                style={{
                                                    color: Colors.descriptionColor,
                                                    // color: Colors.mainTxtColor,
                                                    // fontWeight: "500",
                                                    fontSize: 17,
                                                    fontFamily: 'NunitoSans-SemiBold',
                                                    marginBottom: 15,
                                                }}
                                            >
                                                Special Remarks:
                                            </Text>
                                            <TextInput
                                                testID="remarks"
                                                underlineColorAndroid={Colors.fieldtBgColor}
                                                style={[styles.textInput, {
                                                    height: windowHeight * 0.1
                                                }]}
                                                placeholder="eg: no onions"
                                                onChangeText={(text) => {
                                                    // setState({ remark: text });
                                                    setRemark(text);
                                                }}

                                                multiline={true}
                                            />
                                        </View>
                                    </View>

                                    {/* Scrollview for bottom space (JJ's Comment) */}
                                    <ScrollView style={[{
                                        flex: 1,
                                        // height: (windowHeight - headerHeight) * 0.9,
                                        padding: 16,
                                    }]}
                                        showsVerticalScrollIndicator={false}
                                        contentContainerStyle={{
                                            paddingLeft: 10,
                                            paddingRight: 10,
                                            paddingTop: 5,
                                            // paddingBottom: (windowHeight - headerHeight) * 0.10,
                                        }}
                                        nestedScrollEnabled={true}>

                                        {/* <View style={{ minHeight: 100 }} /> */}
                                    </ScrollView>
                                </ScrollView>
                            </TouchableWithoutFeedback>

                            {/*I think this is bottom (JJ's comment) */}
                            <TouchableWithoutFeedback>
                                <View style={{
                                    position: "absolute",
                                    bottom: insets.bottom,
                                    // bottom: 'env(safe-area-inset-bottom)',
                                    left: 0,
                                    right: 0,
                                    flexDirection: 'row',
                                    backgroundColor: Colors.whiteColor,
                                    width: windowWidth,
                                    height: 60,
                                    alignItems: 'center',
                                    paddingHorizontal: 20,
                                    paddingVertical: 7,
                                    justifyContent: 'space-between',
                                }}>
                                    {/* Bottom part (JJ's Comment) */}
                                    <TouchableOpacity style={{ width: '100%', backgroundColor: Colors.primaryColor, alignItems: 'center', borderRadius: 20, }}
                                        onPress={() => { handleAddToCart() }}>
                                        <View style={{
                                            paddingVertical: 12, flexDirection: 'row', alignItems: 'center'
                                        }}>
                                            <Text
                                                testID="addToCart"
                                                style={{
                                                    color: "#ffffff",
                                                    fontSize: 16,
                                                    // fontWeight: "bold",
                                                    fontFamily: 'NunitoSans-Regular',
                                                    borderRightWidth: 1,
                                                    borderColor: Colors.whiteColor,
                                                    paddingRight: 7,
                                                }}
                                            >
                                                {onUpdatingCartItem ? 'Update' : 'Add'}
                                            </Text>
                                            <Text style={{
                                                color: "#ffffff",
                                                fontSize: 16,
                                                // fontWeight: "bold",
                                                fontFamily: 'NunitoSans-Bold',
                                                paddingLeft: 7,
                                            }}>{'RM '}{(discPrice + addOnPrice).toFixed(2)}
                                            </Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                            </TouchableWithoutFeedback>
                        </View>
                    </View>
                </TouchableWithoutFeedback>
            </Modal >
        </View>
    )
}

const styles = StyleSheet.create({

    addBtn: {
        // width: 20,
        height: 45,

        display: 'flex',
        justifyContent: "center",
        alignItems: "center",

        borderColor: 'transparent',
    },

    confirmBox: {
        width: 350,
        // height: windowHeight * 0.35,
        height: 320,
        borderRadius: 20,
        backgroundColor: Colors.whiteColor,
    },
    container: {
        // flex: 1,
        width: Dimensions.get("window").width,
        height: Dimensions.get("window").height,
        backgroundColor: "#ffffff",
        position: "relative",
    },
    outletCover: {
        // width: isMobile() ? '100%' : windowWidth,
        // alignSelf: 'center',
        // height: isMobile() ? undefined : windowHeight * 0.5,
        // aspectRatio: isMobile() ? 2 : 2,
        // resizeMode: isMobile() ? 'stretch' : 'stretch',
        width: isMobile() ? "100%" : "auto",
        alignSelf: "center",
        height: isMobile() ? undefined : Dimensions.get("window").height * 0.5,
        aspectRatio: isMobile() ? 2 : 2,
        resizeMode: isMobile() ? "stretch" : "stretch",
        ...(!isMobile() && {}),
    },
    infoTab: {
        backgroundColor: Colors.fieldtBgColor,
    },
    workingHourTab: {
        padding: 16,
        flexDirection: "row",
    },
    outletAddress: {
        textAlign: "center",
        color: Colors.mainTxtColor,
    },
    outletName: {
        fontWeight: "bold",
        fontSize: 20,
        marginBottom: 10,
    },
    logo: {
        width: 100,
        height: 100,
    },
    actionTab: {
        flexDirection: "row",
        marginTop: 20,
    },
    actionView: {
        width: Dimensions.get("window").width / 4,
        height: Dimensions.get("window").height / 4,
        justifyContent: "center",
        alignItems: "center",
        alignContent: "center",
    },
    actionBtn: {
        borderRadius: 50,
        width: 70,
        height: 70,
        borderColor: Colors.secondaryColor,
        borderWidth: StyleSheet.hairlineWidth,
        justifyContent: "center",
        alignItems: "center",
    },
    actionText: {
        fontSize: 12,
        marginTop: 10,
    },
    category: {
        // width: 150,
        paddingHorizontal: 25,
        justifyContent: "center",
        alignItems: "center",
    },
    floatCartBtn: {
        zIndex: 2,
        position: "absolute",
        bottom: 30,
        right: 30,
        width: 60,
        height: 60,
        borderRadius: 60 / 2,
        backgroundColor: Colors.secondaryColor,
        justifyContent: "center",
        alignContent: "center",
        alignItems: "center",
        shadowOffset: {
            width: 0,
            height: 1,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 3,
    },
    cartCount: {
        position: "absolute",
        top: -8,
        right: 5,
        backgroundColor: Colors.primaryColor,
        width: 20,
        height: 20,
        borderRadius: 20 / 2,
        alignContent: "center",
        alignItems: "center",
        justifyContent: "center",
    },

    textInput: {
        // height: 100,
        // height: Dimensions.get('window').width * 0.1,
        paddingHorizontal: 20,
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 12,

        fontSize: 14,
        fontFamily: 'NunitoSans-Regular',
        // color: Colors.descriptionColor,
        textAlignVertical: 'top',
        paddingVertical: 15,
        borderWidth: 0.5,
        opacity: 0.7,
    },
    optionContainer: {
        padding: 0,
        paddingTop: 0,
        marginBottom: 25,
        gap: 20,
        flexDirection: 'row',
        overflow: 'scroll',
        maxHeight: 100,
    },
    title: {
        fontSize: 17,
        fontWeight: 'bold',
        marginBottom: 10,
    },
    option: {
        width: 120,
        height: 70,
        marginHorizontal: 20,
        marginLeft: 0,
        padding: 10,
        backgroundColor: '#f0f0f0',
        alignItems: 'center',
        justifyContent: 'center',
        borderWidth: 1,
        borderRadius: 10,
        borderColor: '#ededed',
        fontSize: 16,
    },
    selectedOption: {
        backgroundColor: '#ccded6',
        borderWidth: 2,
        borderRadius: 10,
        borderColor: '#4E9F7D',
    },
    textFieldInput: {
        height: "100%",
        width: "100%",
        paddingHorizontal: 20,
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 10,
    },
    addBtn1: {
        backgroundColor: Colors.primaryColor,
        width: 20,
        height: 20,
        justifyContent: "center",
        alignItems: "center",
    },
    modalBackground: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'center',
        alignItems: 'center',
    },
})


export default VoucherBundle;
