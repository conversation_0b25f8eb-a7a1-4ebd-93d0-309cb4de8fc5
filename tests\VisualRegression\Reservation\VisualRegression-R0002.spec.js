const { test, expect } = require('@playwright/test');

test('Max Diff Pixels 250', async ({ page }) => {
    await page.goto('http://localhost:5400/web/outlet/hominsanttdi/reservation');

    await expect(page).toHaveScreenshot({ maxDiffPixels: 200 });
});

test('Max Diff Pixels 500', async ({ page }) => {
    await page.goto('http://localhost:5400/web/outlet/hominsanttdi/reservation');

    await expect(page).toHaveScreenshot({ maxDiffPixels: 200 });
});

test('Max Diff Pixels 1000', async ({ page }) => {
    await page.goto('http://localhost:5400/web/outlet/hominsanttdi/reservation');

    await expect(page).toHaveScreenshot({ maxDiffPixels: 200 });
});



