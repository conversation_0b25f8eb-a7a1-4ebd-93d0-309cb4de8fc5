https://embedsocial.com/blog/google-review-link/

https://developers.google.com/places/place-id

https://search.google.com/local/writereview?placeid=ChIJA0aeutht2jER5oaOkwo8sdM

=================================================

- use inserted 'place id' in Outlet (in future, merchant can fill in themselves)

https://search.google.com/local/writereview?placeid=ChIJWfQCCaZIzDERlhAHhzmHvqE

https://www.google.com/search?igu=1

`https://search.google.com/local/writereview?placeid=${(selectedOutlet && selectedOutlet.placeId) ? selectedOutlet.placeId`

========================================================

https://www.google.com/search?igu=1&q=desa+park+city&oq=desa+park+city+&gs_lcrp=EgZjaHJvbWUyBggAEEUYOTIHCAEQABiABDINCAIQLhivARjHARiABDINCAMQLhivARjHARiABDIGCAQQRRg9MgcIBRAAGIAEMg0IBhAuGK8BGMcBGIAEMg0IBxAuGK8BGMcBGIAEMgcICBAAGIAEMgcICRAAGIAE0gEINDM5NWowajeoAgCwAgA&client=ms-android-longcheer&sourceid=chrome-mobile&ie=UTF-8

https://www.google.com/search?igu=1&hl=en-MY&gl=my&q=Lot+GF-03,+Ground+Floor,+%E6%98%9F%E5%B7%B4%E5%85%8B+The+Waterfront+@,+5,+Persiaran+Residen,+Desa+Parkcity,+52200+Kuala+Lumpur,+Federal+Territory+of+Kuala+Lumpur&ludocid=11654901566829695126&lsig=AB86z5UoB9Gid3IvzxgIYtYlsaD0#lrd=0x31cc48a60902f459:0xa1be873987071096,3&gs_lcrp=EgZjaHJvbWUyBggAEEUYOTIHCAEQABiABDINCAIQLhivARjHARiABDINCAMQLhivARjHARiABDIGCAQQRRg9MgcIBRAAGIAEMg0IBhAuGK8BGMcBGIAEMg0IBxAuGK8BGMcBGIAEMgcICBAAGIAEMgcICRAAGIAE0gEINDM5NWowajeoAgCwAgA&client=ms-android-longcheer&sourceid=chrome-mobile&ie=UTF-8

========================================================

// starbucks desa park city

// desktop
https://www.google.com/search?igu=1&hl=en-MY&gl=my&q=Lot+GF-03,+Ground+Floor,+%E6%98%9F%E5%B7%B4%E5%85%8B+The+Waterfront+@,+5,+Persiaran+Residen,+Desa+Parkcity,+52200+Kuala+Lumpur,+Federal+Territory+of+Kuala+Lumpur&ludocid=11654901566829695126&lsig=AB86z5UoB9Gid3IvzxgIYtYlsaD0#lrd=0x31cc48a60902f459:0xa1be873987071096,3

// desktop simulate mobile, then click on review part
https://www.google.com/search?igu=1&hl=en-MY&gl=my&q=Lot+GF-03,+Ground+Floor,+%E6%98%9F%E5%B7%B4%E5%85%8B+The+Waterfront+@,+5,+Persiaran+Residen,+Desa+Parkcity,+52200+Kuala+Lumpur,+Federal+Territory+of+Kuala+Lumpur&ludocid=11654901566829695126&lsig=AB86z5UoB9Gid3IvzxgIYtYlsaD0&ptid=19034382&ptt=8&fpts=1701749097347&pli=1#pli=1&wptab=si:ALGXSlb9ylv6FQuI8zCROKWgJkMHV_7qC6_qVQjaGVH5mNU2SfoBIch9Av48LmNl-WdUFIQ7Ng0-AGOSC4jleWKpZg4MhcPBXTjRJIrUknzRR3ImEGdUjpN6XMLf3m0yfGIdUG7S9ZNauqCpMjcJN64cSVRoz_35pWdheUvzyuhAYdLErLhHH_w%3D

========================================================

// takdak nama johor 

https://www.google.com/search?hl=en-MY&gl=my&q=Cafe+Takdak+Nama,+Johor+Bahru,+29,+Jalan+Kebun+Teh+1,+Taman+Kebun+Teh,+80250+Johor+Bahru,+Johor&ludocid=15254039429003511526&lsig=AB86z5XtbFw7TotJVmIdpNcz32h9#lrd=0x31da6dd8ba9e4603:0xd3b13c0a938e86e6,3

https://www.google.com/search?hl=en-MY&gl=my&q=Cafe+Takdak+Nama,+Johor+Bahru,+29,+Jalan+Kebun+Teh+1,+Taman+Kebun+Teh,+80250+Johor+Bahru,+Johor&ludocid=15254039429003511526&lsig=AB86z5XtbFw7TotJVmIdpNcz32h9&ptid=19034382&ptt=8&fpts=1701749097347&pli=1#pli=1&wptab=si:ALGXSlb9ylv6FQuI8zCROKWgJkMHV_7qC6_qVQjaGVH5mNU2SfoBIch9Av48LmNl-WdUFIQ7Ng0-AGOSC4jleWKpZg4MhcPBXTjRJIrUknzRR3ImEGdUjpN6XMLf3m0yfGIdUG7S9ZNauqCpMjcJN64cSVRoz_35pWdheUvzyuhAYdLErLhHH_w%3D

----------------------------------

during review submission page:
<div class="s2xyy" role="radio" aria-label="5 星" aria-checked="true" tabindex="0" jsaction="click:uOnRTe; keydown:g6LwHf" data-rating="5"></div>

after done:
div VIpgJd-TUo6Hb-xJ5Hnf goog-reviews-write-widget-modal-bg desktop-device
div VIpgJd-TUo6Hb goog-reviews-write-widget-modal-content desktop-device expanded-height
iframe name="goog-reviews-write-widget" role="presentation" class="goog-reviews-write-widget"

-----------------------------------

Generate the JavaScript code that able to find the 'a' element, with following criteria:
- Its child has the attribute of 'data-vt'
- It had the <span> element with wording of Reviews, for its grandchild

<a aria-selected="false" data-index="2" tabindex="-1" role="tab">
<div data-vt="1" jsslot="">
    <span>Reviews</span>
</div>
<div>
</div>
</a>

Generate the JavaScript + React.JS code to solve the following problem:

Assuming there was a <a> link button called with 'Reviews' wording, and is white color for button background color, inside an iframe, and the iframe is inside the webpage. The iframe itself is from different domain, thus the button is unable to be accessed in DOM.

How to use the JavsScript to 'click' on the button, based on the button color and wording (without DOM access, and without postMessage access)?


https://www.google.com/search?igu=1&hl=en-MY&gl=my&q=Lot+GF-03,+Ground+Floor,+%E6%98%9F%E5%B7%B4%E5%85%8B+The+Waterfront+@,+5,+Persiaran+Residen,+Desa+Parkcity,+52200+Kuala+Lumpur,+Federal+Territory+of+Kuala+Lumpur&ludocid=11654901566829695126&lsig=AB86z5UoB9Gid3IvzxgIYtYlsaD0#lrd=0x31cc48a60902f459:0xa1be873987071096,3,5&wptab=si:ALGXSlb9ylv6FQuI8zCROKWgJkMHV_7qC6_qVQjaGVH5mNU2SfoBIch9Av48LmNl-WdUFIQGbKOt3rWy1U4sJ29y9a3gPS-9Zea1Ez0DfNn9qL60QcRC2267DgBTbrGe6FqwlGo3MauUTvIhHHSWBNbtdGW7dnkB-vMLGbMyo3MhcW8qRuf0Snk%3D

