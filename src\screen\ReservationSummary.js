import React, { useState, Component, useEffect } from 'react';
import { StyleSheet, ScrollView, Image, Text, View, TextInput, TouchableOpacity, Alert, Modal, KeyboardAvoidingView, Dimensions, ActivityIndicator, useWindowDimensions, TouchableHighlight } from 'react-native';
// import firebase from 'firebase';
import { getFirestore, collection, query, where, getDocs, limit } from "firebase/firestore";
import { signInAnonymously, signInWithCustomToken } from "firebase/auth";
import AsyncStorage from '@react-native-async-storage/async-storage';
//import EvilIcons from 'react-native-vector-icons/EvilIcons';
import Colors from '../constant/Colors';
import Styles from '../constant/Styles';
import API from '../constant/API';
import { CommonStore } from '../store/commonStore';
import { UserStore } from '../store/userStore';
import ApiClient from '../util/ApiClient';
// import AwesomeAlert from 'react-native-awesome-alerts';
import { useLinkTo, useRoute } from "@react-navigation/native";

import Ionicons from "react-native-vector-icons/Ionicons";
import AntDesign from 'react-native-vector-icons/AntDesign';

// import DatePicker2 from "react-horizontal-datepicker";
//import DatePicker from "react-native-datepicker";
// import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
// import "../constant/datePicker.css";
import moment, { now } from "moment";

import { ReactComponent as Alrt } from '../svg/alert.svg';
import { ReactComponent as Clock } from '../svg/clock.svg';
import { ReactComponent as Prsn } from '../svg/person.svg';
import { ReactComponent as Tbl } from '../svg/table.svg';
import { ReactComponent as Cldr } from '../svg/calendar.svg';
import { ReactComponent as Cnfrm } from '../svg/confirm.svg';
import { ReactComponent as Location } from '../svg/locationpin.svg';
import { ReactComponent as Cancel } from '../svg/cancel.svg';

import imgLogo from '../asset/image/logo.png';
import { isMobile } from '../util/commonFuncs';
import { DataStore } from '../store/dataStore';
// import { color, set } from 'react-native-reanimated';
import { Picker } from 'react-native-web';

//import Icon from "react-native-vector-icons/MaterialIcons";

//import Dropdown from 'react-dropdown';
// import Select from 'react-select';

// import DayPickerInput from 'react-day-picker/DayPickerInput';
// import { DateUtils } from 'react-day-picker';
// import 'react-day-picker/lib/style.css';
// import dateFnsFormat from 'date-fns/format';
// import dateFnsParse from 'date-fns/parse';

import { googleCloudApiKey, prefix } from "../constant/env";

import Hashids from 'hashids';
import { ORDER_REGISTER_QR_SALT, USER_RESERVATION_STATUS } from '../constant/common';

import { Collections } from '../constant/firebase';
import { setUserData } from '../util/User';
import { TempStore } from '../store/tempStore';

// import { sendCancelReservationEmail } from '../util/commonFuncs';

const hashids = new Hashids(ORDER_REGISTER_QR_SALT);

const ReservationSummary = props => {
    const {
        route,
    } = props;

    console.log('render scan!');

    console.log('route');
    console.log(route);

    // this.goToLoginState = this.goToLoginState.bind(this);

    const linkTo = useLinkTo();
    // const windowDimensions = useWindowDimensions();
    const {
        width: windowWidth,
        height: windowHeight,
    } = useWindowDimensions();

    const selectedReservationId = CommonStore.useState(s => s.selectedReservationId);
    const selectedReservationStartTime = CommonStore.useState(s => s.selectedReservationStartTime);
    const selectedReservationPax = CommonStore.useState(s => s.selectedReservationPax);
    const selectedOutlet = CommonStore.useState(s => s.selectedOutlet);

    const editUserReservation = CommonStore.useState(s => s.editUserReservation);

    const [reservationStatus, setReservationStatus] = useState(true);
    const [alertDisplay, setAlertDisplay] = useState(false);

    const [outletId, setOutletId] = useState('');
    const [outletAddress, setOutletAddress] = useState('');
    const [merchantId, setMerchantId] = useState('');
    const [merchantName, setMerchantName] = useState('');
    const [merchantCover, setMerchantCover] = useState('');

    // const [userReservation, setUserReservation] = useState('');
    const [reservationId, setReservationId] = useState(route.params.reservationId);

    const [resName, setResName] = useState('');
    const [pax, setPax] = useState('');
    const [tableCode, setTableCode] = useState('');
    //const [resTime, setResTime] = useState('');
    const [resDate, setResDate] = useState('');
    const [resStatus, setResStatus] = useState('');
    const [userD, setUserD] = useState({});

    const [cancelledBooking, setCancelledBooking] = useState(false);

    useEffect(() => {
        if (linkTo) {
            DataStore.update(s => {
                s.linkToFunc = linkTo;
            });

            global.linkToFunc = linkTo;
        }

        console.log('route');
        console.log(route);
        console.log('window.location.href');
        console.log(window.location.href);

        if (route.params === undefined ||
            route.params.subdomain === undefined) {
            linkTo(`${prefix}/register`);
        }

        if (
            // route.params === undefined ||
            // route.params.outletId === undefined ||
            route.params.reservationId === undefined
            // route.params.tableCode === undefined ||
            // route.params.tablePax === undefined ||
            // route.params.waiterId === undefined ||
            // route.params.outletId.length !== 36 ||
            // route.params.tableId.length !== 36 ||
            // route.params.qrDateTimeEncrypted === undefined
        ) {
            // not valid, need reservationId then only can proceed

            console.log('redirect scan 1!');

            global.errorMsg = 'Invalid link.';

            linkTo && linkTo(`${prefix}/scan`);
        }
        else {
            // valid, got reservationId, need verify reservationId later

            try {
                // firebase.auth().signInAnonymously()
                signInAnonymously(global.auth)
                    .then((result) => {
                        // TempStore.update(s => {
                        //     s.firebaseAuth = true;
                        // });

                        const firebaseUid = result.user.uid;

                        ApiClient.GET(API.getTokenKWeb + firebaseUid).then(async (result) => {
                            console.log('getTokenKWeb');
                            console.log(result);

                            if (result && result.token) {
                                await AsyncStorage.setItem('accessToken', result.token);
                                await AsyncStorage.setItem('refreshToken', result.refreshToken);

                                global.accessToken = result.token;

                                UserStore.update(s => {
                                    s.firebaseUid = result.userId;
                                    s.userId = result.userId;
                                    s.role = result.role;
                                    s.refreshToken = result.refreshToken;
                                    s.token = result.token;
                                    s.name = '';
                                    s.email = '';
                                    s.number = '';
                                });

                                // CommonStore.update(s => {
                                //   s.selectedOutletTableQRUrl = window.location.href;
                                // });

                                var reservationId = hashids.decodeHex(route.params.reservationId).toString().insert(8, '-').insert(13, '-').insert(18, '-').insert(23, '-');

                                console.log('reservationId');
                                console.log(reservationId);

                                // const userReservationSnapshot = await firebase.firestore().collection(Collections.UserReservation)
                                //     .where('uniqueId', '==', reservationId)
                                //     .limit(1)
                                //     .get();

                                const userReservationSnapshot = await getDocs(
                                    query(
                                        collection(global.db, Collections.UserReservation),
                                        where('uniqueId', '==', reservationId),
                                        limit(1),
                                    )
                                );

                                var userReservation = null;

                                if (!userReservationSnapshot.empty) {
                                    userReservation = userReservationSnapshot.docs[0].data();
                                }

                                console.log('userReservation');
                                console.log(userReservation);

                                if (userReservation) {
                                    // if (userOrder.isCashbackClaimed) {
                                    //     linkTo && linkTo(`${prefix}/scan`);
                                    // }

                                    // const outletSnapshot = await firebase.firestore().collection(Collections.Outlet)
                                    //     .where('uniqueId', '==', userReservation.outletId)
                                    //     .limit(1)
                                    //     .get();

                                    const outletSnapshot = await getDocs(
                                        query(
                                            collection(global.db, Collections.Outlet),
                                            where('uniqueId', '==', userReservation.outletId),
                                            limit(1),
                                        )
                                    );

                                    var outlet = {};
                                    if (!outletSnapshot.empty) {
                                        outlet = outletSnapshot.docs[0].data();
                                    }

                                    console.log('User Reservation', userReservation);
                                    //console.log('outlet', outlet)

                                    if (outlet) {
                                        await AsyncStorage.setItem(
                                            "latestOutletId",
                                            outlet.uniqueId
                                        );

                                        if (outlet.subdomain) {
                                            await AsyncStorage.setItem("latestSubdomain", outlet.subdomain);
                                        }

                                        // valid, can proceed to register user
                                        console.log('outlet', outlet)

                                        setOutletId(outlet.uniqueId);
                                        setOutletAddress(outlet.address)
                                        setMerchantId(outlet.merchantId);
                                        setMerchantName(outlet.name);
                                        setMerchantCover(outlet.cover);

                                        setResName(userReservation.userName);
                                        setTableCode(userReservation.tableCode);
                                        setPax(userReservation.pax);
                                        setResDate(userReservation.reservationTime);
                                        setResStatus(userReservation.status);
                                        //setReservationId(userReservation.uniqueId);
                                        setUserD(userReservation);

                                        CommonStore.update(s => {
                                            s.editUserReservation = userReservation;

                                            s.selectedOutlet = outlet;
                                        });

                                        console.log('User D', userD)

                                        // await AsyncStorage.setItem('latestOutletId', outlet.uniqueId);

                                        // await updateUserCart(
                                        //   {
                                        //     ...route.params,
                                        //     outletId: outlet.uniqueId,
                                        //     tableId: tableId,
                                        //     tablePax: outletTable.seated,
                                        //     tableCode: outletTable.code,
                                        //   }
                                        //   , outlet, firebaseUid);

                                        // linkTo && linkTo('/web/outlet');
                                    }
                                    else {
                                        console.log('redirect scan 2!');

                                        global.errorMsg = 'Invalid outlet.';

                                        linkTo && linkTo(`${prefix}/scan`);
                                    }
                                }
                                else {
                                    console.log('redirect scan 3!');

                                    global.errorMsg = 'Invalid reservation data.';

                                    linkTo && linkTo(`${prefix}/scan`);
                                }
                            }
                            else {
                                CommonStore.update(s => {
                                    s.alertObj = {
                                        title: 'Error',
                                        message: 'Unauthorized access',
                                    };

                                    // s.isAuthenticating = false;
                                });

                                console.log('redirect scan 4!');

                                global.errorMsg = 'Invalid token.';

                                linkTo && linkTo(`${prefix}/scan`);
                            }
                        });
                    });
            }
            catch (ex) {
                console.error(ex);
            }
        }
    }, [linkTo, route]);

    // useEffect(() => {

    // }, [resStatus])

    useEffect(() => {
        if (linkTo) {
            DataStore.update(s => {
                s.linkToFunc = linkTo;
            });

            global.linkToFunc = linkTo;
        }
    }, [linkTo]);

    useEffect(() => {
        CommonStore.update(s => {
            s.routeName = route.name;
        });
    }, [route]);

    useEffect(() => {
        CommonStore.update(s => {
            s.currPage = 'ReservationSummary';
        });

        const parent = props.navigation.dangerouslyGetParent();
        parent.setOptions({
            tabBarVisible: false,
        });
        return () =>
            parent.setOptions({
                tabBarVisible: true,
            });
    }, []);

    // useEffect(() => {
    //     if (resStatus === USER_RESERVATION_STATUS.CANCELED) {
    //         setCancelledBooking(true);
    //     }
    //     else {
    //         setCancelledBooking(false);
    //     }
    // }, [resStatus]);

    // useEffect(() => {
    //     firebase.auth().onAuthStateChanged((user) => {
    //         if (user) {
    //             // User is signed in, see docs for a list of available properties
    //             // https://firebase.google.com/docs/reference/js/firebase.User

    //             console.log('auth changed!');
    //             console.log(user);

    //             var uid = user.uid;
    //             // ...                
    //         } else {
    //             // User is signed out
    //             // ...
    //         }
    //     });
    // }, []);

    const cancelReservation = (param) => {

        console.log('PARAMA', param)
        var body = {
            reservationId: param.uniqueId,
            // seated: 1,

            userId: param.userId,
            pax: param.pax,
            reservationTime: param.reservationTime,
            outletName: param.reservationId,
            status: param.status
        };

        console.log('reservation status', param.status)

        ApiClient.POST(API.cancelReservation, body, false)
            .then((result) => {
                if (result.status) {
                    if (result.uniqueId) {
                        const cancelledUserReservation = result;
                    }

                    // getReservationList()
                    Alert.alert('Success', 'Cancelled successfully');
                    setCancelledBooking(true);

                    //sendCancelReservationEmail(body)
                }
            })
            .catch((err) => {
                console.log(err);
            });
        console.log('reservation status', userD.status)

        //setResStatus(userD.status);
    };

    const responseGoogle = e => {
        console.log(e);

        if (e && e.profileObj && e.profileObj.email) {
            const domainName = e.profileObj.email.slice(e.profileObj.email.indexOf('@') + 1);

            console.log(domainName);

            if (domainName === 'mykoodoo.com' || domainName === 'perksense.com') {
                console.log('valid');

                CommonStore.update(s => {
                    s.isAuthenticating = true;
                });

                UserStore.update(s => {
                    s.email = e.profileObj.email;
                    s.name = e.profileObj.name;
                    s.googleId = e.profileObj.googleId;
                    s.imageUrl = e.profileObj.imageUrl;

                    s.tokenId = e.tokenId;
                });

                try {
                    // firebase.auth().signInAnonymously()
                    signInAnonymously(global.auth)
                        .then((result) => {
                            // Signed in..

                            console.log('signed in!');
                            console.log(result);

                            var body = {
                                email: e.profileObj.email,
                                name: e.profileObj.name,
                                googleId: e.profileObj.googleId,
                                imageUrl: e.profileObj.imageUrl,
                                tokenId: e.profileObj.tokenId,

                                firebaseUid: result.user.uid,
                            };

                            ApiClient.POST(API.loginKoodooCRMByGoogleAccount, body).then(async result => {
                                // if (result === true)
                                // getOrderHistory()
                                //console.log('getOrderHistory');
                                //console.log(getOrderHistory);

                                console.log('loginKoodooCRMByGoogleAccount');
                                console.log(result);

                                if (result && result.customToken) {
                                    var firebaseToken = result.idToken;

                                    try {
                                        // result = await firebase.auth().signInWithCustomToken(result.customToken);
                                        result = await signInWithCustomToken(global.auth, result.customToken);
                                    }
                                    catch (error) {
                                        console.log('Failed to login with custom token');

                                        CommonStore.update(s => {
                                            s.alertObj = {
                                                title: 'Error',
                                                message: 'Unauthorized access',
                                            };

                                            s.isAuthenticating = false;
                                        });
                                    }

                                    // ApiClient.GET(API.getTokenKCRM + firebaseToken).then(async (result) => {
                                    ApiClient.GET(API.getTokenKCRM + e.profileObj.email).then(async (result) => {
                                        console.log('getTokenKCRM');
                                        console.log(result);

                                        if (result && result.token) {
                                            await AsyncStorage.setItem('accessToken', result.token);
                                            await AsyncStorage.setItem('refreshToken', result.refreshToken);

                                            global.accessToken = result.token;

                                            UserStore.update(s => {
                                                s.userId = result.userId;
                                                s.role = result.role;
                                                s.refreshToken = result.refreshToken;
                                                s.token = result.token;
                                            });

                                            CommonStore.update(s => {
                                                s.isAuthenticating = false;
                                            });

                                            linkTo('/genagmt/dashboard');
                                        }
                                        else {
                                            CommonStore.update(s => {
                                                s.alertObj = {
                                                    title: 'Error',
                                                    message: 'Unauthorized access',
                                                };

                                                s.isAuthenticating = false;
                                            });
                                        }
                                    });
                                }
                            }).catch(err => {
                                console.error(err);

                                // setShowAlertLogin(false);

                                CommonStore.update(s => {
                                    s.alertObj = {
                                        title: 'Error',
                                        message: 'Unauthorized access',
                                    };

                                    s.isAuthenticating = false;
                                });
                            });
                        })
                        .catch((error) => {
                            var errorCode = error.code;
                            var errorMessage = error.message;
                            // ...

                            CommonStore.update(s => {
                                s.alertObj = {
                                    title: 'Error',
                                    message: 'Unauthorized access',
                                };

                                s.isAuthenticating = false;
                            });
                        });
                }
                catch (ex) {
                    console.error(ex);

                    CommonStore.update(s => {
                        s.isAuthenticating = false;
                    });
                }
            }
            else {
                console.log('invalid');

                // setShowAlertLogin(true);

                CommonStore.update(s => {
                    s.alertObj = {
                        title: 'Error',
                        message: 'Unauthorized access',
                    };
                });
            }
        }
        else {
            console.log('invalid');
        }
    };

    const country = [
        { label: '+11', value: 'abc' },
    ];

    const [value, setValue] = useState('')

    const handleChange = (event) => {
        setValue(event.target.value);
    };

    const countrySelect = {
        control: (base, state) => ({
            ...base,
            background: "#ECECEC",
            borderRadius: 5,
            //borderWidth: 1,
            //borderColor: '#000',
            width: 75,
            height: 40,
            //marginTop: 30,
            borderTopRightRadius: 0,
            borderBottomRightRadius: 0,
            borderColor: 'transparent',
        }),
        menu: base => ({
            ...base,
            // match border radius
            borderRadius: 5,
            // remove the gap on top of list
            marginTop: 0,
            background: "#ECECEC",
            zIndex: 1,
        }),
        menuList: base => ({
            ...base,
            // removes white space on first and last option
            padding: 0
        }),
        indicatorSeparator: base => ({
            ...base,
            display: 'none'
        }),
        dropdownIndicator: base => ({
            ...base,
            color: "black"
        }),
    };

    const noGuestSelect = {
        control: (base, state) => ({
            ...base,
            background: "#ECECEC",
            borderRadius: 5,
            //borderWidth: 1,
            //borderColor: '#000',
            width: windowWidth * 0.3,
            height: 40,
        }),
        menu: base => ({
            ...base,
            // match border radius
            borderRadius: 5,
            // remove the gap on top of list
            marginTop: 0,
            background: "#ECECEC",
            zIndex: 1,
        }),
        menuList: base => ({
            ...base,
            // removes white space on first and last option
            padding: 0
        }),
        indicatorSeparator: base => ({
            ...base,
            display: 'none'
        }),
        dropdownIndicator: base => ({
            ...base,
            color: "black"
        }),
    };


    // function end    

    return (
        <ScrollView showsHorizontalScrollIndicator={false}>
            <Modal
                visible={alertDisplay}
                transparent={true}
                supportedOrientations={['portrait', 'landscape']}
                animationType="fade"
                onRequestClose={() => { setAlertDisplay(false) }}
            >
                <View style={{
                    backgroundColor: 'rgba(0,0,0,0.5)',
                    height: windowHeight,
                    justifyContent: 'center',

                    // backgroundColor: 'blue',

                    alignItems: 'center',
                }}>
                    <View style={{
                        alignSelf: 'center',
                        width: isMobile() ? windowWidth * 0.9 : 800,
                        // height: isMobile() ? windowHeight * 0.6 : 350,
                        backgroundColor: 'white',

                        borderRadius: 10,
                        borderColor: '#E5E5E5',

                        shadowOffset: {
                            width: 0,
                            height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 1,
                        zIndex: -1,

                        // marginTop: 40,
                    }}>
                        <TouchableOpacity
                            onPress={() => { setAlertDisplay(false) }}
                        >
                            <View style={{
                                marginTop: 10,
                                marginRight: 10,
                                alignSelf: 'flex-end',
                                height: 20,
                            }}>
                                <AntDesign name="closecircle" size={25} color={Colors.fieldtTxtColor} />
                            </View>
                        </TouchableOpacity>

                        <View
                            style={{
                                //marginLeft: '6%'
                                alignSelf: 'center',

                                // backgroundColor: 'red',
                            }}>
                            <Alrt
                                width={100}
                                height={100}
                                style={{
                                    alignSelf: 'center',
                                    color: Colors.fieldtTxtColor
                                }}
                            />
                            <View style={{
                                justifyContent: 'center',
                                alignItems: 'center',
                                flexDirection: 'row',
                                marginTop: 30,
                                marginLeft: '-1%'
                                //width: isMobile() ? windowWidth : windowWidth * 1,
                            }}>
                                <Text style={{
                                    fontFamily: 'NunitoSans-Regular',
                                    color: 'black',
                                    fontSize: 16,
                                    //textAlign: 'center',
                                    marginTop: 0,
                                    //marginLeft: 5,
                                    // width: '100%',
                                }}>
                                    Are you sure you want to cancel?
                                </Text>
                            </View>
                            <View style={{
                                justifyContent: 'center',
                                alignItems: 'center',
                                flexDirection: 'row',
                                //width: isMobile() ? windowWidth : windowWidth * 1,
                                marginTop: 30,
                            }}>
                                <Text style={{
                                    fontFamily: 'NunitoSans-Regular',
                                    color: 'black',
                                    fontSize: 16,
                                    textAlign: 'center',
                                    marginTop: 0,
                                    //marginLeft: 5,
                                    width: isMobile() ? '60%' : windowWidth,
                                }}>
                                    We can't guarantee that this table will be available later if you change your mind.
                                </Text>
                            </View>
                            <View style={[
                                {
                                    justifyContent: 'space',
                                    alignContent: 'center',
                                    // marginTop: 20,
                                },
                                !isMobile() ? {
                                    flexDirection: 'row',
                                } : {}
                            ]}>
                                <View style={{
                                    flexDirection: 'row',
                                    justifyContent: 'center',
                                    alignContent: 'center',
                                    flex: 0.5,
                                }}>
                                    <TouchableOpacity
                                        style={[styles.btnRed, {
                                            marginTop: 20,
                                            width: windowWidth * 0.7,
                                            alignSelf: 'center',
                                            marginBottom: isMobile() ? 10 : 50,
                                        }]}
                                        onPress={() => {
                                            cancelReservation(userD);
                                            setReservationStatus(false);
                                            setAlertDisplay(false)
                                        }}
                                    >
                                        <View>
                                            <View style={{
                                                flexDirection: 'row',
                                                justifyContent: 'center',
                                                alignContent: 'center',
                                            }}>
                                                <Text style={{
                                                    fontSize: 18,
                                                    fontFamily: 'NunitoSans-Regular',
                                                    textAlign: 'center',
                                                    color: Colors.primaryColor,
                                                }}>
                                                    Yes, Cancel My Booking
                                                </Text>
                                            </View>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                                <View style={{
                                    flexDirection: 'row',
                                    justifyContent: 'center',
                                    alignContent: 'center',
                                    flex: 0.5,
                                }}>
                                    <TouchableOpacity
                                        style={[styles.btnGreen, {
                                            marginTop: 20,
                                            width: windowWidth * 0.7,
                                            alignSelf: 'center',
                                            marginBottom: 50,
                                        }]}
                                        onPress={() => {
                                            setAlertDisplay(false)
                                        }}
                                    >
                                        <View>
                                            <View style={{
                                                flexDirection: 'row',
                                                justifyContent: 'center',
                                                alignContent: 'center',
                                            }}>
                                                <Text style={{
                                                    fontSize: 18,
                                                    fontFamily: 'NunitoSans-Regular',
                                                    textAlign: 'center',
                                                    color: Colors.whiteColor,
                                                }}>
                                                    No, Go Back!
                                                </Text>
                                            </View>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </View>
                    </View>
                </View>
            </Modal>
            <View style={{
                width: isMobile() ? windowWidth : windowWidth,
                height: windowHeight,
                //backgroundColor: 'blue'
            }}>
                {!cancelledBooking ?
                    <>
                        <Image
                            source={{
                                uri: merchantCover,
                            }}
                            hideLoading={true}
                            //item={selectedOutlet}
                            style={{
                                width: isMobile() ? windowWidth : windowWidth,
                                height: isMobile() ? windowWidth * 0.7 : 300,
                                //backgroundColor: Colors.darkBgColor,
                                alignSelf: 'center',
                                zIndex: -10,
                                //opacity: 0,
                                position: 'absolute'
                            }}
                        />
                        <View style={{
                            width: isMobile() ? windowWidth : windowWidth,
                            //height: windowHeight,
                            backgroundColor: 'rgba(0,0,0,0.5)',
                            flexDirection: 'row',
                            //alignContent: 'center',
                        }}>

                            <View style={{
                                width: isMobile() ? windowWidth : windowWidth,
                                height: isMobile() ? windowWidth * 0.7 : 300,
                                //backgroundColor: Colors.darkBgColor,
                                alignSelf: 'center',
                                zIndex: 10,
                            }}>
                                <View style={{
                                    marginHorizontal: 20,
                                    marginTop: 10,
                                }}>
                                    <Image style={{
                                        width: 100,
                                        height: 47,
                                        alignSelf: 'center',
                                        //marginBottom: 25.
                                    }} resizeMode="contain" source={imgLogo} />
                                    <View>
                                        <Ionicons
                                            name="checkmark-circle-outline" size={80} color={Colors.whiteColor}
                                            style={[{
                                                alignSelf: 'center',
                                                color: 'white',
                                                marginVertical: 10
                                                //backgroundColor: 'blue',

                                                // width: windowWidth * 0.1,
                                                // height: windowHeight * 0.1,
                                                //margin: -(windowHeight * 0.05)
                                            },
                                            isMobile() ?
                                                {
                                                    // width: windowWidth * 0.2,
                                                    // height: windowHeight * 0.2,
                                                    //margin: -(windowHeight * 0.05)
                                                } : {},
                                            ]}
                                        />
                                        {/* <Cnfrm
                                            // width={windowWidth * 0.1}
                                            // height={windowHeight * 0.1}
                                            style={[{
                                                alignSelf: 'center',
                                                color: 'white',
                                                //backgroundColor: 'blue',

                                                width: windowWidth * 0.1,
                                                height: windowHeight * 0.1,
                                                //margin: -(windowHeight * 0.05)
                                            },
                                            isMobile() ?
                                                {
                                                    width: windowWidth * 0.2,
                                                    height: windowHeight * 0.2,
                                                    margin: -(windowHeight * 0.05)
                                                } : {},
                                            ]}
                                        /> */}
                                    </View>
                                    <Text style={{
                                        fontFamily: 'NunitoSans-Regular',
                                        color: Colors.whiteColor,
                                        fontSize: 18,
                                        textAlign: 'center',
                                        marginTop: 0,
                                        // width: '100%',
                                    }}>
                                        You have a booking at <b>{merchantName}</b>
                                    </Text>
                                    <Text style={{
                                        fontFamily: 'NunitoSans-Regular',
                                        color: Colors.whiteColor,
                                        fontSize: 14,
                                        textAlign: 'center',
                                        marginTop: 0,
                                        // width: '100%',
                                    }}>
                                        {resName}, we're looking forward to seeing you
                                    </Text>
                                </View>
                            </View>
                        </View>
                    </>
                    :
                    <>
                        <Image
                            source={{
                                uri: merchantCover,
                            }}
                            hideLoading={true}
                            //item={selectedOutlet}
                            style={{
                                width: isMobile() ? windowWidth : windowWidth,
                                height: isMobile() ? windowHeight * 0.29 : 300,
                                //backgroundColor: Colors.darkBgColor,
                                alignSelf: 'center',
                                zIndex: -10,
                                //opacity: 0,
                                position: 'absolute'
                            }}
                        />
                        <View style={{
                            width: isMobile() ? windowWidth : windowWidth,
                            //height: windowHeight,
                            backgroundColor: 'rgba(0,0,0,0.5)',
                            flexDirection: 'row',
                        }}>
                            <View style={{
                                width: isMobile() ? windowWidth : windowWidth,
                                height: isMobile() ? windowHeight * 0.29 : 300,
                                //backgroundColor: Colors.darkBgColor,
                                alignSelf: 'center',
                                zIndex: 10,
                            }}>
                                <View style={{
                                    marginHorizontal: 20,
                                    marginTop: 10
                                }}>
                                    <Image style={{
                                        width: 100,
                                        height: 47,
                                        alignSelf: 'center',
                                        //marginBottom: 25.
                                    }} resizeMode="contain" source={imgLogo} />
                                    <Ionicons
                                        name="close-circle-outline" size={80} color={Colors.whiteColor}
                                        style={[{
                                            alignSelf: 'center',
                                            color: 'white',
                                            marginVertical: 10,
                                            //backgroundColor: 'blue',

                                            // width: windowWidth * 0.1,
                                            // height: windowHeight * 0.1,
                                            //margin: -(windowHeight * 0.05)
                                        },
                                        isMobile() ?
                                            {
                                                // width: windowWidth * 0.2,
                                                // height: windowHeight * 0.2,
                                                // margin: -(windowHeight * 0.05)
                                            } : {},
                                        ]}
                                    />
                                    {/* <Cancel
                                        // width={windowWidth * 0.1}
                                        // height={windowHeight * 0.1}
                                        style={[{
                                            alignSelf: 'center',
                                            color: 'white',
                                            //backgroundColor: 'blue',

                                            width: windowWidth * 0.1,
                                            height: windowHeight * 0.1,
                                            //margin: -(windowHeight * 0.05)
                                        },
                                        isMobile() ?
                                            {
                                                width: windowWidth * 0.2,
                                                height: windowHeight * 0.2,
                                                margin: -(windowHeight * 0.05)
                                            } : {},
                                        ]}
                                    /> */}
                                    <Text style={{
                                        fontFamily: 'NunitoSans-Regular',
                                        color: Colors.whiteColor,
                                        fontSize: 18,
                                        textAlign: 'center',
                                        marginTop: 0,
                                        // width: '100%',
                                    }}>
                                        Your Booking at <b>{merchantName}</b> has been cancelled
                                    </Text>
                                    <Text style={{
                                        fontFamily: 'NunitoSans-Regular',
                                        color: Colors.whiteColor,
                                        fontSize: 14,
                                        textAlign: 'center',
                                        marginTop: 0,
                                        // width: '100%',
                                    }}>
                                        We're sorry you couldn't make it this time!
                                    </Text>
                                    <View style={{
                                        flexDirection: 'row',
                                        justifyContent: 'space',
                                        alignContent: 'center',
                                    }}>
                                        <View style={{
                                            flexDirection: 'row',
                                            justifyContent: 'center',
                                            alignContent: 'center',
                                            flex: 0.5,
                                        }}>
                                            {/* <TouchableOpacity
                                        style={[styles.btnRed, {
                                            marginTop: 20,
                                            width: 270,
                                            alignSelf: 'center',
                                            marginBottom: 50,
                                        }]}
                                        onPress={() => {
                                            setAlertDisplay(true)
                                        }}
                                    >
                                        <View>
                                            <View style={{
                                                flexDirection: 'row',
                                                justifyContent: 'center',
                                                alignContent: 'center',
                                            }}>
                                                <Text style={{
                                                    fontSize: 18,
                                                    fontFamily: 'NunitoSans-Regular',
                                                    textAlign: 'center',
                                                    color: Colors.whiteColor,
                                                }}>
                                                    Cancel Reservation
                                                </Text>
                                            </View>
                                        </View>
                                    </TouchableOpacity> */}
                                        </View>
                                        <View style={{
                                            flexDirection: 'row',
                                            justifyContent: 'center',
                                            alignContent: 'center',
                                            flex: 0.5,
                                        }}>
                                            {/* <TouchableOpacity
                                        style={[styles.btnGreen, {
                                            marginTop: 20,
                                            width: 270,
                                            alignSelf: 'center',
                                            marginBottom: 50,
                                        }]}
                                        onPress={() => {

                                        }}
                                    >
                                        <View>
                                            <View style={{
                                                flexDirection: 'row',
                                                justifyContent: 'center',
                                                alignContent: 'center',
                                            }}>
                                                <Text style={{
                                                    fontSize: 18,
                                                    fontFamily: 'NunitoSans-Regular',
                                                    textAlign: 'center',
                                                    color: Colors.whiteColor,
                                                }}>
                                                    Edit Reservation
                                                </Text>
                                            </View>
                                        </View>
                                    </TouchableOpacity> */}
                                        </View>
                                    </View>
                                </View>
                            </View>
                        </View>
                    </>
                }

                <View style={{
                    // justifyContent: 'center',
                    // alignItems: 'center',

                    width: isMobile() ? windowWidth : windowWidth,
                    height: windowHeight,
                }}>
                    <View style={{
                        //flexDirection: 'row',
                        justifyContent: 'space',
                        alignContent: 'center',
                    }}>
                        {cancelledBooking ?
                            <View style={{
                                flexDirection: 'row',
                                justifyContent: 'center',
                                alignContent: 'center',
                                flex: 0.5,
                            }}>
                                {/* <TouchableOpacity
                                    style={[styles.btnRed, {
                                        marginTop: 20,
                                        width: 200,
                                        alignSelf: 'center',
                                        //marginBottom: 50,
                                    }]}
                                    onPress={() => {
                                        setAlertDisplay(true)
                                        //renderCancelled()
                                    }}
                                >
                                    <View>
                                        <View style={{
                                            flexDirection: 'row',
                                            justifyContent: 'center',
                                            alignContent: 'center',
                                        }}>
                                            <Text style={{
                                                fontSize: 18,
                                                fontFamily: 'NunitoSans-Regular',
                                                textAlign: 'center',
                                                color: Colors.primaryColor,
                                            }}>
                                                Cancel Booking
                                            </Text>
                                        </View>
                                    </View>
                                </TouchableOpacity> */}
                            </View>
                            :
                            <View style={{
                                flexDirection: 'row',
                                justifyContent: 'center',
                                alignContent: 'center',
                                flex: 0.5,
                            }}>
                                <TouchableOpacity
                                    style={[styles.btnRed, {
                                        marginTop: 20,
                                        width: 200,
                                        alignSelf: 'center',
                                        //marginBottom: 50,
                                    }]}
                                    onPress={() => {
                                        setAlertDisplay(true)
                                        //renderCancelled()
                                    }}
                                >
                                    <View>
                                        <View style={{
                                            flexDirection: 'row',
                                            justifyContent: 'center',
                                            alignContent: 'center',
                                        }}>
                                            <Text style={{
                                                fontSize: 18,
                                                fontFamily: 'NunitoSans-Regular',
                                                textAlign: 'center',
                                                color: Colors.primaryColor,
                                            }}>
                                                Cancel Booking
                                            </Text>
                                        </View>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        }

                        <View style={{
                            flexDirection: 'row',
                            justifyContent: 'center',
                            alignContent: 'center',
                            flex: 0.5,
                        }}>
                            <TouchableOpacity
                                style={[styles.btnGreen, {
                                    marginTop: cancelledBooking ? 10 : 20,
                                    width: 200,
                                    alignSelf: 'center',
                                    marginBottom: 20,
                                }]}
                                onPress={async () => {
                                    if (editUserReservation && editUserReservation.uniqueId) {
                                        const subdomain = await AsyncStorage.getItem("latestSubdomain");

                                        CommonStore.update((s) => {
                                            s.selectedReservationId = editUserReservation.uniqueId;
                                            // s.selectedReservationPax = editUserReservation.pax;
                                            s.selectedAddressUserPhone = editUserReservation.userPhone;
                                            s.selectedUserEmail = editUserReservation.userEmail;
                                            s.selectedUserFirstName = editUserReservation.userFirstName;
                                            s.selectedUserLastName = editUserReservation.userLastName;
                                            s.selectedUserRemarks = editUserReservation.remarks;
                                            s.selectedUserDiet = editUserReservation.dRestrictions;
                                            s.selectedUserOccasion = editUserReservation.sOccasions;
                                            // s.selectedReservationId = reservationId;

                                            s.selectedReservationPax = typeof editUserReservation.pax === 'number' ? editUserReservation.pax.toFixed(0) : '1';
                                            s.selectedReservationStartTime = editUserReservation.reservationTime;

                                            s.selectedReservationOutletSectionId = editUserReservation.outletSectionId ? editUserReservation.outletSectionId : '';
                                            s.selectedReservationOutletSectionName = editUserReservation.outletSectionName ? editUserReservation.outletSectionName : '';
                                        });

                                        linkTo && linkTo(`${prefix}/outlet/${subdomain}/reservation-info/${reservationId}`);
                                    }
                                }}
                            >
                                <View>
                                    <View style={{
                                        flexDirection: 'row',
                                        justifyContent: 'center',
                                        alignContent: 'center',
                                    }}>
                                        <Text style={{
                                            fontSize: 18,
                                            fontFamily: 'NunitoSans-Regular',
                                            textAlign: 'center',
                                            color: Colors.whiteColor,
                                        }}>
                                            Edit Booking
                                        </Text>
                                    </View>
                                </View>
                            </TouchableOpacity>
                        </View>
                    </View>
                    <Text style={{
                        fontFamily: 'NunitoSans-Bold',
                        color: 'black',
                        fontSize: 20,
                        textAlign: 'center',
                        marginTop: 30,
                    }}>
                        Booking Details
                    </Text>
                    <View style={{
                        alignSelf: 'center',
                        marginTop: 20
                    }}>
                        <View style={{
                            flexDirection: 'row',
                            marginBottom: 10,
                            //justifyContent: 'space-evenly'
                        }}>
                            <Prsn
                                color='#7A8E92'
                                width={25}
                                height={25}
                                style={{
                                    //marginRight: 30,
                                    width: '50%'
                                }}
                            />
                            <Text style={{
                                fontFamily: 'NunitoSans-Regular',
                                color: 'black',
                                fontSize: 16,
                                //textAlign: 'center',
                                marginTop: 3,
                                width: '50%'
                            }}>
                                {pax ? `${pax} Person` : 'N/A'}
                            </Text>
                        </View>

                        <View style={{
                            flexDirection: 'row',
                            marginBottom: 10,
                            //justifyContent: 'space-evenly'
                        }}>
                            <Tbl
                                color='#7A8E92'
                                width={25}
                                height={25}
                                style={{
                                    //marginRight: 30,
                                    width: '50%'
                                }}
                            />
                            <Text style={{
                                fontFamily: 'NunitoSans-Regular',
                                color: 'black',
                                fontSize: 16,
                                //textAlign: 'center',
                                marginTop: 3,
                                width: '50%'
                            }}>
                                {tableCode ? tableCode : 'N/A'}
                            </Text>
                        </View>

                        <View style={{
                            flexDirection: 'row',
                            marginBottom: 10,
                            //justifyContent: 'space-evenly'
                            width: 200
                        }}>
                            <Clock
                                color='#7A8E92'
                                width={25}
                                height={25}
                                style={{
                                    //marginRight: 30,
                                    width: '50%'
                                }}
                            />
                            <Text style={{
                                fontFamily: 'NunitoSans-Regular',
                                color: 'black',
                                fontSize: 16,
                                //textAlign: 'center',
                                marginTop: 3,
                                width: '50%'
                            }}>
                                {resDate ? `${moment(resDate).format('h:mm A')}` : 'N/A'}
                            </Text>
                        </View>

                        <View style={{
                            flexDirection: 'row',
                            marginBottom: 10,
                            //justifyContent: 'space-between',
                            // width: 200
                        }}>
                            <Cldr
                                color='#7A8E92'
                                width={25}
                                height={25}
                                style={{
                                    //marginRight: 30,
                                    width: '50%'
                                }}
                            />
                            <Text style={{
                                fontFamily: 'NunitoSans-Regular',
                                color: 'black',
                                fontSize: 16,
                                //textAlign: 'center',
                                marginTop: 3,
                                width: '50%',
                            }}>
                                {resDate ? `${moment(resDate).format('DD/MM/YYYY')}` : 'N/A'}
                            </Text>
                        </View>

                        <Text style={{
                            fontFamily: 'NunitoSans-Bold',
                            color: 'black',
                            fontSize: 20,
                            textAlign: 'center',
                            marginTop: 30,
                        }}>
                            {merchantName}
                        </Text>
                    </View>
                    <View style={{
                        justifyContent: 'center',
                        alignItems: 'center',
                        flexDirection: 'row',
                        marginTop: 10,
                        //width: isMobile() ? windowWidth : windowWidth * 1,
                    }}>
                        <Location
                            width={20}
                            height={20}
                            color={Colors.primaryColor}
                        />
                        <Text style={{
                            fontFamily: 'NunitoSans-Regular',
                            color: 'black',
                            fontSize: 16,
                            textAlign: 'center',
                            marginTop: 3,
                            width: '80%'
                        }}>
                            {outletAddress}
                        </Text>
                    </View>
                </View>
            </View>
        </ScrollView>
    );
};

const styles = StyleSheet.create({
    btnWhite: {
        borderColor: Colors.primaryColor,
        borderWidth: 1,
        borderRadius: 5,
        height: 50,
        justifyContent: 'center',
        alignContent: 'center',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 1,
        zIndex: -1,
    },
    btnGreen: {
        backgroundColor: Colors.primaryColor,
        borderRadius: 5,
        height: 50,
        justifyContent: 'center',
        alignContent: 'center',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 1,
        zIndex: -1,
    },
    btnRed: {
        //backgroundColor: 'red',
        borderRadius: 5,
        borderWidth: 1,
        borderColor: Colors.primaryColor,

        height: 50,
        justifyContent: 'center',
        alignContent: 'center',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 1,
        zIndex: -1,
    },
    daysWrd: {
        fontSize: 16,
        fontFamily: 'NunitoSans-Regular',
        textAlign: 'center',
    },
    dateWrd: {
        fontSize: 18,
        fontFamily: 'NunitoSans-Regular',
        textAlign: 'center',
        color: Colors.primaryColor,
    },
})

export default ReservationSummary;