chrome://inspect/#devices

-----------------------------------------------------------------------

dev:
scp -i C:/Users/<USER>/Downloads/mykoodoo.pem -r D:/Users/<USER>/Documents/GitHub/koodooapps/koodoo-web/build/* ubuntu@54.151.164.197:/var/www/html/web
scp -i C:/Users/<USER>/Downloads/mykoodoo.pem -r D:/Users/<USER>/Documents/GitHub/koodooapps/koodoo-web/build/* ubuntu@54.251.36.121:/var/www/html/web

-----------------------------------------------------------------------

dev: (latest)
scp -i C:/Users/<USER>/Downloads/mykoodoo.pem -r D:/Users/<USER>/Documents/GitHub/koodooapps/koodoo-web/build/* ubuntu@54.151.164.197:/var/www/html/web-latest

-----------------------------------------------------------------------

uat:
scp -i C:/Users/<USER>/Downloads/mykoodoo.pem -r D:/Users/<USER>/Documents/GitHub/koodooapps/koodoo-web/build/* ubuntu@54.151.164.197:/var/www/html/web-order2
scp -i C:/Users/<USER>/Downloads/mykoodoo.pem -r D:/Users/<USER>/Documents/GitHub/koodooapps/koodoo-web/build/* ubuntu@54.251.36.121:/var/www/html/web-order2

-----------------------------------------------------------------------

prod:
scp -i C:/Users/<USER>/Downloads/mykoodoo.pem -r D:/Users/<USER>/Documents/GitHub/koodooapps/koodoo-web/build/* ubuntu@54.151.164.197:/var/www/html/web-order
scp -i C:/Users/<USER>/Downloads/mykoodoo.pem -r D:/Users/<USER>/Documents/GitHub/koodooapps/koodoo-web/build/* ubuntu@54.251.36.121:/var/www/html/web-order

scp -i C:/Users/<USER>/Downloads/gcp-kd-main.pem -r D:/Users/<USER>/Documents/GitHub/koodooapps/koodoo-web/build/* pc@34.126.123.57:/var/www/html/web-order

-----------------------------------------------------------------------

git reflog show
git reset --hard ''
git log -10 --oneline

-----------------------------------------------------------------------

- to solve issue of unable to start the web related projects

change browserslist:

"browserslist": [
  ">0.2%",
  "not dead",
  "not op_mini all"
],
(make sure you don't have special config for development in browserslist)

clear npm cache:

npm cache clean --force

reinstall things:

rm -rf node_modules && rm -f package-lock.json && npm i --legacy-peer-deps

-----------------------------------------------------------------------

https://github.com/mifi/stacktracify

npm install -g stacktracify

Usage
Copy a minified stacktrace to your clipboard - then run:

stacktracify /path/to/js.map
Can also read stacktrace from file. For more info:

stacktracify --help

-----------------------------------------------------------------------

cd debug/prod/static/js
cd debug/uat/static/js

ls debug/prod/static/js
ls debug/uat/static/js

stacktracify ./debug/prod/static/js/11.567c2eb5.chunk.js.map
stacktracify ./debug/uat/static/js/11.567c2eb5.chunk.js.map

-----------------------------------------------------------------------

