{"hash": "f9f052655d07ddcb0e25", "version": "5.75.0", "time": 217, "builtAt": 1678243951899, "publicPath": "auto", "outputPath": "D:\\Users\\pc\\Documents\\GitHub\\koodooapps\\koodoo-web\\dist", "assetsByChunkName": {"main": ["main.js"]}, "assets": [{"type": "asset", "name": "main.js", "size": 1246, "emitted": false, "comparedForEmit": false, "cached": true, "info": {"javascriptModule": false, "minimized": true}, "chunkNames": ["main"], "chunkIdHints": [], "auxiliaryChunkNames": [], "auxiliaryChunkIdHints": [], "related": {}, "chunks": [179], "auxiliaryChunks": [], "isOverSizeLimit": false}], "chunks": [{"rendered": true, "initial": true, "entry": true, "recorded": false, "size": 65914, "sizes": {"javascript": 65196, "runtime": 718}, "names": ["main"], "idHints": [], "runtime": ["main"], "files": ["main.js"], "auxiliaryFiles": [], "hash": "d163c47a418b580c8094", "childrenByOrder": {}, "id": 179, "siblings": [], "parents": [], "children": [], "modules": [{"type": "module", "moduleType": "javascript/auto", "layer": null, "size": 64872, "sizes": {"javascript": 64872}, "built": true, "codeGenerated": true, "buildTimeExecuted": false, "cached": false, "identifier": "D:\\Users\\pc\\Documents\\GitHub\\koodooapps\\koodoo-web\\src\\App.js", "name": "./src/App.js", "nameForCondition": "D:\\Users\\pc\\Documents\\GitHub\\koodooapps\\koodoo-web\\src\\App.js", "index": 1, "preOrderIndex": 1, "index2": 0, "postOrderIndex": 0, "cacheable": true, "optional": false, "orphan": false, "dependent": true, "issuer": "D:\\Users\\pc\\Documents\\GitHub\\koodooapps\\koodoo-web\\src\\index.js", "issuerName": "./src/index.js", "issuerPath": [{"identifier": "D:\\Users\\pc\\Documents\\GitHub\\koodooapps\\koodoo-web\\src\\index.js", "name": "./src/index.js", "profile": {"total": 34, "resolving": 19, "restoring": 0, "building": 13, "integration": 1, "storing": 1, "additionalResolving": 0, "additionalIntegration": 0, "factory": 19, "dependencies": 0}, "id": 138}], "failed": true, "errors": 1, "warnings": 0, "profile": {"total": 39, "resolving": 2, "restoring": 0, "building": 37, "integration": 0, "storing": 0, "additionalResolving": 0, "additionalIntegration": 0, "factory": 2, "dependencies": 0}, "id": 436, "issuerId": 138, "chunks": [179], "assets": [], "reasons": [{"moduleIdentifier": "D:\\Users\\pc\\Documents\\GitHub\\koodooapps\\koodoo-web\\src\\index.js", "module": "./src/index.js", "moduleName": "./src/index.js", "resolvedModuleIdentifier": "D:\\Users\\pc\\Documents\\GitHub\\koodooapps\\koodoo-web\\src\\index.js", "resolvedModule": "./src/index.js", "type": "harmony side effect evaluation", "active": true, "explanation": "", "userRequest": "./App", "loc": "2:0-24", "moduleId": 138, "resolvedModuleId": 138}, {"moduleIdentifier": "D:\\Users\\pc\\Documents\\GitHub\\koodooapps\\koodoo-web\\src\\index.js", "module": "./src/index.js", "moduleName": "./src/index.js", "resolvedModuleIdentifier": "D:\\Users\\pc\\Documents\\GitHub\\koodooapps\\koodoo-web\\src\\index.js", "resolvedModule": "./src/index.js", "type": "harmony import specifier", "active": true, "explanation": "", "userRequest": "./App", "loc": "10:43-46", "moduleId": 138, "resolvedModuleId": 138}], "usedExports": null, "providedExports": null, "optimizationBailout": ["ModuleConcatenation bailout: Module is not an ECMAScript module"], "depth": 1}, {"type": "module", "moduleType": "javascript/auto", "layer": null, "size": 324, "sizes": {"javascript": 324}, "built": true, "codeGenerated": true, "buildTimeExecuted": false, "cached": false, "identifier": "D:\\Users\\pc\\Documents\\GitHub\\koodooapps\\koodoo-web\\src\\index.js", "name": "./src/index.js", "nameForCondition": "D:\\Users\\pc\\Documents\\GitHub\\koodooapps\\koodoo-web\\src\\index.js", "index": 0, "preOrderIndex": 0, "index2": 1, "postOrderIndex": 1, "cacheable": true, "optional": false, "orphan": false, "dependent": false, "issuer": null, "issuerName": null, "issuerPath": null, "failed": false, "errors": 0, "warnings": 0, "profile": {"total": 34, "resolving": 19, "restoring": 0, "building": 13, "integration": 1, "storing": 1, "additionalResolving": 0, "additionalIntegration": 0, "factory": 19, "dependencies": 0}, "id": 138, "issuerId": null, "chunks": [179], "assets": [], "reasons": [{"moduleIdentifier": null, "module": null, "moduleName": null, "resolvedModuleIdentifier": null, "resolvedModule": null, "type": "entry", "active": true, "explanation": "", "userRequest": "./src", "loc": "main", "moduleId": null, "resolvedModuleId": null}], "usedExports": [], "providedExports": [], "optimizationBailout": ["Statement (ExpressionStatement) with side effects in source code at 10:0-48", "ModuleConcatenation bailout: Cannot concat with ./src/App.js: Module is not an ECMAScript module"], "depth": 0}, {"type": "module", "moduleType": "runtime", "layer": null, "size": 302, "sizes": {"runtime": 302}, "built": false, "codeGenerated": true, "buildTimeExecuted": false, "cached": false, "identifier": "webpack/runtime/compat get default export", "name": "webpack/runtime/compat get default export", "nameForCondition": null, "index": null, "preOrderIndex": null, "index2": null, "postOrderIndex": null, "optional": false, "orphan": false, "dependent": false, "failed": false, "errors": 0, "warnings": 0, "id": "", "chunks": [179], "assets": [], "reasons": [], "usedExports": null, "providedExports": [], "optimizationBailout": [], "depth": null}, {"type": "module", "moduleType": "runtime", "layer": null, "size": 313, "sizes": {"runtime": 313}, "built": false, "codeGenerated": true, "buildTimeExecuted": false, "cached": false, "identifier": "webpack/runtime/define property getters", "name": "webpack/runtime/define property getters", "nameForCondition": null, "index": null, "preOrderIndex": null, "index2": null, "postOrderIndex": null, "optional": false, "orphan": false, "dependent": false, "failed": false, "errors": 0, "warnings": 0, "id": "", "chunks": [179], "assets": [], "reasons": [], "usedExports": null, "providedExports": [], "optimizationBailout": [], "depth": null}, {"type": "module", "moduleType": "runtime", "layer": null, "size": 103, "sizes": {"runtime": 103}, "built": false, "codeGenerated": true, "buildTimeExecuted": false, "cached": false, "identifier": "webpack/runtime/hasOwnProperty shorthand", "name": "webpack/runtime/hasOwnProperty shorthand", "nameForCondition": null, "index": null, "preOrderIndex": null, "index2": null, "postOrderIndex": null, "optional": false, "orphan": false, "dependent": false, "failed": false, "errors": 0, "warnings": 0, "id": "", "chunks": [179], "assets": [], "reasons": [], "usedExports": null, "providedExports": [], "optimizationBailout": [], "depth": null}], "origins": [{"module": "", "moduleIdentifier": "", "moduleName": "", "loc": "main", "request": "./src"}]}], "modules": [{"type": "module", "moduleType": "javascript/auto", "layer": null, "size": 324, "sizes": {"javascript": 324}, "built": true, "codeGenerated": true, "buildTimeExecuted": false, "cached": false, "identifier": "D:\\Users\\pc\\Documents\\GitHub\\koodooapps\\koodoo-web\\src\\index.js", "name": "./src/index.js", "nameForCondition": "D:\\Users\\pc\\Documents\\GitHub\\koodooapps\\koodoo-web\\src\\index.js", "index": 0, "preOrderIndex": 0, "index2": 1, "postOrderIndex": 1, "cacheable": true, "optional": false, "orphan": false, "issuer": null, "issuerName": null, "issuerPath": null, "failed": false, "errors": 0, "warnings": 0, "profile": {"total": 34, "resolving": 19, "restoring": 0, "building": 13, "integration": 1, "storing": 1, "additionalResolving": 0, "additionalIntegration": 0, "factory": 19, "dependencies": 0}, "id": 138, "issuerId": null, "chunks": [179], "assets": [], "reasons": [{"moduleIdentifier": null, "module": null, "moduleName": null, "resolvedModuleIdentifier": null, "resolvedModule": null, "type": "entry", "active": true, "explanation": "", "userRequest": "./src", "loc": "main", "moduleId": null, "resolvedModuleId": null}], "usedExports": [], "providedExports": [], "optimizationBailout": ["Statement (ExpressionStatement) with side effects in source code at 10:0-48", "ModuleConcatenation bailout: Cannot concat with ./src/App.js: Module is not an ECMAScript module"], "depth": 0}, {"type": "module", "moduleType": "javascript/auto", "layer": null, "size": 64872, "sizes": {"javascript": 64872}, "built": true, "codeGenerated": true, "buildTimeExecuted": false, "cached": false, "identifier": "D:\\Users\\pc\\Documents\\GitHub\\koodooapps\\koodoo-web\\src\\App.js", "name": "./src/App.js", "nameForCondition": "D:\\Users\\pc\\Documents\\GitHub\\koodooapps\\koodoo-web\\src\\App.js", "index": 1, "preOrderIndex": 1, "index2": 0, "postOrderIndex": 0, "cacheable": true, "optional": false, "orphan": false, "issuer": "D:\\Users\\pc\\Documents\\GitHub\\koodooapps\\koodoo-web\\src\\index.js", "issuerName": "./src/index.js", "issuerPath": [{"identifier": "D:\\Users\\pc\\Documents\\GitHub\\koodooapps\\koodoo-web\\src\\index.js", "name": "./src/index.js", "profile": {"total": 34, "resolving": 19, "restoring": 0, "building": 13, "integration": 1, "storing": 1, "additionalResolving": 0, "additionalIntegration": 0, "factory": 19, "dependencies": 0}, "id": 138}], "failed": true, "errors": 1, "warnings": 0, "profile": {"total": 39, "resolving": 2, "restoring": 0, "building": 37, "integration": 0, "storing": 0, "additionalResolving": 0, "additionalIntegration": 0, "factory": 2, "dependencies": 0}, "id": 436, "issuerId": 138, "chunks": [179], "assets": [], "reasons": [{"moduleIdentifier": "D:\\Users\\pc\\Documents\\GitHub\\koodooapps\\koodoo-web\\src\\index.js", "module": "./src/index.js", "moduleName": "./src/index.js", "resolvedModuleIdentifier": "D:\\Users\\pc\\Documents\\GitHub\\koodooapps\\koodoo-web\\src\\index.js", "resolvedModule": "./src/index.js", "type": "harmony side effect evaluation", "active": true, "explanation": "", "userRequest": "./App", "loc": "2:0-24", "moduleId": 138, "resolvedModuleId": 138}, {"moduleIdentifier": "D:\\Users\\pc\\Documents\\GitHub\\koodooapps\\koodoo-web\\src\\index.js", "module": "./src/index.js", "moduleName": "./src/index.js", "resolvedModuleIdentifier": "D:\\Users\\pc\\Documents\\GitHub\\koodooapps\\koodoo-web\\src\\index.js", "resolvedModule": "./src/index.js", "type": "harmony import specifier", "active": true, "explanation": "", "userRequest": "./App", "loc": "10:43-46", "moduleId": 138, "resolvedModuleId": 138}], "usedExports": null, "providedExports": null, "optimizationBailout": ["ModuleConcatenation bailout: Module is not an ECMAScript module"], "depth": 1}, {"type": "module", "moduleType": "runtime", "layer": null, "size": 302, "sizes": {"runtime": 302}, "built": false, "codeGenerated": true, "buildTimeExecuted": false, "cached": false, "identifier": "webpack/runtime/compat get default export", "name": "webpack/runtime/compat get default export", "nameForCondition": null, "index": null, "preOrderIndex": null, "index2": null, "postOrderIndex": null, "optional": false, "orphan": false, "failed": false, "errors": 0, "warnings": 0, "id": "", "chunks": [179], "assets": [], "reasons": [], "usedExports": null, "providedExports": [], "optimizationBailout": [], "depth": null}, {"type": "module", "moduleType": "runtime", "layer": null, "size": 313, "sizes": {"runtime": 313}, "built": false, "codeGenerated": true, "buildTimeExecuted": false, "cached": false, "identifier": "webpack/runtime/define property getters", "name": "webpack/runtime/define property getters", "nameForCondition": null, "index": null, "preOrderIndex": null, "index2": null, "postOrderIndex": null, "optional": false, "orphan": false, "failed": false, "errors": 0, "warnings": 0, "id": "", "chunks": [179], "assets": [], "reasons": [], "usedExports": null, "providedExports": [], "optimizationBailout": [], "depth": null}, {"type": "module", "moduleType": "runtime", "layer": null, "size": 103, "sizes": {"runtime": 103}, "built": false, "codeGenerated": true, "buildTimeExecuted": false, "cached": false, "identifier": "webpack/runtime/hasOwnProperty shorthand", "name": "webpack/runtime/hasOwnProperty shorthand", "nameForCondition": null, "index": null, "preOrderIndex": null, "index2": null, "postOrderIndex": null, "optional": false, "orphan": false, "failed": false, "errors": 0, "warnings": 0, "id": "", "chunks": [179], "assets": [], "reasons": [], "usedExports": null, "providedExports": [], "optimizationBailout": [], "depth": null}], "entrypoints": {"main": {"name": "main", "chunks": [179], "assets": [{"name": "main.js"}], "filteredAssets": 0, "assetsSize": null, "auxiliaryAssets": [], "filteredAuxiliaryAssets": 0, "auxiliaryAssetsSize": 0, "children": {}, "childAssets": {}, "isOverSizeLimit": false}}, "namedChunkGroups": {"main": {"name": "main", "chunks": [179], "assets": [{"name": "main.js"}], "filteredAssets": 0, "assetsSize": null, "auxiliaryAssets": [], "filteredAuxiliaryAssets": 0, "auxiliaryAssetsSize": 0, "children": {}, "childAssets": {}, "isOverSizeLimit": false}}, "errors": [{"moduleIdentifier": "D:\\Users\\pc\\Documents\\GitHub\\koodooapps\\koodoo-web\\src\\App.js", "moduleName": "./src/App.js", "loc": "1443:4", "message": "Module parse failed: Unexpected token (1443:4)\nYou may need an appropriate loader to handle this file type, currently no loaders are configured to process this file. See https://webpack.js.org/concepts#loaders\n| \n|   return (\n>     <View style={{\n|       width: isMobile() ? windowWidth : windowWidth,\n|       height: windowHeight,", "moduleId": 436, "moduleTrace": [{"originIdentifier": "D:\\Users\\pc\\Documents\\GitHub\\koodooapps\\koodoo-web\\src\\index.js", "originName": "./src/index.js", "moduleIdentifier": "D:\\Users\\pc\\Documents\\GitHub\\koodooapps\\koodoo-web\\src\\App.js", "moduleName": "./src/App.js", "dependencies": [{"loc": "2:0-24"}, {"loc": "10:43-46"}], "originId": 138, "moduleId": 436}], "stack": "ModuleParseError: Module parse failed: Unexpected token (1443:4)\nYou may need an appropriate loader to handle this file type, currently no loaders are configured to process this file. See https://webpack.js.org/concepts#loaders\n| \n|   return (\n>     <View style={{\n|       width: isMobile() ? windowWidth : windowWidth,\n|       height: windowHeight,\n    at handleParseError (C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\webpack-cli\\node_modules\\webpack\\lib\\NormalModule.js:976:19)\n    at C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\webpack-cli\\node_modules\\webpack\\lib\\NormalModule.js:1095:5\n    at processResult (C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\webpack-cli\\node_modules\\webpack\\lib\\NormalModule.js:800:11)\n    at C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\webpack-cli\\node_modules\\webpack\\lib\\NormalModule.js:860:5\n    at C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\webpack-cli\\node_modules\\loader-runner\\lib\\LoaderRunner.js:407:3\n    at iterateNormalLoaders (C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\webpack-cli\\node_modules\\loader-runner\\lib\\LoaderRunner.js:233:10)\n    at C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\webpack-cli\\node_modules\\loader-runner\\lib\\LoaderRunner.js:224:4\n    at C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\webpack-cli\\node_modules\\webpack\\lib\\NormalModule.js:834:15\n    at Array.eval (eval at create (C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\webpack-cli\\node_modules\\tapable\\lib\\HookCodeFactory.js:33:10), <anonymous>:10:1)\n    at runCallbacks (C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\webpack-cli\\node_modules\\enhanced-resolve\\lib\\CachedInputFileSystem.js:27:15)"}, {"moduleIdentifier": "D:\\Users\\pc\\Documents\\GitHub\\koodooapps\\koodoo-web\\src\\index.js", "moduleName": "./src/index.js", "loc": "1:0-43", "message": "Module not found: Error: Can't resolve 'react-native' in 'D:\\Users\\pc\\Documents\\GitHub\\koodooapps\\koodoo-web\\src'", "moduleId": 138, "moduleTrace": [], "details": "resolve 'react-native' in 'D:\\Users\\pc\\Documents\\GitHub\\koodooapps\\koodoo-web\\src'\n  Parsed request is a module\n  using description file: D:\\Users\\pc\\Documents\\GitHub\\koodooapps\\koodoo-web\\package.json (relative path: ./src)\n    Field 'browser' doesn't contain a valid alias configuration\n    resolve as module\n      D:\\Users\\pc\\Documents\\GitHub\\koodooapps\\koodoo-web\\src\\node_modules doesn't exist or is not a directory\n      looking for modules in D:\\Users\\pc\\Documents\\GitHub\\koodooapps\\koodoo-web\\node_modules\n        single file module\n          using description file: D:\\Users\\pc\\Documents\\GitHub\\koodooapps\\koodoo-web\\package.json (relative path: ./node_modules/react-native)\n            no extension\n              Field 'browser' doesn't contain a valid alias configuration\n              D:\\Users\\pc\\Documents\\GitHub\\koodooapps\\koodoo-web\\node_modules\\react-native doesn't exist\n            .js\n              Field 'browser' doesn't contain a valid alias configuration\n              D:\\Users\\pc\\Documents\\GitHub\\koodooapps\\koodoo-web\\node_modules\\react-native.js doesn't exist\n            .json\n              Field 'browser' doesn't contain a valid alias configuration\n              D:\\Users\\pc\\Documents\\GitHub\\koodooapps\\koodoo-web\\node_modules\\react-native.json doesn't exist\n            .wasm\n              Field 'browser' doesn't contain a valid alias configuration\n              D:\\Users\\pc\\Documents\\GitHub\\koodooapps\\koodoo-web\\node_modules\\react-native.wasm doesn't exist\n        D:\\Users\\pc\\Documents\\GitHub\\koodooapps\\koodoo-web\\node_modules\\react-native doesn't exist\n      D:\\Users\\pc\\Documents\\GitHub\\koodooapps\\node_modules doesn't exist or is not a directory\n      D:\\Users\\pc\\Documents\\GitHub\\node_modules doesn't exist or is not a directory\n      D:\\Users\\pc\\Documents\\node_modules doesn't exist or is not a directory\n      D:\\Users\\pc\\node_modules doesn't exist or is not a directory\n      D:\\Users\\node_modules doesn't exist or is not a directory\n      D:\\node_modules doesn't exist or is not a directory", "stack": "ModuleNotFoundError: Module not found: Error: Can't resolve 'react-native' in 'D:\\Users\\pc\\Documents\\GitHub\\koodooapps\\koodoo-web\\src'\n    at C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\webpack-cli\\node_modules\\webpack\\lib\\Compilation.js:2016:28\n    at C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\webpack-cli\\node_modules\\webpack\\lib\\NormalModuleFactory.js:798:13\n    at eval (eval at create (C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\webpack-cli\\node_modules\\tapable\\lib\\HookCodeFactory.js:33:10), <anonymous>:8:1)\n    at C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\webpack-cli\\node_modules\\webpack\\lib\\NormalModuleFactory.js:270:22\n    at eval (eval at create (C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\webpack-cli\\node_modules\\tapable\\lib\\HookCodeFactory.js:33:10), <anonymous>:7:1)\n    at C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\webpack-cli\\node_modules\\webpack\\lib\\NormalModuleFactory.js:434:22\n    at C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\webpack-cli\\node_modules\\webpack\\lib\\NormalModuleFactory.js:116:11\n    at C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\webpack-cli\\node_modules\\webpack\\lib\\NormalModuleFactory.js:670:25\n    at C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\webpack-cli\\node_modules\\webpack\\lib\\NormalModuleFactory.js:855:8\n    at C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\webpack-cli\\node_modules\\webpack\\lib\\NormalModuleFactory.js:975:5"}], "errorsCount": 2, "warnings": [{"message": "configuration\nThe 'mode' option has not been set, webpack will fallback to 'production' for this value.\nSet 'mode' option to 'development' or 'production' to enable defaults for each environment.\nYou can also set it to 'none' to disable any default behavior. Learn more: https://webpack.js.org/configuration/mode/", "stack": "NoModeWarning: configuration\nThe 'mode' option has not been set, webpack will fallback to 'production' for this value.\nSet 'mode' option to 'development' or 'production' to enable defaults for each environment.\nYou can also set it to 'none' to disable any default behavior. Learn more: https://webpack.js.org/configuration/mode/\n    at C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\webpack-cli\\node_modules\\webpack\\lib\\WarnNoModeSetPlugin.js:20:30\n    at Hook.eval [as call] (eval at create (C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\webpack-cli\\node_modules\\tapable\\lib\\HookCodeFactory.js:19:10), <anonymous>:19:1)\n    at Hook.CALL_DELEGATE [as _call] (C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\webpack-cli\\node_modules\\tapable\\lib\\Hook.js:14:14)\n    at Compiler.newCompilation (C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\webpack-cli\\node_modules\\webpack\\lib\\Compiler.js:1121:30)\n    at C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\webpack-cli\\node_modules\\webpack\\lib\\Compiler.js:1166:29\n    at Hook.eval [as callAsync] (eval at create (C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\webpack-cli\\node_modules\\tapable\\lib\\HookCodeFactory.js:33:10), <anonymous>:4:1)\n    at Hook.CALL_ASYNC_DELEGATE [as _callAsync] (C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\webpack-cli\\node_modules\\tapable\\lib\\Hook.js:18:14)\n    at Compiler.compile (C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\webpack-cli\\node_modules\\webpack\\lib\\Compiler.js:1161:28)\n    at C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\webpack-cli\\node_modules\\webpack\\lib\\Compiler.js:524:12\n    at Compiler.readRecords (C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\webpack-cli\\node_modules\\webpack\\lib\\Compiler.js:986:5)"}], "warningsCount": 1, "children": []}