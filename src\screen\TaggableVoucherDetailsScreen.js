import React, { Component, useReducer, useState, useEffect } from 'react';
import {
  StyleSheet,
  ScrollView,
  Image,
  View,
  Text,
  TouchableOpacity,
  TextInput,
  FlatList,
  RefreshControl,
  Alert,
  Modal,
  Dimensions,
  Platform,
  useWindowDimensions
} from 'react-native';
import Colors from '../constant/Colors';
import * as Cart from '../util/Cart';
import * as User from '../util/User';
import Styles from '../constant/Styles';
// import ActionSheet from 'react-native-actionsheet';
import ApiClient from '../util/ApiClient';
import API from '../constant/API';
import Ionicons from 'react-native-vector-icons/Ionicons';
import Icon from 'react-native-vector-icons/Ionicons';
import Icons from 'react-native-vector-icons/Feather';
// import NumericInput from 'react-native-numeric-input';
// import { color } from 'react-native-reanimated';
// import QRCode from 'react-native-qrcode-svg';
import Close from 'react-native-vector-icons/AntDesign';
import { CommonStore } from '../store/commonStore';
import { UserStore } from '../store/userStore';
import moment from 'moment';
import Entypo from 'react-native-vector-icons/Entypo';
import AsyncImage from '../components/asyncImage';
// import LinearGradient from 'react-native-linear-gradient';
import LinearGradient from 'react-native-web-linear-gradient';
import { prefix } from "../constant/env";
import { useFocusEffect, useLinkTo } from "@react-navigation/native";

const TaggableVoucherDetailsScreen = (props) => {
  const { navigation, route } = props;

  const { width: windowWidth, height: windowHeight } = useWindowDimensions();
  const linkTo = useLinkTo();

  const [cartItem, setCartItem] = useState([]);
  const [refreshing, setRefreshing] = useState(false);
  const [visible, setVisible] = useState(false);
  const [visible1, setVisible1] = useState(false);
  const [value, setValue] = useState('');
  const [voucher, setVoucher] = useState([]);
  const [qrCodeModalVisibility, setQrCodeModalVisibility] = useState(false);
  const [selectedItem, setSelectedItem] = useState({});
  const [expandDetails, setExpandDetails] = useState(false);
  //const [pageFrom, setPageFrom] = useState(pageFromParam);

  const setState = () => { };

  const selectedOutlet = CommonStore.useState(s => s.selectedOutlet);
  const availableTaggableVouchers = CommonStore.useState(
    (s) => s.availableTaggableVouchers,
  );
  const selectedTaggableVoucher = CommonStore.useState(s => s.selectedTaggableVoucher);

  const selectedOutletItems = CommonStore.useState(s => s.selectedOutletItems);

  const selectedOutletItemsSkuDict = CommonStore.useState(s => s.selectedOutletItemsSkuDict);


  //const allOutletsItemsSkuDict = CommonStore.useState(s => s.allOutletsItemsSkuDict);

  /* useEffect(() => {
      if (availableTaggableVouchers !== '' && availableTaggableVouchers.length > 0 ) {
        var availablePromotionDetails = {};

        for (var i = 0; i < availableTaggableVouchers.length; i++) {
        var PromoDetails = {
        campaignName = availableTaggableVouchers[i].campaignName,
        campaignDescription = availableTaggableVouchers[i].campaignDescription
        };

        availablePromotionDetails[availableTaggableVouchers[i].uniqueId] = PromoDetails;
        }

      }
      }); */



  ///////////////////////// Test Functionalities /////////////////////////

  const selectedOutletItemCategoriesDict = CommonStore.useState(s => s.selectedOutletItemCategoriesDict);
  const selectedOutletItemsCategoriesDict = CommonStore.useState(s => s.selectedOutletItemsCategoriesDict);
  const selectedOutletItemCategory = CommonStore.useState(s => s.selectedOutletItemCategory);

  ///////////////////////// Test Functionalities /////////////////////////

  const [promotionItem, setPromotionItem] = useState({});

  /* useEffect(() => {
      if (selectedTaggableVoucher && selectedTaggableVoucher.uniqueId) {
          if (promotionItem.uniqueId === undefined) {
              retrievePromotionItem();
          }
      }
  }, [selectedTaggableVoucher, promotionItem]);

  const retrievePromotionItem = async () => {
    const outletSnapshot = await firestore().collection(Collections.OutletItem)
        .where('uniqueId', '==', selectedTaggableVoucher.outletId)
        .limit(1)
        .get();

    if (!outletSnapshot.empty) {
        const outlet = outletSnapshot.docs[0].data();

        setPromotionItem(outlet);
    }
  }; */

  navigation.setOptions({
    headerLeft: () => (
      <TouchableOpacity
        style={{}}
        onPress={() => {
          props.navigation.goBack();
        }}>
        <View
          style={{
            marginLeft: 10,
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'flex-start',
          }}>
          <Ionicons
            name="chevron-back"
            size={26}
            color={Colors.fieldtTxtColor}
            style={{}}
          />

          <Text
            style={{
              color: Colors.fieldtTxtColor,
              fontSize: 16,
              textAlign: 'center',
              fontFamily: 'NunitoSans-Regular',
              lineHeight: 22,
              marginTop: -1,
            }}>
            Back
          </Text>
        </View>
      </TouchableOpacity>
    ),
    headerRight: () => (
      <TouchableOpacity
        onPress={() => {
          props.navigation.navigate('Profile');
        }}
        style={{}}>
        <View style={{ marginRight: 15 }}>
          <Ionicons name="menu" size={30} color={Colors.primaryColor} />
        </View>
      </TouchableOpacity>
    ),
    headerTitle: () => (
      <View
        style={{
          justifyContent: 'center',
          alignItems: 'center',
          bottom: -1,
        }}>
        <Text
          style={{
            fontSize: 20,
            lineHeight: 25,
            textAlign: 'center',
            fontFamily: 'NunitoSans-Bold',
            color: Colors.mainTxtColor,
          }}>
          Voucher Details
        </Text>
      </View>
    ),
  });

  var itemNameFontSize = 15;

  if (windowWidth <= 360) {
    itemNameFontSize = 13;
    //console.log(windowWidth)
  }

  const itemNameTextScale = {
    fontSize: itemNameFontSize,
  };

  const renderPromotion = ({ item, index }) => {
    return (
      <View>
        {
          item.criteriaList && item.criteriaList.map(outletItemSku => {
            var outletItem = {};

            let foundItem = selectedOutletItems.find(findItem => findItem.sku === outletItemSku.variationItemsSku);

            if (foundItem) {
              outletItem = foundItem;
            }

            return (

              <View style={{ flexDirection: 'column' }}>
                <View
                  style={{
                    flexDirection: 'row',
                    padding: 15,
                    alignItems: 'center',
                    width: '70%',
                  }}>

                  <View>
                    <View style={[{
                      backgroundColor: Colors.secondaryColor,
                      // width: 60,
                      // height: 60,
                      width: windowWidth * 0.22,
                      height: windowWidth * 0.22,
                      borderRadius: 10,
                    }]}>
                      {outletItem.image
                        ?
                        // <Image source={{ uri: item.image }} style={{
                        //   width: windowWidth * 0.22,
                        //   height: windowWidth * 0.22,
                        //   borderRadius: 10
                        // }} />
                        <AsyncImage source={{ uri: outletItem.image }} item={outletItem} style={{
                          width: windowWidth * 0.22,
                          height: windowWidth * 0.22,
                          borderRadius: 10
                        }} />
                        :
                        <Ionicons name="fast-food-outline" size={50} />
                      }
                    </View>
                  </View>
                  <View style={{ flexDirection: 'column' }}>
                    <Text
                      style={{
                        marginLeft: 5,
                        fontSize: 16,
                        fontFamily: 'NunitoSans-Regular',
                      }}
                      numberOfLines={3}
                    >
                      {outletItem.name}
                    </Text>

                    <Text
                      style={[itemNameTextScale, {
                        textTransform:
                          'uppercase',
                        fontFamily: "NunitoSans-Bold",
                      }]} numberOfLines={3}>{outletItem.name}
                    </Text>

                    <View style={{ flexDirection: 'row', marginTop: 10 }}>
                      <Text
                        style={{
                          marginLeft: 5,
                          fontSize: 13,
                          fontFamily: 'NunitoSans-SemiBold',
                        }}>
                        Before: RM7.99
                      </Text>
                      <Text
                        style={{
                          marginLeft: 10,
                          fontSize: 13,
                          fontFamily: 'NunitoSans-SemiBold',
                        }}>
                        After: RM1.99
                      </Text>
                    </View>
                  </View>

                </View>
              </View>
            );
          })}
      </View>
    )
  };

  return (
    <>
      {selectedTaggableVoucher &&
        <View style={{ flex: 1 }}>
          <View style={[styles.container1, {height: windowHeight}]}>
            <View style={styles.container}>
              {/* <View
                style={{
                  backgroundColor: Colors.primaryColor,
                  height: 160,
                  justifyContent: 'center',
                  alignItems: 'center',
                }}> */}
              <LinearGradient colors={[Colors.primaryColor, Colors.secondaryColor, Colors.whiteColor]}
                style={{
                  backgroundColor: Colors.primaryColor,
                  height: 180,
                  justifyContent: 'center',
                  alignItems: 'center',
                }}>
                <View
                  style={{
                    backgroundColor: '#ffffff',
                    padding: 16,
                    borderRadius: 20,
                    width: '90%',
                    height: '80%',
                    flexDirection: 'row',
                  }}>
                  <View
                    style={{
                      //borderRadius: 8,
                      alignItems: 'center',
                      padding: 6,
                      backgroundColor: '#ffffff',
                      //borderWidth: 1,
                      width: '35%',
                      height: '100%',
                      justifyContent: 'center',
                      borderRightWidth: 1,
                      borderColor: Colors.fieldtTxtColor,
                    }}>
                    <View style={styles.pic}>
                      {selectedTaggableVoucher.image ? (
                        <AsyncImage
                          source={{ uri: selectedTaggableVoucher.image }}
                          item={selectedTaggableVoucher}
                          style={{
                            width: 90,
                            height: Platform.OS == 'ios' ? 90 : 90,
                            borderRadius: 10,
                          }}
                        />
                      ) : null}
                    </View>
                  </View>
                  <View
                    style={{
                      marginLeft: 5,
                      flexDirection: 'column',
                      justifyContent: 'center',
                      width: 0,
                      flexGrow: 1,
                    }}>
                    <Text
                      style={{
                        marginLeft: 5,
                        fontSize: 16,
                        fontFamily: 'NunitoSans-Bold',
                      }}
                      numberOfLines={3}>
                      {selectedTaggableVoucher.campaignName}
                    </Text>
                    <View style={{ marginTop: 15 }}>
                      <Text
                        style={{
                          marginLeft: 5,
                          fontSize: 14,
                          fontFamily: 'NunitoSans-SemiBold',
                        }}>
                        Expiry Date:
                      </Text>
                      <Text
                        style={{
                          marginLeft: 5,
                          fontSize: 11,
                          fontFamily: 'NunitoSans-SemiBold',
                        }}>
                        {moment(selectedTaggableVoucher.expirationDate).format(
                          'dddd, Do MMM YYYY',
                        )}
                      </Text>
                    </View>
                  </View>
                </View>
              </LinearGradient>
              <View style={{ justifyContent: 'center', alignItems: 'center', }}>
                <TouchableOpacity
                  onPress={() => {
                    setExpandDetails(!expandDetails);
                  }}>
                  <Entypo
                    name={expandDetails ? 'chevron-up' : 'chevron-down'}
                    size={30}
                    color={Colors.primaryColor}
                  />
                </TouchableOpacity>
              </View>
              {expandDetails && (
                <View
                  style={{
                    flexDirection: 'column',
                    justifyContent: 'flex-start',
                    padding: 10,
                    borderBottomWidth: 1,
                    borderColor: Colors.primaryColor,
                  }}>
                  <Text
                    style={{
                      marginTop: 0,
                      fontSize: 14,
                      fontFamily: 'NunitoSans-Bold',
                      //color: Colors.whiteColor,
                    }}>
                    Description:
                  </Text>
                  <Text
                    style={{
                      marginTop: 5,
                      fontSize: 11,
                      fontFamily: 'NunitoSans-Regular',
                      //color: Colors.whiteColor,
                    }}>
                    {selectedTaggableVoucher.campaignDescription}
                  </Text>
                </View>
              )}
              <ScrollView>

                {/* <View>
                  <FlatList
                    style={{marginBottom: 10}}
                    data={selectedTaggableVoucher}
                    //extraData={selectedTaggableVoucher}
                    renderItem={renderPromotion}
                    keyExtractor={(item, index) => String(index)}
                  />
                </View> */}

                {
                  selectedTaggableVoucher.criteriaList && selectedTaggableVoucher.criteriaList.map(outletItemSku => {
                    var outletItem = {};

                    let foundItem = selectedOutletItems.find(findItem => findItem.sku === outletItemSku.variationItemsSku);

                    if (foundItem) {
                      outletItem = foundItem;
                    }
                    else {

                    }
                    return (
                      <>
                        {outletItem.name ?
                          <>
                            <View style={{ justifyContent: 'center', padding: 10, alignItems: 'center', borderBottomWidth: 1, borderBottomColor: Colors.fieldtTxtColor }}>
                              <Text style={{ fontSize: 16, fontFamily: 'NunitoSans-Bold', }}>Available Products</Text>
                            </View>
                            <TouchableOpacity onPress={() => {

                              /* CommonStore.update(s => {
                                s.selectedTaggableVoucher = selectedTaggableVoucher;
                              }); */

                              //navigation.navigate('OutletMenu');

                            }}>
                              <View
                                style={{
                                  flexDirection: 'row',
                                  padding: 20,
                                  alignItems: 'center',
                                  width: '70%',
                                }}>
                                <View style={[{
                                  backgroundColor: Colors.secondaryColor,
                                  // width: 60,
                                  // height: 60,
                                  width: windowWidth * 0.22,
                                  height: windowWidth * 0.22,
                                  borderRadius: 10,
                                  alignItems: 'center',
                                  justifyContent: 'center'
                                }
                                ]}>
                                  {outletItem.image
                                    ?
                                    <AsyncImage source={{ uri: outletItem.image }} item={outletItem} style={{
                                      width: windowWidth * 0.22,
                                      height: windowWidth * 0.22,
                                      borderRadius: 10
                                    }} />
                                    :
                                    <Ionicons name="fast-food-outline" size={50} />
                                  }
                                </View>
                                <Text
                                  style={{
                                    marginLeft: 10,
                                    fontSize: 16,
                                    fontFamily: 'NunitoSans-Regular',
                                  }}
                                  numberOfLines={3}
                                >
                                  {outletItem.name}
                                </Text>
                              </View>

                            </TouchableOpacity>

                          </>
                          :
                          <View>
                            <View style={{ justifyContent: 'center', padding: 10, alignItems: 'center', borderBottomWidth: 1, borderBottomColor: Colors.fieldtTxtColor }}>
                              <Text style={{ fontSize: 16, fontFamily: 'NunitoSans-Bold', }}>Available Categories</Text>
                            </View>
                            <View style={{ padding: 10, justifyContent: 'center', alignItems: 'center' }}>
                              <Text style={{ fontSize: 18, fontFamily: 'NunitoSans-Bold' }}>{outletItemSku.variationItemsSku}</Text>
                            </View>
                          </View>

                        }
                      </>

                    )


                  })}
              </ScrollView>

            </View>
          </View>
        </View>
      }
    </>
  );
};

const styles = StyleSheet.create({
  container1: {
    backgroundColor: '#ffffff',
    // backgroundColor: 'red',
  },
  container: {
    backgroundColor: '#ffffff',
    //padding: 16,
  },
  searchBar: {
    marginHorizontal: 16,
    backgroundColor: Colors.fieldtBgColor,
    flexDirection: 'row',
    padding: 12,
    borderRadius: 10,
    alignContent: 'center',
  },
  searchBarText: {
    color: Colors.fieldtTxtColor,
    marginLeft: 10,
  },
  card: {
    flex: 1,
    minWidth: Styles.width - 64,
    minHeight: 100,
    // backgroundColor: Colors.primaryColor,
    backgroundColor: '#416D5C',
    flexDirection: 'row',
    marginTop: 20,
    alignContent: 'center',
    alignItems: 'center',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  card1: {
    flex: 1,
    minWidth: Styles.width - 64,
    minHeight: 100,
    backgroundColor: Colors.fieldtBgColor,
    flexDirection: 'row',
    marginBottom: 20,
    alignContent: 'center',
    alignItems: 'center',
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
    marginBottom: 10,
  },
  cornerleft: {
    backgroundColor: 'white',
    height: 30,
    width: 30,
    alignSelf: 'flex-start',
    marginTop: '22%',
    borderTopRightRadius: 50,
  },
  cornerright: {
    backgroundColor: 'white',
    height: 30,
    width: 30,
    alignSelf: 'flex-start',
    borderBottomRightRadius: 50,

    // borderStyle: 'dashed',
    // borderWidth: 1,
    // borderColor: 'black',
  },

  cornerleft1: {
    backgroundColor: 'white',
    height: 30,
    width: 30,
    alignSelf: 'flex-start',
    marginTop: '22%',
    borderTopLeftRadius: 50,
  },
  cornerright1: {
    backgroundColor: 'white',
    height: 30,
    width: 30,
    alignSelf: 'flex-start',
    borderBottomLeftRadius: 50,
  },
  text: {
    fontSize: 13,
    color: Colors.fieldtTxtColor,
    fontFamily: 'NunitoSans-Bold',
  },
  title: {
    color: Colors.whiteColor,
    // fontSize: Platform.OS == 'ios' ? 30 : 20,
    fontSize: Platform.OS == 'ios' ? 30 : 22,
    fontFamily: 'NunitoSans-SemiBold',
    marginLeft: '1%',
    // marginTop: 10,
    marginBottom: 5,
    textAlign: 'right',
  },
  title1: {
    color: Colors.blackColor,
    fontSize: 16,
    fontFamily: 'NunitoSans-Bold',
  },
  title2: {
    color: Colors.primaryColor,
    fontSize: Platform.OS == 'ios' ? 13 : 15,
    fontFamily: 'NunitoSans-SemiBold',
    // fontWeight: 'bold',
    marginBottom: 8,
  },
  title3: {
    color: Colors.descriptionColor,
    fontSize: Platform.OS == 'ios' ? 13 : 14,
    fontFamily: 'NunitoSans-SemiBold',
    // fontWeight: 'bold',
  },
  viewContainer: {
    marginTop: 10,
    justifyContent: 'space-between',
    flexDirection: 'row',
    flex: 10,
  },
  total: {
    fontSize: 14,
    color: Colors.blackColor,
    fontFamily: 'NunitoSans-Bold',
  },
  price: {
    fontSize: 14,
    color: Colors.primaryColor,
    fontFamily: 'NunitoSans-Regular',
  },
  totalBorder: {
    paddingTop: 5,
    justifyContent: 'space-between',
    flex: 1,
    flexDirection: 'row',
    borderTopWidth: StyleSheet.hairlineWidth,
    borderColor: Colors.blackColor,
    borderRadius: 1,
  },
  pic: {
    backgroundColor: Colors.secondaryColor,
    width: 90,
    height: 90,
    borderRadius: 10,
    alignSelf: 'center',
    justifyContent: 'center',
    alignItems: 'center',
  },
  searchBar1: {
    backgroundColor: Colors.fieldtBgColor,
    flexDirection: 'row',
    padding: 12,
    borderRadius: 10,
    alignContent: 'center',
    marginBottom: '2%',
  },

  centerTextHolder: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  picOutlet: {
    backgroundColor: Colors.secondaryColor,
    width: 45,
    height: 45,
    borderRadius: 10,
    alignSelf: 'center',
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default TaggableVoucherDetailsScreen;
