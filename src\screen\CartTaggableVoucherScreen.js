import React, { Component, useReducer, useState, useEffect } from "react";
import {
  StyleSheet,
  ScrollView,
  Image,
  View,
  Text,
  TouchableOpacity,
  TextInput,
  FlatList,
  RefreshControl,
  Alert,
  Modal,
  Dimensions,
  ActivityIndicator,
  Platform,
  useWindowDimensions,
} from "react-native";
import Colors from "../constant/Colors";
import * as Cart from "../util/Cart";
import * as User from "../util/User";
import { isMobile, setRouteFrom, mondayFirst, checkApplyDiscountPerValidity } from "../util/commonFuncs";
import { CHARGES_TYPE, WEEK } from "../constant/common";
import Styles from "../constant/Styles";
import ApiClient from "../util/ApiClient";
import API from "../constant/API";
import {
  mp_username,
  mp_password,
  mp_merchant_ID,
  mp_app_name,
  mp_verification_key,
  prefix,
} from "../constant/env";
import FontAwesome from "react-native-vector-icons/FontAwesome";
import Ionicons from "react-native-vector-icons/Ionicons";
import Icons from "react-native-vector-icons/Feather";
import Entypo from "react-native-vector-icons/Entypo";
import Close from "react-native-vector-icons/AntDesign";
import AntDesign from "react-native-vector-icons/AntDesign";
import { ReactComponent as Loyalty } from "../svg/loyalty.svg";
// import firestore from '@react-native-firebase/firestore';
// import NumericInput from "react-native-numeric-input";
import Back from "react-native-vector-icons/EvilIcons";
import MaterialIcons from "react-native-vector-icons/MaterialIcons";
// import DateTimePickerModal from "react-native-modal-datetime-picker";
// import { SpinPicker } from 'react-native-spin-picker'
import moment from "moment";
import {
  createIconSetFromFontello,
  createIconSetFromIcoMoon,
} from "react-native-vector-icons";
// import molpay from "molpay-mobile-xdk-reactnative-beta";
import { CommonStore } from "../store/commonStore";
import {
  ORDER_TYPE,
  COURIER_CODE,
  MERCHANT_VOUCHER_TYPE,
  USER_ORDER_PAYMENT_OPTIONS,
  SENDER_DROPDOWN_LIST,
  MPS_CHANNEL_LIST,
  MPS_CHANNEL,
} from "../constant/common";
// import firebase from "firebase";
import { getFirestore, collection, query, where, getDocs, limit } from "firebase/firestore";
import { Collections } from "../constant/firebase";
import { UserStore } from "../store/userStore";
import AsyncStorage from "@react-native-async-storage/async-storage";
import "react-native-get-random-values";
import { v4 as uuidv4 } from "uuid";
import AsyncImage from "../components/asyncImage";
import { CommonActions, useLinkTo } from "@react-navigation/native";
import {
  PROMOTION_TYPE,
  PROMOTION_TYPE_VARIATION,
  APPLY_BEFORE
} from "../constant/promotions";
import { $dataMetaSchema } from "ajv";
import { DataStore } from "../store/dataStore";

import fpx_amb from "../asset/image/banks/logo-ambank.png";
import fpx_bimb from "../asset/image/banks/logo-bank-islam.png";
import fpx_cimbclicks from "../asset/image/banks/logo-cimb-bank.png";
import fpx_hlb from "../asset/image/banks/logo-hong-leong-bank.png";
import fpx_mb2u from "../asset/image/banks/logo-maybank.png";
import fpx_pbb from "../asset/image/banks/logo-public-bank.png";
import fpx_rhb from "../asset/image/banks/logo-rhb.png";
import FPX_OCBC from "../asset/image/banks/logo-ocbc.png";
import FPX_SCB from "../asset/image/banks/logo-standard-chartered.png";
import FPX_ABB from "../asset/image/banks/logo-affinbank.png";
import FPX_BKRM from "../asset/image/banks/logo-bank-rakyat.png";
import FPX_BMMB from "../asset/image/banks/logo-bank-muamalat.png";
import FPX_KFH from "../asset/image/banks/logo-kuwait-finance.png";
import FPX_BSN from "../asset/image/banks/logo-bsn.png";
import FPX_ABMB from "../asset/image/banks/logo-alliance-bank.png";
import FPX_UOB from "../asset/image/banks/logo-uob.png";
import visa from "../asset/image/banks/logo-visa.png";
import mastercard from "../asset/image/banks/logo-mastercard.png";
import DeliveryImg from '../asset/image/delivery.png';
import { ReactComponent as House } from '../svg/house.svg';
import { TempStore } from "../store/tempStore";

import { customAlphabet } from 'nanoid';
import { idbDel, idbGet, idbSet, safelyExecuteIdb } from "../util/db";
import { TableStore } from "../store/tableStore";
import { PaymentStore } from "../store/paymentStore";
const orderIdPrefix = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
const generateUniqueCodeForOrderId = customAlphabet(orderIdPrefix, 2);

const BANK_LOGO = {
  fpx_amb: fpx_amb,
  fpx_bimb: fpx_bimb,
  fpx_cimbclicks: fpx_cimbclicks,
  fpx_hlb: fpx_hlb,
  fpx_mb2u: fpx_mb2u,
  fpx_pbb: fpx_pbb,
  fpx_rhb: fpx_rhb,
  FPX_OCBC: FPX_OCBC,
  FPX_SCB: FPX_SCB,
  FPX_ABB: FPX_ABB,
  FPX_BKRM: FPX_BKRM,
  FPX_BMMB: FPX_BMMB,
  FPX_KFH: FPX_KFH,
  FPX_BSN: FPX_BSN,
  FPX_ABMB: FPX_ABMB,
  FPX_UOB: FPX_UOB,
  visa: visa,
  mastercard: mastercard,
};

/*
 * CartScreen
 * function:
 * *Displays the items inside the cart
 * *Allows user to edit the current items in the cart
 * *Shows other popular items for users to purchase
 * *Place order here, delivery, pickup or dine in
 *
 * route.params:
 * *test: ??
 * *test1: if 1, redemption purchase
 * *test2: if 1, redemption extension
 * *paymentMethod: the selected payment method
 * *outletData: array of data of this cart is purchasing from
 * *navFrom: the navigation stack of this route, from takeaway or from outlet
 */

const CartTaggableVoucherScreen = (props) => {
  const { navigation, route } = props;

  const { width: windowWidth, height: windowHeight } = useWindowDimensions();

  const linkTo = useLinkTo();

  // const { test, test1, test2, paymentMethod, outletData, navFrom } = route.params;
  // const testParam = route.params.test;
  // const test1Param = route.params.test1;
  // const test2Param = route.params.test2;
  // const paymentMethodParam = route.params.paymentMethod;
  // const outletDataParam = route.params.outletData;
  // const navFromParam = route.params.navFrom;

  // const [paymentMethod, setPaymentMethod] = useState(paymentMethodParam);
  const [cartItem, setCartItem] = useState([]);
  const [refreshing, setRefreshing] = useState(false);
  const [visible, setVisible] = useState(false);
  const [visible1, setVisible1] = useState(false);
  const [value, setValue] = useState("");
  const [editingItemId, setEditingItemId] = useState(null);
  // const [outletData, setOutletData] = useState(outletDataParam);
  const [menu, setMenu] = useState([]);
  const [menuItem, setMenuItem] = useState([]);
  const [category, setCategory] = useState([]);
  const [outletMenu, setOutletMenu] = useState([]);
  const [menuItemDetails, setMenuItemDetails] = useState([]);
  const [qty, setQty] = useState([]);
  // const [test, setTest] = useState(testParam);
  // const [test1, setTest1] = useState(test1Param);
  // const [test2, setTest2] = useState(test2Param);
  const [popularOutlet, setPopularOutlet] = useState([]);
  const [popular, setPopular] = useState([]);
  const [showDateTimePicker, setShowDateTimePicker] = useState(false);
  const [pickerMode, setPickerMode] = useState("datetime");
  const [currentMenu, setCurrentMenu] = useState([]);
  const [type, setType] = useState(Cart.getOrderType()); // 0 "Dine In"
  const [schedulteTimeList, setSchedulteTimeList] = useState([]);
  const [schedulteTimeSelected, setSchedulteTimeSelected] = useState("");
  const [totalFloat, setTotalFloat] = useState(0);
  const [taxFloat, setTaxFloat] = useState(0);
  // const [navFrom, setNavFrom] = useState(navFromParam);
  const [deliveryQuotation, setDeliveryQuotation] = useState({});
  const [clicked, setClicked] = useState(1); //delivery
  const [clicked1, setClicked1] = useState(0); //pickup
  const [rev_date, setRev_date] = useState(undefined);

  const [totalPrice, setTotalPrice] = useState(0);
  const [totalTax, setTotalTax] = useState(0);
  const [totalDiscount, setTotalDiscount] = useState(0);
  const [totalSc, setTotalSc] = useState(0);
  const [discountPromotionsTotal, setDiscountPromotionsTotal] = useState(0);
  const [discountPromotionsTotalLCC, setDiscountPromotionsTotalLCC] =
    useState(0);
  const [discountLoyaltyStampsTotal, setDiscountLoyaltyStampsTotal] =
    useState(0);

  const [promoCode, setPromoCode] = useState("");

  const [totalPrepareTime, setTotalPrepareTime] = useState(0);

  const [orderIdLocal, setOrderIdLocal] = useState(uuidv4());
  const [orderIdServer, setOrderIdServer] = useState("");

  const [mpsChannel, setMpsChannel] = useState("");
  const [showMpsChannelModal, setShowMpsChannelModal] = useState(false);

  const [showUserInfoModal, setShowUserInfoModal] = useState(false);

  /////////////////////////////////////////////////////////////////////////////////////

  // 2022-08-02 - Buy voucher online

  const tvCartItems = CommonStore.useState(s => s.tvCartItems);

  const selectedTaggableVoucherPurchase = CommonStore.useState(s => s.selectedTaggableVoucherPurchase);

  const [name, setName] = useState('');
  const [phone, setPhone] = useState('');
  const [birthday, setBirthday] = useState(moment(Date.now()));
  const [email, setEmail] = useState('');
  const [lat, setLat] = useState(0);
  const [lng, setLng] = useState(0);
  const [address, setAddress] = useState('');

  /////////////////////////////////////////////////////////////////////////////////////

  const isLoading = CommonStore.useState((s) => s.isLoading);
  const isPlacingOrder = CommonStore.useState((s) => s.isPlacingOrder);

  const cartItems = CommonStore.useState((s) => s.cartItems);
  const cartOutletItemsDict = CommonStore.useState(
    (s) => s.cartOutletItemsDict
  );
  const cartOutletItemAddOnDict = CommonStore.useState(
    (s) => s.cartOutletItemAddOnDict
  );
  const cartOutletItemAddOnChoiceDict = CommonStore.useState(
    (s) => s.cartOutletItemAddOnChoiceDict
  );

  const selectedAddress = CommonStore.useState(s => s.selectedAddress);
  const selectedAddressDetails = CommonStore.useState(s => s.selectedAddressDetails);
  const selectedAddressNote = CommonStore.useState(s => s.selectedAddressNote);
  const selectedAddressLocation = CommonStore.useState(s => s.selectedAddressLocation);
  const selectedAddressUserName = CommonStore.useState(s => s.selectedAddressUserName);
  const selectedAddressUserPhone = CommonStore.useState(s => s.selectedAddressUserPhone);

  const [isCartLoading, setIsCartLoading] = useState(false);

  const selectedOutletItemCategoriesDict = CommonStore.useState(
    (s) => s.selectedOutletItemCategoriesDict
  );

  const tvCartItemsProcessed = CommonStore.useState((s) => s.tvCartItemsProcessed);

  const outletsTaxDict = CommonStore.useState((s) => s.outletsTaxDict);

  const orderType = CommonStore.useState((s) => s.orderType);

  const selectedOutletTableId = CommonStore.useState(
    (s) => s.selectedOutletTableId
  );
  const selectedOutletTablePax = CommonStore.useState(
    (s) => s.selectedOutletTablePax
  );
  const selectedOutletTableCode = CommonStore.useState(
    (s) => s.selectedOutletTableCode
  );
  const userCart = CommonStore.useState((s) => s.userCart);

  const selectedOutletWaiterName = CommonStore.useState(
    (s) => s.selectedOutletWaiterName
  );
  const selectedOutletWaiterId = CommonStore.useState(
    (s) => s.selectedOutletWaiterId
  );

  const selectedOutletTableQRUrl = CommonStore.useState(
    (s) => s.selectedOutletTableQRUrl
  );

  const selectedOutlet = CommonStore.useState((s) => s.selectedOutlet);
  // const lat = CommonStore.useState(s => s.lat);
  // const lng = CommonStore.useState(s => s.lng);

  const merchantVouchersDict = CommonStore.useState(
    (s) => s.merchantVouchersDict
  );
  const selectedVoucherId = UserStore.useState((s) => s.selectedVoucherId);

  const selectedUserAddress = UserStore.useState((s) => s.selectedUserAddress);
  const firebaseUid = UserStore.useState((s) => s.firebaseUid);
  const userName = UserStore.useState((s) => s.name);
  const userNumber = UserStore.useState((s) => s.number);
  const userEmail = UserStore.useState((s) => s.email);

  const userIdAnonymous = UserStore.useState(s => s.userIdAnonymous);

  const userGroups = UserStore.useState((s) => s.userGroups);

  const availablePromotions = CommonStore.useState(
    (s) => s.availablePromotions
  );
  const overrideItemPriceSkuDict = CommonStore.useState(
    (s) => s.overrideItemPriceSkuDict
  );
  const amountOffItemSkuDict = CommonStore.useState(
    (s) => s.amountOffItemSkuDict
  );
  const percentageOffItemSkuDict = CommonStore.useState(
    (s) => s.percentageOffItemSkuDict
  );
  const buy1Free1ItemSkuDict = CommonStore.useState(
    (s) => s.buy1Free1ItemSkuDict
  );
  const [showPointsToRedeemOptionsLCC, setShowPointsToRedeemOptionsLCC] =
    useState(false);

  const overrideCategoryPriceNameDict = CommonStore.useState(
    (s) => s.overrideCategoryPriceNameDict
  );
  const amountOffCategoryNameDict = CommonStore.useState(
    (s) => s.amountOffCategoryNameDict
  );
  const percentageOffCategoryNameDict = CommonStore.useState(
    (s) => s.percentageOffCategoryNameDict
  );
  const buy1Free1CategoryNameDict = CommonStore.useState(
    (s) => s.buy1Free1CategoryNameDict
  );
  const [promotionIdAppliedList, setPromotionIdAppliedList] = useState([]);
  const [pointsToRedeemPackageIdListLCC, setPointsToRedeemPackageIdListLCC] =
    useState([]);
  const [pointsToRedeemAmountListLCC, setPointsToRedeemAmountListLCC] =
    useState([]);

  const [pointsRedeemedCartItemListLCC, setPointsRedeemedCartItemListLCC] =
    useState([]);
  const [pointsRedeemedCartItemList, setPointsRedeemedCartItemList] = useState(
    []
  );
  const deliveryItemSkuDict = CommonStore.useState(
    (s) => s.deliveryItemSkuDict
  );
  const takeawayItemSkuDict = CommonStore.useState(
    (s) => s.takeawayItemSkuDict
  );
  const deliveryCategoryNameDict = CommonStore.useState(
    (s) => s.deliveryCategoryNameDict
  );
  const takeawayCategoryNameDict = CommonStore.useState(
    (s) => s.takeawayCategoryNameDict
  );
  const loyaltyStampGetItemSkuDict = CommonStore.useState(
    (s) => s.loyaltyStampGetItemSkuDict
  );
  const loyaltyStampGetCategoryNameDict = CommonStore.useState(
    (s) => s.loyaltyStampGetCategoryNameDict
  );
  const loyaltyStampBuyItemSkuDict = CommonStore.useState(
    (s) => s.loyaltyStampBuyItemSkuDict
  );
  const loyaltyStampBuyCategoryNameDict = CommonStore.useState(
    (s) => s.loyaltyStampBuyCategoryNameDict
  );
  const selectedLoyaltyCampaign = CommonStore.useState(
    (s) => s.selectedLoyaltyCampaign
  );
  const userLoyaltyStampGetLsItemDict = CommonStore.useState(
    (s) => s.userLoyaltyStampGetLsItemDict
  );
  const userLoyaltyStampsDict = CommonStore.useState(
    (s) => s.userLoyaltyStampsDict
  );
  const selectedOutletLoyaltyStampsDict = CommonStore.useState(
    (s) => s.selectedOutletLoyaltyStampsDict
  );

  //////////////////////////////////////////////////////
  const selectedTaggableVoucher = CommonStore.useState(
    (s) => s.selectedTaggableVoucher,
  );
  const overrideItemPriceSkuDictLC = CommonStore.useState(
    (s) => s.overrideItemPriceSkuDictLC
  );
  const amountOffItemSkuDictLC = CommonStore.useState(
    (s) => s.amountOffItemSkuDictLC
  );
  const percentageOffItemSkuDictLC = CommonStore.useState(
    (s) => s.percentageOffItemSkuDictLC
  );
  const buy1Free1ItemSkuDictLC = CommonStore.useState(
    (s) => s.buy1Free1ItemSkuDictLC
  );
  const deliveryItemSkuDictLC = CommonStore.useState(
    (s) => s.deliveryItemSkuDictLC
  );
  const takeawayItemSkuDictLC = CommonStore.useState(
    (s) => s.takeawayItemSkuDictLC
  );

  const overrideCategoryPriceNameDictLC = CommonStore.useState(
    (s) => s.overrideCategoryPriceNameDictLC
  );
  const amountOffCategoryNameDictLC = CommonStore.useState(
    (s) => s.amountOffCategoryNameDictLC
  );
  const percentageOffCategoryNameDictLC = CommonStore.useState(
    (s) => s.percentageOffCategoryNameDictLC
  );
  const buy1Free1CategoryNameDictLC = CommonStore.useState(
    (s) => s.buy1Free1CategoryNameDictLC
  );
  const deliveryCategoryNameDictLC = CommonStore.useState(
    (s) => s.deliveryCategoryNameDictLC
  );
  const takeawayCategoryNameDictLC = CommonStore.useState(
    (s) => s.takeawayCategoryNameDictLC
  );

  //////////////////////////////////////////////////////

  const [pointsToRedeemLCC, setPointsToRedeemLCC] = useState(0);
  const [usePointsToRedeemLCC, setUsePointsToRedeemLCC] = useState(false);
  const userPointsBalanceLCC = CommonStore.useState(
    (s) => s.selectedOutletLCCBalance
  );
  const [pointsToRedeemDiscountLCC, setPointsToRedeemDiscountLCC] = useState(0);
  const pointsRedeemItemSkuDictLCC = CommonStore.useState(
    (s) => s.pointsRedeemItemSkuDictLCC
  );
  const pointsRedeemCategoryNameDictLCC = CommonStore.useState(
    (s) => s.pointsRedeemCategoryNameDictLCC
  );
  const [lsRedeemedCartItemListLCC, setLsRedeemedCartItemListLCC] = useState(
    []
  );

  const [showRedeemRewardModalLCC, setShowRedeemRewardModalLCC] =
    useState(false);

  const [deliveryQuotationPromotion, setDeliveryQuotationPromotion] = useState(
    {}
  );
  const [loyaltyStampBuyList, setLoyaltyStampBuyList] = useState([]);
  const [loyaltyStampGetList, setLoyaltyStampGetList] = useState([]);
  const [spentUserPointsLCC, setSpentUserPointsLCC] = useState(0);
  const [spentUserPoints, setSpentUserPoints] = useState(0);
  const [rev_order_date, setRev_order_date] = useState(
    moment().format("DD/MM/YYYY")
  );
  const [rev_time, setRev_time] = useState(moment().format("h.mmA"));
  const outletsOpeningDict = CommonStore.useState((s) => s.outletsOpeningDict);

  //////////////////////////////////////////////////////

  const [lalamoveQuotation, setLalamoveQuotation] = useState({}); // { totalFee: "0.00", totalFeeCurrency: "MYR" }

  const [paymentOptionsList, setPaymentOptionsList] = useState([]);
  const [selectedPaymentOptions, setSelectedPaymentOptions] = useState({});
  const [showPaymentOptions, setShowPaymentOptions] = useState(false);

  const [senderList, setSenderList] = useState(SENDER_DROPDOWN_LIST);
  const [voucherList, setVoucherList] = useState([
    // {
    //   label: 'Birthday Promotion',
    //   value: 'Birthday',
    // },
    // {
    //   label: 'Beverage Off',
    //   value: 'DRINKS',
    // },
  ]);

  const [selectedSender, setSelectedSender] = useState({});
  const [selectedVoucher, setSelectedVoucher] = useState({});
  const [showSender, setShowSender] = useState(false);
  const [showVoucher, setShowVoucher] = useState(false);

  const [cartItemsTotalQuantity, setCartItemsTotalQuantity] = useState(0);

  const [pointsToRedeem, setPointsToRedeem] = useState(0);
  const [pointsToRedeemPackageIdList, setPointsToRedeemPackageIdList] =
    useState([]);
  const [pointsToRedeemAmountList, setPointsToRedeemAmountList] = useState([]);
  const [pointsToRedeemDiscount, setPointsToRedeemDiscount] = useState(0);
  const [usePointsToRedeem, setUsePointsToRedeem] = useState(false);
  const [showPointsToRedeemOptions, setShowPointsToRedeemOptions] =
    useState(false);

  const [lsRedeemedCartItemList, setLsRedeemedCartItemList] = useState([]);

  // const [userInfoName, setUserInfoName] = useState('');
  // const [userInfoPhone, setUserInfoPhone] = useState('');
  const userInfoName = UserStore.useState((s) => s.userInfoName);
  const userInfoPhone = UserStore.useState((s) => s.userInfoPhone);

  const userPointsBalance = UserStore.useState((s) => s.userPointsBalance);

  const pointsRedeemItemSkuDict = CommonStore.useState(
    (s) => s.pointsRedeemItemSkuDict
  );
  const pointsRedeemCategoryNameDict = CommonStore.useState(
    (s) => s.pointsRedeemCategoryNameDict
  );

  const selectedOutletItemsSkuDict = CommonStore.useState(
    (s) => s.selectedOutletItemsSkuDict
  );

  const voucherIdRedemptionList = UserStore.useState(
    (s) => s.voucherIdRedemptionList
  );

  const paymentDetails = CommonStore.useState((s) => s.paymentDetails);

  //////////////////////////////////////////////////////

  // reservation related

  const isPlacingReservation = CommonStore.useState(s => s.isPlacingReservation);
  const isDepositOnly = CommonStore.useState(s => s.isDepositOnly);
  const resFirstName = CommonStore.useState(s => s.resFirstName);
  const resLastName = CommonStore.useState(s => s.resLastName);
  const resPhoneNum = CommonStore.useState(s => s.resPhoneNum);
  const resEmail = CommonStore.useState(s => s.resEmail);
  const resRemarks = CommonStore.useState(s => s.resRemarks);
  const resDietaryRestrictions = CommonStore.useState(s => s.resDietaryRestrictions);
  const resSpecialOccasions = CommonStore.useState(s => s.resSpecialOccasions);

  const selectedReservationId = CommonStore.useState(s => s.selectedReservationId);
  const selectedReservationStartTime = CommonStore.useState(s => s.selectedReservationStartTime);
  const selectedReservationPax = CommonStore.useState(s => s.selectedReservationPax);

  //////////////////////////////////////////////////////

  useEffect(() => {
    if (!isPlacingReservation &&
      (orderType === ORDER_TYPE.DINEIN || orderType === ORDER_TYPE.PICKUP)) {
      readStoredData();
    }
  }, [isPlacingReservation, orderType]);

  const readStoredData = async () => {
    const storedUserName = await AsyncStorage.getItem("storedUserName");
    var storedUserPhone = await AsyncStorage.getItem("storedUserPhone");

    if (storedUserName && storedUserPhone) {
      storedUserPhone = storedUserPhone.replace(/[^0-9]/g, '');

      if (!storedUserPhone.startsWith('6')) {
        storedUserPhone = `6${storedUserPhone}`;
      }

      UserStore.update((s) => {
        s.userInfoName = storedUserName;
        s.userInfoPhone = storedUserPhone;
      });
    }
  };

  //////////////////////////////////////////////////////

  useEffect(() => {
    if (isPlacingReservation) {
      UserStore.update(s => {
        s.userInfoName = `${resFirstName} ${resLastName}`;
        s.userInfoPhone = resPhoneNum;
      });
    }
  }, [resFirstName, resLastName, resPhoneNum, isPlacingReservation]);

  //////////////////////////////////////////////////////

  useEffect(() => {
    if (paymentDetails !== null) {
      const paymentDetailsResult = paymentDetails;

      if (!isLoading) {
        if (selectedOutlet) {
          global.selectedOutlet = selectedOutlet;
        }

        CommonStore.update(
          (s) => {
            s.paymentDetails = null;
          },
          () => {
            if (paymentDetailsResult.statusCode === "00") {
              placeUserOrder(paymentDetailsResult);
            } else {
              // error

              CommonStore.update((s) => {
                s.alertObj = {
                  title: "Error",
                  message: "Payment failed. Please try again.",
                };
              });
            }
          }
        );
      }
      else {
        if (selectedOutlet) {
          global.selectedOutlet = selectedOutlet;
        }

        CommonStore.update(
          (s) => {
            s.paymentDetails = null;
          },
          () => {
            if (paymentDetailsResult.statusCode === "00") {
              placeUserOrder(paymentDetailsResult);
            } else {
              // error

              CommonStore.update((s) => {
                s.alertObj = {
                  title: "Error",
                  message: "Payment failed. Please try again.",
                };
              });
            }
          }
        );
      }
    }
  }, [paymentDetails, isLoading]);

  // 2022-10-12 - Hide first
  // useEffect(() => {
  //   if (isPlacingReservation) {

  //   }
  //   else {
  //     if (orderType === ORDER_TYPE.DINEIN && userCart.uniqueId === undefined) {
  //       linkTo && linkTo(`${prefix}/scan`);
  //     }
  //   }
  // }, [userCart, orderType, isPlacingReservation]);

  useEffect(() => {
    if (linkTo) {
      DataStore.update((s) => {
        s.linkToFunc = linkTo;
      });

      global.linkToFunc = linkTo;
    }
  }, [linkTo]);

  useEffect(() => {
    if (selectedOutlet === null) {
      readStates();
    }

    readCommonStates();
  }, [selectedOutlet]);

  const readStates = async () => {
    console.log('global.selectedoutlet = readStates (1) (==test==)');

    if (selectedOutlet === null) {
      console.log('global.selectedoutlet = readStates (2) (==test==)');

      // const commonStoreDataRaw = await AsyncStorage.getItem("@commonStore");
      const commonStoreDataRaw = await idbGet('@commonStore');
      if (commonStoreDataRaw !== undefined) {
        console.log('global.selectedoutlet = readStates (3) (==test==)');

        const commonStoreData = JSON.parse(commonStoreDataRaw);

        const latestOutletId = await AsyncStorage.getItem("latestOutletId");

        console.log('latestOutletId');
        console.log(latestOutletId);
        console.log('commonStoreData.selectedOutlet');
        console.log(commonStoreData.selectedOutlet);

        if (
          commonStoreData.selectedOutlet &&
          latestOutletId === commonStoreData.selectedOutlet.uniqueId
        ) {
          // check if it's the same outlet user scanned

          console.log('global.selectedoutlet = readStates (4) (==test==)');

          if (isPlacingReservation) {
          } else {
            // if (
            //   commonStoreData.orderType === ORDER_TYPE.DINEIN 
            //   // 2022-10-08 - Try to disable this
            //   // &&
            //   // commonStoreData.userCart.uniqueId === undefined
            // ) {
            //   // logout the user

            //   linkTo && linkTo(`${prefix}/scan`);
            // }
          }

          console.log('global.selectedoutlet = commonStoreData.selectedOutlet (3) (==test==)');

          global.selectedOutlet = commonStoreData.selectedOutlet;

          CommonStore.replace(commonStoreData);

          // const userStoreDataRaw = await AsyncStorage.getItem("@userStore");
          const userStoreDataRaw = await idbGet('@userStore');
          if (userStoreDataRaw !== undefined) {
            const userStoreData = JSON.parse(userStoreDataRaw);

            UserStore.replace(userStoreData);
          }

          // const dataStoreDataRaw = await AsyncStorage.getItem('@dataStore');
          const dataStoreDataRaw = await idbGet('@dataStore');
          if (dataStoreDataRaw !== undefined) {
            const dataStoreData = JSON.parse(dataStoreDataRaw);

            DataStore.replace(dataStoreData);
            // DataStore.replace({
            //   ...dataStoreData,
            //   ...dataStoreData.linkToFunc !== undefined && {
            //     linkToFunc: dataStoreData.linkToFunc,
            //   },
            // });
          }

          // const tableStoreDataRaw = await AsyncStorage.getItem("@tableStore");
          const tableStoreDataRaw = await idbGet('@tableStore');
          if (tableStoreDataRaw !== undefined) {
            const tableStoreData = JSON.parse(tableStoreDataRaw);

            TableStore.replace(tableStoreData);
          }

          // const paymentStoreDataRaw = await AsyncStorage.getItem("@paymentStore");
          const paymentStoreDataRaw = await idbGet('@paymentStore');
          if (paymentStoreDataRaw !== undefined) {
            const paymentStoreData = JSON.parse(paymentStoreDataRaw);

            PaymentStore.replace(paymentStoreData);
          }

          // const dataStoreDataRaw = await AsyncStorage.getItem('@dataStore');
          // if (dataStoreDataRaw !== undefined) {
          //   const dataStoreData = JSON.parse(dataStoreDataRaw);

          //   DataStore.replace(dataStoreData);
          // }
        }
      }
    }
  };

  const readCommonStates = async () => {
    // if (!linkToFunc) {
    //   const dataStoreDataRaw = await AsyncStorage.getItem('@dataStore');
    //   if (dataStoreDataRaw !== undefined) {
    //     const dataStoreData = JSON.parse(dataStoreDataRaw);
    //     DataStore.replace(dataStoreData);
    //   }
    // }
  };

  useEffect(() => {
    var voucherListTemp = [];

    for (var i = 0; i < voucherIdRedemptionList.length; i++) {
      const record = voucherIdRedemptionList[i];

      // to-do: check for start/end date/time, min/max spend
      if (record.usageDate === null) {
        var voucherLogoSource = {
          uri: "",
        };

        if (merchantVouchersDict[record.voucherId]) {
          if (merchantVouchersDict[record.voucherId].customLogo) {
            voucherLogoSource = {
              uri: merchantVouchersDict[record.voucherId].customLogo,
            };
          } else if (merchantVouchersDict[record.voucherId].merchantLogo) {
            voucherLogoSource = {
              uri: merchantVouchersDict[record.voucherId].merchantLogo,
            };
          } else {
            voucherLogoSource = require("../asset/image/extend.png");
          }
        }

        voucherListTemp.push({
          label:
            (merchantVouchersDict[record.voucherId]
              ? merchantVouchersDict[record.voucherId].title
              : "") + ` [${record.code}]`,
          value: record.voucherId,
          code: record.code,
          img: voucherLogoSource,
        });
      }
    }

    setVoucherList(voucherListTemp);
  }, [voucherIdRedemptionList, merchantVouchersDict]);

  useEffect(() => {
    CommonStore.update((s) => {
      s.currPage = "Cart";
    });

    return () => {
      CommonStore.update((s) => {
        s.currPage = "";
      });
    };
  }, []);

  useEffect(() => {
    if (selectedOutlet && selectedOutlet.uniqueId && orderType) {
      var paymentOptionsTemp = "";

      if (orderType === ORDER_TYPE.DELIVERY) {
        paymentOptionsTemp = selectedOutlet.deliveryPaymentOptions;
      } else if (orderType === ORDER_TYPE.PICKUP) {
        paymentOptionsTemp = selectedOutlet.pickupPaymentOptions;
      } else if (orderType === ORDER_TYPE.DINEIN) {
        if (isPlacingReservation) {
          // default change to online only

          paymentOptionsTemp = USER_ORDER_PAYMENT_OPTIONS.ALL;
        }
        else {
          if (selectedOutletTableQRUrl) {
            // means is dynamic qr

            paymentOptionsTemp = selectedOutlet.dineinPaymentOptions;
          }
          else {
            // means is static/generic qr

            paymentOptionsTemp = selectedOutlet.dineinPaymentOptionsGeneric || USER_ORDER_PAYMENT_OPTIONS.ONLINE;
          }
        }
      }

      var paymentOptionsListTemp = [];

      if (paymentOptionsTemp === USER_ORDER_PAYMENT_OPTIONS.ONLINE) {
        // paymentOptionsListTemp.push({
        //   label: "Online",
        //   value: USER_ORDER_PAYMENT_OPTIONS.ONLINE,
        // });

        paymentOptionsListTemp.push({
          label: "Pay Now",
          value: USER_ORDER_PAYMENT_OPTIONS.ONLINE,
        });

        paymentOptionsListTemp.push({
          label: "Pay Later",
          value: USER_ORDER_PAYMENT_OPTIONS.OFFLINE,
        });
      } else if (paymentOptionsTemp === USER_ORDER_PAYMENT_OPTIONS.OFFLINE) {
        // paymentOptionsListTemp.push({
        //   label: "Offline",
        //   value: USER_ORDER_PAYMENT_OPTIONS.OFFLINE,
        // });

        paymentOptionsListTemp.push({
          label: "Pay Now",
          value: USER_ORDER_PAYMENT_OPTIONS.ONLINE,
        });

        paymentOptionsListTemp.push({
          label: "Pay Later",
          value: USER_ORDER_PAYMENT_OPTIONS.OFFLINE,
        });
      } else if (paymentOptionsTemp === USER_ORDER_PAYMENT_OPTIONS.ALL) {
        paymentOptionsListTemp.push({
          label: "Pay Now",
          value: USER_ORDER_PAYMENT_OPTIONS.ONLINE,
        });

        paymentOptionsListTemp.push({
          label: "Offline",
          value: USER_ORDER_PAYMENT_OPTIONS.OFFLINE,
        });
      }

      setPaymentOptionsList(paymentOptionsListTemp);

      setSelectedPaymentOptions(paymentOptionsListTemp[0]);
    }
  }, [selectedOutlet, orderType, isPlacingReservation]);

  useEffect(() => {
    if (selectedOutlet && selectedOutlet.uniqueId && orderType) {
      // var senderListTemp = [
      //   {
      //     label: 'Lalamove',
      //     value: COURIER_CODE.LALAMOVE,
      //   },
      //   {
      //     label: 'Mr. Speedy',
      //     value: COURIER_CODE.MRSPEEDY,
      //   },
      // ];

      // setSenderList(senderListTemp);

      setSelectedSender(senderList[0]);
    }
  }, [selectedOutlet, orderType]);

  useEffect(() => {
    console.log("utc time");
    console.log(moment().utc().toISOString());

    console.log("delay utc time");
    console.log(moment().add(totalPrepareTime, "second").utc().toISOString());

    console.log("current time");
    console.log(moment().toISOString());

    console.log("delay current time");
    console.log(moment().add(totalPrepareTime, "second").toISOString());

    console.log("totalPrepareTime");
    console.log(totalPrepareTime);

    if (
      orderType === ORDER_TYPE.DELIVERY &&
      // selectedUserAddress &&
      // selectedUserAddress.uniqueId &&
      selectedAddress &&
      selectedAddressLocation.lat &&
      selectedAddressLocation.lng &&
      selectedAddressUserName &&
      selectedAddressUserPhone &&
      selectedOutlet &&
      selectedOutlet.uniqueId &&
      totalPrepareTime > 0
    ) {
      if (selectedSender.value === COURIER_CODE.LALAMOVE) {
        var body = {
          outletLat: selectedOutlet.lat,
          outletLng: selectedOutlet.lng,
          outletAddress: selectedOutlet.address,

          outletPhone: selectedOutlet.phone,
          outletName: selectedOutlet.name,

          // userLat: selectedUserAddress.lat,
          // userLng: selectedUserAddress.lng,
          // userAddress: selectedUserAddress.address,
          userLat: selectedAddressLocation.lat,
          userLng: selectedAddressLocation.lng,
          userAddress: selectedAddress,

          // userName: userName,
          // userPhone: userNumber,
          userName: selectedAddressUserName,
          userPhone: selectedAddressUserPhone,
          // userRemarks: selectedUserAddress.note,
          userRemarks: selectedAddressNote,

          scheduleAt: moment()
            .add(totalPrepareTime, "second")
            .add(15, "minute")
            .utc()
            .toISOString(),
        };

        console.log("quotation body");
        console.log(body);

        // ApiClient.POST(API.lalamoveQuotation, body).then((result) => {
        //   console.log("lalamove quotation result");

        //   if (result === undefined) {
        //     // means lalamove can't deliver to this address

        //     window.confirm(
        //       "Info\nSorry, we unable to deliver to this address, please try another one."
        //     );

        //     setDeliveryQuotation({});
        //   } else if (result && result.totalFee) {
        //     // { totalFee: "0.00", totalFeeCurrency: "MYR" }

        //     setDeliveryQuotation({
        //       totalFee: parseFloat(result.totalFee),
        //       totalFeeCurrency: result.totalFeeCurrency,
        //       courierCode: COURIER_CODE.LALAMOVE,
        //     });
        //   }
        // });
      }
      // Mr Speedy excluded
      // else if (selectedSender.value === COURIER_CODE.MRSPEEDY) {
      //   const totalWeightKg = Math.ceil(cartItemsTotalQuantity * 0.2); // for now we assume each item weighted 200 gram

      //   var body = {
      //     outletLat: selectedOutlet.lat,
      //     outletLng: selectedOutlet.lng,
      //     outletAddress: selectedOutlet.address,

      //     outletPhone: selectedOutlet.phone,
      //     outletName: selectedOutlet.name,

      //     userLat: selectedUserAddress.lat,
      //     userLng: selectedUserAddress.lng,
      //     userAddress: selectedUserAddress.address,

      //     userName: userName,
      //     userPhone: userNumber,
      //     userRemarks: selectedUserAddress.note,

      //     totalWeightKg: totalWeightKg,
      //     outletRequiredStartDatetime: moment().add(totalPrepareTime, 'second').add(5, 'minute').utc().toISOString(),
      //     outletRequiredFinishDatetime: moment().add(totalPrepareTime, 'second').add(10, 'minute').utc().toISOString(),
      //     userRequiredStartDatetime: moment().add(totalPrepareTime, 'second').add(15, 'minute').utc().toISOString(),
      //     userRequiredFinishDatetime: moment().add(totalPrepareTime, 'second').add(30, 'minute').utc().toISOString(),
      //   };

      //   console.log('quotation body');
      //   console.log(body);

      //   ApiClient.POST(API.mrSpeedyCalculateOrder, body).then((result) => {
      //     console.log("mrspeedy quotation result");
      //     console.log(result);

      //     if (!result || !result.is_successful) {
      //       // means lalamove can't deliver to this address

      //       window.confirm(
      //         'Info',
      //         'Sorry, we unable to deliver to this address, please try another one.',
      //       );

      //       setDeliveryQuotation({});
      //     }
      //     else if (result.is_successful && result.order && result.order.payment_amount) {
      //       // { totalFee: "0.00", totalFeeCurrency: "MYR" }

      //       setDeliveryQuotation({
      //         totalFee: parseFloat(result.order.payment_amount),
      //         totalFeeCurrency: 'MYR',
      //         courierCode: COURIER_CODE.MRSPEEDY,
      //       });
      //     }
      //   });
      // }
      else if (selectedSender.value === COURIER_CODE.TELEPORT) {
        const totalWeightKg = Math.ceil(cartItemsTotalQuantity * 0.2); // for now we assume each item weighted 200 gram

        var body = {
          outletLat: selectedOutlet.lat,
          outletLng: selectedOutlet.lng,
          outletAddress: selectedOutlet.address,

          outletPhone: selectedOutlet.phone,
          outletName: selectedOutlet.name,

          userLat: selectedUserAddress.lat,
          userLng: selectedUserAddress.lng,
          userAddress: selectedUserAddress.address,

          userName: userName,
          userPhone: userNumber,
          userRemarks: selectedUserAddress.note,

          outletEmail: selectedOutlet.email,
          userEmail: userEmail,

          pickupDatetime: moment()
            .add(totalPrepareTime, "second")
            .add(10, "minute")
            .utc()
            .toISOString(),

          totalWeightKg: totalWeightKg,
        };

        console.log("quotation body");
        console.log(body);

        // ApiClient.POST(API.teleportOrderEstimatePricing, body).then(
        //   (result) => {
        //     console.log("teleport quotation result");
        //     console.log(result);

        //     if (!result) {
        //       // means teleport can't deliver to this address

        //       window.confirm(
        //         "Info\nSorry, we unable to deliver to this address, please try another one."
        //       );

        //       setDeliveryQuotation({});
        //     } else if (result && result.total_amount) {
        //       // { totalFee: "0.00", totalFeeCurrency: "MYR" }

        //       setDeliveryQuotation({
        //         totalFee: parseFloat(result.total_amount),
        //         totalFeeCurrency: "MYR",
        //         courierCode: COURIER_CODE.TELEPORT,
        //       });
        //     }
        //   }
        // );
      }
    }
  }, [
    selectedUserAddress,
    selectedOutlet,
    totalPrepareTime,
    orderType,

    selectedSender,
    cartItemsTotalQuantity,

    selectedAddress,
    selectedAddressLocation,
    selectedAddressNote,
    selectedAddressUserName,
    selectedAddressUserPhone,
  ]);

  useEffect(() => {
    if (selectedVoucherId && selectedOutlet && selectedOutlet.uniqueId) {
      if (
        merchantVouchersDict[selectedVoucherId] &&
        merchantVouchersDict[selectedVoucherId].merchantId ===
        selectedOutlet.merchantId
      ) {
        // means valid

        if (
          merchantVouchersDict[selectedVoucherId].type ===
          MERCHANT_VOUCHER_TYPE.CASH_VOUCHER
        ) {
          setTotalDiscount(merchantVouchersDict[selectedVoucherId].value);
        } else if (
          merchantVouchersDict[selectedVoucherId].type ===
          MERCHANT_VOUCHER_TYPE.PERCENTAGE_VOUCHER
        ) {
          setTotalDiscount(
            (totalPrice * merchantVouchersDict[selectedVoucherId].value) / 100
          );
        } else {
          setTotalDiscount(0);
        }
      } else {
        setTotalDiscount(0);
      }
    } else {
      setTotalDiscount(0);
    }
  }, [selectedVoucherId, selectedOutlet, totalPrice]);

  useEffect(() => {
    // reset redeemed loyalty stamp items

    setLsRedeemedCartItemList([]);
  }, []);

  useEffect(() => {
    // if (cartItems.length > 0) {
    //   updateCartItemsDict();
    // }

    updateCartItemsDict();
  }, [
    tvCartItems,

    // cartItems,
    // cartItems.length,

    // overrideItemPriceSkuDict,
    // amountOffItemSkuDict,
    // percentageOffItemSkuDict,
    // buy1Free1ItemSkuDict,
    // deliveryItemSkuDict,
    // takeawayItemSkuDict,

    // overrideCategoryPriceNameDict,
    // amountOffCategoryNameDict,
    // percentageOffCategoryNameDict,
    // buy1Free1CategoryNameDict,
    // deliveryCategoryNameDict,
    // takeawayCategoryNameDict,

    // usePointsToRedeemLCC,
    // userPointsBalanceLCC,
    // pointsRedeemItemSkuDictLCC,
    // pointsRedeemCategoryNameDictLCC,

    // pointsRedeemedCartItemListLCC,

    // deliveryQuotation, // for calculating delivery promotions

    // loyaltyStampBuyItemSkuDict,
    // loyaltyStampBuyCategoryNameDict,

    // lsRedeemedCartItemList,

    // selectedLoyaltyCampaign,

    // usePointsToRedeem,
    // userPointsBalance,
    // pointsRedeemItemSkuDict,
    // pointsRedeemCategoryNameDict,

    // selectedTaggableVoucher,

    // overrideItemPriceSkuDictLC,
    // amountOffItemSkuDictLC,
    // percentageOffItemSkuDictLC,
    // buy1Free1ItemSkuDictLC,
    // deliveryItemSkuDictLC,
    // takeawayItemSkuDictLC,

    // overrideCategoryPriceNameDictLC,
    // amountOffCategoryNameDictLC,
    // percentageOffCategoryNameDictLC,
    // buy1Free1CategoryNameDictLC,
    // deliveryCategoryNameDictLC,
    // takeawayCategoryNameDictLC,
  ]);

  const updateCartItemsDict = async () => {
    setIsCartLoading(true);

    if (tvCartItems.length > 0) {
      CommonStore.update((s) => {
        s.isLoading = true;
      });
    }

    var tempCartOutletItemsDict = {
      ...cartOutletItemsDict,
    };

    var tempCartOutletItemAddOnDict = {
      ...cartOutletItemAddOnDict,
    };

    var tempCartOutletItemAddOnChoiceDict = {
      ...cartOutletItemAddOnChoiceDict,
    };

    var tempOutletsTaxDict = {
      ...outletsTaxDict,
    };

    var promotionIdAppliedListTemp = [];

    var tempCartItemsProcessed = [];

    var tempTotalPrice = 0;
    var tempTotalPrepareTime = 0;

    var currOutletId = "";

    var cartItemsTotalQuantityTemp = 0;

    var userPointsBalanceTemp = userPointsBalance;
    var pointsToRedeemPackageIdListTemp = [];
    var pointsToRedeemAmountListTemp = [];
    var pointsToRedeemDiscountTemp = 0;
    var pointsToRedeemTemp = 0;

    var cartItemPointsDeducted = 0;
    var cartItemAmountDeducted = 0;

    /////////////////////////////////////////

    var cartItemPointsDeductedLCC = 0;
    var cartItemAmountDeductedLCC = 0;

    /////////////////////////////////////////

    var cartItemQuantitySummaries = [];

    var discountPromotionsTotalTemp = 0;
    var discountPromotionsTotalLCCTemp = 0;
    var discountLoyaltyStampsTotalTemp = 0;

    var deliveryDiscountAmount = 0;
    var takeawayDiscountAmount = 0;

    var loyaltyStampBuyListTemp = [];
    var loyaltyStampGetListTemp = [];

    var cartItemQuantitySummariesLC = [];

    ///////////////////////////////////////////////////////////////////////

    // 2022-08-02 - Buy vouchers online

    if (tvCartItems.length > 0) {
      tempCartItemsProcessed = [
        {
          itemId: tvCartItems[0].taggableVoucherId,

          remarks: '',
          cartItemDate: tvCartItems[0].cartItemDate,

          image: tvCartItems[0].image,
          name: tvCartItems[0].name,
          itemName: tvCartItems[0].itemName,
          price: tvCartItems[0].price.toFixed(2),
          totalPrice: tvCartItems[0].totalPrice.toFixed(2),
          // quantity: tempCartItem.extendQuantity,

          orderType: orderType, // might relocate in other places in future: 
        }
      ];

      tempTotalPrice = tvCartItems[0].totalPrice;
    }

    ///////////////////////////////////////////////////////////////////////

    CommonStore.update((s) => {
      // s.outletsTaxDict = tempOutletsTaxDict;
      // s.cartOutletItemsDict = tempCartOutletItemsDict;
      // s.cartOutletItemAddOnDict = tempCartOutletItemAddOnDict;
      // s.cartOutletItemAddOnChoiceDict = tempCartOutletItemAddOnChoiceDict;
      s.tvCartItemsProcessed = tempCartItemsProcessed;
    });

    setTotalPrice(tempTotalPrice);

    // if (tempOutletsTaxDict && currOutletId) {
    //   setTotalTax(tempTotalPrice * tempOutletsTaxDict[currOutletId].rate);
    // }

    if (selectedOutlet && selectedOutlet.taxActive) {
      setTotalTax(tempTotalPrice * selectedOutlet.taxRate);
    } else {
      setTotalTax(0);
    }

    if (selectedOutlet && selectedOutlet.scActive) {
      setTotalSc(tempTotalPrice * selectedOutlet.scRate);
    } else {
      setTotalSc(0);
    }

    // setTotalPrepareTime(tempTotalPrepareTime);

    // setCartItemsTotalQuantity(cartItemsTotalQuantityTemp);

    // setDiscountPromotionsTotal(discountPromotionsTotalTemp);

    // setDiscountPromotionsTotalLCC(discountPromotionsTotalLCCTemp);

    // /////////////////////////////////////////////////////////

    // setPointsToRedeemDiscount(pointsToRedeemDiscountTemp);
    // setPointsToRedeemPackageIdList(pointsToRedeemPackageIdListTemp);
    // setPointsToRedeemAmountList(pointsToRedeemAmountListTemp);
    // setPointsToRedeem(Math.ceil(pointsToRedeemTemp));

    // if (Math.ceil(pointsToRedeemTemp) > 0) {
    //   setShowPointsToRedeemOptions(true);
    // } else {
    //   setShowPointsToRedeemOptions(false);
    // }

    // setLoyaltyStampBuyList(loyaltyStampBuyListTemp);
    // setLoyaltyStampGetList(loyaltyStampGetListTemp);

    /////////////////////////////////////////////////////////

    CommonStore.update((s) => {
      s.isLoading = false;
    });

    setIsCartLoading(false);
  };

  const setState = () => { };

  console.log("orderType Cart");
  console.log(orderType);

  navigation.setOptions({
    headerLeft: () => (
      <TouchableOpacity
        style={{}}
        onPress={async () => {
          // props.navigation.goBack();

          const subdomain = await AsyncStorage.getItem("latestSubdomain");

          if (!subdomain) {
            linkTo && linkTo(`${prefix}/outlet/menu`);
          } else {
            linkTo && linkTo(`${prefix}/outlet/${subdomain}/menu`);
          }
        }}
      >
        <View
          style={{
            marginLeft: 10,
            display: "flex",
            flexDirection: "row",
            alignItems: "center",
            justifyContent: "flex-start",
          }}
        >
          <Ionicons
            name="chevron-back"
            size={26}
            color={Colors.fieldtTxtColor}
            style={{}}
          />

          <Text
            style={{
              color: Colors.fieldtTxtColor,
              fontSize: 16,
              textAlign: "center",
              fontFamily: "NunitoSans-Regular",
              lineHeight: 22,
              marginTop: -1,
            }}
          >
            Back
          </Text>
        </View>
      </TouchableOpacity>
    ),
    headerRight: () => (
      <TouchableOpacity
        onPress={() => {
          props.navigation.navigate("Profile");
        }}
        style={{
          opacity: 0,
        }}
      >
        <View style={{ marginRight: 15 }}>
          <Ionicons name="menu" size={30} color={Colors.primaryColor} />
        </View>
      </TouchableOpacity>
    ),
    headerTitle: () => (
      <View
        style={{
          justifyContent: "center",
          alignItems: "center",
          bottom: -1,
        }}
      >
        <Text
          style={{
            fontSize: 20,
            lineHeight: 25,
            textAlign: "center",
            fontFamily: "NunitoSans-Bold",
            color: Colors.mainTxtColor,
          }}
        >
          My Cart
        </Text>
      </View>
    ),
  });

  const MenuItem = (param) => {
    ApiClient.GET(API.getItemAddOnChoice + param).then((result) => {
      setState({ menuItemDetails: result });
    });
  };

  useEffect(() => {
    // console.log("CART OUTLET ID", Cart.getOutletId())
    // getCartItem();
    // Cart.setRefreshCartPage(getCartItem.bind(this));
    // console.log("CART OUTLET", outletData)
    // ApiClient.GET(API.merchantMenu + outletData.id).then(
    //   (result) => {
    //     console.log("777");
    //     console.log(result);
    //     if (result != undefined) {
    //       if (result.length > 0) {
    //         setState({
    //           category: result[0].category,
    //           menu: result[0].items,
    //         });
    //       }
    //       setState({ outletMenu: result });
    //     }
    //     else {
    //       setState({
    //         category: result[0].category,
    //         menu: result[0].items,
    //       });
    //       setState({ outletMenu: [] });
    //     }
    //   }
    // );

    getPopular();
    // setInterval(() => {
    //   setState({ deliveryQuotation: Cart.getDeliveryQuotation() == null ? null : Cart.getDeliveryQuotation() })
    // }, 2500);

    // set delivery/pickup button status
    if (Cart.getOrderType() === 1) {
      setState({ clicked: 1, clicked1: 0, shouldShow: true });
    }
    if (Cart.getOrderType() === 2) {
      setState({ clicked1: 1, clicked: 0, shouldShow: false });
    }
  }, []);

  // function here
  const sum = (key) => {
    // return reduce((a, b) => a + (b[key] || 0), 0);
  };

  const getCartItem = () => {
    setState({ cartItem: Cart.getCartItem() }, () => {
      calculateFinalTotal();
    });
    console.log("888", Cart.getCartItem());
  };

  const checkDuplicate = (item, i, j) => {
    for (const r of item) {
      if (r[0] == i && r[1] == j) {
        return true;
      }
    }
    return false;
  };

  const getPopular = () => {
    var newList = [];
    var randomList = [];
    // ApiClient.GET(API.activeMenu + outletData.id).then((result) => {
    //   var maxItem = 0
    //   for (const item of result) {
    //     maxItem = maxItem + item.items.length
    //   }
    //   var maxIteration = 5
    //   if (maxItem < 5) {
    //     maxIteration = maxIteration
    //   }
    //   var k;
    //   for (k = 0; k < maxIteration; k++) {
    //     var i = Math.floor(Math.random() * result.length)
    //     var j = Math.floor(Math.random() * result[i].items.length)

    //     while (checkDuplicate(randomList, i, j)) {
    //       i = Math.floor(Math.random() * result.length)
    //       j = Math.floor(Math.random() * result[i].items.length)
    //     }
    //     newList.push(result[i].items[j])
    //     randomList.push([i, j])
    //     setState({ popular: newList })
    //   }
    // });
  };

  const goToPopular = (item) => {
    // console.log(outletData, "outletData")
    // props.navigation.navigate('MenuItemDetails', {
    //   refresh: refresh.bind(this),
    //   menuItem: item,
    //   outletData: outletData,
    //   // test: test
    // })
  };

  const renderPopularItem = ({ item, index }) => {
    return (
      <View
        style={{
          width: 180,
          height: 80,
          borderWidth: StyleSheet.hairlineWidth,
          marginRight: 10,
          borderRadius: 10,
          justifyContent: "space-around",
        }}
      >
        <Text
          numberOfLines={2}
          style={{ fontSize: 16, fontWeight: "400", marginLeft: 10 }}
        >
          {item.name}
        </Text>
        <View style={{ flexDirection: "row", marginLeft: 10, marginTop: 10 }}>
          <Text style={{ fontSize: 16, color: "#9c9c9c" }}>{item.price}</Text>
          <TouchableOpacity
            style={{ marginLeft: 70 }}
            onPress={() => {
              goToPopular(item);
            }}
          >
            <Close name="pluscircle" size={22} color={Colors.primaryColor} />
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  const refresh = () => {
    setState({ refresh: true });
  };

  const onChangeQty = (e, id) => {
    // const cartItem = cartItem;
    const cartItem = Cart.getCartItem();
    const item = cartItem.find((obj) => obj.itemId === id);
    item.quantity = item.quantity - e;
    if (item.quantity == 0) {
      Cart.deleteCartItem(id);
    } else {
      setState({
        cartItem,
      });
      Cart.updateCartItem(
        Cart.getCartItem().find((obj) => obj.itemId === id),
        item
      );
    }
    console.log(
      "CART",
      Cart.getCartItem().find((obj) => obj.itemId === id)
    );
  };

  const optional = (id) => {
    const cartItem = cartItem;
    console.log(cartItem);
    const item = cartItem.find((obj) => obj.itemId === id);
    if (item.fireOrder == 0) {
      item.fireOrder = 1;
    } else if (item.fireOrder == 1) {
      item.fireOrder = 0;
    }
    setState({
      cartItem,
    });
  };

  const extend = (id, day) => {
    const body = {
      parkingId: id,
      dayExtend: day,
      paymentMethod: "",
    };
    console.log("PARKING BODY", body);
    // ApiClient.POST(API.extendParking, body).then((result) => {
    //   console.log(result);
    //   if (result != null) {
    //     window.confirm(
    //       'Success',
    //       'You have successful extended',
    //       [{ text: 'OK', onPress: () => { props.navigation.navigate('ConfirmOrder', { outletData: outletData }); } }],
    //       { cancelable: false },
    //     );
    //     User.getRefreshCurrentAction();
    //     Cart.clearCart();
    //     getCartItem();
    //   }
    // })
  };

  const newPurchase = (outletId, itemId, qty) => {
    const body = {
      userId: User.getUserId(),
      outletId: outletId,
      itemId: itemId,
      quantity: qty,
      waiterId: 1,
    };
    // ApiClient.POST(API.createUserParking, body).then((result) => {
    //   console.log(result);
    //   if (result != null) {
    //     window.confirm(
    //       'Success',
    //       'You have successful Parked',
    //       [{ text: 'OK', onPress: () => { props.navigation.navigate('ConfirmOrder', { outletData: outletData }); } }],
    //       { cancelable: false },
    //     );
    //     User.getRefreshCurrentAction();
    //     Cart.clearCart();
    //     getCartItem();
    //   }
    // })
  };

  const clearCartItems = async () => {
    // safelyExecuteIdb(() => {
    //   // AsyncStorage.removeItem(`${firebaseUid}.cartItems`);
    //   idbDel(`cartItems`);
    //   // AsyncStorage.removeItem(`${firebaseUid}.cartOutletId`);
    //   idbDel(`cartOutletId`);
    // });

    CommonStore.update((s) => {
      s.tvCartItems = [];
      s.tvCartItemsProcessed = [];

      // s.cartOutletId = null;
    });
  };

  const placeUserOrder = async (paymentDetails, skipUserInfo = false) => {
    // var orderBodyType = '';
    // switch (Cart.getOrderType()) {
    //   case 0:
    //     orderBodyType = 'Dine In';
    //     break;
    //   case 1:
    //     orderBodyType = 'Take Away';
    //     break;
    //   case 2:
    //     orderBodyType = 'Pick Up';
    //     break;
    // }

    if (selectedTaggableVoucherPurchase && selectedTaggableVoucherPurchase.uniqueId) {
      var userPhone = phone.replaceAll('-', '').replaceAll(' ', '').replaceAll('+', '');

      // if (userPhone.startsWith('6')) {
      //   userPhone = userPhone.slice(1);
      // }

      userPhone = userPhone.replace(/[^0-9]/g, '');

      if (!userPhone.startsWith('6')) {
        userPhone = '6' + userPhone;
      }

      if (userPhone && name) {

      }
      else {
        alert('Please fill in the name and phone number before proceed.');

        return;
      }

      const body = {
        userPhone: userPhone,
        outletId: selectedOutlet.uniqueId,
        merchantId: selectedOutlet.merchantId,
        merchantName: selectedTaggableVoucherPurchase.merchantName || '',
        outletName: selectedOutlet.name || '',
        merchantLogo: selectedTaggableVoucherPurchase.merchantLogo || '',
        outletCover: selectedOutlet.cover || '',
        userName: name,

        dob: moment(birthday).valueOf(),
        email: email || '',

        address: address,
        lat: lat,
        lng: lng,

        taggableVoucherId: selectedTaggableVoucherPurchase.uniqueId,

        userIdAnonymous: userIdAnonymous,
        toConvertAnonymousPoints: true,

        // userOrderId: (registerUserOrder.uniqueId && !registerUserOrder.isCashbackClaimed) ? registerUserOrder.uniqueId : null,
      
        tracking: '9',
      };

      //////////////////////////////////////

      // check validity

      if (selectedTaggableVoucherPurchase && selectedTaggableVoucherPurchase.voucherQuantity > 0) {

      }
      else {
        alert('This voucher is already fully redeemed.');

        return;
      }

      CommonStore.update((s) => {
        s.isLoading = true;

        s.isPlacingOrder = true;
      });

      ApiClient.POST(API.claimTaggableVoucherKweb, body).then(async (result) => {
        if (result && result.status === "success" && result.data && result.data.uniqueId) {
          // const voucherSnapshot = await firebase.firestore().collection(Collections.TaggableVoucher)
          //   .where('uniqueId', '==', selectedTaggableVoucherPurchase.uniqueId)
          //   .limit(1)
          //   .get();

          // var voucher = null;

          // if (!voucherSnapshot.empty) {
          //   voucher = voucherSnapshot.docs[0].data();
          // }

          // if (voucher) {
          //   CommonStore.update(s => {
          //     s.selectedTaggableVoucherPurchase = voucher;
          //   });
          // }

          // alert(`${result.message}`);

          ///////////////////////////////////////////////////

          // place actual order code

          if (!isDepositOnly) {
            var merchant = {};
            var outlet = selectedOutlet;

            // const merchantSnapshot = await firebase.firestore().collection(Collections.Merchant)
            //   .where('uniqueId', '==', outlet.merchantId)
            //   .limit(1)
            //   .get();

            const merchantSnapshot = await getDocs(
              query(
                collection(global.db, Collections.Merchant),
                where('uniqueId', '==', outlet.merchantId),
                limit(1),
              )
            );

            if (!merchantSnapshot.empty) {
              merchant = merchantSnapshot.docs[0].data();
            }

            const totalWeightKg = Math.ceil(cartItemsTotalQuantity * 0.2); // for now we assume each item weighted 200 gram

            const orderDateTemp = moment().valueOf();

            var body = {
              // cartItems: cartItems,
              cartItems: tvCartItemsProcessed,
              promotionIdList: promotionIdAppliedList,

              // tableId: Cart.getTableNumber(),
              // outletId: Cart.getOutletId(),
              // type: Cart.getOrderType() == 0 ? "Dine In" : "Take Away",
              orderType: orderType,
              paymentMethod: "Online Banking",
              // userGiftCardId: null,
              userVoucherId: null,
              userAddressId: selectedUserAddress ? selectedUserAddress.uniqueId : null,
              orderDate: orderDateTemp,
              // voucherType: "",
              // customTable: "",
              // sessionId: 0,
              // remarks: null,
              // collectionTime: rev_date === undefined ? new Date() : new Date(rev_date),
              totalPrice: totalPrice,

              // outletId: cartOutletItemsDict[cartItems[0].itemId].outletId,
              outletId: selectedOutlet.uniqueId,

              merchantId: selectedOutlet.merchantId,
              outletCover: selectedOutlet.cover,
              merchantLogo: selectedTaggableVoucherPurchase.merchantLogo,
              outletName: selectedOutlet.name,
              merchantName: selectedTaggableVoucherPurchase.merchantName,

              tableId:
                orderType === ORDER_TYPE.DINEIN && selectedOutletTableId
                  ? selectedOutletTableId
                  : "",
              tablePax:
                orderType === ORDER_TYPE.DINEIN && selectedOutletTablePax
                  ? selectedOutletTablePax
                  : 0,
              tableCode:
                orderType === ORDER_TYPE.DINEIN && selectedOutletTableCode
                  ? selectedOutletTableCode
                  : "",
              tax: selectedOutlet.taxActive ? totalTax : 0,
              // taxId: selectedOutlet && outletsTaxDict[selectedOutlet.uniqueId] ? outletsTaxDict[selectedOutlet.uniqueId].uniqueId : '',
              taxId: '',
              sc: selectedOutlet.scActive ? totalSc : 0,
              discount: totalDiscount,
              discountPromotionsTotal: discountPromotionsTotal,

              deliveryFeeBefore:
                orderType === ORDER_TYPE.DELIVERY
                  ? deliveryQuotationPromotion.totalFeeBefore
                  : 0,
              deliveryFee:
                orderType === ORDER_TYPE.DELIVERY
                  ? deliveryQuotationPromotion.totalFee
                  : 0,
              courierCode:
                orderType === ORDER_TYPE.DELIVERY
                  ? deliveryQuotationPromotion.courierCode
                  : "",

              waiterId:
                orderType === ORDER_TYPE.DINEIN && selectedOutletWaiterId
                  ? selectedOutletWaiterId
                  : "",
              waiterName:
                orderType === ORDER_TYPE.DINEIN && selectedOutletWaiterName
                  ? selectedOutletWaiterName
                  : "",

              totalPrepareTime: totalPrepareTime,
              estimatedPreparedDate: moment(orderDateTemp)
                .add(totalPrepareTime, "second")
                .valueOf(),

              remarks: "",

              outletAddress: selectedOutlet.address,
              outletPhone: selectedOutlet.phone,
              // outletTaxId: selectedOutlet && outletsTaxDict[selectedOutlet.uniqueId]
              //   ? outletsTaxDict[selectedOutlet.uniqueId].uniqueId
              //   : '',
              outletTaxId: '',
              // outletTaxNumber: selectedOutlet && outletsTaxDict[selectedOutlet.uniqueId]
              //   ? outletsTaxDict[selectedOutlet.uniqueId].taxNumber
              //   : '',
              outletTaxNumber: '',
              // outletTaxName: outletsTaxDict[selectedOutlet.uniqueId].name,
              outletTaxName: '',
              // outletTaxRate: outletsTaxDict[selectedOutlet.uniqueId].rate,
              // outletTaxRate: selectedOutlet.taxActive ? selectedOutlet.taxRate : 0,
              outletTaxRate: '',

              outletScRate: selectedOutlet.scActive ? selectedOutlet.scRate : 0,

              paymentDetails: paymentDetails ? paymentDetails : null,

              ///////////////////////////////////////

              // delivery usage, will not appear in UserOrder collections, used for other apis

              deliveryCurrency:
                orderType === ORDER_TYPE.DELIVERY
                  ? deliveryQuotation.totalFeeCurrency
                    ? deliveryQuotation.totalFeeCurrency
                    : "MYR"
                  : "",

              outletLat: selectedOutlet ? selectedOutlet.lat : null,
              outletLng: selectedOutlet ? selectedOutlet.lng : null,
              outletAddress: selectedOutlet.address,

              outletPhone: selectedOutlet.phone,
              // outletName: selectedOutlet.name,

              // userLat: selectedUserAddress ? selectedUserAddress.lat : null,
              // userLng: selectedUserAddress ? selectedUserAddress.lng : null,
              // userAddress: selectedUserAddress ? selectedUserAddress.address : "",

              // userName: userName,
              // userPhone: userNumber,
              // userRemarks: selectedUserAddress ? selectedUserAddress.note : "",

              userLat: selectedAddressLocation.lat ? selectedAddressLocation.lat : null,
              userLng: selectedAddressLocation.lng ? selectedAddressLocation.lng : null,
              userAddress: selectedAddress ? selectedAddress : "",

              // userName: !skipUserInfo ? (selectedAddressUserName ? selectedAddressUserName : userInfoName) : '',
              // userPhone: !skipUserInfo ? (selectedAddressUserPhone ? selectedAddressUserPhone : userInfoPhone) : '',
              userName: name ? name : '',
              userPhone: userPhone ? userPhone : '',
              userRemarks: selectedAddressNote ? selectedAddressNote : "",

              scheduleAt: moment()
                .add(totalPrepareTime, "second")
                .add(15, "minute")
                .utc()
                .toISOString(),

              ///////////////////////////////////////

              // razer usage

              // orderIdLocal: orderIdLocal,

              orderIdServer: orderIdServer,

              ///////////////////////////////////////

              // mr speedy usage

              totalWeightKg: totalWeightKg,
              outletRequiredStartDatetime: moment()
                .add(totalPrepareTime, "second")
                .add(5, "minute")
                .utc()
                .toISOString(),
              outletRequiredFinishDatetime: moment()
                .add(totalPrepareTime, "second")
                .add(10, "minute")
                .utc()
                .toISOString(),
              userRequiredStartDatetime: moment()
                .add(totalPrepareTime, "second")
                .add(15, "minute")
                .utc()
                .toISOString(),
              userRequiredFinishDatetime: moment()
                .add(totalPrepareTime, "second")
                .add(30, "minute")
                .utc()
                .toISOString(),

              ///////////////////////////////////////

              // points usage

              pointsToRedeem: pointsToRedeem,
              pointsToRedeemPackageIdList: pointsToRedeemPackageIdList,
              pointsToRedeemAmountList: pointsToRedeemAmountList,
              pointsToRedeemDiscount: pointsToRedeemDiscount,
              usePointsToRedeem: usePointsToRedeem,

              ///////////////////////////////////////

              // loyalty stamps

              loyaltyStampBuyIdList: loyaltyStampBuyList.map(
                (item) => item.loyaltyStampId
              ),
              loyaltyStampBuyList: loyaltyStampBuyList,
              loyaltyStampGetIdList: loyaltyStampGetList.map(
                (item) => item.loyaltyStampId
              ),
              loyaltyStampGetList: loyaltyStampGetList,

              ///////////////////////////////////////

              loyaltyCampaignId: selectedLoyaltyCampaign.loyaltyCampaignId || null,
              userLoyaltyCampaignId: selectedLoyaltyCampaign.uniqueId || null,
              //GiftCardId: selectedTaggableVoucher.loyaltyCampaignId || null,
              //UserGiftCardId: selectedTaggableVoucher.uniqueId || null,

              ///////////////////////////////////////

              // points usage (LCC)

              // pointsToRedeemLCC: pointsToRedeemLCC,
              // pointsToRedeemPackageIdListLCC: pointsToRedeemPackageIdListLCC,
              // pointsToRedeemAmountListLCC: pointsToRedeemAmountListLCC,
              // pointsToRedeemDiscountLCC: pointsToRedeemDiscountLCC,
              // usePointsToRedeemLCC: usePointsToRedeemLCC,

              discountPromotionsTotalLCC: discountPromotionsTotalLCC,
              discountPromotionsTotal: discountPromotionsTotal,

              ///////////////////////////////////////

              tableQRUrl:
                orderType === ORDER_TYPE.DINEIN && selectedOutletTableQRUrl
                  ? selectedOutletTableQRUrl
                  : "",

              deliveryPackagingFee: selectedOutlet.deliveryPackagingFee && orderType === ORDER_TYPE.DELIVERY ? selectedOutlet.deliveryPackagingFee : 0,
              pickupPackagingFee: selectedOutlet.pickupPackagingFee && orderType === ORDER_TYPE.PICKUP ? selectedOutlet.pickupPackagingFee : 0,

              isReservationOrder: isPlacingReservation,

              ///////////////////////////////////////

              // 2022-08-02 - Buy vouchers online

              tvCartItems: tvCartItems,
              taggableVoucherId: selectedTaggableVoucherPurchase.uniqueId,
              // userTaggableVoucherId: uuidv4(),
              userTaggableVoucherId: result.data.uniqueId,

              ///////////////////////////////////////
            };

            // if (
            //   selectedVoucherId &&
            //   selectedOutlet &&
            //   selectedOutlet.uniqueId &&
            //   merchantVouchersDict[selectedVoucherId] &&
            //   merchantVouchersDict[selectedVoucherId].merchantId ===
            //   selectedOutlet.merchantId
            // ) {
            //   body.userVoucherId = selectedVoucherId;
            //   body.userVoucherCode = selectedVoucher.code;
            // }

            ApiClient.POST(API.createUserOrderTaggableVoucher, body, {
              timeout: 100000,
            }).then(async (result) => {
              console.log("createUserOrderTaggableVoucher");
              console.log(result);

              if (result && result.uniqueId) {
                // if (result.success == true) {
                // if (type == 1) {
                //   orderDelivery(result);
                // }
                // else if (type == 2) {
                //   orderPickUp(result);
                // }

                // if (selectedOutletTableId.length > 0) {
                //   await deleteUserCart();
                // }

                await clearCartItems();

                if (!isPlacingReservation) {
                  CommonStore.update((s) => {
                    s.alertObj = {
                      title: "Successful",
                      message: "Your order has been placed.",
                    };
                  });

                  window.confirm(
                    "Successful\nYour order has been placed.",
                    [
                      {
                        text: "OK",
                        onPress: () => {
                          //if navFrom Takeaway then jump to Home
                          // if (navFrom == "TAKEAWAY") {
                          //   props.navigation.jumpTo('Home')
                          // }
                          // else {
                          //   props.navigation.popToTop()
                          // }

                          // navigation.jumpTo('Home')

                          ///////////////////////////////////////

                          // props.navigation.navigate("ConfirmOrder", {
                          //   orderResult: result,
                          //   outletData: selectedOutlet,
                          // });

                          CommonStore.update((s) => {
                            s.selectedUserOrder = result;
                          });

                          props.navigation.reset({
                            index: 0,
                            routes: [
                              {
                                name: "OrderHistoryDetail",
                                params: {
                                  orderId: result.uniqueId,
                                },
                              },
                            ],
                          });

                          // props.navigation.navigate("OrderHistoryDetail", {
                          //   orderId: result.uniqueId,
                          // });

                          linkTo && linkTo(`${prefix}/outlet/order-details`);
                        },
                      },
                    ],
                    { cancelable: false }
                  );

                  // User.getRefreshCurrentAction();

                  // Cart.clearCart();

                  // getCartItem();

                  CommonStore.update((s) => {
                    s.isLoading = false;

                    s.isPlacingOrder = false;
                  });

                  const subdomain = await AsyncStorage.getItem("latestSubdomain");

                  if (!subdomain) {
                    linkTo && linkTo(`${prefix}/outlet/menu`);
                  } else {
                    linkTo && linkTo(`${prefix}/outlet/${subdomain}/menu`);
                  }

                }
                else {
                  // if is reservation order

                  let combinedRemarks = resRemarks;
                  if (resDietaryRestrictions) {
                    combinedRemarks += '\n\nDietary Restrictions: \n' + resDietaryRestrictions;
                  }
                  if (resSpecialOccasions) {
                    combinedRemarks += '\n\nSpecial occasions: \n' + resSpecialOccasions;
                  }

                  const body = {
                    pax: selectedReservationPax,
                    reservationTime: selectedReservationStartTime, // unix
                    reservationAvailabilityId: selectedReservationId,

                    merchantId: selectedOutlet.merchantId,
                    outletCover: selectedOutlet.cover,
                    outletId: selectedOutlet.uniqueId,
                    outletName: selectedOutlet.name,

                    userName: resFirstName + ' ' + resLastName,
                    userFirstName: resFirstName,
                    userLastName: resLastName,
                    userPhone: resPhoneNum,
                    userEmail: resEmail,

                    remarks: resRemarks,

                    dRestrictions: resDietaryRestrictions,
                    sOccasions: resSpecialOccasions,

                    userOrderId: result.uniqueId,

                    depositAmount: selectedOutlet.reservationDepositAmount,

                    paymentDetails: paymentDetails,
                  }

                  console.log('body', body);

                  const res = await ApiClient.POST(API.createUserReservation, body);

                  if (res) {
                    CommonStore.update((s) => {
                      s.alertObj = {
                        title: "Successful",
                        message: "Your reservation has been placed.",
                      };
                    });

                    window.confirm(
                      "Successful\nYour reservation has been placed.",
                      [
                        {
                          text: "OK",
                          onPress: () => {
                            //if navFrom Takeaway then jump to Home
                            // if (navFrom == "TAKEAWAY") {
                            //   props.navigation.jumpTo('Home')
                            // }
                            // else {
                            //   props.navigation.popToTop()
                            // }

                            // navigation.jumpTo('Home')

                            ///////////////////////////////////////

                            // props.navigation.navigate("ConfirmOrder", {
                            //   orderResult: result,
                            //   outletData: selectedOutlet,
                            // });

                            CommonStore.update((s) => {
                              s.selectedUserOrder = result;
                            });

                            // props.navigation.reset({
                            //   index: 0,
                            //   routes: [
                            //     {
                            //       name: "OrderHistoryDetail",
                            //       params: {
                            //         orderId: result.uniqueId,
                            //       },
                            //     },
                            //   ],
                            // });

                            // props.navigation.navigate("OrderHistoryDetail", {
                            //   orderId: result.uniqueId,
                            // });
                            //linkTo && linkTo(`${prefix}/order-details`);
                            //linkTo && linkTo(`${prefix}/outlet/order-details`);
                          },
                        },
                      ],
                      { cancelable: false }
                    );

                    // User.getRefreshCurrentAction();

                    // Cart.clearCart();

                    // getCartItem();

                    CommonStore.update((s) => {
                      s.isLoading = false;

                      s.isPlacingOrder = false;
                    });

                    const subdomain = await AsyncStorage.getItem("latestSubdomain");

                    if (!subdomain) {
                      linkTo && linkTo(`${prefix}/outlet/menu`);
                    } else {
                      linkTo && linkTo(`${prefix}/outlet/${subdomain}/reservation`);
                    }
                  }
                  else {
                    window.confirm(
                      "Error\nFailed to place the reservation.",
                      [
                        {
                          text: "OK",
                          onPress: () => {
                            //if navFrom Takeaway then jump to Home
                            // if (navFrom == "TAKEAWAY") {
                            //   props.navigation.jumpTo('Home')
                            // }
                            // else {
                            //   props.navigation.popToTop()
                            // }

                            // navigation.jumpTo('Home')
                            // navigation.navigate('Home');

                            navigation.dispatch(
                              CommonActions.reset({
                                index: 0,
                                routes: [{ name: "Home" }],
                              })
                            );
                          },
                        },
                      ],
                      { cancelable: false }
                    );

                    CommonStore.update((s) => {
                      s.isLoading = false;

                      s.isPlacingOrder = false;
                    });
                  }
                }
                // linkTo && linkTo(`${prefix}/outlet/menu`);
              } else {
                window.confirm(
                  "Error\nFailed to place the order",
                  [
                    {
                      text: "OK",
                      onPress: () => {
                        //if navFrom Takeaway then jump to Home
                        // if (navFrom == "TAKEAWAY") {
                        //   props.navigation.jumpTo('Home')
                        // }
                        // else {
                        //   props.navigation.popToTop()
                        // }

                        // navigation.jumpTo('Home')
                        // navigation.navigate('Home');

                        navigation.dispatch(
                          CommonActions.reset({
                            index: 0,
                            routes: [{ name: "Home" }],
                          })
                        );
                      },
                    },
                  ],
                  { cancelable: false }
                );

                CommonStore.update((s) => {
                  s.isLoading = false;

                  s.isPlacingOrder = false;
                });
              }
            });
          }
          else {
            // deposit only

            let combinedRemarks = resRemarks;
            if (resDietaryRestrictions) {
              combinedRemarks += '\n\nDietary Restrictions: \n' + resDietaryRestrictions;
            }
            if (resSpecialOccasions) {
              combinedRemarks += '\n\nSpecial occasions: \n' + resSpecialOccasions;
            }

            const body = {
              pax: selectedReservationPax,
              reservationTime: selectedReservationStartTime, // unix
              reservationAvailabilityId: selectedReservationId,

              merchantId: selectedOutlet.merchantId,
              outletCover: selectedOutlet.cover,
              outletId: selectedOutlet.uniqueId,
              outletName: selectedOutlet.name,

              userName: resFirstName + ' ' + resLastName,
              userFirstName: resFirstName,
              userLastName: resLastName,
              userPhone: resPhoneNum,
              userEmail: resEmail,

              remarks: resRemarks,

              dRestrictions: resDietaryRestrictions,
              sOccasions: resSpecialOccasions,

              // userOrderId: result.uniqueId,

              depositAmount: selectedOutlet.reservationDepositAmount,

              paymentDetails: paymentDetails,
            }

            console.log('body', body);

            const res = await ApiClient.POST(API.createUserReservation, body);

            if (res) {
              CommonStore.update((s) => {
                s.alertObj = {
                  title: "Successful",
                  message: "Your reservation has been placed.",
                };
              });

              window.confirm(
                "Successful\nYour reservation has been placed.",
                [
                  {
                    text: "OK",
                    onPress: () => {
                      //if navFrom Takeaway then jump to Home
                      // if (navFrom == "TAKEAWAY") {
                      //   props.navigation.jumpTo('Home')
                      // }
                      // else {
                      //   props.navigation.popToTop()
                      // }

                      // navigation.jumpTo('Home')

                      ///////////////////////////////////////

                      // props.navigation.navigate("ConfirmOrder", {
                      //   orderResult: result,
                      //   outletData: selectedOutlet,
                      // });

                      // CommonStore.update((s) => {
                      //   s.selectedUserOrder = result;
                      // });

                      // props.navigation.reset({
                      //   index: 0,
                      //   routes: [
                      //     {
                      //       name: "OrderHistoryDetail",
                      //       params: {
                      //         orderId: result.uniqueId,
                      //       },
                      //     },
                      //   ],
                      // });

                      // props.navigation.navigate("OrderHistoryDetail", {
                      //   orderId: result.uniqueId,
                      // });


                    },
                  },
                ],
                { cancelable: false }
              );

              // User.getRefreshCurrentAction();

              // Cart.clearCart();

              // getCartItem();

              CommonStore.update((s) => {
                s.isLoading = false;

                s.isPlacingOrder = false;
              });

              const subdomain = await AsyncStorage.getItem("latestSubdomain");

              if (!subdomain) {
                linkTo && linkTo(`${prefix}/outlet/menu`);
              } else {
                linkTo && linkTo(`${prefix}/outlet/${subdomain}/reservation`);
              }
            }
            else {
              window.confirm(
                "Error\nFailed to place the reservation.",
                [
                  {
                    text: "OK",
                    onPress: () => {
                      //if navFrom Takeaway then jump to Home
                      // if (navFrom == "TAKEAWAY") {
                      //   props.navigation.jumpTo('Home')
                      // }
                      // else {
                      //   props.navigation.popToTop()
                      // }

                      // navigation.jumpTo('Home')
                      // navigation.navigate('Home');

                      navigation.dispatch(
                        CommonActions.reset({
                          index: 0,
                          routes: [{ name: "Home" }],
                        })
                      );
                    },
                  },
                ],
                { cancelable: false }
              );

              CommonStore.update((s) => {
                s.isLoading = false;

                s.isPlacingOrder = false;
              });
            }
          }

          ///////////////////////////////////////////////////
        }
        else {
          CommonStore.update((s) => {
            s.alertObj = {
              title: "Info",
              message: "Sorry, this voucher is unavailable to purchase for now.",
            };
          });
        }

        CommonStore.update((s) => {
          s.isLoading = true;

          s.isPlacingOrder = true;
        });

        return;
      });

      /////////////////////////////////////////            
    }
    else {
      alert('Invalid voucher to proceed.');
    }
  };

  const placeOrder = () => {
    var orderBodyType = "";
    switch (Cart.getOrderType()) {
      case 0:
        orderBodyType = "Dine In";
        break;
      case 1:
        orderBodyType = "Take Away";
        break;
      case 2:
        orderBodyType = "Pick Up";
        break;
    }

    var body = {
      items: Cart.getCartItem(),
      tableId: Cart.getTableNumber(),
      outletId: Cart.getOutletId(),
      // type: Cart.getOrderType() == 0 ? "Dine In" : "Take Away",
      type: orderBodyType,
      paymentMethod: "Online Banking",
      voucherId: "",
      voucherType: "",
      customTable: "",
      sessionId: 0,
      remarks: null,
      collectionTime: rev_date === undefined ? new Date() : new Date(rev_date),
    };
    console.log("PLACE ORDER BODY", body);

    // ApiClient.POST(API.createOrder, body).then((result) => {
    //   console.log("placeOrder", result);
    //   if (result.success == true) {
    //     if (type == 1) {
    //       orderDelivery(result);
    //     }
    //     else if (type == 2) {
    //       orderPickUp(result);
    //     }

    //     window.confirm(
    //       "Successful",
    //       "Your order have been placed.",
    //       [
    //         {
    //           text: "OK",
    //           onPress: () => {
    //             //if navFrom Takeaway then jump to Home
    //             // if (navFrom == "TAKEAWAY") {
    //             //   // props.navigation.jumpTo('Home')
    //             //   navigation.navigate('Home');
    //             // }
    //             // else {
    //             //   // props.navigation.popToTop()
    //             //   navigation.navigate('Home');
    //             // }

    //             navigation.dispatch(
    //               CommonActions.reset({
    //                 index: 0,
    //                 routes: [{ name: "Home" }]
    //               }));
    //           },
    //         },
    //       ],
    //       { cancelable: false }
    //     );
    //     User.getRefreshCurrentAction();
    //     Cart.clearCart();
    //     getCartItem();
    //   }
    // });
  };

  const orderDelivery = (result) => {
    var deliverBody = {
      orderId: result.id,
      userId: User.getUserId(),
      addressId: Cart.getDeliveryAddress().id,
    };

    // ApiClient.POST(API.createOrderDelivery, deliverBody).then((result) => {
    //   console.log("order delivery", result)
    //   var body = {
    //     "serviceType": "MOTORCYCLE",
    //     "specialRequests": [],
    //     "stops": [
    //       {
    //         // Location information for pick-up point
    //         "location": {
    //           // "lat": outletData.latlng.split(',')[0],
    //           // "lng": outletData.latlng.split(',')[1]
    //           "lat": selectedOutlet.lat,
    //           "lng": selectedOutlet.lng,
    //         },
    //         "addresses": {
    //           "ms_MY": {
    //             "displayString": selectedOutlet.address,
    //             "country": "MY_KUL"
    //           }
    //         }
    //       },
    //       {
    //         // Location information for drop-off point (#1)
    //         "location": {
    //           "lat": selectedUserAddress.lat,
    //           "lng": selectedUserAddress.lng,
    //         },
    //         "addresses": {
    //           "ms_MY": {
    //             "displayString": selectedUserAddress.address,
    //             "country": "MY_KUL"
    //           }
    //         }
    //       }
    //     ],
    //     // Pick-up point copntact details
    //     "requesterContact": {
    //       "name": "Chris Wong", //get outlet person in charge?
    //       "phone": "0376886555" //or get waiter?
    //     },
    //     "deliveries": [
    //       {
    //         // Contact information at the drop-off point (#1)
    //         "toStop": 1,
    //         "toContact": {
    //           "name": userName,
    //           "phone": userNumber,
    //         },
    //         "remarks": selectedUserAddress.note,
    //       }
    //     ]
    //   };
    //   if (UTCRevDate != undefined) {
    //     const scheduleAt = UTCRevDate
    //     console.log("scheduleAt", scheduleAt)
    //     body["scheduleAt"] = scheduleAt
    //     console.log("SCHEDULE BODY", body)
    //     getScheduleQuotation(body, result)
    //   }
    //   else {
    //     console.log("NOW BODY ", body)
    //     placeDelivery(Cart.getDeliveryQuotation(), body, result)
    //   }
    // })
  };

  const orderPickUp = (result) => {
    var pickUpBody = {
      orderId: result.id,
      userId: User.getUserId(),
      // addressId: Cart.getDeliveryAddress().id,
    };
    ApiClient.POST(API.createOrderPickUp, pickUpBody).then((result) => {
      console.log("order pickup", result);
    });
  };

  const getScheduleQuotation = (body, order) => {
    ApiClient.POST(API.lalamoveQuotation, body).then((result) => {
      console.log("quotation result", result);
      placeDelivery(result, body, order);
    });
  };

  const placeDelivery = (quotation, body, order) => {
    console.log("Placing delivery", quotation);
    body["quotedTotalFee"] = {
      amount: quotation.totalFee,
      currency: quotation.totalFeeCurrency,
    };
    console.log("LALA ORDER BODY", body);
    ApiClient.POST(API.lalamovePlaceOrder, body).then((result) => {
      console.log("lalamvoe place order", result);
      if (result) {
        const lalaOrderId = result.orderRef;
        ApiClient.GET(API.lalamoveGetOrderStatus + result.orderRef).then(
          (result) => {
            console.log("lalamove order status", result);

            var updateBody = {
              orderId: order.orderId,
              lalaOrderId: lalaOrderId,
              orderStatus: result.status,
            };
            console.log("UPDATE BODY", updateBody);
            ApiClient.PATCH(API.updateOrderDelivery, updateBody).then(
              (result) => {
                console.log("update order delivery", result);
              }
            );
          }
        );
      }
    });
  };

  // api keys should saved in server, but this is client sdk....
  // const molPayment = (amount, orderId) => {
  //   var paymentSuccess = false
  //   var paymentDetails = {
  //     // Optional, REQUIRED when use online Sandbox environment and account credentials.
  //     'mp_dev_mode': true,

  //     // Mandatory String. Values obtained from Razer Merchant Services.
  //     'mp_username': 'api_SB_mykoodoo',
  //     'mp_password': 'WaaU1IeZ*&(%%',
  //     'mp_merchant_ID': 'SB_mykoodoo',
  //     'mp_app_name': 'mykoodoo',
  //     'mp_verification_key': '0bd0efac623106b4c19bc28b8e9a0d8d',

  //     // Mandatory String. Payment values.
  //     'mp_amount': amount, // Minimum 1.01
  //     'mp_order_ID': orderId,
  //     'mp_currency': 'MYR',
  //     'mp_country': 'MY',

  //     // Optional String.
  //     'mp_channel': 'multi', // Use 'multi' for all available channels option. For individual channel seletion, please refer to https://github.com/RazerMS/rms-mobile-xdk-examples/blob/master/channel_list.tsv.
  //     'mp_bill_description': outletData.name + ' purchase RM' + parseFloat(amount).toFixed(2),
  //     'mp_bill_name': User.getUserData().name,
  //     'mp_bill_email': User.getUserData().email,
  //     'mp_bill_mobile': User.getUserData().number,
  //   }
  //   molpay.startMolpay(paymentDetails, (data) => {
  //     //callback after payment success
  //     console.log(data);
  //     if (data) {
  //       console.log("payment success")
  //       placeOrder()
  //     }
  //   });
  // }

  const startMolPay = (channel) => {
    // need get readable order id from api first

    var body = {
      outletId: selectedOutlet.uniqueId,
      orderType: orderType,
    };

    ApiClient.POST(API.getNextOrderId, body).then(async (result) => {
      if (result && result.status === "success") {
        var orderIdServerTemp = result.orderId;

        setOrderIdServer(orderIdServerTemp);

        var amountToPay = +(
          Math.round(
            Math.max(
              totalPrice +
              totalTax +
              totalSc +
              (orderType === ORDER_TYPE.DELIVERY
                ? deliveryQuotation.totalFee
                  ? deliveryQuotation.totalFee
                  : 0
                : 0) +
              (selectedOutlet.deliveryPackagingFee && orderType === ORDER_TYPE.DELIVERY ? selectedOutlet.deliveryPackagingFee : 0) +
              (selectedOutlet.pickupPackagingFee && orderType === ORDER_TYPE.DELIVERY ? selectedOutlet.pickupPackagingFee : 0) -
              totalDiscount -
              (usePointsToRedeem ? pointsToRedeemDiscount : 0) -
              (usePointsToRedeemLCC ? pointsToRedeemDiscountLCC : 0) +
              ((isPlacingReservation && selectedOutlet.reservationDepositAmount >= 1.01) ? selectedOutlet.reservationDepositAmount : 0),
              0
            ) *
            20
          ) / 20
        ).toFixed(2);

        // Testing
        // amountToPay = amountToPay > 5 ? 5 : amountToPay;
        // amountToPay = 1.01;

        const subdomain = await AsyncStorage.getItem("latestSubdomain");
        // const metaInfo = ` | ${subdomain ? `${subdomain}.` : ''}mykoodoo.com`;
        const metaInfo = `|${subdomain ? subdomain : "mykoodoo"}`;

        const razerOrderId = `#${orderType === ORDER_TYPE.DINEIN ? "" : "T"
          }${orderIdServerTemp}-${generateUniqueCodeForOrderId()}${metaInfo}`;

        body = {
          amount: amountToPay,
          // merchantId: 'mykoodoo_Dev',
          merchantId: "mykoodoo",
          orderId: razerOrderId,
        };

        console.log("before call getRazerVCode");

        ApiClient.POST(API.getRazerVCode, body).then(async (result) => {
          console.log("afater call getRazerVCode");
          console.log(result);

          if (result && result.status === "success") {
            const vcode = result.vcode;

            console.log("subdomain before");
            console.log(subdomain);

            // var paymentDetails = {
            //   // Optional, REQUIRED when use online Sandbox environment and account credentials.
            //   'mp_dev_mode': true,

            //   // Mandatory String. Values obtained from Razer Merchant Services.
            //   'mp_username': 'api_SB_mykoodoo',
            //   'mp_password': 'WaaU1IeZ*&(%%',
            //   'mp_merchant_ID': 'SB_mykoodoo',
            //   'mp_app_name': 'mykoodoo',
            //   'mp_verification_key': '0bd0efac623106b4c19bc28b8e9a0d8d',
            //   // 'mp_username': 'api_mykoodoo_Dev',
            //   // 'mp_password': 'E81E65f5&^',
            //   // 'mp_merchant_ID': 'mykoodoo_Dev',
            //   // 'mp_app_name': 'mykoodoo',
            //   // 'mp_verification_key': '0bd0efac623106b4c19bc28b8e9a0d8d',

            //   // Mandatory String. Payment values.
            //   'mp_amount': amountToPay, // Minimum 1.01
            //   'mp_order_ID': `#${orderType === ORDER_TYPE.DINEIN ? '' : 'T'}${orderIdServerTemp}${metaInfo}`,
            //   'mp_currency': 'MYR',
            //   'mp_country': 'MY',

            //   // Optional String.
            //   'mp_channel': 'multi', // Use 'multi' for all available channels option. For individual channel seletion, please refer to https://github.com/RazerMS/rms-mobile-xdk-examples/blob/master/channel_list.tsv.
            //   'mp_bill_description': selectedOutlet.name + ' purchase RM' + parseFloat(amountToPay).toFixed(2),
            //   'mp_bill_name': userName,
            //   'mp_bill_email': userEmail,
            //   'mp_bill_mobile': userNumber,

            //   'mp_sandbox_mode': true,
            //   'mp_tcctype': 'AUTH',
            // }

            var paymentDetails = {
              // Optional, REQUIRED when use online Sandbox environment and account credentials.
              // sandbox
              // 'mp_dev_mode': true,
              // uat
              // 'mp_dev_mode': false,

              // Mandatory String. Values obtained from Razer Merchant Services.
              // 'mp_username': 'api_SB_mykoodoo',
              // 'mp_password': 'WaaU1IeZ*&(%%',
              // 'mp_merchant_ID': 'SB_mykoodoo',
              // 'mp_app_name': 'mykoodoo',
              // 'mp_verification_key': '0bd0efac623106b4c19bc28b8e9a0d8d',

              // sandbox
              // mpsmerchantid: 'SB_mykoodoo',
              // uat
              // mpsmerchantid: 'mykoodoo_Dev',
              mpsmerchantid: "mykoodoo",
              mpsvcode: vcode,

              // Mandatory String. Payment values.
              // 'mp_amount': amountToPay, // Minimum 1.01
              // 'mp_order_ID': `#${orderType === ORDER_TYPE.DINEIN ? '' : 'T'}${orderIdServerTemp}`,
              // 'mp_currency': 'MYR',
              // 'mp_country': 'MY',
              mpsamount: amountToPay,
              mpsorderid: razerOrderId,
              mpscurrency: "MYR",
              mpscountry: "MY",

              // Optional String.
              // 'mp_channel': 'multi', // Use 'multi' for all available channels option. For individual channel seletion, please refer to https://github.com/RazerMS/rms-mobile-xdk-examples/blob/master/channel_list.tsv.
              // 'mp_bill_description': selectedOutlet.name + ' purchase RM' + parseFloat(amountToPay).toFixed(2),
              // mpsbill_name: userName ? userName : userInfoName,
              // mpsbill_email: userEmail ? userEmail : '',
              mpsbill_mobile: selectedAddressUserPhone ? selectedAddressUserPhone : userInfoPhone,
              mpsbill_desc:
                selectedOutlet.name +
                " purchase RM" +
                parseFloat(amountToPay).toFixed(2),
              mpsbill_name: selectedAddressUserName ? selectedAddressUserName : userInfoName,

              // 'mp_sandbox_mode': true,
              // 'mp_tcctype': 'AUTH',

              // mpschannel: "fpx",
              mpschannel: channel.value,
            };

            console.log("paymentDetails");
            console.log(paymentDetails);

            // molpay.startMolpay(paymentDetails, (data) => {
            // $('#myPay').MOLPaySeamless(paymentDetails, (data) => {

            window.initMolpay(paymentDetails, (data) => { });

            window.startMolpay(paymentDetails, (data) => {
              // callback after payment success

              console.log("razer result v2");
              console.log(data);

              // const result = JSON.parse(data);
              const result = data;
              console.log(result);

              // if (result.error_code || result.Error) {
              //   console.log('razer error');

              //   window.confirm(
              //     "Error",
              //     "Failed to process your payment",
              //     [
              //       {
              //         text: "OK",
              //         onPress: () => {
              //           // navigation.jumpTo('Home')
              //           // navigation.navigate('Home');
              //           navigation.dispatch(
              //             CommonActions.reset({
              //               index: 0,
              //               routes: [{ name: "Home" }]
              //             }));
              //         },
              //       },
              //     ],
              //     { cancelable: false }
              //   );

              //   CommonStore.update(s => {
              //     s.isLoading = false;
              //   });
              // }
              // else {
              //   console.log("payment success v2");

              //   placeUserOrder(result);
              // }

              // placeUserOrder(result);
            });
          } else {
            window.confirm(
              "Error\nSorry, an error occurred, please try again."
            );
          }
        });
      } else {
        window.confirm("Error\nSorry, an error occurred, please try again.");
      }
    });
  };

  const calculateFinalTotal = () => {
    console.log("cartItem", cartItem);
    const totalFloat = parseFloat(
      cartItem.reduce(
        (accumulator, current) =>
          accumulator + current.price * current.quantity,
        0
      )
    );

    const totalTax = parseFloat(
      (cartItem
        .reduce(
          (accumulator, current) =>
            accumulator + current.price * current.quantity,
          0
        )
        .toFixed(2) /
        parseFloat(100)) *
      parseFloat(Cart.getTax())
    );

    //+ '001' //.toFixed() bug, occour only if last significant digit is 5 and if fixing to only 1 less digit, i.e. 1.75.toFixed(1) => 1.7 instead of 1.8
    var taxStr = totalTax.toString().split(".");
    console.log(taxStr);
    if (taxStr.length > 1) {
      taxStr[1] = taxStr[1] + "001";
    }
    const taxFloat = parseFloat(taxStr[0] + "." + taxStr[1]);

    setState({
      totalFloat: parseFloat(totalFloat).toFixed(2),
      taxFloat: parseFloat(taxFloat).toFixed(2),
    });
  };

  const generateScheduleTime = () => {
    const now = new Date();
    var m = ((((now.getMinutes() + 7.5) / 15) | 0) * 15) % 60;
    var h = (((now.getMinutes() / 105 + 0.5) | 0) + now.getHours()) % 24;
    const nearestNow = new Date(
      now.getFullYear(),
      now.getMonth(),
      now.getDate(),
      h,
      m
    );
    var startTime = moment(nearestNow, "YYYY-MM-DD hh:mm");
    var endTime = moment(
      new Date(
        now.getFullYear(),
        now.getMonth(),
        now.getDate() + 1,
        now.getHours(),
        now.getMinutes()
      ),
      "YYYY-MM-DD hh:mm a"
    );

    var result = [];
    var current = moment(startTime);

    var tempstr = current.format("llll").split(",");
    var daystr = tempstr[0];
    var datestr = tempstr[1].split(" ");
    var yeartimestr = tempstr[2].split(" ");
    var currentNearestTime =
      daystr +
      " " +
      datestr[2] +
      " " +
      datestr[1] +
      "," +
      yeartimestr[2] +
      "," +
      yeartimestr[1] +
      "," +
      yeartimestr[3];
    result.push(currentNearestTime);

    current.add(15, "minutes");
    while (current <= endTime) {
      var tempstr = current.format("llll").split(",");
      console.log("tempstr", tempstr);
      var daystr = tempstr[0];
      var datestr = tempstr[1].split(" ");
      var yeartimestr = tempstr[2].split(" ");
      var finalstr =
        daystr +
        " " +
        datestr[2] +
        " " +
        datestr[1] +
        "," +
        yeartimestr[2] +
        "," +
        yeartimestr[1] +
        "," +
        yeartimestr[3];
      result.push(finalstr);

      current.add(15, "minutes");
    }
    console.log("schedule result", result);
    setState({
      schedulteTimeList: result,
      schedulteTimeSelected: result[0],
      currentNearestTime: currentNearestTime,
    });
  };

  const renderSchedule = (item) => (
    <View
      style={{
        flexDirection: "row",
        padding: 20,
        justifyContent: "space-between",
        alignContent: "center",
      }}
    >
      <View style={{ justifyContent: "flex-start", marginHorizontal: 10 }}>
        <Text>
          {/* {currentNearestTime == item.toString() ? 'TODAY' : item.toString().split(',')[0]} */}
        </Text>
      </View>
      <View style={{ justifyContent: "flex-end", marginHorizontal: 10 }}>
        <Text>
          {/* {currentNearestTime == item.toString() ? 'ASAP' : item.toString().split(',')[1]} */}
        </Text>
      </View>
    </View>
  );

  const proceedToMenuItemDetailsForUpdating = async (item) => {
    // const outletItemSnapshot = await firebase
    //   .firestore()
    //   .collection(Collections.OutletItem)
    //   .where("uniqueId", "==", item.itemId)
    //   .limit(1)
    //   .get();

    const outletItemSnapshot = await getDocs(
      query(
        collection(global.db, Collections.OutletItem),
        where("uniqueId", "==", item.itemId),
        limit(1),
      )
    );

    if (!outletItemSnapshot.empty) {
      const outletItem = outletItemSnapshot.docs[0].data();

      CommonStore.update((s) => {
        s.selectedOutletItem = outletItem;

        // s.selectedAddOnIdForChoiceQtyDict = {};

        s.onUpdatingCartItem = item;
      });

      // props.navigation.navigate("MenuItemDetails", {
      //   refresh: refresh.bind(this),
      //   menuItem: outletItem,
      //   cartItem: item,
      //   outletData: selectedOutlet,
      // });

      const subdomain = await AsyncStorage.getItem("latestSubdomain");

      if (!subdomain) {
        linkTo && linkTo(`${prefix}/outlet/menu/item`);
      } else {
        linkTo && linkTo(`${prefix}/outlet/${subdomain}/menu/item`);
      }

      // linkTo && linkTo(`${prefix}/outlet/menu/item`);
    }
  };

  const removeFromCartItems = async (item) => {
    var updateCartItemIndex = 0;

    for (var i = 0; i < cartItems.length; i++) {
      if (
        cartItems[i].itemId === item.itemId &&
        cartItems[i].cartItemDate === item.cartItemDate
      ) {
        updateCartItemIndex = i;
      }
    }

    const newCartItems = [
      ...cartItems.slice(0, updateCartItemIndex),
      ...cartItems.slice(updateCartItemIndex + 1),
    ];

    if (newCartItems.length > 0) {
      safelyExecuteIdb(() => {
        // AsyncStorage.setItem(
        //   `${firebaseUid}.cartItems`,
        //   JSON.stringify(newCartItems)
        // );
        idbSet(`cartItems`, JSON.stringify(newCartItems));
        // AsyncStorage.setItem(
        //   `${firebaseUid}.cartOutletId`,
        //   selectedOutlet.uniqueId
        // );
        idbSet(`cartOutletId`, selectedOutlet.uniqueId);
      });
    } else {
      safelyExecuteIdb(() => {
        // AsyncStorage.removeItem(`${firebaseUid}.cartItems`);
        idbDel(`cartItems`);
        // AsyncStorage.removeItem(`${firebaseUid}.cartOutletId`);
        idbDel(`cartOutletId`);
      });
    }

    CommonStore.update((s) => {
      s.cartItems = newCartItems;

      if (newCartItems.length <= 0) {
        s.cartOutletId = null;
      }
    });
  };

  const onPressMpsChannel = (channel) => {
    CommonStore.update((s) => {
      s.isLoading = true;
    });

    startMolPay(channel);
  };

  const renderMpsChannelList = ({ item }) => {
    return (
      <TouchableOpacity
        style={{
          flexDirection: "column",
          justifyContent: "center",
          alignItems: "center",
          width: "90%",
          height: 100,
          alignSelf: "center",

          // marginHorizontal: 24,
          marginVertical: 10,
          paddingVertical: 12,

          backgroundColor: Colors.primaryColor,
          shadowColor: "#000",
          shadowOffset: {
            width: 0,
            height: 1,
          },
          shadowOpacity: 0.22,
          shadowRadius: 2.22,
          elevation: 3,

          justifyContent: "center",
          borderRadius: 8,

          paddingBottom: item.value !== MPS_CHANNEL.credit ? 12 : "8%",
          // paddingTop: item.value !== MPS_CHANNEL.credit ? 12 : '5%',
        }}
        onPress={() => onPressMpsChannel(item)}
      >
        {item.value !== MPS_CHANNEL.credit ? (
          <Image
            style={{
              // width: 64,
              // height: 64,
              width: "50%",
              height: "50%",
              marginBottom: "2%",
            }}
            source={BANK_LOGO[item.value]}
          ></Image>
        ) : (
          <View
            style={{
              flexDirection: "row",
              justifyContent: "space-around",
              alignItems: "center",

              width: "84%",
              height: "80%",

              // backgroundColor: 'green',

              // marginBottom: '-6%',
              // marginTop: '-2%',
            }}
          >
            <Image
              style={{
                // width: 64,
                // height: 64,
                width: "40%",
                height: "60%",
                // marginBottom: '2%',
              }}
              source={BANK_LOGO["visa"]}
            ></Image>
            <Image
              style={{
                // width: 64,
                // height: 64,
                width: "40%",
                height: "60%",
                // marginBottom: '2%',
              }}
              source={BANK_LOGO["mastercard"]}
            ></Image>
          </View>
        )}

        <Text
          style={{
            fontSize: 14,
            textAlign: "center",
            color: "white",
            width: "94%",
            alignSelf: "center",
            // backgroundColor: 'red',

            // paddingBottom: item.value !== MPS_CHANNEL.credit ? '0%' : '8%',
          }}
        // numberOfLines={1}
        >
          {item.label}
        </Text>
      </TouchableOpacity>
    );
  };

  const redeemCartItemLoyaltyStamp = async (item, loyaltyStampGet) => {
    var isRedeemed = false;
    var itemIndex = 0;

    for (var i = 0; i < lsRedeemedCartItemList.length; i++) {
      if (
        lsRedeemedCartItemList[i].cartItemDate === item.cartItemDate &&
        lsRedeemedCartItemList[i].itemId === item.itemId
      ) {
        isRedeemed = true;
        itemIndex = i;
        break;
      }
    }

    if (!isRedeemed) {
      setLsRedeemedCartItemList([
        ...lsRedeemedCartItemList,
        {
          cartItemDate: item.cartItemDate,
          itemId: item.itemId,
          loyaltyStampGet: loyaltyStampGet,
        },
      ]);
    } else {
      // Alert.alert('Info', 'You already redeemed this item(s).');

      setLsRedeemedCartItemList([
        ...lsRedeemedCartItemList.slice(0, itemIndex),
        ...lsRedeemedCartItemList.slice(itemIndex + 1),
      ]);
    }
  };

  const redeemCartItemLCC = async (item) => {
    var isRedeemed = false;
    var itemIndex = 0;

    for (var i = 0; i < pointsRedeemedCartItemListLCC.length; i++) {
      if (
        pointsRedeemedCartItemListLCC[i].cartItemDate === item.cartItemDate &&
        pointsRedeemedCartItemListLCC[i].itemId === item.itemId
      ) {
        isRedeemed = true;
        itemIndex = i;
        break;
      }
    }

    if (!isRedeemed) {
      var leftUserPoints = userPointsBalanceLCC - spentUserPointsLCC;

      if (leftUserPoints > 0) {
        // var pointsRedeemObj = null;

        // var pointsRedeemCategory = undefined;
        // if (
        //   selectedOutletItemCategoriesDict[item.categoryId] &&
        //   pointsRedeemCategoryNameDictLCC[
        //   selectedOutletItemCategoriesDict[item.categoryId].name
        //   ] !== undefined
        // ) {
        //   pointsRedeemCategory =
        //     pointsRedeemCategoryNameDictLCC[
        //     selectedOutletItemCategoriesDict[item.categoryId].name
        //     ];
        // }

        // if (pointsRedeemItemSkuDictLCC[item.itemSku] !== undefined) {
        //   pointsRedeemObj = pointsRedeemItemSkuDictLCC[item.itemSku];
        // } else if (pointsRedeemCategory !== undefined) {
        //   pointsRedeemObj = pointsRedeemCategory;
        // }

        // if (pointsRedeemObj) {
        //   // const requiredUserPoints =
        //   //   (item.price / pointsRedeemObj.conversionCurrencyTo) *
        //   //   pointsRedeemObj.conversionPointsFrom;

        //   const requiredUserPoints = item.price;

        //   if (leftUserPoints >= requiredUserPoints) {
        //     setSpentUserPoints(spentUserPoints + requiredUserPoints);

        //     setPointsRedeemedCartItemListLCC([
        //       ...pointsRedeemedCartItemListLCC,
        //       {
        //         cartItemDate: item.cartItemDate,
        //         itemId: item.itemId,
        //       },
        //     ]);
        //   } else {
        //     Alert.alert(
        //       'Info',
        //       `Your points not enough to redeem this item(s).\n\nPoints required: ${requiredUserPoints}\nYour points: ${leftUserPoints}`,
        //     );
        //   }
        // } else {
        //   Alert.alert('Info', 'This item(s) is not available for redemption.');
        // }

        const requiredUserPoints = item.price;

        if (leftUserPoints >= requiredUserPoints) {
          setSpentUserPoints(spentUserPoints + requiredUserPoints);

          setPointsRedeemedCartItemListLCC([
            ...pointsRedeemedCartItemListLCC,
            {
              cartItemDate: item.cartItemDate,
              itemId: item.itemId,
            },
          ]);
        } else {
          Alert.alert(
            "Info",
            `Your credits not enough to redeem this item(s).\n\Credits required: ${requiredUserPoints}\nYour credits: ${leftUserPoints}`
          );
        }
      } else {
        Alert.alert(
          "Info",
          "You doesn't have any credits to redeem the item(s)."
        );
      }
    } else {
      // Alert.alert('Info', 'You already redeemed this item(s).');

      setPointsRedeemedCartItemListLCC([
        ...pointsRedeemedCartItemListLCC.slice(0, itemIndex),
        ...pointsRedeemedCartItemListLCC.slice(itemIndex + 1),
      ]);
    }
  };

  const renderItem = (params) => {
    // var item = {
    //   ...params.item,
    // };

    // if (cartOutletItemsDict[item.itemId]) {
    //   item.image = cartOutletItemsDict[item.itemId].image;
    //   item.name = cartOutletItemsDict[item.itemId].name;
    //   item.itemName = cartOutletItemsDict[item.itemId].name;

    // }

    const item = params.item;

    console.log("item");
    console.log(item);

    var itemNameFontSize = 19;

    if (windowWidth <= 360) {
      itemNameFontSize = 14;
      //console.log(windowWidth)
    }

    const itemNameTextScale = {
      fontSize: itemNameFontSize,
    };

    var priceFontSize = 14;

    if (windowWidth <= 360) {
      priceFontSize = 10;
      //console.log(windowWidth)
    }

    const priceTextScale = {
      fontSize: priceFontSize,
    };

    var addOnFontSize = 14;

    if (windowWidth <= 360) {
      addOnFontSize = 11;
      //console.log(windowWidth)
    }

    const addOnTextScale = {
      fontSize: addOnFontSize,
    };

    var overrideCategoryPrice = undefined;
    if (
      selectedOutletItemCategoriesDict[item.categoryId] &&
      overrideCategoryPriceNameDict[
      selectedOutletItemCategoriesDict[item.categoryId].name
      ] !== undefined
    ) {
      overrideCategoryPrice =
        overrideCategoryPriceNameDict[
          selectedOutletItemCategoriesDict[item.categoryId].name
        ].overridePrice;
    }

    var isRedeemed = false;

    for (var i = 0; i < pointsRedeemedCartItemList.length; i++) {
      if (
        pointsRedeemedCartItemList[i].cartItemDate === item.cartItemDate &&
        pointsRedeemedCartItemList[i].itemId === item.itemId
      ) {
        isRedeemed = true;
        break;
      }
    }

    /////////////////////////////////////////////////////////

    var loyaltyStampCategory = undefined;

    if (
      selectedOutletItemCategoriesDict[item.categoryId] &&
      loyaltyStampGetCategoryNameDict[
      selectedOutletItemCategoriesDict[item.categoryId].name
      ] !== undefined
    ) {
      loyaltyStampCategory =
        loyaltyStampGetCategoryNameDict[
        selectedOutletItemCategoriesDict[item.categoryId].name
        ];
    }

    var loyaltyStampGet = null;

    if (loyaltyStampGetItemSkuDict[item.itemSku] !== undefined) {
      if (item.quantity >= loyaltyStampGetItemSkuDict[item.itemSku].quantity) {
        loyaltyStampGet = loyaltyStampGetItemSkuDict[item.itemSku];
      }
    } else if (loyaltyStampCategory !== undefined) {
      if (item.quantity >= loyaltyStampCategory.quantity) {
        loyaltyStampGet = loyaltyStampCategory;
      }
    }
    /////////////////////////////////////////////////////////

    var overrideCategoryPriceLC = undefined;
    if (
      selectedOutletItemCategoriesDict[item.categoryId] &&
      overrideCategoryPriceNameDictLC[
      selectedOutletItemCategoriesDict[item.categoryId].name
      ] !== undefined
    ) {
      overrideCategoryPriceLC =
        overrideCategoryPriceNameDictLC[
        selectedOutletItemCategoriesDict[item.categoryId].name
        ];
    }

    /////////////////////////////////////////////////////////

    var isRedeemedLCC = false;

    for (var i = 0; i < pointsRedeemedCartItemListLCC.length; i++) {
      if (
        pointsRedeemedCartItemListLCC[i].cartItemDate === item.cartItemDate &&
        pointsRedeemedCartItemListLCC[i].itemId === item.itemId
      ) {
        isRedeemedLCC = true;
        break;
      }
    }

    /////////////////////////////////////////////////////////

    return (
      <>
        <View
          style={{
            flexDirection: "row",
            paddingVertical: 10,
            borderColor: Colors.descriptionColor,
            justifyContent: "flex-start",
            alignContent: "center",
            alignItems: "center",
            display: "flex",
            // borderBottomWidth: StyleSheet.hairlineWidth,
            marginBottom: 5,
          }}
        >
          <View
            style={[
              {
                marginRight: 15,
                backgroundColor: Colors.secondaryColor,
                borderRadius: 5,
                alignSelf: "flex-start",
                // flex: 1,
              },
              item.image
                ? {
                  // display: 'flex',
                  // alignItems: 'center',
                  // justifyContent: 'center',
                }
                : {
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  paddingTop: 5,
                  paddingBottom: 5,
                },
            ]}
          >
            {item.image ? (
              <AsyncImage
                source={{ uri: item.image }}
                item={item}
                style={{
                  // width: 70,
                  // height: 70,
                  width: isMobile() ? windowWidth * 0.22 : windowWidth * 0.05,
                  height: isMobile() ? windowWidth * 0.22 : windowWidth * 0.05,
                  borderRadius: 5,
                }}
              />
            ) : (
              <View
                style={{
                  width: isMobile() ? windowWidth * 0.22 : windowWidth * 0.05,
                  height: isMobile() ? windowWidth * 0.22 : windowWidth * 0.05,
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                }}
              >
                <Ionicons
                  name="fast-food-outline"
                  // size={45}
                  size={isMobile() ? windowWidth * 0.1 : windowWidth * 0.02}
                />
              </View>
            )}
          </View>

          <View
            style={{
              // backgroundColor: 'red',
              width: isMobile() ? "40%" : windowWidth * 0.78,
            }}
          >
            <View
              style={{
                display: "flex",
                // backgroundColor: 'red',
              }}
            >
              <Text
                style={[
                  itemNameTextScale,
                  {
                    // fontWeight: "700",
                    //fontSize: 19,
                    fontFamily: "NunitoSans-Bold",
                    color: Colors.mainTxtColor,
                  },
                ]}
                numberOfLines={3}
              >
                {item.name}
              </Text>

              {/* {item.options ? item.options.map((item, index) => {
              return (
                (item.quantity > 0 ? <Text style={{ color: Colors.descriptionColor, marginTop: 5, fontWeight: '400', fontSize: 11, fontFamily: "NunitoSans-Regular" }}>
                  {" "}
                + {item.quantity} ({item.itemName})
              </Text> : null)
              );
            }) : null} */}

              {item.addOns &&
                item.addOns.length > 0 &&
                item.addOns.map((addOn, index) => {
                  const addOnChoices = addOn.choiceNames.join(", ");

                  return (
                    <Text
                      key={index}
                      style={[
                        addOnTextScale,
                        {
                          color: Colors.descriptionColor,
                          marginTop: 3,
                          // fontWeight: "700",
                          //fontSize: 14,
                          marginBottom: 3,
                          fontFamily: "NunitoSans-Regular",
                        },
                      ]}
                    >
                      {/* Size: {item.size == "small" ? "Small" : item.size == "medium" ? "Medium" : item.size == "big" ? "Big" : null} */}
                      {addOn.quantities > 0 ? (
                        <>
                          {`+ ${addOn.quantities.reduce(
                            (accum, value) => accum + value,
                            0
                          )} (${addOnChoices})`}
                        </>
                      ) : (
                        <>{`${addOn.name} (${addOnChoices})`}</>
                      )}
                    </Text>
                  );
                })}
            </View>

            {/* <Text style={{ 
            fontWeight: "700", 
            fontSize: 19, 
            fontFamily: 
            "NunitoSans-Regular" 
          }}>
            {test1 == 1 ? item.name : test2 == 1 ? item.name : ('x' + item.quantity + " " + item.name)}
          </Text> */}

            {/* {test1 == 1 ?
            <Text style={{ fontSize: 14, fontFamily: "NunitoSans-Regular", color: Colors.descriptionColor }}> ({item.quantity} days extension)
          </Text>
            : test2 == 1 ? <Text style={{ fontSize: 14, fontFamily: "NunitoSans-Regular", color: Colors.descriptionColor }}> x{item.quantity} mugs
            </Text> : null} */}

            {item.remarks ? (
              <Text
                style={{
                  color: Colors.descriptionColor,
                  marginTop: 0,
                  fontWeight: "400",
                  fontSize: 11,
                  fontFamily: "NunitoSans-Italic",
                }}
              >
                Remarks: {item.remarks}
              </Text>
            ) : null}

            {/* {type == 0 ? <TouchableOpacity
              onPress={() => {
                optional(item.itemId);
              }}
            >
              <Text
                style={{
                  color: Colors.primaryColor,
                  marginTop: 3,
                  fontWeight: "700",
                  fontSize: 14,
                  marginBottom: 3,
                  fontFamily: "NunitoSans-Regular"
                }}
              >
                {item.fireOrder == 0 ? "Serve now" : item.fireOrder == 1 ? "Serve later" : null}
              </Text>
            </TouchableOpacity> : null} */}
          </View>

          <View
            style={{
              flexDirection: "row",
              justifyContent: "flex-end",
              alignItems: "center",
              justifyContent: "center",

              width: isMobile() ? "20%" : "4%",
              // backgroundColor: 'blue',
            }}
          >
            <Text
              style={[
                priceTextScale,
                {
                  // fontWeight: "400",
                  // textAlign: "center",
                  color: Colors.descriptionColor,
                  //fontSize: 14,
                  fontFamily: "NunitoSans-Regular",
                },
              ]}
            >
              {/* RM{(parseFloat(item.price) * item.quantity).toFixed(2)} */}
              RM{" "}
              {!item.isFreeItem &&
                (overrideItemPriceSkuDictLC[item.sku] !== undefined ||
                  overrideCategoryPriceLC !== undefined)
                ? overrideItemPriceSkuDictLC[item.sku] &&
                  overrideItemPriceSkuDictLC[item.sku].overridePrice !==
                  undefined
                  ? parseFloat(
                    overrideItemPriceSkuDictLC[item.sku].overridePrice
                  ).toFixed(2)
                  : parseFloat(overrideCategoryPriceLC).toFixed(2)
                : !item.isFreeItem &&
                  (overrideItemPriceSkuDict[item.sku] !== undefined ||
                    overrideCategoryPrice !== undefined)
                  ? overrideItemPriceSkuDict[item.sku] &&
                    overrideItemPriceSkuDict[item.sku].overridePrice !== undefined
                    ? parseFloat(
                      overrideItemPriceSkuDict[item.sku].overridePrice
                    ).toFixed(2)
                    : parseFloat(overrideCategoryPrice).toFixed(2)
                  : parseFloat(item.price).toFixed(2)}
            </Text>

            {/* <TouchableOpacity
            onPress={() => {
              if (item.quantity <= 1) {
                Cart.deleteCartItem(item.itemId, item);
                getCartItem();
              }
              else {
                setState({
                  visible: true,
                  editingItemId: item.itemId,
                  qty: item.quantity,
                  value: item.quantity,
                });
              }
            }}
          >
            <Entypo name="cross" size={28} color="red" />
          </TouchableOpacity> */}
          </View>

          {/* <View
            style={{
              width: "10%",
              marginLeft: 15,
              alignItems: "center",
              // marginRight: 5,
            }}
          >
            {!item.isFreeItem ? (
              <>
                <TouchableOpacity
                  onPress={() => {
                    console.log("ITEM@@@@@@@@@@@@@@@@@@@@@@@@@@@@", item);

                    proceedToMenuItemDetailsForUpdating(item);

                    // props.navigation.navigate("MenuItemDetailsUpdate", {
                    //   refresh: refresh.bind(this),
                    //   menuItem: item.menuItem,
                    //   cartItem: item,
                    //   outletData: outletData,
                    // });
                  }}
                // style={{ width: 40, alignItems: 'center' }}
                >
                  <View>
                    <Icons name="edit" size={25} color={Colors.primaryColor} />
                  </View>
                </TouchableOpacity>

                <TouchableOpacity
                  onPress={() => {
                    removeFromCartItems(item);
                  }}
                  style={{
                    // backgroundColor: 'green',
                    marginTop: 10,
                    right: 1,
                    // width: 40,
                    // alignItems: 'center'
                  }}
                >
                  <FontAwesome name="trash-o" size={25} color={Colors.tabRed} />
                </TouchableOpacity>

                <TouchableOpacity
                  onPress={() => {
                    redeemCartItemLCC(item);
                  }}
                  style={{
                    // backgroundColor: 'green',
                    marginTop: 10,
                    right: 1,
                    // width: 40,
                    // alignItems: 'center'
                  }}
                >
                  <Loyalty
                    style={{ width: 25, height: 25 }}
                    color={Colors.secondaryColor}
                  />
                </TouchableOpacity>
                {loyaltyStampGet ? (
                  <TouchableOpacity
                    onPress={() => {
                      // setShowRedeemRewardModal(true);

                      var userLoyaltyStamp =
                        userLoyaltyStampsDict[loyaltyStampGet.loyaltyStampId];

                      if (userLoyaltyStamp) {
                        // got the stamp, need check if user redeemed before

                        if (
                          userLoyaltyStampGetLsItemDict[
                          loyaltyStampGet.lsItemId
                          ]
                        ) {
                          // means redeemed already

                          Alert.alert(
                            "Info",
                            "You have redeemed this item before."
                          );
                        } else {
                          // need check quantity of stamps user owned

                          if (
                            userLoyaltyStamp.stampCount >=
                            loyaltyStampGet.noOfStamp
                          ) {
                            // enough stamp, proceed

                            redeemCartItemLoyaltyStamp(item, loyaltyStampGet);
                          } else {
                            // means not enough stamp

                            Alert.alert(
                              "Info",
                              `Not enough stamp to redeem, still need ${loyaltyStampGet.noOfStamp -
                              userLoyaltyStamp.stampCount
                              } more stamp(s).`
                            );
                          }
                        }
                      } else {
                        // means doesn't have any stamp at all

                        Alert.alert(
                          "Info",
                          `This item required ${loyaltyStampGet.noOfStamp
                          } stamp(s) of "${selectedOutletLoyaltyStampsDict[
                            loyaltyStampGet.loyaltyStampId
                          ].name
                          }" to exchange.`
                        );
                      }

                      // if ( loyaltyStampGet.noOfStamp)
                    }}
                    style={{
                      // backgroundColor: 'green',
                      marginTop: 10,
                      right: 1,
                      // width: 40,
                      // alignItems: 'center'
                    }}
                  >
                    <Ionicons
                      name="ribbon-outline"
                      size={28}
                      color={Colors.tabCyan}
                    />
                  </TouchableOpacity>
                ) : (
                  <></>
                )}
              </>
            ) : (
              <Text
                style={{
                  fontFamily: "NunitoSans-Regular",
                  fontSize: 14,
                  color: Colors.descriptionColor,
                }}
              >
                {"Bundle\nItem"}
              </Text>
            )}
          </View> */}
        </View>

        <View
          style={{
            height: 1.5,
            left: "-2%",
            width: "104%",
            backgroundColor: "#C2C1C0",
            opacity: 0.2,
            marginBottom: 4,

            shadowColor: "#000",
            shadowOffset: {
              width: 0,
              height: 1,
            },
            shadowOpacity: 0.22,
            shadowRadius: 2.22,
            elevation: 3,
          }}
        ></View>
      </>
    );
  };

  const changeClick = () => {
    //delivery
    if (clicked == 1) {
      //setState({ clicked: 0 })
    } else {
      // setState({ clicked: 1, clicked1: 0, shouldShow: true })
      setClicked(1);
      setClicked1(0);
      // setShouldShow(true);

      Cart.setOrderType(1);

      CommonStore.update((s) => {
        s.orderType = ORDER_TYPE.DELIVERY;
      });
    }
  };

  const changeClick1 = () => {
    //pickup
    if (clicked1 == 1) {
      //setState({ clicked1: 0 })
    } else {
      // setState({ clicked1: 1, clicked: 0, shouldShow: false })
      setClicked(0);
      setClicked1(1);
      // setShouldShow(false);

      Cart.setOrderType(2);

      CommonStore.update((s) => {
        s.orderType = ORDER_TYPE.PICKUP;
      });
    }
  };

  const changeOrderType = () => {
    if (orderType === ORDER_TYPE.DELIVERY) {
      Cart.setOrderType(2);

      CommonStore.update((s) => {
        s.orderType = ORDER_TYPE.PICKUP;
      });
    } else if (orderType === ORDER_TYPE.PICKUP) {
      Cart.setOrderType(1);

      CommonStore.update((s) => {
        s.orderType = ORDER_TYPE.DELIVERY;
      });
    }
  };

  const deleteUserCart = async () => {
    const body = {
      userCartId: userCart.uniqueId,
    };

    ApiClient.POST(API.deleteUserCart, body).then((result) => {
      if (result && result.status === "success") {
        console.log("ok");
      }
    });
  };

  const proceedOrderAfterUserInfo = async (skipUserInfo = false) => {
    if (!userInfoName) {
      CommonStore.update((s) => {
        s.alertObj = {
          title: "Error",
          message: "Name cannot be empty.",
        };
      });
      setShowUserInfoModal(false);
      return;
    }

    if (!userInfoPhone) {
      CommonStore.update((s) => {
        s.alertObj = {
          title: "Error",
          message: "Phone number cannot be empty.",
        };
      });
      setShowUserInfoModal(false);
      return;
    }

    var isValidLongNumber = false;
    if (
      (userInfoPhone.startsWith('011') && userInfoPhone.length === 11)
      ||
      (userInfoPhone.startsWith('6011') && userInfoPhone.length === 12)
    ) {
      isValidLongNumber = true;
    }

    if (userInfoPhone.length === 10 || userInfoPhone.length === 11 || isValidLongNumber) {
      // ok

      //////////////////////////////////

      // save user info

      await AsyncStorage.setItem('storedUserName', userInfoName);
      await AsyncStorage.setItem('storedUserPhone', userInfoPhone);

      global.storedUserName = userInfoName;
      global.storedUserPhone = userInfoPhone;

      //////////////////////////////////

      if (selectedPaymentOptions.value === USER_ORDER_PAYMENT_OPTIONS.ONLINE) {
        // startMolPay();
        setShowMpsChannelModal(true);
      } else if (
        selectedPaymentOptions.value === USER_ORDER_PAYMENT_OPTIONS.OFFLINE
      ) {
        CommonStore.update((s) => {
          s.isLoading = true;
        });

        placeUserOrder(null, skipUserInfo);
      } else {
        window.confirm("Error\nInvalid payment options");
      }
    } else {
      CommonStore.update((s) => {
        s.alertObj = {
          title: "Error",
          message: "Invalid phone number format.",
        };
      });
      setShowUserInfoModal(false);
      return;
    }
  };

  // function end

  return (
    <>
      {isLoading && paymentDetails !== null ? (
        <View
          style={{
            // backgroundColor: 'red',
            width: "100%",
            height: "100%",
            alignItems: "center",
            justifyContent: "center",

            marginBottom: "25%",
          }}
        >
          <ActivityIndicator color={Colors.primaryColor} size={"large"} />
        </View>
      ) : (
        <>
          {!isPlacingOrder ? (
            (!isPlacingReservation ? (tvCartItemsProcessed.length === 0) : (!isDepositOnly ? (tvCartItemsProcessed.length === 0) : false))
              ||
              isCartLoading
              ? (
                <View
                  style={[
                    styles.container,
                    {
                      width: windowWidth,
                      height: windowHeight,
                    },
                  ]}
                >
                  <View style={{ flex: 1 }}>
                    <View style={{ flex: 1 }}>
                      {isLoading || isCartLoading ? (
                        <View
                          style={{
                            // backgroundColor: 'red',
                            width: "100%",
                            height: "100%",
                            alignItems: "center",
                            justifyContent: "center",

                            marginBottom: "25%",
                          }}
                        >
                          <ActivityIndicator
                            color={Colors.primaryColor}
                            size={"large"}
                          />
                        </View>
                      ) : (
                        <View
                          style={{
                            // backgroundColor: 'red',
                            width: "100%",
                            height: "100%",
                            alignItems: "center",
                            justifyContent: "center",
                          }}
                        >
                          <Text
                            style={{
                              color: Colors.descriptionColor,
                              textAlign: "center",
                              fontFamily: "NunitoSans-Regular",
                              fontSize: 16,
                              marginBottom: "30%",
                            }}
                          >
                            No items in your cart now.
                          </Text>
                        </View>
                      )}
                    </View>

                    {/* <TouchableOpacity
                  onPress={() => {
                    props.navigation.navigate("Home");
                  }}
                >
                  <View
                    style={{
                      backgroundColor: Colors.primaryColor,
                      padding: 20,
                      paddingVertical: 16,
                      borderRadius: 10,
                      alignItems: 'center',

                      marginHorizontal: 48,
                      marginTop: 24,
                      marginBottom: 24,
                    }}>
                    <Text style={{
                      color: '#ffffff',
                      fontSize: 21,
                      fontFamily: 'NunitoSans-Regular',
                    }}>Explore outlet now</Text>
                  </View>
                </TouchableOpacity> */}
                  </View>
                </View>
              ) : (
                <View
                  style={[
                    styles.container,
                    {
                      width: windowWidth,
                      height: windowHeight,
                    },
                  ]}
                >
                  {/*<ActionSheet
              ref={(o) => (ActionSheet = o)}
              title={'Select Your Payment Method'}
              options={['Online payment', 'Credit/Debit Card', 'cancel', 'Cash on delivery']}
              cancelButtonIndex={2}
              destructiveButtonIndex={2}
              onPress={(index) => {
                console.log(index);
                if (index != 2) {
                  setState({
                    paymentMethod:
                      index == 0 ? 'Online payment' : 'Cash on delivery',
                  });
                }
              }}
            />*/}
                  <Modal
                    style={{ flex: 1 }}
                    visible={showUserInfoModal}
                    transparent={true}
                  >
                    <View style={styles.modalContainer}>
                      <View
                        style={{
                          // width: isMobile() ? windowWidth * 0.8 : windowWidth * 0.3,
                          // height: isMobile() ? windowHeight * 0.4 : windowHeight * 0.35,
                          backgroundColor: Colors.whiteColor,
                          borderRadius: 8,
                          padding: isMobile()
                            ? windowWidth * 0.07
                            : windowWidth * 0.02,
                          alignItems: "center",
                          justifyContent: "space-between",
                        }}
                      >
                        <TouchableOpacity
                          style={[
                            styles.closeButton,
                            {
                              right: isMobile()
                                ? windowWidth * 0.04
                                : windowWidth * 0.01,
                              top: isMobile()
                                ? windowWidth * 0.04
                                : windowWidth * 0.01,
                            },
                          ]}
                          onPress={() => {
                            setShowUserInfoModal(false);
                          }}
                        >
                          <AntDesign
                            name="closecircle"
                            size={25}
                            color={Colors.fieldtTxtColor}
                          />
                          {/* <SimpleLineIcons name="close" size={25}
                                    color={Colors.fieldtTxtColor}
                                /> */}
                        </TouchableOpacity>

                        <View
                          style={
                            (styles.modalTitle,
                            {
                              // backgroundColor: 'red'
                            })
                          }
                        >
                          <Text style={styles.modalTitleText}>
                            User Info Required
                          </Text>
                        </View>

                        <View
                          style={[
                            styles.modalBody,
                            {
                              width: "100%",
                              alignItems: "center",
                              // alignItems: 'flex-start',
                              justifyContent: "flex-start",
                              // backgroundColor: 'blue'
                            },
                          ]}
                        >
                          <View
                            style={{
                              flexDirection: "row",
                              alignItems: "center",
                              justifyContent: "space-between",
                              width: "100%",
                              // backgroundColor: 'green',
                            }}
                          >
                            <Text
                              style={[
                                styles.modalBodyText,
                                { fontSize: 16, fontFamily: "NunitoSans-Bold" },
                              ]}
                            >
                              Name
                            </Text>

                            <TextInput
                              style={[
                                styles.textInput,
                                {
                                  // marginHorizontal: 24,
                                  width: "75%",
                                },
                              ]}
                              multiline={false}
                              clearButtonMode="while-editing"
                              placeholder="Enter your name here"
                              onChangeText={(text) => {
                                // setUserInfoName(text)

                                UserStore.update((s) => {
                                  s.userInfoName = text;
                                });
                              }}
                              value={userInfoName}
                            />
                          </View>

                          <View
                            style={{
                              flexDirection: "row",
                              alignItems: "center",
                              justifyContent: "space-between",
                              width: "100%",
                              // backgroundColor: 'green',
                            }}
                          >
                            <Text
                              style={[
                                styles.modalBodyText,
                                { fontSize: 16, fontFamily: "NunitoSans-Bold" },
                              ]}
                            >
                              Phone
                            </Text>

                            <TextInput
                              style={[
                                styles.textInput,
                                {
                                  // marginHorizontal: 24,
                                  width: "75%",
                                },
                              ]}
                              multiline={false}
                              clearButtonMode="while-editing"
                              placeholder="Enter your phone here"
                              onChangeText={(text) => {
                                // setUserInfoPhone(text)

                                UserStore.update((s) => {
                                  s.userInfoPhone = text;
                                });
                              }}
                              value={userInfoPhone}
                              keyboardType={"decimal-pad"}
                            />
                          </View>
                        </View>

                        <View
                          style={{
                            alignItems: "center",
                            flexDirection: "row",
                            // justifyContent: 'space-between',
                            justifyContent: "space-around",
                            width: "100%",
                          }}
                        >
                          {/* <TouchableOpacity
                style={styles.modalSaveButton}
                onPress={() => {
                  setInputTablePax(selectedOutletTable.capacity);
                  setUpdateTableModal(true);
                }}>
                <Text style={[styles.modalDescText, { color: Colors.primaryColor }]}>Edit</Text>
              </TouchableOpacity> */}

                          <TouchableOpacity
                            style={[
                              styles.modalSaveButton,
                              {
                                // width: isMobile() ? windowWidth * 0.3 : windowWidth * 0.1,
                                marginTop: windowHeight * 0.04,
                                paddingHorizontal: windowWidth * 0.05,

                                // width: windowWidth * 0.01,
                                width: '40%',
                              },
                            ]}
                            onPress={() => {
                              proceedOrderAfterUserInfo();
                            }}
                          >
                            <Text
                              style={[
                                styles.modalDescText,
                                { color: Colors.primaryColor },
                              ]}
                            >
                              Proceed
                            </Text>
                          </TouchableOpacity>

                          <TouchableOpacity
                            style={[
                              styles.modalSaveButton,
                              {
                                // width: isMobile() ? windowWidth * 0.3 : windowWidth * 0.1,
                                marginTop: windowHeight * 0.04,
                                paddingHorizontal: windowWidth * 0.05,

                                // width: windowWidth * 0.01,
                                width: '40%',
                              },
                            ]}
                            onPress={() => {
                              proceedOrderAfterUserInfo(true);
                            }}
                          >
                            <Text
                              style={[
                                styles.modalDescText,
                                { color: Colors.primaryColor },
                              ]}
                            >
                              {'Skip'}
                            </Text>
                          </TouchableOpacity>
                        </View>
                      </View>
                    </View>
                  </Modal>

                  <ScrollView
                    style={{ flex: 1 }}
                    contentContainerStyle={{
                      paddingBottom: 60,
                    }}
                    showsVerticalScrollIndicator={false}
                    refreshControl={
                      <RefreshControl
                        refreshing={refreshing}
                        onRefresh={() => {
                          getCartItem();
                          getPopular();
                        }}
                      />
                    }
                  >
                    <div id="myPayHolder">
                      {/* <button
          id="myPay"
          style={{
            display: "none",
          }}
          type="button"
          data-toggle="molpayseamless"
        >
          Pay by RazerPay
        </button> */}
                    </div>

                    {/* <button
                      id="myPay"
                      style={{
                        display: "none",
                      }}
                      type="button"
                      data-toggle="molpayseamless"
                    >
                      Pay by RazerPay
                    </button> */}

                    {/* <View style={{ flexDirection: "row", marginBottom: 10, marginTop: 5 }}>
                <TouchableOpacity style={{
                  width: "30%",
                  height: 30,
                  backgroundColor: orderType === ORDER_TYPE.DELIVERY ? Colors.primaryColor : Colors.whiteColor,
                  borderRadius: 40, justifyContent: "center", alignItems: "center", borderWidth: 1, borderColor: Colors.descriptionColor
                }}
                  onPress={() => { changeClick() }}
                >
                  <Text style={{ color: orderType === ORDER_TYPE.DELIVERY ? Colors.whiteColor : "#adadad", fontFamily: "NunitoSans-Regular" }}>Delivery</Text>
                </TouchableOpacity>
                <TouchableOpacity style={{
                  width: "30%",
                  height: 30,
                  backgroundColor: orderType === ORDER_TYPE.PICKUP ? Colors.primaryColor : Colors.whiteColor,
                  borderRadius: 40, justifyContent: "center", alignItems: "center", borderWidth: 1, borderColor: Colors.descriptionColor, marginLeft: 8
                }}
                  onPress={() => { changeClick1() }}
                >
                  <Text style={{ color: orderType === ORDER_TYPE.PICKUP ? Colors.whiteColor : "#adadad", fontFamily: "NunitoSans-Regular" }}>Pick-up</Text>
                </TouchableOpacity>
              </View> */}

                    {/* {orderType !== ORDER_TYPE.DINEIN &&
                  <View style={{
                    flexDirection: "row",
                    marginBottom: 15,
                    marginTop: 5,
                    paddingHorizontal: 24,
                  }}>
                    <TouchableOpacity style={{
                      // width: "30%",
                      // height: 30,
                      width: 86,
                      backgroundColor: orderType === ORDER_TYPE.DELIVERY ? Colors.primaryColor : Colors.whiteColor,
                      borderRadius: 40, justifyContent: "center", alignItems: "center", borderWidth: 1,
                      borderColor: orderType === ORDER_TYPE.DELIVERY ? Colors.descriptionColor : "#adadad",
                      paddingVertical: 6,
                    }}
                      onPress={() => { changeOrderType() }}
                    >
                      <Text style={{ color: orderType === ORDER_TYPE.DELIVERY ? Colors.whiteColor : "#adadad", fontFamily: "NunitoSans-SemiBold", fontSize: 13 }}>Delivery</Text>
                    </TouchableOpacity>
                    <TouchableOpacity style={{
                      // width: "30%",
                      // height: 30,
                      width: 86,
                      backgroundColor: orderType === ORDER_TYPE.PICKUP ? Colors.primaryColor : Colors.whiteColor,
                      borderRadius: 40, justifyContent: "center", alignItems: "center", borderWidth: 1,
                      borderColor: orderType === ORDER_TYPE.PICKUP ? Colors.descriptionColor : "#adadad",
                      marginLeft: 8,
                      paddingVertical: 6,
                    }}
                      onPress={() => { changeOrderType() }}
                    >
                      <Text style={{ color: orderType === ORDER_TYPE.PICKUP ? Colors.whiteColor : "#adadad", fontFamily: "NunitoSans-SemiBold", fontSize: 13 }}>Takeaway</Text>
                    </TouchableOpacity>
                  </View>
                } */}

                    <View
                      style={{
                        width: "100%",
                        // height: 60,
                        paddingVertical: 16,
                        backgroundColor: "#ddebe5",
                        justifyContent: "center",
                        paddingHorizontal: 28,
                        // marginTop: 5,

                        shadowColor: "#000",
                        shadowOffset: {
                          width: 0,
                          height: 1,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 2.22,
                        elevation: 1,
                      }}
                    >
                      <Text
                        style={{
                          color: Colors.primaryColor,
                          marginLeft: 4,
                          fontSize: 17,
                          fontFamily: "NunitoSans-SemiBold",
                        }}
                      >
                        Order Summary
                      </Text>
                    </View>

                    <View
                      style={{
                        flexDirection: "row",
                        marginTop: 15,
                      }}
                    >
                      <FlatList
                        style={{ marginBottom: 10 }}
                        data={tvCartItemsProcessed}
                        extraData={tvCartItemsProcessed}
                        renderItem={renderItem}
                        keyExtractor={(item, index) => String(index)}
                        contentContainerStyle={{
                          paddingHorizontal: 30,
                          paddingBottom: 5,
                        }}
                      />
                    </View>
                    {/* <Modal
                style={{ flex: 1 }}
                visible={visible}
                transparent={true}
                animationType="slide">
                <View
                  style={{
                    backgroundColor: 'rgba(0,0,0,0.5)',
                    flex: 1,
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}>
                  <View style={styles.confirmBox}>
                    <TouchableOpacity
                      onPress={() => {
                        setState({ visible: false });
                        setState({ visible1: false });
                      }}>
                      <View
                        style={{
                          alignSelf: 'flex-end',
                          padding: 16,
                        }}>
                        <Close name="closecircle" size={28} />
                      </View>
                    </TouchableOpacity>
                    <View>
                      <Text
                        style={{
                          textAlign: 'center',
                          fontWeight: 'bold',
                          fontSize: 16,
                          marginBottom: 5,
                        }}>
                        Do you want to delete all?
                    </Text>
                    </View>
                    <View
                      style={{
                        justifyContent: 'center',
                        alignItems: 'center',
                        width: '100%',
                        alignContent: 'center',
                        marginBottom: '6%',
                        height: '50%',
                      }}>
                      <View
                        style={{
                          alignItems: 'center',
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            Cart.deleteCartItem(editingItemId);
                            getCartItem();
                            setState({ visible1: false });
                            setState({ visible: false });
                          }}
                          style={{
                            backgroundColor: Colors.fieldtBgColor,
                            width: '30%',
                            justifyContent: 'center',
                            alignItems: 'center',
                            alignContent: 'center',
                            borderRadius: 5,
                            height: '75%',
                          }}>
                          <Text style={{ fontSize: 15, color: Colors.primaryColor }}>
                            Yes
                        </Text>
                        </TouchableOpacity>
                        <View style={{ marginLeft: '3%', marginRight: '3%' }}></View>
                        <TouchableOpacity
                          onPress={() => {
                            setState({ visible1: true });
                          }}
                          style={{
                            backgroundColor: Colors.fieldtBgColor,
                            width: '30%',
                            justifyContent: 'center',
                            alignItems: 'center',
                            alignContent: 'center',
                            borderRadius: 5,
                            height: '75%',
                          }}>
                          <Text style={{ fontSize: 15, color: Colors.primaryColor }}>
                            No
                        </Text>
                        </TouchableOpacity>
                        
                      </View>
                    </View>
                  </View>
                </View>
              </Modal> */}
                    <Modal
                      style={{ flex: 1 }}
                      visible={visible}
                      transparent={true}
                      animationType="slide"
                    >
                      <View
                        style={{
                          backgroundColor: "rgba(0,0,0,0.5)",
                          flex: 1,
                          justifyContent: "center",
                          alignItems: "center",
                        }}
                      >
                        <View
                          style={[
                            styles.confirmBox,
                            {
                              width: windowWidth * 0.6,
                              height: windowHeight * 0.33,
                            },
                          ]}
                        >
                          <TouchableOpacity
                            onPress={() => {
                              setState({ visible1: false });
                              setState({ visible: false });
                            }}
                          >
                            <View
                              style={{
                                alignSelf: "flex-end",
                                padding: 13,
                              }}
                            >
                              <Close name="closecircle" size={28} />
                            </View>
                          </TouchableOpacity>
                          <View>
                            <Text
                              style={{
                                textAlign: "center",
                                fontWeight: "bold",
                                fontSize: 16,
                                marginBottom: 10,
                              }}
                            >
                              Delete
                            </Text>
                          </View>
                          <View
                            style={{
                              justifyContent: "center",
                              alignItems: "center",
                              width: "100%",
                              alignContent: "center",
                              marginBottom: "10%",
                              height: "50%",
                            }}
                          >
                            <View
                              style={{
                                alignItems: "center",
                                marginBottom: "2%",
                              }}
                            >
                              {/* <NumericInput
                            value={value}
                            onChange={(value) => setState({ value })}
                            minValue={1}
                            maxValue={qty}
                            totalWidth={200}
                            totalHeight={40}
                            iconSize={25}
                            step={1}
                            valueType="real"
                            rounded
                            textColor={Colors.primaryColor}
                            iconStyle={{ color: "white" }}
                            rightButtonBackgroundColor={Colors.primaryColor}
                            leftButtonBackgroundColor={"grey"}
                          /> */}
                            </View>
                            <View
                              style={{
                                alignItems: "center",
                                flexDirection: "row",
                                justifyContent: "space-between",
                              }}
                            >
                              <TouchableOpacity
                                onPress={() => {
                                  onChangeQty(value, editingItemId);
                                  getCartItem();
                                  setState({ visible1: false });
                                  setState({ visible: false });
                                  setState({ value: "" });
                                }}
                                style={{
                                  backgroundColor: Colors.primaryColor,
                                  width: "30%",
                                  justifyContent: "center",
                                  alignItems: "center",
                                  alignContent: "center",
                                  borderRadius: 5,
                                  height: "75%",
                                  marginTop: 10,
                                }}
                              >
                                <Text
                                  style={{
                                    fontSize: 15,
                                    color: Colors.whiteColor,
                                  }}
                                >
                                  Update
                                </Text>
                              </TouchableOpacity>
                            </View>
                          </View>
                        </View>
                      </View>
                    </Modal>

                    <Modal
                      style={{ flex: 1 }}
                      visible={showMpsChannelModal}
                      transparent={true}
                      animationType="fade"
                    >
                      <View
                        style={{
                          backgroundColor: "rgba(0,0,0,0.5)",
                          flex: 1,
                          justifyContent: "center",
                          alignItems: "center",
                        }}
                      >
                        <View
                          style={[
                            styles.confirmBox,
                            {
                              // width: isMobile() ? '80%' : '40%',
                              // height: isMobile() ? '80%' : '70%',
                              width: isMobile()
                                ? windowWidth * 0.8
                                : windowWidth * 0.4,
                              height: isMobile()
                                ? windowHeight * 0.8
                                : windowHeight * 0.7,
                              borderRadius: 10,
                            },
                          ]}
                        >
                          <TouchableOpacity
                            onPress={() => {
                              setShowMpsChannelModal(false);
                            }}
                          >
                            <View
                              style={{
                                alignSelf: "flex-end",
                                padding: 13,
                              }}
                            >
                              <Close name="closecircle" size={28} />
                            </View>
                          </TouchableOpacity>
                          <View>
                            <Text
                              style={{
                                textAlign: "center",
                                fontSize: 16,
                                marginBottom: 10,
                                fontFamily: "NunitoSans-Bold",
                              }}
                            >
                              Choose your payment method
                            </Text>
                          </View>
                          <View
                            style={{
                              marginTop: 15,
                              justifyContent: "center",
                              alignItems: "center",
                              width: "100%",
                              alignContent: "center",
                              // marginBottom: "10%",
                              height: "75%",
                            }}
                          >
                            {!isLoading ? (
                              <FlatList
                                data={MPS_CHANNEL_LIST}
                                renderItem={renderMpsChannelList}
                                keyExtractor={(item, index) => String(index)}
                                contentContainerStyle={{
                                  paddingHorizontal: 24,
                                }}
                              />
                            ) : (
                              <ActivityIndicator
                                color={Colors.primaryColor}
                                size={"large"}
                              />
                            )}
                          </View>
                        </View>
                      </View>
                    </Modal>

                    {/* <Text style={{
                fontWeight: "bold",
                fontSize: 20,
                marginTop: 10,
                marginBottom: 10,
    
                paddingHorizontal: 24
              }}>Other popular items</Text>
    
              <FlatList
                horizontal={true}
                data={popular}
                extraData={popular}
                renderItem={renderPopularItem}
                keyExtractor={(item, index) => String(index)}
                contentContainerStyle={{
                  paddingHorizontal: 24,
                }}
              /> */}

                    {/* <View style={{ height: 20 }}></View> */}

                    <View
                      style={{
                        // backgroundColor: 'red',
                        flexDirection: "row",
                        width: isMobile() ? "80%" : "94%",
                        marginLeft: 40,
                        marginTop: 0,

                        paddingHorizontal: 0,
                      }}
                    >
                      <View style={{ flex: 1 }}>
                        <Text style={styles.description}>Subtotal</Text>
                        <Text style={styles.description}>Discount</Text>
                        {selectedOutlet && selectedOutlet.taxActive ? (
                          <Text style={[styles.description]}>
                            {`Tax (${(selectedOutlet.taxRate * 100).toFixed(
                              0
                            )}%)`}
                          </Text>
                        ) : (
                          <></>
                        )}
                        {selectedOutlet && selectedOutlet.scActive ? (
                          <Text style={[styles.description]}>
                            {`${selectedOutlet.scName ? selectedOutlet.scName : 'Service Charge'} (${(
                              selectedOutlet.scRate * 100
                            ).toFixed(0)}%)`}
                          </Text>
                        ) : (
                          <></>
                        )}
                        {orderType == ORDER_TYPE.DELIVERY ? (
                          <View>
                            <Text
                              style={styles.description}
                            >{`Delivery Fees (${selectedSender.label})`}</Text>
                            <Text style={styles.smallDescription}>
                              (This fees is quoted by 3rd party logistic and it
                              might be different as it depends on the exact
                              distance)
                            </Text>
                          </View>
                        ) : null}

                        {
                          (selectedOutlet.deliveryPackagingFee && orderType === ORDER_TYPE.DELIVERY)
                            ?
                            <Text
                              style={[
                                styles.description,
                                // switchMerchant ? { fontSize: 10 } : {},
                              ]}>
                              {`Delivery Packaging Fee`}
                            </Text>
                            :
                            <></>
                        }

                        {
                          (selectedOutlet.pickupPackagingFee && orderType === ORDER_TYPE.PICKUP)
                            ?
                            <Text
                              style={[
                                styles.description,
                                // switchMerchant ? { fontSize: 10 } : {},
                              ]}>
                              {`Takeaway Packaging Fee`}
                            </Text>
                            :
                            <></>
                        }

                        {
                          (isPlacingReservation && selectedOutlet.reservationDepositAmount >= 1.01)
                            ?
                            <Text
                              style={[
                                styles.description,
                                // switchMerchant ? { fontSize: 10 } : {},
                              ]}>
                              {`Reservation Deposit`}
                            </Text>
                            :
                            <></>
                        }

                        <Text style={styles.description}></Text>
                      </View>

                      <View
                        style={{
                          width: "25%",
                        }}
                      >
                        <View
                          style={{
                            flexDirection: "row",
                            justifyContent: "space-between",
                            width: "100%",
                          }}
                        >
                          <Text style={styles.price}>RM</Text>

                          <Text style={styles.price}>
                            {totalPrice.toFixed(2)}
                          </Text>
                        </View>

                        <View
                          style={{
                            flexDirection: "row",
                            justifyContent: "space-between",
                            width: "100%",
                          }}
                        >
                          <Text style={styles.price}>RM</Text>

                          <Text style={styles.price}>
                            {(
                              totalDiscount +
                              (usePointsToRedeem ? pointsToRedeemDiscount : 0) +
                              (usePointsToRedeemLCC
                                ? pointsToRedeemDiscountLCC
                                : 0) +
                              discountPromotionsTotal
                            ).toFixed(2)}
                          </Text>
                        </View>

                        {selectedOutlet && selectedOutlet.taxActive ? (
                          <View
                            style={{
                              flexDirection: "row",
                              justifyContent: "space-between",
                              width: "100%",
                            }}
                          >
                            <Text style={styles.price}>RM</Text>

                            <Text style={styles.price}>
                              {totalTax.toFixed(2)}
                            </Text>
                          </View>
                        ) : (
                          <></>
                        )}

                        {selectedOutlet && selectedOutlet.scActive ? (
                          <View
                            style={{
                              flexDirection: "row",
                              justifyContent: "space-between",
                              width: "100%",
                            }}
                          >
                            <Text style={styles.price}>RM</Text>

                            <Text style={styles.price}>{totalSc.toFixed(2)}</Text>
                          </View>
                        ) : (
                          <></>
                        )}

                        {orderType == ORDER_TYPE.DELIVERY ?
                          <View
                            style={{
                              flexDirection: "row",
                              justifyContent: "space-between",
                              width: "100%",
                            }}>

                            <Text
                              style={[styles.price,
                                // switchMerchant ? { fontSize: 10 } : {}
                              ]}>
                              RM
                            </Text>
                            <Text
                              style={[styles.price,
                                // switchMerchant ? { fontSize: 10 } : {}
                              ]}>
                              {deliveryQuotation.totalFee == null
                                ? '0.00'
                                : deliveryQuotation.totalFee.toFixed(2)}
                            </Text>
                          </View>
                          :
                          <></>
                        }

                        {
                          (selectedOutlet.deliveryPackagingFee && orderType === ORDER_TYPE.DELIVERY)
                            ?
                            <View
                              style={{
                                flexDirection: "row",
                                justifyContent: "space-between",
                                width: "100%",
                              }}>
                              <Text
                                style={[styles.price,
                                  // switchMerchant ? { fontSize: 10 } : {}
                                ]}>
                                RM
                              </Text>
                              <Text
                                style={[styles.price,
                                  // switchMerchant ? { fontSize: 10 } : {}
                                ]}>
                                {selectedOutlet.deliveryPackagingFee.toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                              </Text>
                            </View>
                            :
                            <></>
                        }

                        {
                          (selectedOutlet.pickupPackagingFee && orderType === ORDER_TYPE.PICKUP)
                            ?
                            <View
                              style={{
                                flexDirection: "row",
                                justifyContent: "space-between",
                                width: "100%",
                              }}>
                              <Text
                                style={[styles.price,
                                  // switchMerchant ? { fontSize: 10 } : {}
                                ]}>
                                RM
                              </Text>
                              <Text
                                style={[styles.price,
                                  // switchMerchant ? { fontSize: 10 } : {}
                                ]}>
                                {selectedOutlet.pickupPackagingFee.toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                              </Text>
                            </View>
                            :
                            <></>
                        }

                        {
                          (isPlacingReservation && selectedOutlet.reservationDepositAmount >= 1.01)
                            ?
                            <View
                              style={{
                                flexDirection: "row",
                                justifyContent: "space-between",
                                width: "100%",
                              }}>
                              <Text
                                style={[styles.price,
                                  // switchMerchant ? { fontSize: 10 } : {}
                                ]}>
                                RM
                              </Text>
                              <Text
                                style={[styles.price,
                                  // switchMerchant ? { fontSize: 10 } : {}
                                ]}>
                                {selectedOutlet.reservationDepositAmount.toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                              </Text>
                            </View>
                            :
                            <></>
                        }

                        {/* <Text style={styles.price}>
                            {`RM ${(totalDiscount +
                              (usePointsToRedeem ? pointsToRedeemDiscount : 0)
                            ).toFixed(2)}`}
                          </Text>
                          <Text style={styles.price}>
                            RM{' '}
                            {totalTax.toFixed(2)}
                          </Text>
                          {orderType == ORDER_TYPE.DELIVERY ? <Text style={styles.price}>RM {deliveryQuotation.totalFee == null ? "0.00" : deliveryQuotation.totalFee.toFixed(2)}</Text> : null} */}
                        {/* <Text style={styles.price}></Text>
                  <Text style={styles.price}></Text> */}
                      </View>
                    </View>

                    <View
                      style={{
                        flexDirection: "row",
                        width: isMobile() ? "80%" : "94%",
                        marginLeft: 40,

                        paddingHorizontal: 0,
                        marginTop: 0,
                        marginBottom: 12,
                      }}
                    >
                      <View style={{ flex: 1 }}>
                        <Text style={styles.total}>TOTAL</Text>
                      </View>
                      <View
                        style={{
                          width: "25%",
                        }}
                      >
                        <View
                          style={{
                            flexDirection: "row",
                            justifyContent: "space-between",
                            width: "100%",
                          }}
                        >
                          <Text
                            style={[
                              styles.totalPrice,
                              {
                                fontSize: 13,
                              },
                            ]}
                          >
                            RM
                          </Text>

                          <Text
                            style={[
                              styles.totalPrice,
                              {
                                fontSize: 13,
                              },
                            ]}
                          >
                            {(
                              Math.round(
                                Math.max(
                                  totalPrice +
                                  totalTax +
                                  totalSc +
                                  (orderType === ORDER_TYPE.DELIVERY
                                    ? deliveryQuotation.totalFee
                                      ? deliveryQuotation.totalFee
                                      : 0
                                    : 0) +
                                  (selectedOutlet.deliveryPackagingFee && orderType === ORDER_TYPE.DELIVERY ? selectedOutlet.deliveryPackagingFee : 0) +
                                  (selectedOutlet.pickupPackagingFee && orderType === ORDER_TYPE.PICKUP ? selectedOutlet.pickupPackagingFee : 0) -
                                  totalDiscount
                                  -
                                  (usePointsToRedeem
                                    ? pointsToRedeemDiscount
                                    : 0) -
                                  (usePointsToRedeemLCC
                                    ? pointsToRedeemDiscountLCC
                                    : 0) +
                                  ((isPlacingReservation && selectedOutlet.reservationDepositAmount >= 1.01) ? selectedOutlet.reservationDepositAmount : 0)
                                  ,
                                  0
                                ) *
                                20
                              ) / 20
                            )
                              .toFixed(2)
                              .replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,")}
                          </Text>
                        </View>

                        {/* <Text style={styles.totalPrice}>
                            RM{' '}
                            {(
                              // parseFloat(totalFloat) +
                              // parseFloat(taxFloat) +
                              // parseFloat(deliveryQuotation == null ? 0.00 : parseFloat(deliveryQuotation.totalFee).toFixed(2))
                              Math.max(totalPrice + totalTax +
                                (orderType === ORDER_TYPE.DELIVERY ? (deliveryQuotation.totalFee ? deliveryQuotation.totalFee : 0) : 0) -
                                totalDiscount - (usePointsToRedeem ? pointsToRedeemDiscount : 0),
                                0)
                            ).toFixed(2)}
                          </Text> */}
                      </View>
                    </View>

                    {/* <TouchableOpacity onPress={() => { }} style={[styles.textInput1, {
                  marginHorizontal: 36,
                  width: '80%',
                  alignSelf: 'center',

                }]}
                  onPress={() => {
                    setRouteFrom(1);
                    props.navigation.navigate("Voucher", { pageFrom: 'CartScreen' });

                    props.navigation.navigate("Voucher", {
                      screen: 'Voucher',
                      params: {
                        pageFrom: 'CartScreen',
                      }
                    });
                  }}
                >
                  <View style={{
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                  }}>
                    <Text
                      style={{
                        paddingVertical: 10,
                        fontSize: 15,
                        fontFamily: 'NunitoSans-Bold',
                      }}>
                      Apply Vouchers
                    </Text>


                    <View style={{
                      marginLeft: '30%', 
                      justifyContent: 'center'
                    }}>
                      <Entypo name="chevron-thin-down" size={22} />
                    </View>

                  </View>

                  
                </TouchableOpacity> */}
                    {/* <DropDownPicker style={[styles.textInput1, { marginHorizontal: 36, width: '80%', alignSelf: 'center', }]}
                    //disabled={}
                    // dropDownStyle={{ borderWidth: 1, borderColor: '#E5E5E5', borderRadius: 5, width: 200, paddingVertical: 0, marginHorizontal: 10 }}
                    items={[{ label: 'Yes', value: 'Yes' }, { label: 'No', value: 'No' }]}
                    arrowSize={22}
                    arrowColor={'black'}
                    arrowStyle={{ paddingVertical: 0 }}
                    itemStyle={{ justifyContent: 'flex-start', marginLeft: 5 }}
                    placeholderStyle={{ marginLeft: 10, fontSize: 15, fontFamily: 'NunitoSans-Bold', }}
                    placeholder={"Apply Voucher"}
                    searchable={true}
                    // containerStyle={{ height: 30 }}
                    //   style={styles.textInput}
                    //   itemStyle={{
                    //     justifyContent: 'flex-start',
                    //   }}
                   
                    //onChangeItem={(item) => {
                    //  console.log(item);
                    //}}
                    //defaultValue={}
                  /> */}

                    {/* voucher */}
                    <View
                      style={{
                        width: isMobile() ? "80%" : "94%",
                        alignSelf: "center",
                        marginTop: 20,
                        display: "none",
                      }}
                    >
                      <Text
                        style={[
                          styles.payment,
                          {
                            // paddingHorizontal: 24,
                          },
                        ]}
                      >
                        Select your voucher
                      </Text>

                      <TouchableOpacity
                        // disabled={voucherList.length <= 1}
                        onPress={() => {
                          setShowVoucher(!showVoucher);
                        }}
                        style={[
                          styles.textInput,
                          {
                            // marginHorizontal: 24,
                          },
                        ]}
                      >
                        <View
                          style={{
                            flexDirection: "row",
                            justifyContent: "space-between",
                            alignItems: "center",
                            // backgroundColor: 'red',
                            height: "100%",
                          }}
                        >
                          <View
                            style={{
                              flexDirection: "row",
                              alignItems: "center",
                            }}
                          >
                            <Image
                              source={selectedVoucher ? selectedVoucher.img : ""}
                              style={{
                                marginRight: 10,
                                width: 32,
                                height: 32,
                                borderRadius: 8,
                                top: 1,
                              }}
                            />

                            <Text
                              style={{
                                // paddingVertical: 14
                                fontSize: 15,
                                fontFamily: "NunitoSans-Regular",
                                color: Colors.fieldtTxtColor,
                              }}
                            >
                              {/* {'Credit/Debit Card'} */}
                              {selectedVoucher && selectedVoucher.label
                                ? selectedVoucher.label
                                : ""}
                            </Text>
                          </View>

                          <View
                            style={{
                              // marginLeft: '40%',
                              justifyContent: "center",
                              // opacity: voucherList.length > 1 ? 100 : 0,
                            }}
                          >
                            <Entypo
                              name={
                                showVoucher
                                  ? "chevron-thin-up"
                                  : "chevron-thin-down"
                              }
                              size={22}
                              color={Colors.primaryColor}
                            />
                          </View>
                        </View>
                      </TouchableOpacity>

                      <View
                        style={{
                          display: showVoucher ? "flex" : "none",
                          marginTop: -20,
                          paddingTop: 10,
                        }}
                      >
                        {voucherList.map((item, i) => {
                          return (
                            <>
                              <TouchableOpacity
                                style={{
                                  // marginHorizontal: 24,
                                  height: 50,
                                  paddingHorizontal: 20,
                                  backgroundColor: Colors.fieldtBgColor,
                                  borderRadius: 5,
                                  // marginTop: 10,
                                  // marginBottom: 10,

                                  marginTop: -5,
                                }}
                                onPress={() => {
                                  setSelectedVoucher(item);

                                  UserStore.update((s) => {
                                    s.selectedVoucherId = item.value;
                                  });

                                  setShowVoucher(false);
                                }}
                              >
                                <View
                                  style={{
                                    flexDirection: "row",
                                    justifyContent: "space-between",
                                    alignItems: "center",
                                    // backgroundColor: 'red',
                                    height: "100%",
                                  }}
                                >
                                  <View
                                    style={{
                                      flexDirection: "row",
                                      alignItems: "center",
                                    }}
                                  >
                                    { }
                                    <Image
                                      source={item.img}
                                      style={{
                                        marginRight: 10,
                                        width: 32,
                                        height: 32,
                                        borderRadius: 8,
                                        top: 1,
                                      }}
                                    />

                                    <Text
                                      style={{
                                        // paddingVertical: 14
                                        fontSize: 15,
                                        fontFamily: "NunitoSans-Regular",
                                        color: Colors.fieldtTxtColor,
                                      }}
                                    >
                                      {item.label}
                                    </Text>
                                  </View>

                                  <View
                                    style={{
                                      // marginLeft: '40%',
                                      justifyContent: "center",
                                      opacity: 0,
                                    }}
                                  >
                                    <Entypo
                                      name="chevron-thin-down"
                                      size={22}
                                      color={Colors.primaryColor}
                                    />
                                  </View>
                                </View>
                              </TouchableOpacity>
                            </>
                          );
                        })}
                      </View>
                    </View>

                    {/* promotion */}
                    <View
                      style={{
                        width: isMobile() ? "80%" : "94%",
                        alignSelf: "center",
                        marginTop: 20,
                        display: "none",
                      }}
                    >
                      <Text
                        style={[
                          styles.payment,
                          {
                            // paddingHorizontal: 24,
                          },
                        ]}
                      >
                        Enter promotions code
                      </Text>
                      <TextInput
                        style={[
                          styles.textInput,
                          {
                            // marginHorizontal: 24,
                          },
                        ]}
                        multiline={true}
                        clearButtonMode="while-editing"
                        placeholder="Enter promotions code"
                        onChangeText={(text) => {
                          setPromoCode(text);
                        }}
                        value={promoCode}
                      />
                    </View>

                    {selectedVoucherId &&
                      merchantVouchersDict[selectedVoucherId] &&
                      merchantVouchersDict[selectedVoucherId] &&
                      selectedOutlet.merchantId && (
                        <View
                          style={{
                            width: "100%",
                            display: "flex",
                            flexDirection: "row",
                            alignItems: "center",
                            marginTop: 20,
                            justifyContent: "space-around",
                          }}
                        >
                          <View
                            style={[
                              styles.pic,
                              {
                                // backgroundColor: 'red',
                                borderColor: "#E9CE8B",
                                borderWidth: 1,

                                marginLeft: 10,

                                shadowColor: "#000",
                                shadowOffset: {
                                  width: 0,
                                  height: 1,
                                },
                                shadowOpacity: 0.22,
                                shadowRadius: 2.22,
                                elevation: 2,
                              },
                            ]}
                          >
                            {merchantVouchersDict[selectedVoucherId]
                              .customLogo ? (
                              <Image
                                source={{
                                  uri: merchantVouchersDict[selectedVoucherId]
                                    .customLogo,
                                }}
                                style={{
                                  width: 90,
                                  height: Platform.OS == "ios" ? 90 : 90,
                                  borderRadius: 10,
                                }}
                              />
                            ) : null}
                            {merchantVouchersDict[selectedVoucherId]
                              .merchantLogo ? (
                              <Image
                                source={{
                                  uri: merchantVouchersDict[selectedVoucherId]
                                    .merchantLogo,
                                }}
                                style={{
                                  width: 90,
                                  height: Platform.OS == "ios" ? 90 : 90,
                                  borderRadius: 10,
                                }}
                              />
                            ) : null}
                            {!(
                              merchantVouchersDict[selectedVoucherId]
                                .customLogo ||
                              merchantVouchersDict[selectedVoucherId].merchantLogo
                            ) ? (
                              <Image
                                source={require("../asset/image/extend.png")}
                                style={{
                                  width: 90,
                                  height: Platform.OS == "ios" ? 90 : 90,
                                  borderRadius: 10,
                                }}
                              />
                            ) : null}
                          </View>

                          <Text
                            style={[
                              styles.text,
                              {
                                width: "50%",
                                fontFamily: "NunitoSans-Regular",
                              },
                            ]}
                          >
                            {merchantVouchersDict[selectedVoucherId].description}
                          </Text>
                        </View>
                      )}

                    {/* payment method */}
                    <View
                      style={{
                        width: isMobile() ? "80%" : "94%",
                        ...(!isMobile() &&
                        {
                          // marginRight: '60%',
                        }),
                        // backgroundColor: 'red',
                        alignSelf: "center",
                        marginTop: 20,
                      }}
                    >
                      <Text
                        style={[
                          styles.payment,
                          {
                            // paddingHorizontal: 24,
                          },
                        ]}
                      >
                        Select your payment method
                      </Text>

                      <TouchableOpacity
                        disabled={paymentOptionsList.length <= 1}
                        onPress={() => {
                          setShowPaymentOptions(!showPaymentOptions);
                        }}
                        style={[
                          styles.textInput,
                          {
                            // marginHorizontal: 24,
                          },
                        ]}
                      >
                        <View
                          style={{
                            flexDirection: "row",
                            justifyContent: "space-between",
                            alignItems: "center",
                            // backgroundColor: 'red',
                            height: "100%",
                          }}
                        >
                          <Text
                            style={{
                              // paddingVertical: 14
                              fontSize: 15,
                              fontFamily: "NunitoSans-Regular",
                              color: Colors.fieldtTxtColor,
                            }}
                          >
                            {/* {'Credit/Debit Card'} */}
                            {selectedPaymentOptions &&
                              selectedPaymentOptions.label
                              ? selectedPaymentOptions.label
                              : ""}
                          </Text>
                          <View
                            style={{
                              // marginLeft: '40%',
                              justifyContent: "center",
                              opacity: paymentOptionsList.length > 1 ? 100 : 0,
                            }}
                          >
                            <Entypo
                              name={
                                showPaymentOptions
                                  ? "chevron-thin-up"
                                  : "chevron-thin-down"
                              }
                              size={22}
                              color={Colors.primaryColor}
                            />
                          </View>
                        </View>
                      </TouchableOpacity>

                      <View
                        style={{
                          display: showPaymentOptions ? "flex" : "none",
                          marginTop: -20,
                          paddingTop: 10,
                        }}
                      >
                        {paymentOptionsList.map((item, i) => {
                          return (
                            <TouchableOpacity
                              style={{
                                // marginHorizontal: 24,
                                height: 50,
                                paddingHorizontal: 20,
                                backgroundColor: Colors.fieldtBgColor,
                                borderRadius: 5,
                                // marginTop: 10,
                                // marginBottom: 10,

                                marginTop: -5,
                              }}
                              onPress={() => {
                                setSelectedPaymentOptions(item);

                                setShowPaymentOptions(false);
                              }}
                            >
                              <View
                                style={{
                                  flexDirection: "row",
                                  justifyContent: "space-between",
                                  alignItems: "center",
                                  // backgroundColor: 'red',
                                  height: "100%",
                                }}
                              >
                                <Text
                                  style={{
                                    // paddingVertical: 14
                                    fontSize: 15,
                                    fontFamily: "NunitoSans-Regular",
                                    color: Colors.fieldtTxtColor,
                                  }}
                                >
                                  {item.label}
                                </Text>
                                <View
                                  style={{
                                    // marginLeft: '40%',
                                    justifyContent: "center",
                                    opacity: 0,
                                  }}
                                >
                                  <Entypo
                                    name="chevron-thin-down"
                                    size={22}
                                    color={Colors.primaryColor}
                                  />
                                </View>
                              </View>
                            </TouchableOpacity>
                          );
                        })}
                      </View>
                    </View>

                    <View style={{
                      // alignSelf: 'center', marginTop: '4%', width: isMobile() ? windowWidth * 0.7 : windowWidth * 0.4, justifyContent: 'center'
                      width: isMobile() ? "80%" : "94%",
                      ...(!isMobile() &&
                      {
                        // marginRight: '60%',
                      }),
                      // backgroundColor: 'red',
                      alignSelf: "center",
                      marginTop: 20,
                    }}>
                      <View style={{
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                        marginVertical: 5,
                        //borderWidth: 1,
                        //borderColor: 'red',
                      }}>
                        <View style={{ justifyContent: 'center' }}>
                          <Text style={{
                            fontFamily: 'NunitoSans-Bold',
                            color: 'Black',
                            fontSize: 16,
                            textAlign: 'center',

                          }}>Name :</Text>
                        </View>

                        <TextInput
                          value={name}
                          onChangeText={(text) => { setName(text) }}
                          style={[styles.textInput,
                          {
                            // width: isMobile() ? windowWidth * 0.4 : windowWidth * 0.325,
                            width: '100%',
                            height: isMobile() ? 35 : windowHeight * 0.04,
                            borderRadius: 10,
                            borderColor: '#E5E5E5',

                            shadowOffset: {
                              width: 0,
                              height: 2,
                            },
                            shadowOpacity: 0.22,
                            shadowRadius: 3.22,
                            elevation: 0,
                            marginLeft: isMobile() ? 20 : 50,
                          }]}
                          underlineColorAndroid={Colors.fieldtBgColor}
                          clearButtonMode="while-editing"
                          placeholder='Full name for this voucher sent to' />
                      </View>

                      <View style={{
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                        marginVertical: 5,
                        //borderWidth: 1,
                        //borderColor: 'red',
                      }}>
                        <View style={{ justifyContent: 'center' }}>
                          <Text style={{
                            fontFamily: 'NunitoSans-Bold',
                            color: 'Black',
                            fontSize: 16,
                            textAlign: 'center',

                          }}>Phone:</Text>
                        </View>
                        <TextInput
                          value={phone}
                          onChangeText={(text) => { setPhone(text) }}
                          style={[styles.textInput,
                          {
                            // width: isMobile() ? windowWidth * 0.4 : windowWidth * 0.325,
                            width: '100%',
                            height: isMobile() ? 35 : windowHeight * 0.04,
                            borderRadius: 10,
                            borderColor: '#E5E5E5',

                            shadowOffset: {
                              width: 0,
                              height: 2,
                            },
                            shadowOpacity: 0.22,
                            shadowRadius: 3.22,
                            elevation: 0,
                            marginLeft: isMobile() ? 20 : 50,
                          }]}
                          underlineColorAndroid={Colors.fieldtBgColor}
                          clearButtonMode="while-editing"
                          placeholder='Phone number that used to bind this voucher (Ex: 60153391134)' />
                      </View>

                      <View style={{
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                        marginVertical: 5,
                        //borderWidth: 1,
                        //borderColor: 'red',
                      }}>
                        <View style={{ justifyContent: 'center' }}>
                          <Text style={{
                            fontFamily: 'NunitoSans-Bold',
                            color: 'Black',
                            fontSize: 16,
                            textAlign: 'center',

                          }}>Email :</Text>
                        </View>
                        <TextInput
                          value={email}
                          onChangeText={(text) => { setEmail(text) }}
                          style={[styles.textInput,
                          {
                            // width: isMobile() ? windowWidth * 0.4 : windowWidth * 0.325,
                            width: '100%',
                            height: isMobile() ? 35 : windowHeight * 0.04,
                            borderRadius: 10,
                            borderColor: '#E5E5E5',

                            shadowOffset: {
                              width: 0,
                              height: 2,
                            },
                            shadowOpacity: 0.22,
                            shadowRadius: 3.22,
                            elevation: 0,
                            marginLeft: isMobile() ? 20 : 50,
                          }]}
                          underlineColorAndroid={Colors.fieldtBgColor}
                          clearButtonMode="while-editing"
                          placeholder='Email address that used to inform the purchased voucher info (Optional)' />
                      </View>
                    </View>

                    {Cart.getPayment() && (
                      <View
                        style={[
                          styles.card,
                          {
                            backgroundColor: "transparent",
                            width: "100%",
                            display: "flex",
                            flexDirection: "row",
                            alignItems: "center",
                            marginTop: 10,
                            justifyContent: "flex-start",
                            minWidth: windowWidth - 32,
                          },
                        ]}
                      >
                        <View
                          style={{
                            marginRight: Platform.OS == "ios" ? 120 : 25,
                            left: -40,
                          }}
                        >
                          <View
                            style={[
                              styles.pic,
                              {
                                // width: 0,
                                // height: 0,
                                backgroundColor: "transparent",
                                marginTop: 0,
                                marginRight: 30,
                              },
                            ]}
                          >
                            <Image
                              resizeMode={"contain"}
                              source={{ uri: Cart.getPayment().image }}
                              style={{ width: 200, height: 70, borderRadius: 10 }}
                            />
                          </View>
                        </View>
                        <View
                          style={
                            {
                              // width: '60%',
                            }
                          }
                        >
                          <Text style={styles.title}>
                            {Cart.getPayment().cardType}
                          </Text>
                          <Text style={styles.text}>
                            {Cart.getPayment().cardNumber.replace(/./g, "*")}
                          </Text>
                        </View>
                      </View>
                    )}

                    {showPointsToRedeemOptions ? (
                      <View
                        style={{
                          width: "80%",
                          alignSelf: "center",
                          marginTop: 10,
                          marginBottom: 10,
                          paddingLeft: 2,
                        }}
                      >
                        <View
                          style={{
                            flexDirection: "row",
                            alignItems: "center",
                          }}
                        >
                          <TouchableOpacity
                            underlayColor="transparent"
                            onPress={() => {
                              setUsePointsToRedeem(!usePointsToRedeem);
                            }}
                          >
                            <View
                              style={[
                                styles.checkBox,
                                {
                                  backgroundColor: usePointsToRedeem
                                    ? Colors.primaryColor
                                    : null,
                                },
                              ]}
                            >
                              <AntDesign name="check" size={16} color="#ffffff" />
                            </View>
                          </TouchableOpacity>

                          <Text
                            style={{
                              color: Colors.descriptionColor,
                              marginLeft: 12,
                              fontSize: 14,
                              fontFamily: "NunitoSans-Regular",
                            }}
                          >
                            {`Use ${pointsToRedeem} points to redeem (RM${pointsToRedeemDiscount.toFixed(
                              2
                            )} off)`}
                          </Text>
                        </View>

                        {/* <Text style={[styles.payment, {
                        // paddingHorizontal: 24,
                      }]}>{`Points to redeem (${userPointsBalance} points available)`}</Text>

                      <TextInput
                        underlineColorAndroid={Colors.fieldtBgColor}
                        style={styles.textInput}
                        placeholder="0"
                        onSubmitEditing={({ text }) => {
                          // setState({ remark: text });

                          var point = text.length > 0 ? parseInt(text) : 0;
                          point = Math.max(0, point);
                          point = Math.min(userPointsBalance, point);

                          setPointsToRedeem(point);
                        }}
                        onChangeText={(text) => {
                          // setState({ remark: text });

                          var point = text.length > 0 ? parseInt(text) : 0;
                          point = Math.max(0, point);
                          point = Math.min(userPointsBalance, point);

                          setPointsToRedeem(point);
                        }}
                        defaultValue={pointsToRedeem.toString()}
                        keyboardType={"decimal-pad"}
                      /> */}
                      </View>
                    ) : (
                      <></>
                    )}

                    {showPointsToRedeemOptionsLCC ? (
                      <View
                        style={{
                          width: "80%",
                          alignSelf: "center",
                          marginTop: 10,
                          marginBottom: 10,
                          paddingLeft: 2,
                        }}
                      >
                        <View
                          style={{
                            flexDirection: "row",
                            alignItems: "center",
                          }}
                        >
                          <TouchableOpacity
                            underlayColor="transparent"
                            onPress={() => {
                              console.log("usePointsToRedeemLCC clicked");
                              setUsePointsToRedeemLCC(!usePointsToRedeemLCC);
                            }}
                          >
                            <View
                              style={[
                                styles.checkBox,
                                {
                                  backgroundColor: usePointsToRedeemLCC
                                    ? Colors.primaryColor
                                    : null,
                                },
                              ]}
                            >
                              <AntDesign name="check" size={16} color="#ffffff" />
                            </View>
                          </TouchableOpacity>

                          <Text
                            style={{
                              color: Colors.descriptionColor,
                              marginLeft: 12,
                              fontSize: 14,
                              fontFamily: "NunitoSans-Regular",
                            }}
                          >
                            {`Use ${pointsToRedeemLCC} credits to redeem (RM${pointsToRedeemDiscountLCC.toFixed(
                              2
                            )} off)`}
                          </Text>
                        </View>

                        {/* <Text style={[styles.payment, {
                                  // paddingHorizontal: 24,
                                }]}>{`Points to redeem (${userPointsBalance} points available)`}</Text>

                                <TextInput
                                  underlineColorAndroid={Colors.fieldtBgColor}
                                  style={styles.textInput}
                                  placeholder="0"
                                  onSubmitEditing={({ text }) => {
                                    // setState({ remark: text });

                                    var point = text.length > 0 ? parseInt(text) : 0;
                                    point = Math.max(0, point);
                                    point = Math.min(userPointsBalance, point);

                                    setPointsToRedeem(point);
                                  }}
                                  onChangeText={(text) => {
                                    // setState({ remark: text });

                                    var point = text.length > 0 ? parseInt(text) : 0;
                                    point = Math.max(0, point);
                                    point = Math.min(userPointsBalance, point);

                                    setPointsToRedeem(point);
                                  }}
                                  defaultValue={pointsToRedeem.toString()}
                                  keyboardType={"decimal-pad"}
                                /> */}
                      </View>
                    ) : (
                      <></>
                    )}

                    {/* <View style={{ height: 20 }}></View> */}
                  </ScrollView>

                  <TouchableOpacity
                    disabled={isLoading}
                    style={{
                      backgroundColor: "white",
                      shadowColor: "#000",
                      shadowOffset: {
                        width: 0,
                        height: 1,
                      },
                      shadowOpacity: 0.22,
                      shadowRadius: 2.22,
                      elevation: 9,

                      // bottom: -10,
                      bottom: 45,
                    }}
                    onPress={() => {
                      // if (test1 == 1) {
                      //   extend(Cart.getParkingId(), cartItem[0].quantity)
                      // }
                      // else {
                      //   if (type == 1 && (Cart.getDeliveryAddress() == null || Cart.getDeliveryAddress() == undefined)) {
                      //     window.confirm(
                      //       'Error',
                      //       'Delivery address not selected.',
                      //       [{ text: 'OK', onPress: () => { } }],
                      //       { cancelable: false },
                      //     );
                      //   } else if (Cart.getOrderType() != 0) {
                      //     var amount = (
                      //       parseFloat(totalFloat) +
                      //       parseFloat(taxFloat)
                      //     ).toFixed(2)
                      //     var amount = 5.00 //sandbox only allow maximum RM5
                      //     // change to uuidv4
                      //     var orderId = User.getUserId() + ':T' + Date.now().toString()
                      //     console.log("ORDER ID", orderId)
                      //     //remember to change to this in production
                      //     //molPayment(amount, 'orderId')
                      //     molPayment(5, orderId)
                      //     //placeOrder();
                      //   }
                      //   else {
                      //     placeOrder();
                      //   }
                      // }

                      var isValidProceed = false;

                      var isValidOrderTime = false;

                      const newRev = moment(
                        `${rev_order_date} ${rev_time}`,
                        "DD/MM/YYYY hh.mmA"
                      );

                      try {
                        const currDay = mondayFirst(moment(newRev).day());

                        var outletOpeningToday = null;

                        var startTimeStr = null;
                        var endTimeStr = null;

                        const outletOpening =
                          outletsOpeningDict[selectedOutlet.uniqueId];

                        if (outletOpening) {
                          outletOpeningToday = outletOpening[WEEK[currDay]];
                        }

                        if (outletOpeningToday) {
                          startTimeStr = outletOpeningToday.split("-")[0];
                          endTimeStr = outletOpeningToday.split("-")[1];

                          // const startTime = moment(startTimeStr, 'HHmm');
                          // const endTime = moment(endTimeStr, 'HHmm');

                          var startDateTime = moment(
                            `${rev_order_date} ${startTimeStr}`,
                            "DD/MM/YYYY HHmm"
                          );
                          var endDateTime = moment(
                            `${rev_order_date} ${endTimeStr}`,
                            "DD/MM/YYYY HHmm"
                          );

                          if (moment(endDateTime).hours() === 0 &&
                            moment(endDateTime).minutes() === 0 &&
                            moment(endDateTime).seconds() === 0) {
                            endDateTime = moment(endDateTime).set({
                              hour: 23,
                              minute: 59,
                              second: 59,
                            });
                          }

                          console.log(moment(newRev).format());
                          console.log(moment(startDateTime).format());
                          console.log(moment(endDateTime).format());
                          console.log(
                            moment(newRev).isSameOrAfter(startDateTime)
                          );
                          console.log(moment(newRev).isBefore(endDateTime));

                          isValidOrderTime =
                            moment(newRev).isSameOrAfter(startDateTime) &&
                            moment(newRev).isBefore(endDateTime);
                        }
                      } catch (ex) {
                        console.error(ex);
                      }

                      if (orderType === ORDER_TYPE.DELIVERY) {
                        if (deliveryQuotation && deliveryQuotation.courierCode) {
                          isValidProceed = true;
                        } else {
                          window.confirm(
                            "Info\nSorry, we unable to deliver to this address, please try another one."
                          );
                        }
                      } else {
                        isValidProceed = true;
                      }

                      if (isValidOrderTime || true) {
                        if (isValidProceed) {
                          ////////////////////////////////////////

                          // checking here

                          if (selectedTaggableVoucherPurchase && selectedTaggableVoucherPurchase.uniqueId) {
                            var userPhone = phone.replaceAll('-', '').replaceAll(' ', '').replaceAll('+', '');

                            // if (userPhone.startsWith('6')) {
                            //   userPhone = userPhone.slice(1);
                            // }

                            userPhone = userPhone.replace(/[^0-9]/g, '');

                            if (!userPhone.startsWith('6')) {
                              userPhone = '6' + userPhone;
                            }

                            if (userPhone && name) {

                            }
                            else {
                              alert('Please fill in the name and phone number before proceed.');

                              return;
                            }

                            if (selectedTaggableVoucherPurchase && selectedTaggableVoucherPurchase.voucherQuantity > 0) {

                            }
                            else {
                              alert('This voucher is already fully redeemed.');

                              return;
                            }
                          }
                          else {
                            alert('Invalid voucher to proceed.');

                            return;
                          }

                          ////////////////////////////////////////

                          if (
                            selectedPaymentOptions.value ===
                            USER_ORDER_PAYMENT_OPTIONS.ONLINE
                          ) {
                            // startMolPay();
                            setShowMpsChannelModal(true);
                          } else if (
                            selectedPaymentOptions.value ===
                            USER_ORDER_PAYMENT_OPTIONS.OFFLINE
                          ) {
                            CommonStore.update((s) => {
                              s.isLoading = true;
                            });

                            placeUserOrder(null);
                          } else {
                            window.confirm("Error\nInvalid payment options");
                          }
                        }
                      } else {
                        window.confirm(
                          "Notice\nOutlet is outside of operating hours."
                        );
                      }
                    }}
                  >
                    <View
                      style={{
                        backgroundColor: Colors.primaryColor,
                        padding: 20,
                        paddingVertical: 16,
                        borderRadius: 10,
                        alignItems: "center",

                        marginHorizontal: 48,
                        marginTop: 20,
                        marginBottom: 24,

                        shadowColor: "#000",
                        shadowOffset: {
                          width: 0,
                          height: 1,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 2.22,
                        elevation: 3,

                        ...(!isMobile() && {
                          width: "40%",
                          alignSelf: "center",
                        }),
                      }}
                    >
                      <Text
                        style={{
                          color: "#ffffff",
                          fontSize: 21,
                          fontFamily: "NunitoSans-Regular",
                        }}
                      >
                        {/* Address : come from commonStore -> selectedAddress */}
                        {/* AddressDetails : come from commonStore -> selectedAddressDetails */}
                        {/* AddressNote : come from commonStore -> selectedAddressNotes */}
                        {isPlacingReservation
                          ?
                          (!isDepositOnly ? 'PLACE RESERVATION WITH ORDER' : 'PLACE RESERVATION')
                          :
                          'PLACE ORDER'}
                      </Text>
                    </View>
                  </TouchableOpacity>
                </View>
              )
          ) : (
            <View
              style={{
                // backgroundColor: 'red',
                width: isMobile() ? "100%" : windowWidth,
                height: isMobile() ? "100%" : windowHeight,
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              <View
                style={{
                  marginBottom: "15%",
                }}
              >
                <ActivityIndicator color={Colors.primaryColor} size={"large"} />

                <Text
                  style={{
                    color: Colors.descriptionColor,
                    textAlign: "center",
                    marginTop: 20,
                    fontFamily: "NunitoSans-Bold",
                    fontSize: 16,
                  }}
                >
                  Processing your order now...
                </Text>
              </View>
            </View>
          )}
        </>
      )}
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    // flex: 1,
    width: Dimensions.get("window").width,
    height: Dimensions.get("window").height,
    backgroundColor: "#ffffff",
    padding: 16,
    paddingLeft: 0,
    paddingRight: 0,
    paddingTop: 0,
  },
  headerLogo: {
    width: 112,
    height: 25,
  },
  description: {
    paddingVertical: 5,
    fontSize: 13,
    color: Colors.descriptionColor,
    fontFamily: "NunitoSans-Regular",
    // fontWeight: '400',
    marginBottom: 2,
  },
  smallDescription: {
    marginTop: -5,
    paddingVertical: 5,
    fontSize: 10,
    color: "#9c9c9c",
    fontFamily: "NunitoSans-Regular",
    fontWeight: "400",
    width: "90%",
  },
  payment: {
    // color: Colors.descriptionColor,
    color: Colors.mainTxtColor,
    paddingVertical: 5,
    fontSize: 14,
    // marginTop: 20,
    fontFamily: "NunitoSans-SemiBold",
    marginBottom: -5,
  },
  total: {
    // paddingVertical: 5,
    fontSize: 16,
    // fontWeight: "700",
    // marginTop: 5,
    fontFamily: "NunitoSans-Bold",
  },
  price: {
    paddingVertical: 5,
    fontSize: 13,
    alignSelf: "flex-end",
    fontFamily: "NunitoSans-Regular",
    color: Colors.descriptionColor,
    // fontWeight: "400",
    marginBottom: 2,
  },
  textInput: {
    height: 50,
    paddingHorizontal: 20,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    marginTop: 10,
    marginBottom: 10,

    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
    elevation: 2,
  },
  textInput1: {
    height: 50,
    paddingHorizontal: 20,
    backgroundColor: "#E9CE8B",
    borderRadius: 4,
    marginTop: 20,
    justifyContent: "center",

    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
    elevation: 2,
  },
  totalPrice: {
    color: Colors.primaryColor,
    // paddingVertical: 5,
    fontSize: 16,
    // fontWeight: "700",
    fontFamily: "NunitoSans-Bold",
    // marginTop: 5,
  },
  confirmBox: {
    width: "60%",
    height: "33%",
    borderRadius: 30,
    backgroundColor: Colors.whiteColor,
  },
  scheduleBox: {
    width: "80%",
    height: "65%",
    borderRadius: 30,
    backgroundColor: Colors.whiteColor,
  },
  textFieldInput: {
    height: "100%",
    width: "100%",
    paddingHorizontal: 20,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 10,
  },
  pic: {
    backgroundColor: Colors.secondaryColor,
    width: 92, // 90
    height: 92, // 90
    borderRadius: 10,
    alignSelf: "center",
    justifyContent: "center",
  },
  card: {
    flex: 1,
    minWidth: Dimensions.get("window").width - 32,
    minHeight: 50,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 12,
    paddingHorizontal: Platform.OS == "ios" ? 0 : 20,
    paddingVertical: 15,
    flexDirection: "row",
    marginBottom: 20,
    alignItems: "center",
  },
  text: {
    fontSize: 15,
    fontFamily: "NunitoSans-Bold",
  },
  text1: {
    fontSize: 13,
    fontFamily: "NunitoSans-Bold",
    marginTop: "2%",
  },
  text2: {
    fontSize: 15,
    fontWeight: "700",
  },
  title: {
    color: Colors.blackColor,
    fontSize: 20,
    fontFamily: "NunitoSans-Bold",
  },

  button: {
    backgroundColor: Colors.fieldtBgColor,
    padding: 10,
    borderRadius: 10,
    marginVertical: 20,
    borderWidth: 1,
    borderColor: Colors.fieldtTxtColor,
    marginBottom: 0,
  },

  checkBox: {
    borderWidth: StyleSheet.hairlineWidth,
    borderColor: Colors.descriptionColor,
    width: 26,
    height: 26,
    borderRadius: 8,
    justifyContent: "center",
    alignItems: "center",
  },

  modalContainer: {
    flex: 1,
    backgroundColor: Colors.modalBgColor,
    alignItems: "center",
    justifyContent: "center",
  },
  modalView: {
    height: Dimensions.get("window").width * 1,
    width: Dimensions.get("window").width * 0.8,
    backgroundColor: Colors.whiteColor,
    borderRadius: Dimensions.get("window").width * 0.07,
    padding: Dimensions.get("window").width * 0.07,
    alignItems: "center",
    justifyContent: "space-between",
  },
  closeButton: {
    position: "absolute",
    right: isMobile()
      ? Dimensions.get("window").width * 0.04
      : Dimensions.get("window").width * 0.01,
    top: isMobile()
      ? Dimensions.get("window").width * 0.04
      : Dimensions.get("window").width * 0.01,
  },
  modalTitle: {
    alignItems: "center",
  },
  modalBody: {
    flex: 0.8,
    alignItems: "center",
    justifyContent: "center",
  },
  modalTitleText: {
    fontFamily: "NunitoSans-Bold",
    // marginBottom: 10,
    fontSize: 20,
  },
  modalDescText: {
    fontFamily: "NunitoSans-SemiBold",
    fontSize: 16,
    color: Colors.fieldtTxtColor,
  },
  modalBodyText: {
    // flex: 1,
    fontFamily: "NunitoSans-SemiBold",
    fontSize: 16,
    width: "20%",
  },
  modalSaveButton: {
    width: isMobile()
      ? Dimensions.get("window").width * 0.3
      : Dimensions.get("window").width * 0.1,
    backgroundColor: Colors.fieldtBgColor,
    height: 45,
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 8,

    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 1,
  },
});
export default CartTaggableVoucherScreen;
