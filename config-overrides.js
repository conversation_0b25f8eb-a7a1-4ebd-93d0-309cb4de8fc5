const path = require('path');
const { override, addBabelPlugins, babelInclude, setWebpackOptimizationSplitChunks, addBundleVisualizer, setWebpackPublicPath, addWebpackPlugin } = require('customize-cra');
const { sentryWebpackPlugin } = require("@sentry/webpack-plugin");

let overrideArguments = [
  ...addBabelPlugins('@babel/plugin-proposal-class-properties'),
  ...addBabelPlugins('@babel/plugin-proposal-private-methods'),
  ...addBabelPlugins('@babel/plugin-proposal-private-property-in-object'),
];

if (process.env.NODE_ENV === 'production') {
  overrideArguments.push(...addBabelPlugins('babel-plugin-transform-remove-console'));

  // overrideArguments.push(addWebpackPlugin(sentryWebpackPlugin({
  //   org: process.env.SENTRY_ORG,
  //   project: process.env.SENTRY_PROJECT,

  //   // Auth tokens can be obtained from https://sentry.io/orgredirect/organizations/:orgslug/settings/auth-tokens/
  //   authToken: process.env.SENTRY_AUTH_TOKEN,
  // })));
}

module.exports = override(
  //   ...addBabelPlugins('@babel/plugin-proposal-class-properties'),
  //   ...addBabelPlugins('@babel/plugin-proposal-private-methods'),
  //   ...addBabelPlugins('@babel/plugin-proposal-private-property-in-object'),
  //   // ...(process.env.NODE_ENV === 'production') && addBabelPlugins('babel-plugin-transform-remove-console'),
  //   addBabelPlugins('babel-plugin-transform-remove-console'),
  ...overrideArguments,

  babelInclude([
    path.resolve(__dirname, 'node_modules/react-native-vector-icons'),
    path.resolve(__dirname, 'node_modules/react-native-awesome-alerts'),
    path.resolve(__dirname, 'node_modules/react-native-reanimated'),
    path.resolve(__dirname, 'node_modules/react-native-draggable'),
    path.resolve(__dirname, 'node_modules/molpay-mobile-xdk-reactnative-beta'),
    path.resolve(__dirname, 'node_modules/react-native-dropdown-picker'),
    path.resolve(__dirname, 'src'),
  ]),

  // setWebpackPublicPath('https://kd-koodoo-web.s3.ap-southeast-1.amazonaws.com/'),
  setWebpackPublicPath(process.env.S3_URL),


  // addBundleVisualizer(),
  // setWebpackOptimizationSplitChunks({
  //   chunks: 'async',
  //   minSize: 20000,
  //   // minRemainingSize: 0,
  //   minChunks: 1,
  //   maxAsyncRequests: 30,
  //   maxInitialRequests: 30,
  //   enforceSizeThreshold: 50000,
  //   cacheGroups: {
  //     // defaultVendors: {
  //     //   test: /[\\/]node_modules[\\/]/,
  //     //   priority: -10,
  //     //   reuseExistingChunk: true,
  //     // },
  //     // default: {
  //     //   minChunks: 2,
  //     //   priority: -20,
  //     //   reuseExistingChunk: true,
  //     // },
  //     commons: {
  //       test: /[\\/]node_modules[\\/]((?!(geofire-common|moment|nanoid|rc-checkbox|react-datepicker|react-google-autocomplete|react-horizontal-datepicker|react-native-vector-icons|react-native-web-linear-gradient|react-select)).*)[\\/]/,
  //       name: 'commons',
  //       chunks: 'all',
  //       enforce: true
  //     },
  //     geofireCommon: {
  //       test: /[\\/]node_modules[\\/](geofire-common)[\\/]/,
  //       name: 'geofireCommon',
  //       chunks: 'all',
  //       enforce: true
  //     },
  //     moment: {
  //       test: /[\\/]node_modules[\\/](moment)[\\/]/,
  //       name: 'moment',
  //       chunks: 'all',
  //       enforce: true
  //     },
  //     nanoid: {
  //       test: /[\\/]node_modules[\\/](nanoid)[\\/]/,
  //       name: 'nanoid',
  //       chunks: 'all',
  //       enforce: true
  //     },
  //     rcCheckbox: {
  //       test: /[\\/]node_modules[\\/](rc-checkbox)[\\/]/,
  //       name: 'rcCheckbox',
  //       chunks: 'all',
  //       enforce: true
  //     },
  //     datepicker: {
  //       test: /[\\/]node_modules[\\/](react-datepicker)[\\/]/,
  //       name: 'datepicker',
  //       chunks: 'all',
  //       enforce: true
  //     },
  //     googleAutocomplete: {
  //       test: /[\\/]node_modules[\\/](react-google-autocomplete)[\\/]/,
  //       name: 'googleAutocomplete',
  //       chunks: 'all',
  //       enforce: true
  //     },
  //     horizontalDatepicker: {
  //       test: /[\\/]node_modules[\\/](react-horizontal-datepicker)[\\/]/,
  //       name: 'horizontalDatepicker',
  //       chunks: 'all',
  //       enforce: true
  //     },
  //     vectorIcons: {
  //       test: /[\\/]node_modules[\\/](react-native-vector-icons)[\\/]/,
  //       name: 'vectorIcons',
  //       chunks: 'all',
  //       enforce: true
  //     },
  //     webLinearGradient: {
  //       test: /[\\/]node_modules[\\/](react-native-web-linear-gradient)[\\/]/,
  //       name: 'webLinearGradient',
  //       chunks: 'all',
  //       enforce: true
  //     },
  //     reactSelect: {
  //       test: /[\\/]node_modules[\\/](react-select)[\\/]/,
  //       name: 'reactSelect',
  //       chunks: 'all',
  //       enforce: true
  //     },
  //   },
  // }),
);
