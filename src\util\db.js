import AsyncStorage from '@react-native-async-storage/async-storage';
import { openDB } from 'idb';

const dbPromise = openDB('offline-db-web', 2, {
    upgrade(db) {
        db.createObjectStore('offline');
        // db.createObjectStore('@commonStore');
        // db.createObjectStore('@dataStore');
        // db.createObjectStore('@userStore');
        // db.createObjectStore('@merchantStore');
        // db.createObjectStore('@outletStore');
    },
});

export async function idbGet(key) {
    return (await dbPromise).get('offline', key);
};

export async function idbSet(key, val) {
    return (await dbPromise).put('offline', val, key);
};

export async function idbGetAsyncStorage(key) {
    // return (await dbPromise).get('offline', key);
    return await AsyncStorage.getItem(key);
};

export async function idbSetAsyncStorage(key, val) {
    // return (await dbPromise).put('offline', val, key);
    await AsyncStorage.setItem(key, val);
};

export async function idbDel(key) {
    return (await dbPromise).delete('offline', key);
};

export async function idbClear() {
    return (await dbPromise).clear('offline');
};

export async function idbKeys() {
    return (await dbPromise).getAllKeys('offline');
};

// export async function idbTransactions(transactions) {
//     const tx = db.transaction('offline', 'readwrite');

//     let promises = [];

//     for (let i = 0; i < transactions.length; i++) {
//         promises.push(
//             tx.store.put(transactions[i].val, transactions[i].key),
//         );
//     }

//     if (promises.length > 0) {
//         promises.push(tx.done);
//         await Promise.all(promises);
//     }
// };

export const safelyExecuteIdb = (cb) => {
    try {
        let e = new Error('Database is closed');
        e.name = 'InvalidStateError';
        throw e;

        cb && cb();
    }
    catch (ex) {
        try {
            if (ex.name === 'InvalidStateError') {
                // reopen the db connection

                openDB('offline-db-web', 2, {
                    upgrade(db) {
                        db.createObjectStore('offline');
                    },
                }).then(() => {
                    // open successfully

                    // run callback again
                    cb && cb();
                });
            }
            else {
                ex.message = `${ex.message} - db.js - 1`;

                throw ex;
            }
        }
        catch (ex2) {
            if (ex2.message.includes('database connection is closing')) {
                openDB('offline-db-web', 2, {
                    upgrade(db) {
                        db.createObjectStore('offline');
                    },
                }).then(() => {
                    // open successfully

                    // run callback again
                    cb && cb();
                });
            }
            else {
                ex2.message = `${ex2.message} - db.js - 2`;

                throw ex2;
            }
        }
    }
};