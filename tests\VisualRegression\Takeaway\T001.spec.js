const { test, expect } = require('@playwright/test');

test('Operation > Landing Page', async ({ page }) => {
  // Set the viewports for different devices
  const viewports = [
    { name: 'iPhone 12', width: 1170, height: 2532 },
    { name: 'Pixel 5', width: 1080, height: 2340 },
  ];

  for (const viewport of viewports) {
    await page.setViewportSize(viewport);
    await page.goto('http://localhost:5400/web/outlet/hominsanttdi/takeaway');

    await page.waitForTimeout(500);

    // Capture the screenshot
    const screenshot = await page.screenshot({ path: `./tests/VisualRegression/Takeaway/Screenshots/${viewport.name}_T001.png`, });

    // Compare the screenshot with the baseline
    await expect(screenshot).toMatchSnapshot({ path: `./tests/VisualRegression/Takeaway/Screenshots/iPhone 12_T001.png`, });
  }
  //1st Loop is to get 1st device's screenshot to compare during the 2nd Loop
});

  // Check  playwright.config.js has these
  //projects: [
  //   {
  //     name: 'Pixel5',
  //     use: { ...devices['Pixel 5'] },
  //   },
  //   {
  //     name: 'iPhone12',
  //     use: { ...devices['iPhone 12'] },
  //   },
  // ]
