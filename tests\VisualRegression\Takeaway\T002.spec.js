const { test, expect } = require('@playwright/test');

test('Operation > Menu', async ({ page }) => {
  // Set the viewports for different devices
  const viewports = [
    { name: 'iPhone 12', width: 1170, height: 2532 },
    { name: 'Pixel 5', width: 1080, height: 2340 },
  ];

  for (const viewport of viewports) {
    await page.goto('http://localhost:5400/web/outlet/hominsanttdi/takeaway');

    await page.waitForTimeout(5000);

    await page.waitForTimeout(500);
    await page.getByTestId('startOrdering').click();

    // Capture the screenshot
    const screenshot = await page.screenshot({ path: `./tests/VisualRegression/Takeaway/Screenshots/${viewport.name}_T002.png`, });

    // Compare the screenshot with the baseline
    await expect(screenshot).toMatchSnapshot({ path: `./tests/VisualRegression/Takeaway/Screenshots/iPhone 12_T002.png`, maxDiffPixelRatio: 0.2 });
  }
  //1st Loop is to get 1st device's screenshot to compare during the 2nd Loop
});

test('Operation > Menu (Category)', async ({ page }) => {
  // Set the viewports for different devices
  const viewports = [
    { name: 'iPhone 12', width: 1170, height: 2532 },
    { name: 'Pixel 5', width: 1080, height: 2340 },
  ];

  // for (const viewport of viewports) {
    await page.goto('http://localhost:5400/web/outlet/hominsanttdi/takeaway');

    await page.waitForTimeout(5000);

    await page.waitForTimeout(500);
    await page.getByTestId('startOrdering').click();

    const categoryList = page.getByTestId('categoryList');
    await expect(categoryList).toHaveScreenshot( { maxDiffPixelRatio: 0.2, } );

    // Capture the screenshot
    // const screenshot = await page.screenshot({ path: `./tests/VisualRegression/Takeaway/Screenshots/${viewport.name}_T002.png`, });

    // // Compare the screenshot with the baseline
    // await expect(screenshot).toMatchSnapshot({ path: `./tests/VisualRegression/Takeaway/Screenshots/iPhone 12_T002.png`, maxDiffPixelRatio: 0.2 });
  // }
  //1st Loop is to get 1st device's screenshot to compare during the 2nd Loop
});

test('Operation > Menu (Item)', async ({ page }) => {
  // Set the viewports for different devices
  const viewports = [
    { name: 'iPhone 12', width: 1170, height: 2532 },
    { name: 'Pixel 5', width: 1080, height: 2340 },
  ];

  // for (const viewport of viewports) {
    await page.goto('http://localhost:5400/web/outlet/hominsanttdi/takeaway');

    await page.waitForTimeout(5000);

    await page.waitForTimeout(500);
    await page.getByTestId('startOrdering').click();

    const itemList = page.getByTestId('itemList');
    await expect(itemList).toHaveScreenshot( { maxDiffPixelRatio: 0.2, } );

    // Capture the screenshot
    // const screenshot = await page.screenshot({ path: `./tests/VisualRegression/Takeaway/Screenshots/${viewport.name}_T002.png`, });

    // // Compare the screenshot with the baseline
    // await expect(screenshot).toMatchSnapshot({ path: `./tests/VisualRegression/Takeaway/Screenshots/iPhone 12_T002.png`, maxDiffPixelRatio: 0.2 });
  // }
  //1st Loop is to get 1st device's screenshot to compare during the 2nd Loop
});

test('Operation > Menu (User Login Icon)', async ({ page }) => {
  // Set the viewports for different devices
  const viewports = [
    { name: 'iPhone 12', width: 1170, height: 2532 },
    { name: 'Pixel 5', width: 1080, height: 2340 },
  ];

  //for (const viewport of viewports) {
    await page.goto('http://localhost:5400/web/outlet/hominsanttdi/takeaway');

    await page.waitForTimeout(5000);

    await page.waitForTimeout(500);
    await page.getByTestId('startOrdering').click();

    const userLogin = page.getByTestId('userLogin');
    await expect(userLogin).toHaveScreenshot( { maxDiffPixelRatio: 0.2, } );

    // Capture the screenshot
    // const screenshot = await page.screenshot({ path: `./tests/VisualRegression/Takeaway/Screenshots/${viewport.name}_T002.png`, });

    // // Compare the screenshot with the baseline
    // await expect(screenshot).toMatchSnapshot({ path: `./tests/VisualRegression/Takeaway/Screenshots/iPhone 12_T002.png`, maxDiffPixelRatio: 0.2 });
  //}
  //1st Loop is to get 1st device's screenshot to compare during the 2nd Loop
});

test('Operation > Menu (Buy Voucher Icon)', async ({ page }) => {

  const timeout = 10000;
  // Set the viewports for different devices
  const viewports = [
    { name: 'iPhone 12', width: 1170, height: 2532 },
    { name: 'Pixel 5', width: 1080, height: 2340 },
  ];

  //for (const viewport of viewports) {
    await page.goto('http://localhost:5400/web/outlet/hominsanttdi/takeaway');

    await page.waitForTimeout(5000);

    await page.waitForTimeout(500);
    await page.getByTestId('startOrdering').click();

    //const buyVoucher = page.getByTestId('buyVoucher');
    const buyVoucher = await page.waitForSelector('[data-testid="buyVoucher"]', { timeout });
    await expect(buyVoucher).toHaveScreenshot( { maxDiffPixelRatio: 0.2, } );

    // Capture the screenshot
    // const screenshot = await page.screenshot({ path: `./tests/VisualRegression/Takeaway/Screenshots/${viewport.name}_T002.png`, });

    // // Compare the screenshot with the baseline
    // await expect(screenshot).toMatchSnapshot({ path: `./tests/VisualRegression/Takeaway/Screenshots/iPhone 12_T002.png`, maxDiffPixelRatio: 0.2 });
  //}
  //1st Loop is to get 1st device's screenshot to compare during the 2nd Loop
});

test('Operation > Menu (Search Icon)', async ({ page }) => {
  // Set the viewports for different devices
  const viewports = [
    { name: 'iPhone 12', width: 1170, height: 2532 },
    { name: 'Pixel 5', width: 1080, height: 2340 },
  ];

  //for (const viewport of viewports) {
    await page.goto('http://localhost:5400/web/outlet/hominsanttdi/takeaway');

    await page.waitForTimeout(5000);

    await page.waitForTimeout(500);
    await page.getByTestId('startOrdering').click();

    const categoryList = page.getByTestId('search');
    await expect(categoryList).toHaveScreenshot( { maxDiffPixelRatio: 0.2, } );

    // Capture the screenshot
    // const screenshot = await page.screenshot({ path: `./tests/VisualRegression/Takeaway/Screenshots/${viewport.name}_T002.png`, });

    // // Compare the screenshot with the baseline
    // await expect(screenshot).toMatchSnapshot({ path: `./tests/VisualRegression/Takeaway/Screenshots/iPhone 12_T002.png`, maxDiffPixelRatio: 0.2 });
  //}
  //1st Loop is to get 1st device's screenshot to compare during the 2nd Loop
});

test('Operation > Menu (Notification Bell Icon)', async ({ page }) => {
  // Set the viewports for different devices
  const viewports = [
    { name: 'iPhone 12', width: 1170, height: 2532 },
    { name: 'Pixel 5', width: 1080, height: 2340 },
  ];

  //for (const viewport of viewports) {
    await page.goto('http://localhost:5400/web/outlet/hominsanttdi/takeaway');

    await page.waitForTimeout(5000);

    await page.waitForTimeout(500);
    await page.getByTestId('startOrdering').click();

    const notificationBell = page.getByTestId('notificationBell');
    await expect(notificationBell).toHaveScreenshot( { maxDiffPixelRatio: 0.2, } );

    // Capture the screenshot
    // const screenshot = await page.screenshot({ path: `./tests/VisualRegression/Takeaway/Screenshots/${viewport.name}_T002.png`, });

    // // Compare the screenshot with the baseline
    // await expect(screenshot).toMatchSnapshot({ path: `./tests/VisualRegression/Takeaway/Screenshots/iPhone 12_T002.png`, maxDiffPixelRatio: 0.2 });
  //}
  //1st Loop is to get 1st device's screenshot to compare during the 2nd Loop
});

test('Operation > Menu (Cart Icon Top)', async ({ page }) => {
  // Set the viewports for different devices
  const viewports = [
    { name: 'iPhone 12', width: 1170, height: 2532 },
    { name: 'Pixel 5', width: 1080, height: 2340 },
  ];

  //for (const viewport of viewports) {
    await page.goto('http://localhost:5400/web/outlet/hominsanttdi/takeaway');

    await page.waitForTimeout(5000);

    await page.waitForTimeout(500);
    await page.getByTestId('startOrdering').click();

    const cartIconTop = page.getByTestId('cartIconTop');
    await expect(cartIconTop).toHaveScreenshot( { maxDiffPixelRatio: 0.2, } );

    // Capture the screenshot
    // const screenshot = await page.screenshot({ path: `./tests/VisualRegression/Takeaway/Screenshots/${viewport.name}_T002.png`, });

    // // Compare the screenshot with the baseline
    // await expect(screenshot).toMatchSnapshot({ path: `./tests/VisualRegression/Takeaway/Screenshots/iPhone 12_T002.png`, maxDiffPixelRatio: 0.2 });
  //}
  //1st Loop is to get 1st device's screenshot to compare during the 2nd Loop
});

  // Check  playwright.config.js has these
  //projects: [
  //   {
  //     name: 'Pixel5',
  //     use: { ...devices['Pixel 5'] },
  //   },
  //   {
  //     name: 'iPhone12',
  //     use: { ...devices['iPhone 12'] },
  //   },
  // ]




