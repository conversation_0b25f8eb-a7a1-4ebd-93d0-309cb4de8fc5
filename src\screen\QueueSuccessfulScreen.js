import React, { useState, useEffect, useMemo } from 'react';
import {
	StyleSheet,
	ScrollView,
	Image,
	View,
	Text,
	TouchableOpacity,
	Dimensions,
	Modal,
	useWindowDimensions,
} from 'react-native';
import Colors from '../constant/Colors';
import Styles from '../constant/Styles';
import API from '../constant/API';
import ApiClient from '../util/ApiClient';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { CommonStore } from '../store/commonStore';
import { UserStore } from '../store/userStore';
import { collection, query, where, getDocs, limit } from "firebase/firestore";
import { signInAnonymously } from "firebase/auth";
import { Collections } from '../constant/firebase';
import AntDesign from 'react-native-vector-icons/AntDesign';
import { prefix } from "../constant/env";
import { useLinkTo } from "@react-navigation/native";
import { DataStore } from '../store/dataStore';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { ReactComponent as DR } from "../asset/svg/Direction-run.svg";
import { ReactComponent as Call } from '../svg/call.svg';
import { ReactComponent as Booking } from "../asset/svg/Dining-table.svg";
import { ReactComponent as Queue } from "../asset/svg/Queue.svg";
import { ReactComponent as Connect } from "../asset/svg/Connect.svg";
import { ReactComponent as Refresh } from "../asset/svg/Refresh.svg";
import { ReactComponent as Shop } from "../asset/svg/Shop.svg";
import { USER_QUEUE_STATUS } from '../constant/common';
import { ActivityIndicator } from 'react-native';
import QueueChatInterface from '../components/queueChatInterface';

const QueueSuccessfulScreen = (props) => {
	const { navigation, route } = props;
	const linkTo = useLinkTo();
	const { height: windowHeight, width: windowWidth } = useWindowDimensions();

	const selectedOutlet = CommonStore.useState((s) => s.selectedOutlet);
	const outletsOpeningDict = CommonStore.useState((s) => s.outletsOpeningDict);

	const [showCancelConfirmation, setShowCancelConfirmation] = useState(false);
	const [operationTime, setOperationTime] = useState('-');

	useEffect(() => {
		if(selectedOutlet) {
			setOperationTime(getOutletOperationTime())
		}
	}, [outletsOpeningDict, selectedOutlet])

	useEffect(() => {
		if (linkTo) {
			DataStore.update((s) => {
				s.linkToFunc = linkTo;
			});

			global.linkToFunc = linkTo;
		}

		if (route.params === undefined ||
			route.params.subdomain === undefined) {
			linkTo && linkTo(`${prefix}/error`);
			console.log('rs > err > 1');
		} 
		else {
			CommonStore.update(s => {
				s.isLoading = true;
			});

			const subdomain = route.params.subdomain;

			if (subdomain) {
				try {
					signInAnonymously(global.auth)
						.then((result) => {
							const firebaseUid = result.user.uid;

							ApiClient.GET(API.getTokenKWeb + firebaseUid).then(
								async (result) => {
									console.log("getTokenKWeb");
									console.log(result);

									if (result && result.token) {
										await AsyncStorage.setItem("accessToken", result.token);
										await AsyncStorage.setItem(
											"refreshToken",
											result.refreshToken
										);

										global.accessToken = result.token;

										UserStore.update((s) => {
											s.firebaseUid = result.userId;
											s.userId = result.userId;
											s.role = result.role;
											s.refreshToken = result.refreshToken;
											s.token = result.token;
											s.name = '';
											s.email = '';
											s.number = '';
										});

										var outletSnapshot = null;

										if (subdomain) {
											if (subdomain === "192") {
												// outletSnapshot = await firebase
												//   .firestore()
												//   .collection(Collections.Outlet)
												//   .where(
												//     "uniqueId",
												//     "==",
												//     "b422c1d9-d30b-4de7-ad49-2e601d950919"
												//   )
												//   .limit(1)
												//   .get();

												outletSnapshot = await getDocs(
													query(
														collection(global.db, Collections.Outlet),
														where(
															"uniqueId",
															"==",
															"b422c1d9-d30b-4de7-ad49-2e601d950919"
														),
														limit(1),
													)
												);
											} else {
												// outletSnapshot = await firebase
												//   .firestore()
												//   .collection(Collections.Outlet)
												//   .where("subdomain", "==", subdomain)
												//   .limit(1)
												//   .get();

												outletSnapshot = await getDocs(
													query(
														collection(global.db, Collections.Outlet),
														where("subdomain", "==", subdomain),
														limit(1),
													)
												);
											}
										} else {
											CommonStore.update(s => {
												s.isLoading = false;
											});

											console.log("web scan 3");
											linkTo && linkTo(`${prefix}/error`);
											console.log('rs > err > 2');
										}

										var outlet = {};
										if (!outletSnapshot.empty) {
											outlet = outletSnapshot.docs[0].data();
										}

										if (
											outlet &&
											(outlet.subdomain === subdomain || subdomain === "192")
										) {
											// show pax modal before proceed

											global.subdomain = outlet.subdomain ? outlet.subdomain : '';

											await AsyncStorage.setItem(
												"latestOutletId",
												outlet.uniqueId
											);
											await AsyncStorage.setItem("latestSubdomain", subdomain);

											document.title = outlet.name;
											document.getElementsByTagName("META")[2].content =
												outlet.address;

											CommonStore.update(
												(s) => {
													s.selectedOutlet = outlet;
												}
											);

											////////////////////////////////////////

											// end

											CommonStore.update(s => {
												s.isLoading = false;
											});

											////////////////////////////////////////
										} else {
											CommonStore.update(s => {
												s.isLoading = false;
											});

											console.log("web scan 3");
											linkTo && linkTo(`${prefix}/error`);
											console.log('rs > err > 3');
										}
									} else {
										CommonStore.update((s) => {
											s.alertObj = {
												title: "Error",
												message: "Unauthorized access",
											};

											s.isLoading = false;
										});

										console.log("web scan 4");
										linkTo && linkTo(`${prefix}/error`);
										console.log('rs > err > 4');
									}
								}
							).catch(ex => {
								console.error(ex);

								CommonStore.update(s => {
									s.isLoading = false;
								});
							});
						}).catch(ex => {
							console.error(ex);

							CommonStore.update(s => {
								s.isLoading = false;
							});
						});
				}
				catch (ex) {
					console.error(ex);

					CommonStore.update(s => {
						s.isLoading = false;
					});
				}
			} else {
				CommonStore.update(s => {
					s.isLoading = false;
				});

				console.log('rs > err > 5');
				console.loe(route);

				if (selectedOutlet && selectedOutlet.subdomain) {
					linkTo && linkTo(`${prefix}/outlet/${selectedOutlet.subdomain}`);
				}
				else {
					linkTo && linkTo(`${prefix}/error`);
				}
			}
		}
	}, [linkTo, route]);

	const getOutletOperationTime = () => {
		try {
		  if (!selectedOutlet || !selectedOutlet.uniqueId) {
			console.error('No selected outlet or outlet ID');
			return '-';
		  }
	
		  let outletOpeningData = outletsOpeningDict[selectedOutlet.uniqueId];
		  
		  if(!outletOpeningData){
			throw new Error('No Outlet Data');
		  }
	
		  const today = new Date();
		  const dayOfWeek = today.getDay();
		  const dayNames = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
		  const currentDay = dayNames[dayOfWeek];
	
		  const operatingHours = outletOpeningData[currentDay];
		  if (!operatingHours) {
			console.error('No operating hours found for today');
			return '-';
		  }
	
		  const formatTime = (timeString) => {
			const hours = parseInt(timeString.slice(0, 2));
			const minutes = timeString.slice(2);
			const period = hours >= 12 ? 'pm' : 'am';
			const formattedHours = hours % 12 || 12;
			return `${formattedHours}.${minutes} ${period}`;
		  };
		  
		  const [openTime, closeTime] = operatingHours.split('-');
		  const formattedOpenTime = formatTime(openTime);
		  const formattedCloseTime = formatTime(closeTime);
		  
		  return `${formattedOpenTime} - ${formattedCloseTime}`;
		} 
		catch (ex) {
		  console.error('Error getting outlet operation time:', ex);
		  return '-';
		}
	};

	//////////////////////////////////////////

	const [queueData, setQueueData] = useState(null);
	const outletQueueNumberDict = CommonStore.useState(s => s.outletQueueNumberDict);

	// If QueueId not exist in url, Go Back
	useEffect(() => {
		if (!route.params.queueId) {
			props.navigation.navigate('Queue Now - KooDoo Web Order');
		}
		else {
			fetchQueueData();
		}
	}, [route.params.queueId]);

	// Fetch Queue Data
	const fetchQueueData = async () => {
		let query = { queueId: route.params.queueId }
		
		await ApiClient.GET(API.getQueueByQueueId, query)
			.then((response) => {
				if (response.status === 'success') {
					setQueueData(response.data);
				}
				else {
					console.error('Error fetching queue data:', response.error);
					navigation.navigate('KooDoo Web Order Error');
				}
			})
			.catch((error) => {
				console.error('Error fetching queue data:', error);
				navigation.navigate('KooDoo Web Order Error');
			});
	};

	// Get Status isCancelled
	const isCancelled = useMemo(() => {
		if (!queueData) return false;
		return queueData.status === USER_QUEUE_STATUS.CANCELED;
	}, [queueData]);

	// Get Place In Line
	const getPlaceInLine = useMemo(() => {
		// if (!queueData || !queueData.number) {
		// 	return <ActivityIndicator size="small" color="#D8B456" />;
		// }
		
		// const category = queueData.category;
		
		// if (!outletQueueNumberDict || !outletQueueNumberDict[category]) {
		// 	return <ActivityIndicator size="small" color="#D8B456" />;
		// }
		
		// const queueNumbers = outletQueueNumberDict[category];
		// const currentQueueIndex = queueNumbers.indexOf(queueData.uniqueId);
		
		// if (currentQueueIndex === -1) {
		// 	return <ActivityIndicator size="small" color="#D8B456" />;
		// }
		
		// return (currentQueueIndex + 1).toString();

		return queueData ? queueData.status : '-';
	}, [queueData, outletQueueNumberDict]);

	// Cancel Queue Operation
	const handleCancelQueue = async () => {
		try {
			const body = {
				queueId: queueData.uniqueId,
				status: USER_QUEUE_STATUS.CANCELED,
			};

			const response = await ApiClient.PATCH(API.updateQueueStatus, body);

			if (response.status === 'success') {
				console.log('Successfully cancelled queue');
			}
			else {
				console.error('Error cancelling queue:', response.message);

				CommonStore.update((s) => {
					s.alertObj = {
						title: 'Error',
						message: 'Error cancelling queue',
					};
				});
			}
		} catch (error) {
			console.error('Error cancelling queue:', error);

			CommonStore.update((s) => {
				s.alertObj = {
					title: 'Error',
					message: 'Error cancelling queue',
				};
			});
		}
	};

	//////////////////////////////////////////
	// 19 Nov 2024 - Chat Modal
	const [isChatModalVisible, setIsChatModalVisible] = useState(route.params?.chat || false);

	//////////////////////////////////////////

	useEffect(() => {
		navigation.setOptions({
			headerLeft: () => (
				<TouchableOpacity
					style={{}}
					onPress={() => {
						props.navigation.goBack();
					}}>
					<View
						style={{
							marginLeft: 10,
							display: 'flex',
							flexDirection: 'row',
							alignItems: 'center',
							justifyContent: 'flex-start',
						}}>
						<Ionicons
							name="chevron-back"
							size={26}
							color={Colors.fieldtTxtColor}
							style={{}}
						/>
	
						<Text
							style={{
								color: Colors.fieldtTxtColor,
								fontSize: 16,
								textAlign: 'center',
								fontFamily: 'NunitoSans-Regular',
								lineHeight: 22,
								marginTop: -1,
							}}>
							Back
						</Text>
					</View>
				</TouchableOpacity>
			),
			headerRight: () => (
				<TouchableOpacity
					onPress={() => {
						props.navigation.navigate('Profile');
					}}
					style={{}}>
					<View style={{ marginRight: 15 }}>
						<Ionicons name="menu" size={30} color={Colors.primaryColor} />
					</View>
				</TouchableOpacity>
			),
			headerTitle: () => (
				<View
					style={{
						justifyContent: 'center',
						alignItems: 'center',
						bottom: -1,
					}}>
					<Text
						style={{
							fontSize: 20,
							lineHeight: 25,
							textAlign: 'center',
							fontFamily: 'NunitoSans-Bold',
							color: Colors.mainTxtColor,
						}}>
						Queue
					</Text>
				</View>
			),
			headerTintColor: '#000000',
		});
	}, [navigation]);

	return (
		<ScrollView style={{height: windowHeight, backgroundColor: '#f3f2f8'}}>
			<Modal
				visible={isChatModalVisible}
				transparent={true}
				animationType="fade"
			>
				<View style={{
					flex: 1,
					justifyContent: 'center',
					alignItems: 'center',
					backgroundColor: 'rgba(0, 0, 0, 0.5)',
				}}>
					<View style={{
						width: windowWidth * 0.95,
						height: windowHeight * 0.9,
						backgroundColor: 'white',
						borderRadius: 15,
						padding: 20,
						flexDirection: 'column',
						justifyContent: 'center',
						alignItems: 'center',
					}}>
						<TouchableOpacity 
							style={{
								position: 'absolute',
								top: 15,
								right: 15,
							}}
							onPress={() => {setIsChatModalVisible(false)}}>
							<Ionicons name="close" size={32} color="black" />
						</TouchableOpacity>

						<Text style={{
							fontSize: 20,
							fontWeight: 'bold',
							fontFamily: 'NunitoSans-Bold',
						}}>
							Chat with Merchant
						</Text>

						<View style={{
							flex: 1,
							width: '100%',
							justifyContent: 'center',
							alignItems: 'center',
						}}>
							<QueueChatInterface queueId={queueData?.uniqueId || ''} />
						</View>
					</View>
				</View>
				
			</Modal>
			
			<View style={{ 
					flex: 1, 
				marginTop: 70, 
				// borderWidth: 1, 
				width: '90%', 
				borderRadius: 15, 
				alignSelf: 'center', 
				backgroundColor: 'white',
				marginBottom: 20,
			}}>
				<View style={{
					// backgroundColor: Colors.primaryColor,
					width: '80%', 
					alignSelf: 'center',
					// borderWidth: 1,

					// paddingVertical: 10,

				}}>
					<View style={{paddingVertical: '5%'}}>
						<Image
							style={{
								// width: windowWidth * 0.35,
								// height: windowWidth * 0.35,
								width: 120,
								height: 120,
								// backgroundColor: 'red',
								alignSelf: "center",
								shadowColor: '#000', 
								shadowOffset: {
								width: 0,
								height: 2, 
								},
								shadowOpacity: 0.25, 
								shadowRadius: 3.5, 
								elevation: 5, 
							}}
							source={ !isCancelled ?
								{ uri: 'https://static.vecteezy.com/system/resources/previews/024/098/425/non_2x/young-persons-celebrating-free-png.png'} :
								{ uri: 'https://static.vecteezy.com/system/resources/previews/019/943/290/non_2x/red-x-free-download-free-png.png'}
							}
							onError={(e) => console.log("Error loading image:", e.nativeEvent.error)}
							// source={{ uri: 'https://play-lh.googleusercontent.com/kaJwa62PCR3fdebyeXs6JGuqUY5KdzZugPMLIhd3CjA-132oYHxdiB0l04gTfATnzg=w240-h480-rw'}}
						>
						</Image>
					</View>
					<View style={{ width: windowWidth * 0.7, alignSelf: 'center', textAlign: 'center' }}>
						<Text style={{
							fontFamily: 'NunitoSans-Bold',
							// color: Colors.whiteColor,
							// color: !isCancelled ? '#D8B456' : Colors.blackColor,
							fontWeight:'bold',
							fontSize: 25,
							marginBottom: 10,
							}}>
							{!isCancelled ? "Lineup Successful!" : "Lineup Cancelled"}
						</Text>
					</View>
					<View style={{ width: windowWidth * 0.7, alignSelf: 'center', textAlign: 'center' }}>
						<Text
							style={{
								// fontWeight: 'bold',
								// color: Colors.whiteColor,
								color: Colors.tabRed,
								// fontWeight: '500',
								marginBottom: 5,
								fontSize:18,
							}}
							>
							{!isCancelled ? 
							<></>: 
							"Your queue has been cancelled."}
						</Text>
						<Text
							style={{
								// fontWeight: 'bold',
								// color: Colors.whiteColor,
								color: Colors.blackColor,
								// fontWeight: '500',
								marginBottom: 20,
								fontSize:16,
							}}
							>
							{!isCancelled ? 
							"Thanks for waiting !\nYou will be notified when it's your turn" : 
							"We're looking forward to seeing you again!"}
						</Text>
					</View>
				</View>
				<View 
					style={{ 
						width: '90%', 
						alignSelf: 'center',
						// borderWidth: 1,
						borderRadius: 15,
						marginTop: 20,
						backgroundColor: Colors.lightPrimary,
						paddingVertical: 20,
						// paddingHorizontal: 5,

						// borderTopLeftRadius: 20,
						// borderTopRightRadius: 20
					}}>
					{!isCancelled ? (
					<View style={{ alignSelf: 'center', 
						backgroundColor: Colors.whiteColor, 
						borderWidth: 1,
						borderRadius: 15, 
						borderColor: '#d5dbd6',
						padding: 8,
						position: 'absolute',
						top: -17,
						left: '50%',
						transform: [{ translateX: -50 }],
					}}>
						<Text style={{ fontWeight: '400' }}>Now serving</Text>
					</View>
					) : null}

					<View 
						style={{
							flexDirection: 'row', 
							marginBottom: '5%',
							borderBottomWidth: 1,
							borderBottomColor: '#92c4af',
							paddingHorizontal: 10,
							paddingBottom: 10,
							// paddingVertical: 20,
							textAlign: 'center',
							}}>
						<View style={{ 
							flex: 1, 
							width: windowWidth * 0.5, 
							alignSelf: 'center', 
							paddingHorizontal:1, 
							justifyContent: 'center', 
							alignItems: 'center'
							}}>
							<Booking color='black' />
							<Text
								style={{
									fontFamily: 'NunitoSans-Bold',
									fontWeight: 'bold',
									fontSize: 14,
									// color: '#368F69',
								}}>
								Booking Number
							</Text>
							<Text 
								style={{
									// paddingVertical: 5,
									fontSize: 25,
									fontWeight:'bold',
									color: '#D8B456',
									// color: '#368F69',
								}}> 
								{queueData?.number ? queueData.number.toString().padStart(3, '0') : ''}
							</Text>
						</View>
						{ !isCancelled ? (
						<View style={{ 
							flex: 1, 
							width: windowWidth * 0.5, 
							alignSelf: 'center', 
							borderLeftWidth: 1, 
							borderLeftStyle: 'dotted', 
							borderLeftColor: '#92c4af',
							justifyContent: 'center', 
							alignItems: 'center'
							}}
						>
							<Queue />
							<Text
								style={{
									fontFamily: 'NunitoSans-Bold',
									fontWeight:'bold',
									fontSize: 14,
									// color: '#368F69',
								}}>
								Status
							</Text>
							<Text 
								style={{
									// paddingVertical: 5,
									fontSize: 25,
									fontWeight:'bold',
									color: '#D8B456',
									// color: '#368F69',
								}}> 
								{getPlaceInLine || '-'}
							</Text>
						</View>
						) : null }
					</View>
					<View 
						style={{ 
							flexDirection: 'row', 
							flexWrap: 'wrap',
							paddingBottom: 10,
							// paddingHorizontal: 30,
							paddingLeft: 30,
							paddingRight: 0,
							textAlign: 'left',
							rowGap: 15,
							}}>
						<View style={{ flexDirection: 'column', flex: '45%' }}>
							<Text style={{ color: 'gray', fontSize: 13, }}>Name</Text>
							<Text style={{ fontWeight:'semibold', fontSize: 17, }}>{queueData?.userName || '-'}</Text>
						</View>
						<View style={{ flexDirection: 'column', flex: '55%' }}>
							<Text style={{ color: 'gray', fontSize: 13, }}>Phone</Text>
							<Text style={{ fontWeight:'semibold', fontSize: 17, }}>{queueData?.phoneNo ? `+${queueData.phoneNo}` : '-'}</Text>
						</View>
						<View style={{ flexDirection: 'column', flex: '45%' }}>
							<Text style={{ color: 'gray', fontSize: 14, }}>Pax</Text>
							<Text style={{ fontWeight:'semibold', fontSize: 17, }}>{queueData?.pax || '-'}</Text>
						</View>
						<View style={{ flexDirection: 'column', flex: '55%' }}>
							<Text style={{ color: 'gray', fontSize: 14, }}>Pax Category</Text>
							<Text style={{ fontWeight:'semibold', fontSize: 17, }}>{queueData?.category || '-'}</Text>
						</View>
						{/* <View style={{ flexDirection: 'column', flex: '100%', alignSelf: '' }}>
							<Text style={{ color: 'gray', fontSize: 11, }}>Email</Text>
							<Text style={{ fontWeight: 'bold', fontSize: 13, }}><EMAIL></Text>
						</View> */}
					</View>
				</View>
				<View style={{borderBottomWidth: 1, marginVertical:'5%', width: '80%', alignSelf: 'center', borderStyle: 'dotted', borderColor: '#92c4af'}}>

				</View>
				{/* <View style={{
					width: windowWidth,
					// height: 160,
					backgroundColor: Colors.primaryColor,
					paddingHorizontal: 10,
					paddingTop:"16%",
					paddingBottom: 20,
					width:"90%",
					marginBottom:20,
					marginTop:"34%",
					marginHorizontal:'auto',
					borderRadius:15,
					}}>
					<View style={{ 
						position: 'absolute',
						top: -50,
						left: '50%',
						transform: [{ translateX: -50 }],
						marginBottom: 0,
						shadowColor: '#000', 
						shadowOffset: {
						width: 0,
						height: 2, 
						},
						shadowOpacity: 0.25, 
						shadowRadius: 3.5, 
						elevation: 5, 
					}}>
						<Image
						source={{uri: 'https://media-cdn.tripadvisor.com/media/photo-p/1c/7e/56/75/ho-min-san-logo.jpg'}}
						// source={selectedMerchant && selectedMerchant.logo !== ""
						//     ? { uri: selectedMerchant.logo }
						//     : require('../asset/image/logo.png')}
						style={{
							width: 100,
							height: 100,
							alignSelf: "center",
						}}
						onError={(e) => console.log("Error loading image:", e.nativeEvent.error)}
						/>
					</View>
					<Text style={{
						fontFamily: 'NunitoSans-SemiBold',
						color: Colors.whiteColor,
						fontSize: 22,
						textAlign: 'center',
						marginTop: 0,
						fontWeight: 400,
						// width: '100%',
					}}>
						Ho Min San (TTDI)
					</Text>
					<View style={{
						justifyContent: 'center',
						alignItems: 'flex-start',
						flexDirection: 'row',
						marginTop: 20,
						marginHorizontal: 'auto',
						width:"85%",
						//width: isMobile() ? windowWidth : windowWidth * 1,
					}}>
						<Ionicons name='location-outline' size={14} color={Colors.whiteColor} />
						<Text style={{
						fontFamily: 'NunitoSans-Regular',
						color: Colors.whiteColor,
						fontSize: 12,
						textAlign: 'center',
						marginTop: 0,
						// width: '100%',
						paddingLeft: 5,
						}}>
						Laluan Tasek Perdana 1, Kawasan Perindustrian Tasek, 31400 Ipoh, Perak, Malaysia
						</Text>
					</View>
					<View style={{
						justifyContent: 'center',
						alignItems: 'center',
						flexDirection: 'row',
						marginTop: 15,
						//width: isMobile() ? windowWidth : windowWidth * 1,
					}}>
						<View style={{ backgroundColor: '#284a3a', paddingHorizontal: 8, paddingVertical: 6, borderRadius: 5 }}>
						<TouchableOpacity
							style={{
							justifyContent: 'center',
							alignItems: 'center',
							flexDirection: 'row',
							//width: isMobile() ? windowWidth : windowWidth * 1,
							}}
							// onPress={() => { setDisplayDetails(true) }}
						>
							<AntDesign name="clockcircleo" size={13} color={Colors.whiteColor} />
							< Text style={{
							fontFamily: 'NunitoSans-Regular',
							color: Colors.whiteColor,
							fontSize: 13,
							// textAlign: 'center',
							marginTop: 0,
							marginLeft: 5,
							// width: '100%',
							}}>
							10:00 AM - 10:30 PM
							</Text>
						</TouchableOpacity>
						</View>
						<View style={{ backgroundColor: '#284a3a', paddingHorizontal: 8, paddingVertical: 6, borderRadius: 5, marginLeft: 10, }}>
						<TouchableOpacity>
							<View style={{
							justifyContent: 'center',
							alignItems: 'center',
							flexDirection: 'row',
							//width: isMobile() ? windowWidth : windowWidth * 1,
							}}>
							<Call
								width={16}
								height={16}
								color={Colors.whiteColor}
							/>
							<Text style={{
								fontFamily: 'NunitoSans-Regular',
								color: Colors.whiteColor,
								fontSize: 13,
								textAlign: 'center',
								marginTop: 0,
								marginLeft: 5,
								// width: '100%',
							}}>
								60312456789
							</Text>
							</View>
						</TouchableOpacity>
						</View>
					</View>
				</View> */}
				<View style={{
					// width: windowWidth,
					// height: 160,
					backgroundColor: Colors.primaryColor,
					paddingHorizontal: 10,
					// paddingTop:"16%",
					paddingTop: 10,
					paddingBottom: 10,
					width:"90%",
					marginBottom:20,
					// marginTop:"34%",
					marginHorizontal:'auto',
					borderRadius:15,

					}}>
					{/* <View style={{ 
						position: 'absolute',
						top: -50,
						left: '50%',
						transform: [{ translateX: -50 }],
						marginBottom: 0,
						shadowColor: '#000', 
						shadowOffset: {
						width: 0,
						height: 2, 
						},
						shadowOpacity: 0.25, 
						shadowRadius: 3.5, 
						elevation: 5, 
					}}>
						<Image
						source={{uri: 'https://media-cdn.tripadvisor.com/media/photo-p/1c/7e/56/75/ho-min-san-logo.jpg'}}
						// source={selectedMerchant && selectedMerchant.logo !== ""
						//     ? { uri: selectedMerchant.logo }
						//     : require('../asset/image/logo.png')}
						style={{
							width: 100,
							height: 100,
							alignSelf: "center",
						}}
						onError={(e) => console.log("Error loading image:", e.nativeEvent.error)}
						/>
					</View> */}
					<View style={{ flexDirection: 'row', justifyContent: 'center', alignItems: 'center'}}>
						<Shop color='white'/>
						{/* <Restaurant color='white'/> */}
						<Text style={{
							fontFamily: 'NunitoSans-SemiBold',
							color: Colors.whiteColor,
							fontSize: 15,
							textAlign: 'center',
							marginTop: 0,
							fontWeight: 400,
							paddingLeft: 5,
							// width: '100%',
						}}>
							{selectedOutlet ? selectedOutlet.name : ''}
						</Text>

						<Ionicons name='chatbubble-ellipses-outline' size={22} color={Colors.whiteColor} style={{ marginLeft: 5 }} onPress={() => {
							setIsChatModalVisible(true);
						}} />
					</View>
					{/* <View style={{
						justifyContent: 'center',
						alignItems: 'flex-start',
						flexDirection: 'row',
						marginTop: 20,
						marginHorizontal: 'auto',
						width:"85%",
						//width: isMobile() ? windowWidth : windowWidth * 1,
					}}>
						<Ionicons name='location-outline' size={14} color={Colors.whiteColor} />
						<Text style={{
						fontFamily: 'NunitoSans-Regular',
						color: Colors.whiteColor,
						fontSize: 12,
						textAlign: 'center',
						marginTop: 0,
						// width: '100%',
						paddingLeft: 5,
						}}>
						Laluan Tasek Perdana 1, Kawasan Perindustrian Tasek, 31400 Ipoh, Perak, Malaysia
						</Text>
					</View> */}
					<View style={{
						justifyContent: 'center',
						alignItems: 'center',
						flexDirection: 'column',
						marginTop: 10,
						rowGap: 10,
						//width: isMobile() ? windowWidth : windowWidth * 1,
					}}>
						<View style={{ backgroundColor: '#284a3a', paddingHorizontal: 8, paddingVertical: 6, borderRadius: 5 }}>
						<TouchableOpacity
							style={{
							justifyContent: 'center',
							alignItems: 'center',
							flexDirection: 'row',
							//width: isMobile() ? windowWidth : windowWidth * 1,
							}}
							// onPress={() => { setDisplayDetails(true) }}
						>
							<AntDesign name="clockcircleo" size={13} color={Colors.whiteColor} />
							<Text style={{
							fontFamily: 'NunitoSans-Regular',
							color: Colors.whiteColor,
							fontSize: 13,
							// textAlign: 'center',
							marginTop: 0,
							marginLeft: 5,
							// width: '100%',
							}}>
								{operationTime}
							</Text>
						</TouchableOpacity>
						</View>
						<View style={{ backgroundColor: '#284a3a', paddingHorizontal: 8, paddingVertical: 6, borderRadius: 5 }}>
						<TouchableOpacity>
							<View style={{
							justifyContent: 'center',
							alignItems: 'center',
							flexDirection: 'row',
							//width: isMobile() ? windowWidth : windowWidth * 1,
							}}>
							<Call
								width={16}
								height={16}
								color={Colors.whiteColor}
							/>
							<Text style={{
								fontFamily: 'NunitoSans-Regular',
								color: Colors.whiteColor,
								fontSize: 13,
								textAlign: 'center',
								marginTop: 0,
								marginLeft: 5,
								// width: '100%',
							}}>
								{selectedOutlet ? selectedOutlet.phone : ''}
							</Text>
							</View>
						</TouchableOpacity>
						</View>
					</View>
				</View>
				{!isCancelled ? (
					<View style={{ flex: 1, flexDirection: 'row', alignSelf: 'center', width: '80%', justifyContent: 'space-between'}}>
						<TouchableOpacity
							onPress={() => {
								fetchQueueData();
							}}>
							<View
								style={[styles.bottomButtons, { backgroundColor: Colors.lightPrimary }]
							}>
								<Refresh color="black" size={20} />
								<Text
									style={{
									color: 'black',
									marginLeft: 5,
									// fontSize: 12,
									fontFamily: 'NunitoSans-SemiBold',
									}}>
									Refresh status
								</Text>
							</View>
						</TouchableOpacity>
						<Modal
							visible={showCancelConfirmation}
							transparent={true}
							animationType="fade">
							<View style={styles.modalContainer}>
								<View style={styles.modalContent}>
									<Text style={styles.modalTitle}>Leave Queue</Text>
									<Text style={styles.modalMessage}>Are you sure you want to leave the queue?</Text>
									<Text style={styles.modalWarning}>You won't be able to cancel this action.</Text>
									<View style={styles.modalButtonContainer}>
										<TouchableOpacity
											onPress={() => setShowCancelConfirmation(false)}
											style={[styles.modalButton, styles.modalCancelButton]}>
											<Text style={{color: Colors.blackColor}}>Cancel</Text>
										</TouchableOpacity>
										<TouchableOpacity
											onPress={() => {
												setShowCancelConfirmation(false);
												handleCancelQueue();
											}}
											style={[styles.modalButton, styles.modalConfirmButton]}>
											<Text style={styles.modalButtonText}>Leave</Text>
										</TouchableOpacity>
									</View>
								</View>
							</View>
						</Modal>
						<TouchableOpacity
							onPress={() => {
							// this.cancelQueue();
							setShowCancelConfirmation(true)
							}}>
							<View
								style={[styles.bottomButtons, { backgroundColor: 'red' }]
							}>
								<DR color="white" size={20} />
								<Text
									style={{
									color: 'white',
									marginLeft: 5,
									// fontSize: 16,
									fontFamily: 'NunitoSans-SemiBold',
									}}>
									Leave queue
								</Text>
							</View>
						</TouchableOpacity>
					</View>
				): (
				<View>
					<TouchableOpacity
						onPress={async () => {
							const subdomain = await AsyncStorage.getItem('latestSubdomain');
							if (!subdomain) {
								linkTo && linkTo(`${prefix}/outlet/`);
							}
							else {
								linkTo && linkTo(`${prefix}/outlet/${subdomain}/queue-now`);
							}
						}}>
						<View
							style={[styles.bottomButtons, { backgroundColor: Colors.lightPrimary, width:'55%', }]
						}>
							<Connect color="black" size={20} />
							<Text
								style={{
								color: 'black',
								marginLeft: 5,
								// fontSize: 16,
								fontFamily: 'NunitoSans-SemiBold',
								}}>
								Rejoin the queue
							</Text>
						</View>
					</TouchableOpacity>
					<TouchableOpacity onPress={
						async () => {
							const subdomain = await AsyncStorage.getItem('latestSubdomain');
							if (!subdomain) {
								linkTo && linkTo(`${prefix}/outlet/`);
							}
							else {
								linkTo && linkTo(`${prefix}/outlet/${subdomain}/`);
							}
						}}
					>
						<View
							style={[styles.bottomButtons, { backgroundColor: Colors.whiteColor, borderColor: Colors.primaryColor, borderWidth:1, width:'55%',}]
						}>
							<Ionicons name="arrow-back-circle-outline" size={28} style={{color:Colors.primaryColor}}/>
							<Text
								style={{
								color: Colors.primaryColor,
								marginLeft: 5,
								// fontSize: 16,
								fontFamily: 'NunitoSans-SemiBold',
								}}>
								Back to home
							</Text>
						</View>
					</TouchableOpacity>
				</View>
				)}
			</View>
		</ScrollView>
	);
};

const styles = StyleSheet.create({
	container: {
		flex: 1,
		backgroundColor: '#ffffff',
		padding: 16,
	},
	outletCover: {
		width: '100%',
		alignSelf: 'center',
		height: undefined,
		aspectRatio: 2,
		borderRadius: 5,
	},
	infoTab: {
		backgroundColor: Colors.fieldtBgColor,
		padding: 16,
		justifyContent: 'center',
		alignContent: 'center',
		alignItems: 'center',
	},
	workingHourTab: {
		padding: 16,
		flexDirection: 'row',
	},
	outletAddress: {
		textAlign: 'center',
		color: Colors.mainTxtColor,
	},
	outletName: {
		fontWeight: 'bold',
		fontSize: 20,
		marginBottom: 10,
	},
	logo: {
		width: 100,
		height: 100,
	},
	actionTab: {
		flexDirection: 'row',
		marginTop: 20,
	},
	actionView: {
		width: Styles.width / 4,
		height: Styles.width / 4,
		justifyContent: 'center',
		alignItems: 'center',
		alignContent: 'center',
	},
	actionBtn: {
		borderRadius: 50,
		width: 70,
		height: 70,
		borderColor: Colors.secondaryColor,
		borderWidth: StyleSheet.hairlineWidth,
		justifyContent: 'center',
		alignItems: 'center',
	},
	actionText: {
		fontSize: 12,
		marginTop: 10,
	},
	textInput: {
		height: 45,
		paddingHorizontal: 25,
		backgroundColor: Colors.fieldtBgColor,
		borderRadius: 10,
		marginTop: 10,
		fontFamily: 'NunitoSans-Regular',
		fontSize: 14,
		lineHeight: 20,
	},
	modalContainerTable: {
		flex: 1,
		backgroundColor: Colors.modalBgColor,
		alignItems: 'center',
		justifyContent: 'center',
	},
	modalViewTable: {
		width: Dimensions.get('screen').width * 0.65,
		backgroundColor: Colors.whiteColor,
		borderRadius: Dimensions.get('screen').width * 0.03,
		padding: 20,
		alignItems: 'center',
		justifyContent: 'center',
	},
	closeButton: {
		position: 'absolute',
		right: Dimensions.get('screen').width * 0.02,
		top: Dimensions.get('screen').width * 0.02,
		elevation: 1000,
		zIndex: 1000,
	},
	description: {
		paddingVertical: 5,
		fontSize: 19,
		marginTop: 20,
		fontFamily: 'NunitoSans-Bold',
	},
	sectionAreaButton: {
		marginLeft: 10,
		// width: Dimensions.get('screen').width * 0.085,
		backgroundColor: Colors.whiteColor,
		height: Dimensions.get('screen').height * 0.04,
		borderRadius: 8,
		justifyContent: 'flex-start',
		alignItems: 'center',
		flexDirection: 'row',

		paddingLeft: 15,

		paddingRight: 35,

		elevation: 0,
		shadowOpacity: 0,
		shadowColor: '#000',
		shadowOffset: {
			width: 0,
			height: 2,
		},
		shadowOpacity: 0.22,
		shadowRadius: 3.22,
		elevation: 3,
	},
	sectionAreaButtonTxt: {
		fontFamily: 'NunitoSans-SemiBold',
		fontSize: 14,
		color: Colors.primaryColor,
		textAlign: 'center',
	},
	tableSlotDisplay: {
		width: Dimensions.get('screen').height * 0.12,
		height: Dimensions.get('screen').height * 0.12,
		margin: 12,
		borderRadius: 8,
		padding: Dimensions.get('screen').width * 0.01,
		borderWidth: 1,
		borderStyle: 'dashed',
		borderColor: Colors.fieldtTxtColor,
		alignItems: 'center',
		justifyContent: 'center',
	},
	emptyTableDisplay: {
		backgroundColor: Colors.whiteColor,
		width: Dimensions.get('screen').height * 0.12,
		height: Dimensions.get('screen').height * 0.12,
		margin: 10,
		//borderRadius: Dimensions.get('screen').width * 0.02,
		padding: Dimensions.get('screen').width * 0.01,
	},
	tableCode: {
		fontFamily: 'NunitoSans-Bold',
		fontSize: 18,
	},
	buttonContainer: {
		flexDirection: 'row',
		justifyContent: 'space-between',
		width: Dimensions.get('window').width * 0.5,
		alignSelf: 'center',
		marginTop: Dimensions.get('screen').height * 0.05,
	},
	button: {
		flex: 1,
		backgroundColor: '#4E9F7D',
		borderRadius: 10,
		borderWidth: 1,
		borderColor: Colors.primaryColor,
		padding: 10,
		alignItems: 'center',
		justifyContent: 'center',
		marginHorizontal: 5,
	},
	cancelButton: {
		backgroundColor: Colors.secondaryColor,
	},
	buttonText: {
		color: 'white',
		fontFamily: 'NunitoSans-Bold',
		fontSize: 17,
	},
	modalContainer: {
		flex: 1,
		backgroundColor: 'rgba(0, 0, 0, 0.5)',
		justifyContent: 'center',
		alignItems: 'center',
	},
	modalContent: {
		backgroundColor: 'white',
		borderRadius: 10,
		padding: 20,
		width: '80%',
		alignItems: 'center',
	},
	modalTitle: {
		fontSize: 18,
		fontWeight: 'bold',
		marginBottom: 10,
	},
	modalMessage: {
		fontSize: 16,
		marginBottom: 20,
		textAlign: 'center',
	},
	modalWarning: {
		fontSize: 14,
		color: 'red',
		marginBottom: 20,
		textAlign: 'center',
	},
	modalButtonContainer: {
		flexDirection: 'row',
		justifyContent: 'space-around',
		width: '100%',
	},
	modalButton: {
		paddingVertical: 10,
		paddingHorizontal: 20,
		borderRadius: 5,
		minWidth: 100,
		alignItems: 'center',
	},
	modalCancelButton: {
		borderWidth: 1,
	},
	modalConfirmButton: {
		backgroundColor: 'red',
	},
	modalButtonText: {
		color: 'white',
		fontWeight: 'bold',
	},
	bottomButtons: {
		flexDirection: 'row',
		paddingVertical: 10,
		paddingHorizontal: 10,
		marginBottom: 20,
		justifyContent:'center',
		alignItems: 'center',
		alignSelf: 'center',
		borderRadius: 10,
		height: 45,
	},
});

export default QueueSuccessfulScreen;
