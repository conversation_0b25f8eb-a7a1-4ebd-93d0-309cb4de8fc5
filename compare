├── @babel/plugin-proposal-class-properties@7.14.5
├── @babel/plugin-proposal-private-methods@7.18.6
├── @babel/plugin-proposal-private-property-in-object@7.21.0
├── @loadable/component@5.15.3
├── @playwright/test@1.34.0
├── @react-native-async-storage/async-storage@1.17.11
├── @react-navigation/bottom-tabs@5.11.15
├── @react-navigation/native@5.9.8
├── @react-navigation/stack@5.14.9
├── @sentry/browser@7.118.0
├── @sentry/webpack-plugin@2.21.1
├── axios@1.7.3
├── babel-plugin-transform-remove-console@6.9.4
├── bignumber.js@9.1.1
├── bindings@1.5.0 extraneous
├── crypto-js@4.1.1
├── customize-cra@1.0.0
├── file-saver@2.0.5
├── file-uri-to-path@1.0.0 extraneous
├── firebase@9.17.2
├── geofire-common@5.2.0
├── hashids@2.2.11
├── html2canvas@1.4.1
├── http@0.0.1-security
├── https@1.0.0
├── idb@7.1.1
├── moment@2.29.4
├── nan@2.20.0 extraneous
├── nanoid@4.0.1
├── pullstate@1.25.0
├── rc-checkbox@2.3.2
├── react-app-rewired@2.2.1
├── react-datepicker@4.10.0
├── react-dom@17.0.2
├── react-google-autocomplete@2.7.3
├── react-horizontal-datepicker@2.0.3
├── react-multiple-select-dropdown-lite@2.0.6
├── react-native-awesome-alerts@1.5.2
├── react-native-draggable@3.3.0
├── react-native-get-random-values@1.8.0
├── react-native-safe-area-context@3.4.1
├── react-native-screens@3.20.0
├── react-native-vector-icons@8.1.0
├── react-native-web-linear-gradient@1.1.2
├── react-native-web@0.17.7
├── react-scripts@4.0.3
├── react-select@5.7.0
├── react@17.0.2
├── shx@0.3.4
├── toastify-js@1.12.0
├── uuid@8.3.2
├── w-json-stream@1.0.21
├── web-vitals@1.1.2
├── webpack-bundle-analyzer@4.8.0
└── webpack@4.44.2