import React, { useState, Component, useEffect } from 'react';
import { StyleSheet, ScrollView, Image, Text, View, TextInput, TouchableOpacity, Alert, Modal, KeyboardAvoidingView, Dimensions, ActivityIndicator, useWindowDimensions, Platform } from 'react-native';
// import firebase from 'firebase';
import { getFirestore, collection, query, where, getDocs, limit } from "firebase/firestore";
import { signInAnonymously } from "firebase/auth";
import { logEvent } from "firebase/analytics";
import AsyncStorage from '@react-native-async-storage/async-storage';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import Colors from '../constant/Colors';
import Styles from '../constant/Styles';
import API from '../constant/API';
import { CommonStore } from '../store/commonStore';
import { UserStore } from '../store/userStore';
import ApiClient from '../util/ApiClient';
// import AwesomeAlert from 'react-native-awesome-alerts';
import { useLinkTo, useRoute } from "@react-navigation/native";
import imgLogo from '../asset/image/logo.png';
import { DataStore } from '../store/dataStore';
import { Collections } from '../constant/firebase';
import { ORDER_TYPE, TABLE_QR_SALT } from '../constant/common';
import { prefix } from '../constant/env';
import { isMobile, isSafari, safariChecking, uploadScanQrInfo } from '../util/commonFuncs';
import moment from 'moment';
import Hashids from 'hashids';
import { ANALYTICS, ANALYTICS_PARSED } from '../constant/analytics';

import "react-native-get-random-values";
import { v4 as uuidv4 } from "uuid";
import { TempStore } from '../store/tempStore';
import { PaymentStore } from '../store/paymentStore';
import { idbDel, idbSet } from '../util/db';

var CryptoJS = require('crypto-js');

const hashids = new Hashids(TABLE_QR_SALT);

const NewOrderScreen = props => {
    const {
        route,
    } = props;

    console.log('route');
    console.log(route);

    // this.goToLoginState = this.goToLoginState.bind(this);

    const linkTo = useLinkTo();
    // const windowDimensions = useWindowDimensions();
    const {
        width: windowWidth,
        height: windowHeight,
    } = useWindowDimensions();

    const [showAlertLogin, setShowAlertLogin] = useState(false);

    const email = UserStore.useState(s => s.email);
    const name = UserStore.useState(s => s.name);
    const googleId = UserStore.useState(s => s.googleId);
    const imageUrl = UserStore.useState(s => s.imageUrl);
    const tokenId = UserStore.useState(s => s.tokenId);
    const userIdAnonymous = UserStore.useState((s) => s.userIdAnonymous);

    const isAuthenticating = CommonStore.useState(s => s.isAuthenticating);

    useEffect(() => {
        (async () => {
            if (linkTo) {
                DataStore.update(s => {
                    s.linkToFunc = linkTo;
                });

                global.linkToFunc = linkTo;
            }

            console.log('route');
            console.log(route);
            console.log('window.location.href');
            console.log(window.location.href);

            safariChecking();

            if (route.params === undefined ||
                // route.params.outletId === undefined ||
                route.params.tableId === undefined ||
                // route.params.tableCode === undefined ||
                // route.params.tablePax === undefined ||
                route.params.waiterId === undefined ||
                // route.params.outletId.length !== 36 ||
                // route.params.tableId.length !== 36 ||
                route.params.qrDateTimeEncrypted === undefined
            ) {
                global.errorMsg = 'Invalid link.';

                linkTo && linkTo(`${prefix}/scan`);
            }
            else {
                // check qrDateTimeEncrypted, is it valid

                var qrDateTimeEncrypted = route.params.qrDateTimeEncrypted;
                var qrDateTime = null;
                var qrDateTimeStr = '';
                var expiryMinutes = 60;

                if (qrDateTimeEncrypted) {
                    // var qrDateTimeEncryptedReal = qrDateTimeEncrypted.replaceAll('mykoodoo', '/');
                    // var qrDateTimeBytes = CryptoJS.AES.decrypt(qrDateTimeEncryptedReal, TABLE_QR_SALT);
                    // qrDateTime = parseInt(qrDateTimeBytes.toString(CryptoJS.enc.Utf8));

                    qrDateTimeStr = hashids.decodeHex(qrDateTimeEncrypted);
                    qrDateTime = parseInt(qrDateTimeStr);
                }

                console.log(`qrDateTime: ${qrDateTime}`);

                //////////////////////////////////////////

                // 2023-12-14 - custom table qr scan time

                if (qrDateTimeStr.length === 15) {
                    // means is the new qr code from new build

                    qrDateTime = parseInt(qrDateTimeStr.slice(0, 13));

                    expiryMinutes = parseInt(qrDateTimeStr.slice(13, 15));
                }

                //////////////////////////////////////////

                if (qrDateTime && moment().diff(qrDateTime, 'minute') <= expiryMinutes) {
                    // within 60 minutes still can use

                    // means all got, can login this user to check all info valid or not

                    ///////////////////////////////////////////////////////////////////////
                    ///////////////////////////////////////////////////////////////////////
                    ///////////////////////////////////////////////////////////////////////

                    ////////////////////////////////////////////////////////////////////////////////

                    // 2025-04-27 - clear previous outlet data first

                    await idbDel('@commonStore');
                    await idbDel('@dataStore');
                    await idbDel('@tableStore');
                    await idbDel('@paymentStore');

                    ////////////////////////////////////////////////////////////////////////////////

                    TempStore.update(s => {
                        s.showGreetingPopup = true;
                        s.toWaitForQrLoading = true;
                    });

                    CommonStore.update(s => {
                        s.selectedOutletTableQRUrl = window.location.href;

                        s.selectedOutletSectionId = '';
                        s.selectedOutletTableId = '';
                        s.selectedOutletWaiterId = '';
                        s.selectedOutletTablePax = 0;
                        s.selectedOutletTableCode = '';
                    });

                    var tableId = hashids.decodeHex(route.params.tableId).toString().insert(8, '-').insert(13, '-').insert(18, '-').insert(23, '-');

                    // const outletTableSnapshot = await firebase.firestore().collection(Collections.OutletTable)
                    //     .where('uniqueId', '==', tableId)
                    //     .limit(1)
                    //     .get();

                    ///////////////////////////////////////////////////////////////////////
                    ///////////////////////////////////////////////////////////////////////
                    ///////////////////////////////////////////////////////////////////////

                    // firebase.auth().signInAnonymously()

                    try {
                        setTimeout(() => {
                            signInAnonymously(global.auth)
                                .then((result) => {
                                    TempStore.update(s => {
                                        s.firebaseAuth = true;
                                    });

                                    const firebaseUid = result.user.uid;

                                    ApiClient.GET(API.getTokenKWeb + firebaseUid).then(async (result) => {
                                        console.log('getTokenKWeb');
                                        console.log(result);

                                        if (result && result.token) {
                                            // if (Platform.OS === 'ios') {
                                            //     AsyncStorage.multiSet([
                                            //         ['accessToken', result.token],
                                            //         ['refreshToken', result.refreshToken],
                                            //     ]);
                                            //     // AsyncStorage.setItem('accessToken', result.token);
                                            //     // AsyncStorage.setItem('refreshToken', result.refreshToken);
                                            // }
                                            // else {
                                            //     await AsyncStorage.multiSet([
                                            //         ['accessToken', result.token],
                                            //         ['refreshToken', result.refreshToken],
                                            //     ]);

                                            //     // await AsyncStorage.setItem('accessToken', result.token);
                                            //     // await AsyncStorage.setItem('refreshToken', result.refreshToken);
                                            // }

                                            AsyncStorage.multiSet([
                                                ['accessToken', result.token],
                                                ['refreshToken', result.refreshToken],

                                                ['latestUserId', result.userId],
                                            ]);

                                            global.accessToken = result.token;

                                            UserStore.update(s => {
                                                s.firebaseUid = result.userId;
                                                s.userId = result.userId;
                                                s.role = result.role;
                                                s.refreshToken = result.refreshToken;
                                                s.token = result.token;
                                                s.name = '';
                                                s.email = '';
                                                s.number = '';

                                                s.userGroups = ['EVERYONE'];
                                            });
                                        }
                                        else {
                                            CommonStore.update(s => {
                                                s.alertObj = {
                                                    title: 'Error',
                                                    message: 'Unauthorized access',
                                                };

                                                // s.isAuthenticating = false;
                                            });

                                            global.errorMsg = 'Invalid token.';

                                            linkTo && linkTo(`${prefix}/scan`);
                                        }
                                    });
                                });
                        });
                    }
                    catch (ex) {
                        console.error(ex);
                    }

                    ///////////////////////////////////////////////////////////////////////
                    ///////////////////////////////////////////////////////////////////////
                    ///////////////////////////////////////////////////////////////////////

                    const outletTableSnapshot = await getDocs(
                        query(
                            collection(global.db, Collections.OutletTable),
                            where('uniqueId', '==', tableId),
                            limit(1),
                        )
                    );

                    var outletTable = null;

                    if (!outletTableSnapshot.empty) {
                        outletTable = outletTableSnapshot.docs[0].data();
                    }

                    if (outletTable) {
                        if (outletTable.seated <= 0) {
                            global.errorMsg = 'Table is already unseated.';

                            linkTo && linkTo(`${prefix}/scan`);
                        }
                        else {
                            // const outletSnapshot = await firebase.firestore().collection(Collections.Outlet)
                            //     .where('uniqueId', '==', outletTable.outletId)
                            //     .limit(1)
                            //     .get();

                            const outletSnapshot = await getDocs(
                                query(
                                    collection(global.db, Collections.Outlet),
                                    where('uniqueId', '==', outletTable.outletId),
                                    limit(1),
                                )
                            );

                            var outlet = {};
                            if (!outletSnapshot.empty) {
                                outlet = outletSnapshot.docs[0].data();
                            }

                            if (outlet) {
                                // if (Platform.OS === 'ios') {
                                //     AsyncStorage.setItem('latestOutletId', outlet.uniqueId);

                                //     AsyncStorage.setItem('latestSubdomain', outlet.subdomain || '');

                                //     AsyncStorage.setItem('latestTableId', route.params.tableId);
                                //     AsyncStorage.setItem('latestUserId', result.userId);
                                // }
                                // else {
                                //     await AsyncStorage.setItem('latestOutletId', outlet.uniqueId);

                                //     await AsyncStorage.setItem('latestSubdomain', outlet.subdomain || '');

                                //     await AsyncStorage.setItem('latestTableId', route.params.tableId);
                                //     await AsyncStorage.setItem('latestUserId', result.userId);
                                // }

                                global.subdomainOnly = (outlet.subdomain || '');

                                global.subdomain = outlet.subdomain ? outlet.subdomain : '';

                                AsyncStorage.multiSet([
                                    ['latestOutletId', outlet.uniqueId],
                                    ['latestSubdomain', (outlet.subdomain || '')],
                                    ['latestTableId', route.params.tableId],
                                    // ['latestUserId', result.userId],
                                ]);

                                PaymentStore.update(s => {
                                    s.cartItemsPayment = [];
                                });

                                CommonStore.update(s => {
                                    // 2022-10-08 - Reset cart items
                                    s.cartItems = [];
                                    s.cartItemsProcessed = [];
                                    s.cartOutletId = outlet.uniqueId;

                                    s.cartItemsReservation = [];
                                    s.cartItemsProcessedReservation = [];

                                    s.tcCartItems = [];
                                    s.tcCartItemsProcessed = [];

                                    s.selectedOutletItem = {};
                                    s.selectedOutletItemAddOn = {};
                                    s.selectedOutletItemAddOnChoice = {};
                                    s.onUpdatingCartItem = null;

                                    s.cachedAddOnChoiceIdDict = {};

                                    s.isLoading = false;

                                    s.molpayResult = null;

                                    s.payHybridBody = null;
                                    s.paymentDetails = null;
                                    s.orderIdCreated = '';

                                    s.payTopupCreditBody = null;

                                    s.currPage = '';

                                    s.menuItemDetailModal = false;

                                    ////////////////////////////////////

                                    // 2023-06-06 - To clear reservation states

                                    s.selectedReservationId = '';
                                    s.selectedReservationStartTime = null;
                                    s.selectedReservationPax = '1';
                                    s.selectedReservationOutletSectionId = '';
                                    s.selectedReservationOutletSectionName = '';

                                    // s.reservationIdTemp = '';

                                    s.editUserReservation = {};

                                    s.selectedAddressUserPhone = '';
                                    s.selectedUserEmail = '';
                                    s.selectedUserFirstName = '';
                                    s.selectedUserLastName = '';
                                    s.selectedUserRemarks = '';
                                    s.selectedUserDiet = '';
                                    s.selectedUserOccasion = '';

                                    s.resFirstName = '';
                                    s.resLastName = '';
                                    s.resPhoneNum = '';
                                    s.resEmail = '';
                                    s.resRemarks = '';
                                    s.resDietaryRestrictions = '';
                                    s.resSpecialOccasions = '';

                                    s.isPlacingReservation = false;
                                    s.isPlacingReservationUpselling = false;

                                    s.isDepositOnly = false;

                                    s.resShowConfirmationPage = false;

                                    s.selectedTaggableVoucher = null;
                                    s.selectedBundleTaggableVoucher = null;

                                    s.userLoyaltyStamps = [];
                                    s.redeemableStackedLoyaltyStamps = [];
                                    s.toRedeemStackedLoyaltyStamp = {};

                                    ////////////////////////////////////
                                });

                                await updateUserCart(
                                    {
                                        ...route.params,
                                        outletId: outlet.uniqueId,
                                        tableId: tableId,
                                        tablePax: outletTable.seated ? outletTable.seated : 1,
                                        tableCode: outletTable.code,
                                    },
                                    outlet,
                                    // firebaseUid,
                                    '',
                                );

                                // CommonStore.update(s => {
                                //     s.selectedOutlet = outlet;

                                //     s.scannedQrData = route.params;

                                //     s.orderType = ORDER_TYPE.DINEIN;

                                //     s.selectedOutletTableId = outletTable.tableId;
                                //     s.selectedOutletWaiterId = route.params.waiterId;
                                //     s.selectedOutletTablePax = route.params.tablePax;
                                //     s.selectedOutletTableCode = route.params.tableCode;
                                // }, async () => {
                                //     if (!outlet.subdomain) {
                                //         linkTo(`${prefix}/outlet/menu`);
                                //     }
                                //     else {
                                //         linkTo(`${prefix}/outlet/${outlet.subdomain}/menu`);
                                //     }
                                // });

                                // firebase.analytics().logEvent(ANALYTICS.EVENT_QR_DINEIN_U, {
                                //     eventNameParsed: ANALYTICS_PARSED.EVENT_QR_DINEIN_U,

                                //     // merchantName: global.merchantName,
                                //     outletName: outlet.name,

                                //     merchantId: outlet.merchantId,
                                //     outletId: outlet.uniqueId,
                                //     // userId: global.userId,
                                // });

                                logEvent(global.analytics, ANALYTICS.EVENT_QR_DINEIN_U, {
                                    eventNameParsed: ANALYTICS_PARSED.EVENT_QR_DINEIN_U,

                                    // merchantName: global.merchantName,
                                    // outletName: outlet.name,
                                    outletName: global.outletName ? `${global.outletName} (Web)` : '',

                                    merchantId: outlet.merchantId,
                                    outletId: outlet.uniqueId,
                                    // userId: global.userId,

                                    webUserInfo: `${moment().format('YYYYMMDD-HHmm')}>${global.userName ? global.userName : 'N/A'} (${global.userIdAnonymous ? global.userIdAnonymous : ''})`,
                                });

                                //////////////////////////////////////////////////////

                                AsyncStorage.setItem('orderType', ORDER_TYPE.DINEIN);

                                //////////////////////////////////////////////////////

                                // 2022-12-28 - Get and set user id anonymous

                                setTimeout(async () => {
                                    const userIdAnonymousRaw = await AsyncStorage.getItem('userIdAnonymous');

                                    if (userIdAnonymousRaw) {
                                        // means existed

                                        UserStore.update(s => {
                                            s.userIdAnonymous = userIdAnonymousRaw;
                                        });
                                    }
                                    else {
                                        var userIdAnonymousTemp = uuidv4();
                                        UserStore.update(s => {
                                            s.userIdAnonymous = userIdAnonymousTemp;
                                        });

                                        await AsyncStorage.setItem('userIdAnonymous', userIdAnonymousTemp);
                                    }

                                    global.userIdAnonymousLoaded = true;

                                    // 2023-05-23 - Track QR info

                                    await uploadScanQrInfo(
                                        outlet.uniqueId,
                                        outlet.merchantId,

                                        '',

                                        route.params.tableId,
                                        route.params.waiterId,
                                        route.params.qrDateTimeEncrypted,

                                        userIdAnonymous,
                                    );
                                }, 100);

                                if (userIdAnonymous === 'none') {
                                    // means haven't set before


                                }

                                //////////////////////////////////////////////////////                            
                            }
                            else {
                                global.errorMsg = 'Invalid outlet.';

                                linkTo && linkTo(`${prefix}/scan`);
                            }
                        }
                    }
                    else {
                        global.errorMsg = 'Invalid table.';

                        linkTo && linkTo(`${prefix}/scan`);
                    }
                }
                else {
                    global.errorMsg = 'This QR code is expired already.';

                    linkTo && linkTo(`${prefix}/scan`);
                }
            }
        })();
    }, [linkTo, route]);

    useEffect(() => {
        if (linkTo) {
            DataStore.update(s => {
                s.linkToFunc = linkTo;
            });

            global.linkToFunc = linkTo;
        }
    }, [linkTo]);

    useEffect(() => {
        CommonStore.update(s => {
            s.routeName = route.name;
        });
    }, [route]);

    useEffect(() => {
        const parent = props.navigation.dangerouslyGetParent();
        parent.setOptions({
            tabBarVisible: false,
        });
        return () =>
            parent.setOptions({
                tabBarVisible: true,
            });
    }, []);

    // useEffect(() => {
    //     firebase.auth().onAuthStateChanged((user) => {
    //         if (user) {
    //             // User is signed in, see docs for a list of available properties
    //             // https://firebase.google.com/docs/reference/js/firebase.User

    //             console.log('auth changed!');
    //             console.log(user);

    //             var uid = user.uid;
    //             // ...                
    //         } else {
    //             // User is signed out
    //             // ...
    //         }
    //     });
    // }, []);

    // function here

    const updateUserCart = async (json, outlet, firebaseUid) => {
        const body = {
            // userId: firebaseUid,

            userId: '',

            // outletId: outlet.uniqueId,
            outletId: json.outletId,
            tableId: json.tableId,
            tableCode: json.tableCode,
            tablePax: json.tablePax ? json.tablePax : 1,
            cartItems: [],

            waiterId: json.waiterId,
        };

        global.selectedOutlet = outlet;

        await idbSet(`dso`, outlet.dso !== undefined ? outlet.dso : false);
        await idbSet(`dsopn`, outlet.dsopn !== undefined ? outlet.dsopn : false);

        CommonStore.update(s => {
            s.selectedOutlet = outlet;
            s.forcePayAtCashier = outlet.forcePayAtCashier || false;

            s.scannedQrData = json;

            s.orderType = ORDER_TYPE.DINEIN;

            s.selectedOutletTableId = json.tableId;
            s.selectedOutletWaiterId = json.waiterId;
            s.selectedOutletTablePax = json.tablePax;
            s.selectedOutletTableCode = json.tableCode;

            s.timestampOutletCategory = Date.now();

            // 2022-10-08 - Reset cart items
            // s.cartItems = [];
            // s.cartItemsProcessed = [];
            // s.cartOutletId = outlet.uniqueId;

            // s.selectedOutletItem = {};
            // s.selectedOutletItemAddOn = {};
            // s.selectedOutletItemAddOnChoice = {};
            // s.onUpdatingCartItem = null;

            // s.isLoading = false;

            // s.molpayResult = null;
        }, async () => {
            if (outlet && outlet.uniqueId) {
                // navigation.navigate('OutletMenu', { 
                //     outletData: outlet, 
                //     orderType: 0, 
                //     test: 1 
                // });

                // const subdomain = await AsyncStorage.getItem('latestSubdomain');
                const subdomain = global.subdomainOnly;

                if (!subdomain) {
                    linkTo(`${prefix}/outlet/menu`);
                }
                else {
                    linkTo(`${prefix}/outlet/${subdomain}/menu`);
                }

                // linkTo && linkTo(`${prefix}/outlet/menu`);
            }

            // if (scanner && scanner.current) {
            //     scanner.current.reactivate();
            // }
        });
    };

    // const updateUserCart = async (json, outlet, firebaseUid) => {
    //     const body = {
    //         userId: firebaseUid,
    //         // outletId: outlet.uniqueId,
    //         outletId: json.outletId,
    //         tableId: json.tableId,
    //         tableCode: json.tableCode,
    //         tablePax: json.tablePax,
    //         cartItems: [],

    //         waiterId: json.waiterId,
    //     };

    //     ApiClient.POST(API.updateUserCart, body).then((result) => {
    //         if (result && result.status === 'success') {
    //             global.selectedOutlet = outlet;

    //             CommonStore.update(s => {
    //                 s.selectedOutlet = outlet;

    //                 s.scannedQrData = json;

    //                 s.orderType = ORDER_TYPE.DINEIN;

    //                 s.selectedOutletTableId = json.tableId;
    //                 s.selectedOutletWaiterId = json.waiterId;
    //                 s.selectedOutletTablePax = json.tablePax;
    //                 s.selectedOutletTableCode = json.tableCode;

    //                 s.timestampOutletCategory = Date.now();

    //                 // 2022-10-08 - Reset cart items
    //                 // s.cartItems = [];
    //                 // s.cartItemsProcessed = [];
    //                 // s.cartOutletId = outlet.uniqueId;

    //                 // s.selectedOutletItem = {};
    //                 // s.selectedOutletItemAddOn = {};
    //                 // s.selectedOutletItemAddOnChoice = {};
    //                 // s.onUpdatingCartItem = null;

    //                 // s.isLoading = false;

    //                 // s.molpayResult = null;
    //             }, async () => {
    //                 if (outlet && outlet.uniqueId) {
    //                     // navigation.navigate('OutletMenu', { 
    //                     //     outletData: outlet, 
    //                     //     orderType: 0, 
    //                     //     test: 1 
    //                     // });

    //                     const subdomain = await AsyncStorage.getItem('latestSubdomain');

    //                     if (!subdomain) {
    //                         linkTo(`${prefix}/outlet/menu`);
    //                     }
    //                     else {
    //                         linkTo(`${prefix}/outlet/${subdomain}/menu`);
    //                     }

    //                     // linkTo && linkTo(`${prefix}/outlet/menu`);
    //                 }

    //                 // if (scanner && scanner.current) {
    //                 //     scanner.current.reactivate();
    //                 // }
    //             });
    //         }
    //     });
    // };

    // function end    

    console.log('TEST LOGIN');

    return (
        <View style={{
            width: isMobile() ? windowWidth : windowWidth,
            height: windowHeight,
        }}>

            <View style={{
                justifyContent: 'center',
                alignItems: 'center',

                width: isMobile() ? windowWidth : windowWidth,
                height: windowHeight,
            }}>
                <Image style={{
                    width: 300,
                    height: 67,
                    alignSelf: 'center',
                    // marginBottom: 50,
                }} resizeMode="contain" source={imgLogo} />

                {
                    isAuthenticating
                        ?
                        <View style={{
                            flexDirection: 'column',
                            // alignItems: 'center',
                            // backgroundColor: 'red'
                        }}>
                            {/* <ActivityIndicator color={Colors.primaryColor} size={'large'} />

                            <Text style={{
                                fontFamily: 'NunitoSans-Bold',
                                color: Colors.darkBgColor,
                                fontSize: 16,
                                textAlign: 'center',
                                marginTop: 15,
                            }}>
                                Processing
                            </Text> */}
                        </View>
                        :
                        <></>
                }
            </View>
        </View >
    );
};

const styles = StyleSheet.create({
    container: {
        width: Dimensions.get('window').width,
        height: Dimensions.get('window').height,
    },
    headerLogo: {
        width: 112,
        height: 25
    },
    logo: {
        width: 300,
        height: 67,
        alignSelf: 'center',
        marginTop: 10,
    },
    logoTxt: {
        color: Colors.descriptionColor,
        fontSize: 20,
        letterSpacing: 7,
        marginTop: 10,
        alignSelf: 'center',
        marginBottom: 40,
        fontFamily: 'NunitoSans-Regular',
    },
    loginTxt: {
        color: Colors.mainTxtColor,
        // fontWeight: "500",
        fontSize: 26,
        fontFamily: 'NunitoSans-Bold',
    },
    description: {
        color: Colors.descriptionColor,
        paddingVertical: 10,
        fontFamily: 'NunitoSans-Regular',
        fontSize: 16,
        marginBottom: 5,
        marginTop: -5,
    },
    textInput: {
        height: 50,
        paddingHorizontal: 20,
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 8,
        marginTop: 20,
        fontFamily: 'NunitoSans-Regular',
        fontSize: 16,
    },
    checkBox: {
        borderWidth: StyleSheet.hairlineWidth,
        borderColor: Colors.descriptionColor,

        width: 30,
        height: 10,
        borderRadius: 10,
        justifyContent: 'center',
        alignItems: 'center',

        // marginRight: 5,
    },
    floatbtn: {
        zIndex: 1,
        position: 'absolute',
        bottom: 30,
        right: 30,
        width: 60,
        height: 60,
        borderRadius: 60 / 2,
        backgroundColor: Colors.primaryColor,
        justifyContent: 'center',
        alignContent: 'center',
        alignItems: 'center',
        shadowOffset: {
            width: 0,
            height: 1,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 3
    },
    loginImg: {
        width: undefined,
        height: '100%',
        resizeMode: 'cover'
    },
    resetContainer: {
        alignItems: 'center',
        flexDirection: 'row',
        marginTop: 0,
        alignSelf: 'center'
    },

    switchContainer: {
        position: 'absolute',
        bottom: 0,
        display: 'flex',
        alignItems: 'center',
        width: '100%',
    },

    centerText: {
        padding: 10,
        fontSize: 18,
        color: Colors.descriptionColor,
        textAlign: 'center',
        fontFamily: 'NunitoSans-Regular',
    },
    scanText: {
        fontSize: 20,
        color: '#000000',
        textAlign: 'center',
        backgroundColor: '#008000'
    },
})

export default NewOrderScreen;