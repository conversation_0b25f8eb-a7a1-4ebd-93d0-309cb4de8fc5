import { Store } from 'pullstate';

export const TempStore = new Store({
    reservationFirstName: '',
    reservationLastName: '',
    reservationPhoneNum: '',
    reservationEmail: '',
    reservationRemarks: '',
    reservationDietaryRestrictions: '',
    reservationSpecialOccasions: '',

    isPlacingReservation: false,

    isDepositOnly: false,

    //////////////////////////////////////

    showGeneralAskUserInfo: false,

    // order voucher promo flow

    showVoucherInfo: null,
    // showVoucherInfo: {
    //     campaignName: 'Coffee voucher',
    //     campaignDescription: 'Buy 1 free 1 coffee!',
    //     uniqueId: '',
    // },
    showVoucherPromotionInterestedInfo: false, // false
    isPaidFirstOrder: false,

    createUserOrderBody: null,

    cartItemsT: [],
    cartItemsProcessedT: [],
    cartOutletIdT: null,

    userCrm: null,
    userActual: null,
    userTaggableVoucher: null,

    //////////////////////////////////////

    claimableVoucher: null,

    //////////////////////////////////////

    // currPageStack: [],

    //////////////////////////////////////

    clickedChannelOptionTimes: 0,
    isSentHardwareInfo: false,
    showUserHelpPopup: false,

    //////////////////////////////////////

    // initHrefLink: 

    //////////////////////////////////////

    startAsMember: false,
    showStartAsGuestButton: true,
    checkedSignInWithPhone: '',

    showGreetingPopup: false,

    toWaitForQrLoading: false,
    qrGenericOutlet: {},

    qrGenericJson: {},
    qrGenericParams: {},

    selectedPromoCodePromotion: {},

    isJustApplyPromoCodePromotion: false,

    //////////////////////////////////////

    //Claim Voucher Popup - 03/04/23

    showClaimVoucher: false,
    claimingVoucher: false,
    claimVoucherList: [],
    selectedClaimVoucher: null,

    supplementModal: false,

    // resShowConfirmationPage: false,

    recommendedItems: [],

    rewardsModal: true,
    claimVoucherAnonymous: {}, // to assign a claimable (buyable) voucher (with points), for anonymous user

    rewardsModalEU: true, // for existing user
    claimVoucherEU: {}, // to assign a claimable (buyable) voucher (with points), for existing user

    redeemVoucherModal: false,
    redeemVoucher: {},

    newsw: undefined,

    // 20240618 e-invoice
    showSignUpMember: false,

    // 20240618 e-invoice changes
    showNamePhoneFieldsForRegister: true,

    showLoginPhoneModal: false,
    fromApplyVoucherButton: false,

    typedVoucherCode: '',
    
    voucherBundle: false,

    isJustApplyCredit: false,

    rewardSection: 'REWARDS',

    firebaseAuth: null,

    tickboxConsent: true,
});
